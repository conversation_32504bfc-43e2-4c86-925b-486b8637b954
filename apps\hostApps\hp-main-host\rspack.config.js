const { composePlugins, withNx, withReact } = require('@nx/rspack');
const { withModuleFederation } = require('@nx/rspack/module-federation');
const commonRulesRsPack = require('../../../scripts/rspack.common');
const baseConfig = require('./module-federation.config');
const config = {
  ...baseConfig,
};

// Nx plugins for rspack to build config object from Nx options and context.
// DTS Plugin is disabled in Nx Workspaces as Nx already provides Typing support for Module Federation
// The DTS Plugin can be enabled by setting dts: true
// Learn more about the DTS Plugin here: https://module-federation.io/configure/dts.html

module.exports = composePlugins(
  withNx(),
  withReact(),
  withModuleFederation(config),
  (config, { context }) => {
    commonRulesRsPack(config);

    // Allow external access
    config.devServer = {
      host: '0.0.0.0',
      port: 4200,
      allowedHosts: 'all',
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
      hot: true, // Enable hot module replacement
      compress: true, // Reduce data transfer
      static: false, // Avoid serving static files unnecessarily
      liveReload: false, // Disable full-page reload
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
      devMiddleware: {
        writeToDisk: false, // avoid writing files to disk unless needed
      },
    };
    config.resolve = {
      ...(config.resolve || {}),
      fallback: {
        ...(config.resolve?.fallback || {}),
        crypto: false, // 🔥 this disables Node's crypto polyfill in browser builds
      },
    };
    return config;
  }
);
