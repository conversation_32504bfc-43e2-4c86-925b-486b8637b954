/* eslint-disable no-console */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @create date 13-07-2021 10:13:11
 * @modify date 02-05-2023 12:14:17
 * @desc [description]
 */
import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  showDialog,
  getMultiselectFilterData,
  passInvEventTime,
  getCoordsList,
  inputTextDescription,
  passINVEditedDetails,
  extractData,
} from '@hp/mainstore';
import { utils } from '@hp/utils';
import { InvDatatable } from './INV-DataTable';
import { PoDataTable } from './PO-DataTable';
import { AP_file_url } from '@hp/constants';
import {
  TextInput,
  Input,
  globalutils,
  dataTableServiceProvider,
} from '@hp/components';
import cloneDeep from 'lodash.clonedeep';
import { AreaSelector } from '@bmunozg/react-image-area';
import { Document, Page } from 'react-pdf';

import { getDueDateCalculated } from '@hp/mainstore';
const PoInvMatchingCompareCompTesting = (props) => {
  const {
    disabled,
    className,
    tableName,
    headerCheckBoxes,
    onSaveClick,
    onSaveDisable,
    addCommentsValue,
    notificationBar,
    onSubmittedClick,
    onSubmittedClickDisable,
    onSubmitvalidationSuccess,
    setisLoading,
    setDisToolTip,
    setDisabled,
  } = props;

  const {
    accpayFormControls,
    accpayRowIcons,
    dueDateFromBaselineDate,
    dueDateFromBaselineDateError,
    multiselectFilterData,
    Extraction,
    ExtractData,
  } = useSelector((store) => store.accpay);

  const [disabledFlag, setDisabledFlag] = useState(true);
  const [baselineDate, setBaselineDate] = React.useState();
  const [dueDateList, setDueDateList] = React.useState({});
  const [paymentTermsId, setPaymentTermsId] = useState();
  const imgHandle = React.useState(['hide', 'hide']);
  const [invImageFilePath, setInvImageFilePath] = React.useState([]);
  const [primaryFormElements, setPrimaryFormElements] = React.useState([]);
  const [secondaryFormElements, setSecondaryFormElements] = React.useState([]);
  const [classToDisabled, setClassToDisabled] =
    React.useState('is-not-editing');
  const [invFormControls, setInvFormControls] = React.useState();
  const [poFormControls, setPoFormControls] = React.useState();
  const [multiselectFilterValue, setMultiselectFilterValue] =
    React.useState('');
  const [errorFlagCount, setErrorFlagCount] = React.useState();
  const [itemListMessage, setItemListMessage] = React.useState('');

  const [extractionData, setExtractionData] = useState(null);
  const [areas, setAreas] = useState([]);
  const [enableDraw, setEnableDraw] = useState(false);
  const [numPages, setNumPages] = useState();
  const [focusedInput, setFocusedInput] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageScale, setPageScale] = useState(1);
  const docRef = useRef(null);

  const onChangeHandler = (areas) => {
    setAreas(areas);
  };
  const [checkFlag, setcheckFlag] = useState({
    invNumberFlag: false,
    poNumberFlag: false,
    supplierNameFlag: false,
    invDateFlag: false,
    baselineDateFlag: false,
    amountFlag: false,
    currencyFlag: false,
    itemListFlag: false,
  });

  const dispatch = useDispatch();

  useEffect(() => {
    if (accpayFormControls) {
      setInvFormControls(accpayFormControls.invFormResponse.data.value);
      setPoFormControls(
        accpayFormControls.poFormResponse
          ? accpayFormControls.poFormResponse.data.value
          : []
      );
      setAreas([]);

      dispatch(
        getCoordsList({
          supplierId:
            accpayFormControls?.invFormResponse?.data?.value?.supplierId,
          invId: accpayFormControls?.invFormResponse?.data?.value?.inv_id,
        })
      );

      setInvImageFilePath(
        accpayFormControls.invFormResponse.data.value.filePath
      );

      dispatch(
        passInvEventTime(
          accpayFormControls.invFormResponse.data.value.eventTime
        )
      );
    }
    return () => {
      setInvImageFilePath(null);
    };
  }, [accpayFormControls, dispatch]);

  useEffect(() => {
    setDisabledFlag(disabled);
    if (!disabled) {
      setEnableDraw(true);
      setClassToDisabled('is-editing');
    } else {
      setEnableDraw(false);
      setClassToDisabled('is-not-editing');
    }
  }, [disabled]);

  useEffect(() => {
    if (onSubmittedClick) {
      checkValidation();
    }
    if (onSaveClick) {
      let formDetails = cloneDeep(invFormControls);
      dispatch(
        passINVEditedDetails(
          globalutils.getDataFromStorage('clientId'),
          globalutils.getDataFromStorage('userId'),
          formDetails
        )
      );
      dispatch(inputTextDescription([]));
    }
  }, [
    checkValidation,
    dispatch,
    invFormControls,
    onSaveClick,
    onSubmittedClick,
  ]);

  useEffect(() => {
    dueDateFromBaselineDate && dueDateFromBaselineDate !== undefined
      ? setDueDateList({
          dueDateFromBaselineDateList: dueDateFromBaselineDate.value,
        })
      : '';
  }, [dueDateFromBaselineDate, dueDateFromBaselineDateError]);

  useEffect(() => {
    if (invFormControls) {
      let formDetails = cloneDeep(invFormControls);

      formDetails.formDetailsDtoList.map(function (data, index) {
        if (data.uniqueKey === 'baselineDate') {
          if (
            dueDateList &&
            dueDateList.dueDateFromBaselineDateList &&
            dueDateList.dueDateFromBaselineDateList.baselineDate !== undefined
          ) {
            formDetails.formDetailsDtoList[index].value =
              dueDateList.dueDateFromBaselineDateList.baselineDate;
          }
        }
        if (data.uniqueKey === 'dueDate') {
          if (
            dueDateList &&
            dueDateList.dueDateFromBaselineDateList &&
            dueDateList.dueDateFromBaselineDateList.dueDate !== undefined
          ) {
            formDetails.formDetailsDtoList[index].value =
              dueDateList.dueDateFromBaselineDateList.dueDate;
          }
        }
        return null;
      });
      setInvFormControls({ ...formDetails });
    }
  }, [dueDateList, invFormControls]);

  useEffect(() => {
    if (invFormControls) {
      let formDetails = cloneDeep(invFormControls);
      formDetails && formDetails.formDetailsDtoList
        ? formDetails.formDetailsDtoList.map(function (data) {
            if (data.uniqueKey === 'baselineDate') {
              data.value ? setBaselineDate(data.value) : setBaselineDate('');
            }
            if (data.uniqueKey === 'pymtTerms') {
              data.value
                ? setPaymentTermsId(data.value)
                : setPaymentTermsId(null);
            }
          })
        : null;
    }
  }, [invFormControls]);

  const getDueDateFromBaselineDate = (uniqueName, targetValue) => {
    let formDetails = cloneDeep(invFormControls);
    let invId = formDetails.inv_id;
    let clientId = formDetails.clientId;
    if (uniqueName === 'invoiceDate') {
      let invoiceDate = targetValue;
      dispatch(
        getDueDateCalculated({
          invId,
          paymentTermsId: null,
          clientId,
          invoiceDate,
          baselineDate,
        })
      );
    }
    if (uniqueName === 'baselineDate') {
      let baseline_Date = targetValue;
      paymentTermsId
        ? dispatch(
            getDueDateCalculated({
              invId,
              paymentTermsId: null,
              clientId,
              invoiceDate: null,
              baselineDate: baseline_Date,
            })
          )
        : '';
    }
    if (uniqueName === 'pymtTerms') {
      baselineDate
        ? dispatch(
            getDueDateCalculated({
              invId,
              paymentTermsId: targetValue,
              clientId,
              invoiceDate: null,
              baselineDate,
            })
          )
        : '';
    }
  };

  const handleINVOnChange = (event, uniqueKey) => {
    let formDetails = cloneDeep(invFormControls);
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = event.target.value)
        : null;
    });
    setInvFormControls({ ...formDetails });
  };

  const handleDateChange = (event, uniqueKey) => {
    let formDetails = cloneDeep(invFormControls);
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = event)
        : null;
    });
    setInvFormControls({ ...formDetails });
    getDueDateFromBaselineDate(uniqueKey, event);
  };

  const handleComboBoxValue = (event, uniqueKey) => {
    let targetValue = parseInt(event.target.value);
    let formDetails = cloneDeep(invFormControls);
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = targetValue)
        : null;
    });
    setInvFormControls({ ...formDetails });
    if (uniqueKey === 'pymtTerms') {
      getDueDateFromBaselineDate(uniqueKey, targetValue);
    }
  };

  const handleMultiselectComboBoxValue = (event, uniqueKey) => {
    if (!event.id || !event.value) return;
    let formDetails = cloneDeep(invFormControls);
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = event)
        : null;
    });
    setInvFormControls({ ...formDetails });
  };

  const checkValidation = () => {
    let count = 0;
    let itemListErrorCount = 0;
    let formDetails = cloneDeep(invFormControls);
    formDetails.userComments = addCommentsValue ? addCommentsValue : '';
    const iterableData = formDetails.formDetailsDtoList;
    formDetails.formDetailsDtoList.map(function (data, index) {
      switch (data.uniqueKey) {
        case 'invoiceNo': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, invNumberFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'poNo': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, poNumberFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'supplier': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, supplierNameFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'invoiceDate': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, invDateFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'baselineDate': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, baselineDateFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'amount': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, amountFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'currency': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, currencyFlag: true };
              }),
              (iterableData[index].errorFlag = false))
            : (iterableData[index].errorFlag = true);
        }
        case 'itemList': {
          count = count + 1;
          return (
            data.value.length &&
            data.value.map((item) => {
              item.serialNo && item.description && item.amountConverted
                ? setcheckFlag((prevState) => {
                    return { ...prevState, itemListFlag: true };
                  })
                : (itemListErrorCount = itemListErrorCount + 1);
            })
          );
        }
      }
    });
    itemListErrorCount > 0
      ? (setcheckFlag((prevState) => {
          return { ...prevState, itemListFlag: false };
        }),
        setItemListMessage('Data missing in item list!'))
      : null;

    setInvFormControls({ ...formDetails });
    setErrorFlagCount(count);
  };

  useEffect(() => {
    if (errorFlagCount && errorFlagCount === 8) {
      let checkFlagresult = true;
      try {
        for (var i in checkFlag) {
          if (i && checkFlag[i] === false) {
            checkFlagresult = false;
            break;
          }
        }
      } catch (err) {
        notificationBar('error', 'Something went wrong!..');
      }

      checkFlagresult === true
        ? onSubmittedClick
          ? onSubmitvalidationSuccess(true)
          : null
        : (setisLoading(false),
          notificationBar(
            'error',
            'Process failed! Mandatory fields missing. ' + itemListMessage
          ));
      onSaveDisable(false);
      onSubmittedClickDisable(false);
      setErrorFlagCount(0);
    }
  }, [errorFlagCount]);

  const onChangeHandlingFunctions = {
    handleINVOnChange,
    handleDateChange,
    handleComboBoxValue,
    handleMultiselectComboBoxValue,
  };

  const multiselectFilter = (options, filter, uniqueKey, formtype) => {
    if (uniqueKey && uniqueKey === 'supplier') {
      if (filter) {
        setMultiselectFilterValue(filter);

        let data =
          multiselectFilterData &&
          multiselectFilterData.value &&
          multiselectFilterData.value.length
            ? multiselectFilterData.value.map((item) => {
                const container = {};
                container.label = item.supplierName;
                container.value = item.supplierName;
                container.disabled =
                  globalutils.setMultiSelectStaticDisable(formtype);
                return container;
              })
            : options;
        return data;
      } else {
        return options;
      }
    } else {
      const re = new RegExp(filter, 'i');
      return options.filter(({ label }) => label && label.match(re));
    }
  };

  useEffect(() => {
    if (multiselectFilterValue) {
      dispatch(getMultiselectFilterData(multiselectFilterValue));
    }
  }, [multiselectFilterValue]);

  /**Function returns a list of labels from incoming form array list.
   * <AUTHOR> R B
   */
  const getLabelListFromFormControls = (formList) => {
    if (formList) {
      return formList.map((obj) =>
        obj.displayName && obj.type !== 'DataTable' ? obj.displayName : null
      );
    }
  };
  /**Function returns list of labels to form array list.
   * <AUTHOR> Sherlin
   */
  function getLabelList(invLabelList, poLabelList) {
    if (invLabelList && poLabelList) {
      return invLabelList.concat(poLabelList);
    } else if (invLabelList) {
      return invLabelList;
    } else if (poLabelList) {
      return poLabelList;
    } else {
      return null;
    }
  }

  /**Function returns Icon class name to form array list.
   * <AUTHOR> Sherlin
   */
  function getIcons(filteredLabelList, i) {
    if (
      tableName &&
      tableName[1] !== 'approved' &&
      tableName[1] !== 'for-approve'
    ) {
      if (accpayRowIcons && accpayRowIcons.data.value) {
        let formIconData = accpayRowIcons.data.value;
        const findIndex = formIconData.findIndex(
          (item) => item.fieldName === filteredLabelList[i]
        );
        if (findIndex !== -1) {
          return formIconData[findIndex].fieldClassName;
        } else {
          return 'ico-';
        }
      }
    }
    return '';
  }
  function getIconTitle(filteredLabelList, i) {
    if (accpayRowIcons && accpayRowIcons.data.value) {
      let formIconData = accpayRowIcons.data.value;
      const findIndex = formIconData.findIndex(
        (item) => item.fieldName === filteredLabelList[i]
      );
      if (findIndex !== -1) {
        return formIconData[findIndex].fieldDisplayTitle;
      } else {
        return '';
      }
    }
  }

  const setValueOfDataTable = (dataTableValue) => {
    let formDetails = cloneDeep(invFormControls);
    if (formDetails) {
      formDetails.invItemsDtoList = dataTableValue;
      formDetails.formDetailsDtoList.map((item) => {
        item.type === 'DataTable' ? (item.value = dataTableValue) : null;
      });
    }
    setInvFormControls({ ...formDetails });
  };
  const onDocPointer = () => {
    if (docRef.current) {
      docRef.current.scrollIntoView({ behavior: 'smooth' });
      setDisToolTip(true);
      setDisabled(false);
    }
  };

  /**This hook returns a map with label as key and list of po and inv matched result as value .
   * <AUTHOR> R B
   */
  useEffect(() => {
    if (invFormControls && poFormControls) {
      if (invFormControls.formDetailsDtoList) {
        var invoiceFormControlsData = invFormControls.formDetailsDtoList.filter(
          (e) => e.type !== 'div'
        );
        var invLabelList = getLabelListFromFormControls(
          invoiceFormControlsData
        ).filter((e) => e != null);
      }
      if (poFormControls.formDetailsDtoList) {
        var poFormControlsData = poFormControls.formDetailsDtoList;
        var poLabelList = getLabelListFromFormControls(
          poFormControlsData
        ).filter((e) => e != null);
      }
      var combinedArrayList = null;
      combinedArrayList = getLabelList(invLabelList, poLabelList);

      let primaryArray = [];
      let secondaryArray = [];
      if (combinedArrayList != null) {
        let filteredLabelList = [...new Set(combinedArrayList)];

        filteredLabelList.some((labelName, i) => {
          var labelAndValueMap = [];
          let poForm = [];
          let invForm = [];
          var invFlag = false;
          var poFlag = false;
          invoiceFormControlsData
            ? invoiceFormControlsData.map((invoice) => {
                if (
                  invoice.displayName &&
                  invoice.displayName === labelName &&
                  invoice.type !== 'DataTable'
                ) {
                  invForm.push(invoice);
                  invFlag = true;
                }
              })
            : null;

          poFormControlsData
            ? poFormControlsData.map((purchase) => {
                if (
                  purchase.displayName &&
                  purchase.displayName === labelName &&
                  purchase.type !== 'DataTable'
                ) {
                  poForm.push(purchase);
                  poFlag = true;
                }
              })
            : null;

          labelAndValueMap.push({
            [labelName]: {
              poForm: poForm,
              invForm: invForm,
              iconClassName:
                tableName[0] === 'INV'
                  ? getIcons(filteredLabelList, i)
                  : 'ico-',
              iconTitle:
                tableName[0] === 'INV'
                  ? getIconTitle(filteredLabelList, i)
                  : '',
              isEditable:
                poForm && poForm.length ? 'row-not-editable' : 'row-editable',
              required: invForm[0] ? invForm[0].required : '',
            },
          });
          if (invFlag && poFlag) {
            if (poForm.length && invForm.length) {
              primaryArray.push(...labelAndValueMap);
            }
          } else {
            secondaryArray.push(...labelAndValueMap);
          }
        });
      }
      setPrimaryFormElements(primaryArray);
      setSecondaryFormElements(secondaryArray);
    }
  }, [poFormControls, invFormControls, accpayRowIcons]);

  /**Invoice Extraction Controls.
   * <AUTHOR> Wani
   */

  useEffect(() => {
    if (Extraction !== undefined) {
      setExtractionData(Extraction);
    }
  }, [Extraction]);

  useEffect(() => {
    if (ExtractData !== undefined) {
      if (areas && areas[0]) {
        var msg = ``;
        if (ExtractData?.key && ExtractData?.outputValue) {
          msg = `Extracted ${ExtractData?.key} : ${ExtractData?.outputValue} `;
        } else {
          msg = `No datas have been Extracted `;
        }
        dispatch(showDialog(true, 'success', msg, true, false));
      }

      if (invFormControls && poFormControls) {
        if (invFormControls.formDetailsDtoList) {
          var invoiceFormControlsData =
            invFormControls.formDetailsDtoList.filter((e) => e.type !== 'div');
          var invLabelList = getLabelListFromFormControls(
            invoiceFormControlsData
          ).filter((e) => e != null);
        }
        if (poFormControls.formDetailsDtoList) {
          var poFormControlsData = poFormControls.formDetailsDtoList;
          var poLabelList = getLabelListFromFormControls(
            poFormControlsData
          ).filter((e) => e != null);
        }
        var combinedArrayList = null;
        combinedArrayList = getLabelList(invLabelList, poLabelList);

        let primaryArray = [];
        let secondaryArray = [];
        if (combinedArrayList != null) {
          let filteredLabelList = [...new Set(combinedArrayList)];

          filteredLabelList.some((labelName, i) => {
            var labelAndValueMap = [];
            let poForm = [];
            let invForm = [];
            var invFlag = false;
            var poFlag = false;
            invoiceFormControlsData
              ? invoiceFormControlsData.map((invoice) => {
                  if (
                    invoice.displayName &&
                    invoice.displayName === labelName &&
                    invoice.type !== 'DataTable'
                  ) {
                    if (invoice.uniqueKey === ExtractData.key) {
                      invoice.value = ExtractData?.outputValue;
                    }

                    invForm.push(invoice);
                    invFlag = true;
                  }
                })
              : null;

            poFormControlsData
              ? poFormControlsData.map((purchase) => {
                  if (
                    purchase.displayName &&
                    purchase.displayName === labelName &&
                    purchase.type !== 'DataTable'
                  ) {
                    poForm.push(purchase);
                    poFlag = true;
                  }
                })
              : null;

            labelAndValueMap.push({
              [labelName]: {
                poForm: poForm,
                invForm: invForm,
                iconClassName:
                  tableName[0] === 'INV'
                    ? getIcons(filteredLabelList, i)
                    : 'ico-',
                iconTitle:
                  tableName[0] === 'INV'
                    ? getIconTitle(filteredLabelList, i)
                    : '',
                isEditable:
                  poForm && poForm.length ? 'row-not-editable' : 'row-editable',
                required: invForm[0] ? invForm[0].required : '',
              },
            });
            if (invFlag && poFlag) {
              if (poForm.length && invForm.length) {
                primaryArray.push(...labelAndValueMap);
              }
            } else {
              secondaryArray.push(...labelAndValueMap);
            }
          });
        }
        const primary = cloneDeep(primaryArray);
        const secondary = cloneDeep(secondaryArray);
        setPrimaryFormElements(primary);
        setSecondaryFormElements(secondary);
      }
    }
  }, [ExtractData]);

  const sendForExtractionHandler = () => {
    if (
      Extraction?.filePath === undefined ||
      Extraction?.filePath === null ||
      Extraction?.filePath === ''
    ) {
      return;
    } else if (
      areas === undefined ||
      areas === null ||
      areas[0] === undefined ||
      areas[0] === null
    ) {
      dispatch(
        showDialog({
          showPopup: true,
          type: 'error',
          responseMessage: 'Please draw a box to extract information...',
          canClose: true,
          autoHide: true,
        })
      );
    }

    if (areas && areas[0]) {
      dispatch(
        extractData({
          data: {
            fileFullPath: Extraction?.filePath,
            rectangle: {
              x: areas[0].x,
              y: areas[0].y,
              width: areas[0].width * 2,
              height: areas[0].height * 2,
            },
            key: focusedInput,
            clientId: globalutils.getDataFromStorage('clientId'),
          },
          reqType: '',
        })
      );
    }
    setAreas([]);
  };

  const saveCordsHandler = () => {
    if (
      (accpayFormControls &&
        accpayFormControls?.invFormResponse?.data?.value?.supplierId ===
          undefined) ||
      accpayFormControls?.invFormResponse?.data?.value?.supplierId === null ||
      accpayFormControls?.invFormResponse?.data?.value?.supplierId === ''
    ) {
      dispatch(showDialog(true, 'error', 'Supplier id not found', true, true));
    }
  };

  const customRenderer = (IAreaRendererProps) => {
    if (!IAreaRendererProps.isChanging) {
      if (focusedInput !== null && focusedInput !== '') {
        sendForExtractionHandler();
        saveCordsHandler();
      }

      return (
        <div
          key={1}
          className="label_holder"
          style={{ height: '100%', width: '100%' }}
        >
          <p className="box_label">{IAreaRendererProps.areaNumber}</p>
        </div>
      );
    }
  };

  const focusHandler = (event, formType) => {
    setFocusedInput(formType?.uniqueKey);
  };

  /**Function returns Form Controls.
   * <AUTHOR> Sherlin
   */

  function getForms(getParameter) {
    return getParameter.map((formControl, index) => {
      let formData = formControl[Object.keys(formControl)];
      return (
        <div className="single" key={index}>
          <div className="td label">
            {formData.iconClassName !== 'ico-' ? (
              <i className={formData.iconClassName} title={formData.iconTitle}>
                <span className="path1"></span>
                <span className="path2"></span>
                <span className="path3"></span>
              </i>
            ) : (
              <i className={'ico-'}></i>
            )}
            <p
              className={
                formData.required === 'Y' ? 'required-asterisk-field' : ''
              }
            >
              {Object.keys(formControl)}
            </p>
          </div>

          <div
            className={[
              'td invoice',
              formControl &&
              formControl[Object.keys(formControl)].invForm.length
                ? ''
                : 'this-is-empty',
            ].join(' ')}
          >
            {formControl &&
            formControl[Object.keys(formControl)].invForm.length ? (
              <Input
                disabledFlag={props.disabled}
                formType={formControl[Object.keys(formControl)].invForm[0]}
                onChangeHandler={(element, event) =>
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey
                  )
                }
                multiselectFilter={multiselectFilter}
                onFocusHandler={(event, formType) =>
                  focusHandler(event, formType)
                }
              />
            ) : (
              ''
            )}
          </div>

          <div
            className={[
              'td po',
              formControl && formControl[Object.keys(formControl)].poForm.length
                ? ''
                : 'this-is-empty',
            ].join(' ')}
          >
            {formControl &&
            formControl[Object.keys(formControl)].poForm.length ? (
              formData.isEditable === 'row-not-editable' ? (
                <p>{formControl[Object.keys(formControl)].poForm[0].value}</p>
              ) : (
                <TextInput
                  name=""
                  autoComplete="off"
                  disabledFlag={props.disabled}
                  value={formControl[Object.keys(formControl)].poForm[0].value}
                />
              )
            ) : (
              ''
            )}{' '}
          </div>
        </div>
      );
    });
  }

  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const rowClickCondition = (event) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      event.poItemId,
      'poItemId'
    );
    setConditionalStyles(styleAttribute);
  };

  function handleNext() {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1);
    }
  }
  function handlePrevious() {
    if (pageNumber > 0) {
      setPageNumber(pageNumber - 1);
    }
  }
  function handleZoomIn() {
    if (pageScale < 3) {
      setPageScale(pageScale + 0.2);
    }
  }

  function handleZoomOut() {
    if (pageScale > 0.3) {
      setPageScale(pageScale - 0.2);
    }
  }
  return (
    <div
      className={[
        className,
        'edit-only-half',
        tableName[0] === 'INV' ? classToDisabled : 'is-not-editing',
      ].join(' ')}
    >
      <div className="inv-div">
        <div className="inv-table-view">
          <div className="inv-table-header">
            <div className="inv-table-format">
              <div className="th label"></div>
              <div className="th invoice">Invoice</div>
              <div className="th po">Purchase Order</div>
            </div>
            {headerCheckBoxes && headerCheckBoxes.headerInvImage ? (
              <div className="th inv-image">
                Invoice Image
                <span
                  title="Click to open in new Tab"
                  className="icon icon-attachment"
                  style={{ cursor: 'pointer', float: 'right' }}
                  onClick={() =>
                    utils.redirectToPDF(invImageFilePath, dispatch)
                  }
                ></span>
              </div>
            ) : null}
          </div>

          <div className="inv-table-body">
            <div
              className={[
                'primary-secondary-table-wrap',
                'data-full-view-wrap',
              ].join(' ')}
            >
              <div className="inv-table-body-format">
                {primaryFormElements && primaryFormElements.length
                  ? getForms(primaryFormElements)
                  : ''}
              </div>
              <h3 className="reference-title">Reference</h3>
              <div className="inv-table-body-format reference-table">
                {secondaryFormElements && secondaryFormElements.length
                  ? getForms(secondaryFormElements)
                  : ''}
              </div>
            </div>

            {headerCheckBoxes && headerCheckBoxes.headerInvImage ? (
              <div className={['inv-image', imgHandle[0]].join(' ')}>
                <div className="inv-widget mb40">
                  <div className="inv-widget-element">
                    {invImageFilePath ? (
                      enableDraw ? (
                        <div>
                          <AreaSelector
                            areas={areas}
                            onChange={onChangeHandler}
                            maxAreas={1}
                            customAreaRenderer={customRenderer}
                            wrapperStyle={{
                              display: 'block',
                            }}
                            globalAreaStyle={{
                              border: '2px dotted blue',
                            }}
                            unit="pixel"
                          >
                            <Document
                              file={AP_file_url + invImageFilePath || ''}
                              onLoadError={console.error}
                              onLoadSuccess={({ numPages }) => {
                                setNumPages(numPages);
                              }}
                            >
                              <Page pageNumber={pageNumber} scale={pageScale} />
                            </Document>
                          </AreaSelector>
                          <button
                            className="button secondary"
                            onClick={handleZoomIn}
                            disabled={pageScale >= 3}
                          >
                            Zoom +
                          </button>
                          <button
                            className="button secondary"
                            onClick={handleZoomOut}
                            disabled={pageScale <= 0.3}
                          >
                            Zoom -
                          </button>
                          {numPages > 1 && (
                            <div className="button-container">
                              <button
                                className="button outline"
                                onClick={handlePrevious}
                                disabled={pageNumber === 1}
                              >
                                ‹ Previous
                              </button>
                              <button
                                className="button outline"
                                onClick={handleNext}
                                disabled={pageNumber === numPages}
                              >
                                Next ›
                              </button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div>
                          <div className="doc-viewer-toolbar">
                            <span
                              className="icon-zoom-in mr20"
                              onClick={handleZoomIn}
                              disabled={pageScale >= 3}
                            ></span>
                            <span
                              className="icon-zoom-out"
                              onClick={handleZoomOut}
                              disabled={pageScale <= 0.3}
                            ></span>

                            {true && (
                              <div className="inv-ext-pagination-button-container">
                                <span
                                  className="icon-keyboard_arrow_left"
                                  onClick={handlePrevious}
                                  disabled={pageNumber === 1}
                                ></span>
                                <span
                                  className="icon-keyboard_arrow_right1"
                                  onClick={handleNext}
                                  disabled={pageNumber === numPages}
                                ></span>
                              </div>
                            )}
                          </div>
                          <Document
                            file={AP_file_url + invImageFilePath || ''}
                            onLoadError={console.error}
                            onLoadSuccess={({ numPages }) => {
                              setNumPages(numPages);
                            }}
                          >
                            <Page pageNumber={pageNumber} scale={pageScale} />
                          </Document>
                        </div>
                      )
                    ) : (
                      <div className="no-data">No data available!</div>
                    )}
                  </div>
                </div>
              </div>
            ) : null}
          </div>
        </div>

        <div className="force-full-width">
          <div className={'data-table-wrap'}>
            <InvDatatable
              disabledFlag={disabledFlag}
              conditionalRowStyles={conditionalRowStyles}
              dataTableClick={rowClickCondition}
              setValueOfDataTable={setValueOfDataTable}
              onDocPointer={onDocPointer}
            />
            <PoDataTable
              conditionalRowStyles={conditionalRowStyles}
              dataTableClick={rowClickCondition}
              disabledFlag={disabledFlag}
              setValueOfDataTable={setValueOfDataTable}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export { PoInvMatchingCompareCompTesting };
