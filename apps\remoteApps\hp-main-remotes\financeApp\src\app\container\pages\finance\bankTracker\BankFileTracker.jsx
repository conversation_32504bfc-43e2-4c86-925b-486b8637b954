/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-04-10 17:38:15
 * @modify date 2025-04-10 17:38:15
 * @desc [description]
 */

import { useState, useEffect } from 'react';
import { But<PERSON>, CommonSpinner } from '@hp/components';
import { DatePicker } from 'antd';
import { AP_USER } from '@hp/constants';
import { FileCard } from './bankFileTrackerSubEelements/FileCard';
import { useDispatch, useSelector } from 'react-redux';

import { Select, Empty } from 'antd';

import './BankFileTracker.scss';
import Scrollbars from 'react-custom-scrollbars';

import {
  showDialog,
  getBankFileTrackerDetailsByDate,
  getRecentBankFileTrackerDetails,
  financeResetStateField,
  loading,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';
function BankFileTracker() {
  let user = globalutils.getDataFromStorage('all');
  let clientDateFormat = user?.clientDateFormat ?? 'YYYY-MM-DD';

  const clientId = user.clientId;
  const { recentBankSessionRes, bankSessionResonseByDate } = useSelector(
    (store) => store.finance
  );
  const [dateRange, setDateRange] = useState([]);
  const [selectedBanks, setSelectedBanks] = useState([]);
  const [bankOptions, setBankOptions] = useState([]);
  const [bankSessionTrackRecords, setBankSessionTrackRecords] = useState({});
  const { RangePicker } = DatePicker;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getRecentBankFileTrackerDetails(clientId));
    return () => {
      dispatch(
        financeResetStateField([
          'recentBankSessionRes',
          'bankSessionResonseByDate',
          'trackRcdError',
        ])
      );
    };
  }, []);

  useEffect(() => {
    const sessionData = recentBankSessionRes?.value;

    if (!sessionData) return;

    // Set bank session tracking records
    let sessionDetails = sessionData?.sessionDetailsMap ?? {};
    setBankSessionTrackRecords(sessionDetails);

    // Set bank options if valid
    const bankList = Array.isArray(sessionData.bankComboBox)
      ? sessionData.bankComboBox
          .filter((item) => item.commonName !== '')
          .map((item) => ({ value: item.commonName, label: item.commonName }))
      : [];

    setBankOptions(bankList);
  }, [recentBankSessionRes]);

  useEffect(() => {
    const sessionData = bankSessionResonseByDate?.value;
    if (!sessionData) return;

    // Set bank session tracking records
    const sessionDetails = sessionData.sessionDetailsMap ?? {};
    setBankSessionTrackRecords(sessionDetails);
  }, [bankSessionResonseByDate]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  function convertToFormatCapital(format) {
    // Mapping of common date format patterns
    const formatMap = {
      yyyy: 'YYYY',
      yy: 'YY',
      MM: 'MM',
      M: 'M',
      dd: 'DD',
      d: 'D',
    };

    // Replace keys in the input format with corresponding dayjs format
    const regex = new RegExp(Object.keys(formatMap).join('|'), 'g');
    return format.replace(regex, (match) => formatMap[match]);
  }
  const dateFnsFormatToCapital = convertToFormatCapital(clientDateFormat);
  const fileCardHandler = (data) => {
    return Object.keys(data).map((key) => (
      <FileCard key={key} date={key} dateData={data[key]} />
    ));
  };

  const searchHandler = () => {
    const [fromDate, toDate] = dateRange || [];

    if (!fromDate) {
      funcToSetResponseMessage('info', 'Date Range is missing');
      return;
    }

    if (!Array.isArray(selectedBanks) || selectedBanks.length === 0) {
      funcToSetResponseMessage('info', 'Bank is missing');
      return;
    }

    const payload = {
      bankNames: selectedBanks,
      clientId,
      fromDate,
      toDate,
    };

    dispatch(getBankFileTrackerDetailsByDate(payload));
  };

  return (
    <>
      <CommonSpinner visible={loading} />
      <div className="page-title mb16">Bank File Tracker</div>
      <div className="bank-file-tracker">
        <div
          className="card  mb16"
          style={{ display: 'flex', gap: '12px', alignItems: 'end' }}
        >
          <div>
            <p className="mb8" style={{ fontSize: '12px' }}>
              Date Range
            </p>
            <RangePicker
              style={{ width: '300px' }}
              format={dateFnsFormatToCapital}
              onChange={(e, range) => setDateRange(range)}
            />
          </div>
          <div>
            <p className="mb8" style={{ fontSize: '12px' }}>
              Banks
            </p>
            <Select
              lable={'Banks'}
              mode="multiple"
              style={{ width: '300px' }}
              maxTagCount="responsive"
              options={bankOptions}
              onChange={(e) => setSelectedBanks(e)}
            />
          </div>
          <Button className={'default small button'} onClick={searchHandler}>
            search
          </Button>
        </div>
        {Object.keys(bankSessionTrackRecords).length > 0 ? (
          <Scrollbars autoHeight autoHeightMin={100} autoHeightMax={500}>
            {fileCardHandler(bankSessionTrackRecords)}
          </Scrollbars>
        ) : (
          <Empty />
        )}
        <div className="mb20"></div>
      </div>
    </>
  );
}

export { BankFileTracker };
