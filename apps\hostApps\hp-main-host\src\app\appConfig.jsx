// AppConfigProvider.js
import { ConfigProvider } from 'antd';
import React from 'react';

const AppConfigProvider = ({ children }) => {
  return (
    <ConfigProvider
      direction="ltr"
      theme={{
        token: {
          colorPrimary: '#3b73b9',
          colorLink: '#1DA57A',
          colorSuccess: '#52c41a',
          colorError: '#f5222d',
          colorWarning: '#faad14',
          colorText: '#000000',
          colorTextSecondary: '#8c8c8c',
          colorTextPlaceholder: '#bfbfbf',
          colorBorder: '#d9d9d9',
          colorBackground: '#ffffff',
          colorFill: '#f5f5f5',
          borderRadius: 0,
          controlHeight: 32,
          fontSize: 14,
          fontSizeSM: 12,
          fontSizeLG: 16,
          sizeStep: 4,
          lineHeight: 1.5,
          paddingSM: 8,
          paddingMD: 12,
          paddingLG: 16,
        },
        components: {
          Button: {
            colorPrimary: '#3b73b9',
            borderRadius: 0,
            padding: '13px',
            fontSize: 16,
            lineHeight: 1.5,
            borderColor: '#3b73b9',
            backgroundColor: 'lightblue',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          },
          Input: {
            borderRadius: 0,
            fontSize: 18,
            padding: '13px',
            lineHeight: 1.5,
            borderColor: '#3b73b9',
            backgroundColor: '#ffffff',
            height: '150px', // Adjust height as needed
            width: '100px', // Adjust width as needed
            boxSizing: 'border-box', // Ensures padding and border are included in the size
          },
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export default AppConfigProvider;
