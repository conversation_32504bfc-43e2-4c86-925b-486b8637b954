/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable no-restricted-globals */

import React, { useCallback, useEffect, useState } from 'react';
import {

  Input,
  CommonSpinner,
  dataTableServiceProvider,
} from '@hp/components';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { getDetailsScreenAction } from '@hp/mainstore';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import ExpenseDialogComponent from '../DialogComponent';
import { nanoid } from 'nanoid';
const UpdateRequestProcess = (props) => {
  const { state, search } = props.location;
  let locations = location.pathname.split('/');
  let selectedURL = locations[4];
  const { expenseDetailsResponse, isExpReportFormloading } = useSelector(
    (store) => store.expenseReducer,
    shallowEqual
  );
  const dispatch = useDispatch();
  const [formElements, setFormElements] = useState([]);
  const [selectedRowFormControls, setselectedRowFormControls] = useState([]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [isModal, setIsModal] = useState(false);
  const [selectedRowDataTable, setselectedRowDataTable] = useState(null);
  const [conditionalRowStyles, setConditionalStyles] = React.useState([]);
  var trID =
    state && state !== undefined
      ? state.selectedRowData.trNumber
      : search.slice(2);

  useEffect(() => {
    dispatch(
      getDetailsScreenAction({ listName: 'TRItem', itemID: parseInt(trID) })
    );
    return () => {
      setFormElements([]);
    };
  }, [selectedURL]);

  useEffect(() => {
    if (expenseDetailsResponse) {
      const selectedTabIndx = expenseDetailsResponse.findIndex(
        (item) => item.displayName === 'Expenses'
      );
      if (selectedTabIndx !== -1) {
        let duplicateCopy =
          expenseDetailsResponse[selectedTabIndx].formSubDetailsInternalDTOList;
        setselectedRowFormControls(duplicateCopy);
        const filterFormData = expenseDetailsResponse[
          selectedTabIndx
        ].formSubDetailsInternalDTOList.filter(
          (item) => item.uniqueKey === 'AirFare' || item.type === 'DataTable'
        );
        expenseDetailsResponse[selectedTabIndx].formSubDetailsInternalDTOList =
          [];
        expenseDetailsResponse[selectedTabIndx].formSubDetailsInternalDTOList =
          filterFormData;
        setFormElements(expenseDetailsResponse);
      }
    }
    return () => {
      setFormElements([]);
    };
  }, [expenseDetailsResponse, selectedURL]);

  const formControlsBinding = (data) => {
    return data && data.length
      ? data.map((element, index) => {
          if (
            element.type === 'PageTitle' &&
            element.formSubDetailsInternalDTOList &&
            element.formSubDetailsInternalDTOList.length
          ) {
            return (
              <div key={nanoid(3)}>
                <div key={index + 1} className="page-title">
                  Update Travel Request &nbsp;
                  <b>TR No. - {props.location.search.slice(2)}</b>{' '}
                </div>
                <div className="card mb20">
                  <div key={index + 2} className="three-col-layout">
                    {' '}
                    {formControlsBinding(element.formSubDetailsInternalDTOList)}
                  </div>
                </div>
              </div>
            );
          }
          return (
            <Input
              key={nanoid(3)}
              formType={element}
              dataTableEventHandler={onRowClick}
              conditionalRowStyles={conditionalRowStyles}
              dataTablePagination={false}
              // onChangeHandler={(onChangeFunction, e, uniqueKey) => onChangeHandlingFunctions[onChangeFunction](e, uniqueKey, index, element)}
            />
          );
        })
      : null;
  };

  const tabChange = (index) => {
    setSelectedTab(index);
  };

  const onRowClick = useCallback(
    (e) => {
      if (selectedTab === 1) {
        const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
          e.trExpId,
          'trExpId'
        );
        setConditionalStyles(styleAttribute);
        setselectedRowDataTable(e);
        setIsModal(true);
      }
    },
    [selectedTab]
  );

  const memoChildDialog = React.useMemo(() => {
    if (selectedRowDataTable) {
      var clickedRow = selectedRowFormControls.filter(
        (item) => item.uniqueKey === selectedRowDataTable.expense
      );
    }
    return (
      <ExpenseDialogComponent
        formElements={clickedRow ? clickedRow[0] : []}
        isModal={isModal}
        isModalClose={() => setIsModal(false)}
      />
    );
  }, [isModal]);

  return (
    <>
     
        <CommonSpinner visible={isExpReportFormloading} />
        {formElements && formElements.length ? (
          <section>
            {formControlsBinding(formElements)}
            <div className="mb20"></div>
            <Tabs
              onSelect={(tabIndex) => tabChange(tabIndex)}
              className="mb24"
              selectedIndex={selectedTab}
            >
              <TabList>
                {formElements && formElements.length
                  ? formElements.map((tabListElement, index) => {
                      if (tabListElement.type.includes('TabPanel')) {
                        return (
                          <Tab onClick={() => tabChange(index)} key={nanoid(3)}>
                            {tabListElement.displayName}
                          </Tab>
                        );
                      }
                    })
                  : null}
              </TabList>
              {formElements && formElements.length
                ? formElements.map((tabPanelElement, tabIndex) => {
                    if (tabPanelElement.type.includes('TabPanel')) {
                      return (
                        <TabPanel key={tabIndex}>
                          <div className="card mb40">
                            <div
                              className={
                                selectedTab !== 1 && selectedTab !== 0
                                  ? 'flex-row-wrap two-col-layout'
                                  : ''
                              }
                            >
                              {formControlsBinding(
                                tabPanelElement.formSubDetailsInternalDTOList
                              )}
                            </div>
                          </div>
                        </TabPanel>
                      );
                    }
                  })
                : null}
            </Tabs>
          </section>
        ) : null}

        {isModal ? memoChildDialog : ''}
 
    </>
  );
};

export { UpdateRequestProcess };
