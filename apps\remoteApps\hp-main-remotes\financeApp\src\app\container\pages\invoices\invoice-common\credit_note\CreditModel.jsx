/* eslint-disable react-hooks/exhaustive-deps */
import { useDispatch, useSelector } from 'react-redux';
import React from 'react';
import { useEffect, useState } from 'react';
import { accPayConstants } from '@hp/mainstore';
import { AP_USER } from '@hp/constants';
import { getInvoiceCompareFormsINV, getInvoiceList } from '@hp/mainstore';
import { addCreditNote } from '@hp/mainstore';
import Modal from 'react-modal';
import { LinkInvoiceModal } from '../LinkInvoiceModal/LinkInvoiceModal';
import { resetDynamicPagination } from '@hp/mainstore';
import CreditNote from './CreditNote';
import { CommonSpinner, useConfirm } from '@hp/components';
import { globalutils } from '@hp/components';
function CreditModel(props) {
  const { creditActive, creditClose } = props;

  const { accpayFormControls, invoiceListData, loading } = useSelector(
    (state) => state.accpay
  );

  const { resetDataTable } = useSelector((store) => store.util);

  const { confirm } = useConfirm();

  const dispatch = useDispatch();

  const [addCreditModel, setAddCreditModel] = useState(false);
  const [rowDetails, setRowDetails] = useState('');

  let user = globalutils.getDataFromStorage('all');

  const clientId = user?.clientId;
  const userId = user?.userId;

  const onCreditNoteAdd = (data) => {
    if (data && data.inv_id) {
      console.log('datafrom credit model', data);
      dispatch(getInvoiceCompareFormsINV({ invID: data.inv_id, userId }));
      setRowDetails(data);
    }
  };

  useEffect(() => {
    if (accpayFormControls) {
      setAddCreditModel(true);
    }
  }, [accpayFormControls]);

  useEffect(() => {
    if (!creditActive) {
      dispatch(resetDynamicPagination(!resetDataTable));
      setAddCreditModel(false);

      dispatch({
        type: accPayConstants.GET_INVOICE_LIST_SUCCESS,
        payload: null,
      });
      dispatch({
        type: accPayConstants.ACCPAY_FORM_PO_INV_DETAILS_SUCCESS,
        payload: null,
      });
    }
  }, [creditActive]);

  const addCreditHandler = async (data) => {
    if (!data.length) {
      return;
    }
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (isConfirmed) {
      dispatch(addCreditNote({ data, invId: rowDetails.inv_id, clientId }));
    }
  };

  return (
    <>
      <LinkInvoiceModal
        modalOpen={creditActive}
        modalClose={creditClose}
        tableData={invoiceListData?.value ? invoiceListData.value : null}
        apiCallFunction={(obj) =>
          dispatch(getInvoiceList({ data: obj, clientId }))
        }
        linkButtonName={'Add Credit Note'}
        linkButtonClick={onCreditNoteAdd}
        isLoading={loading}
      />
      <Modal
        className="Modal-content"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={creditActive && addCreditModel}
        style={{
          overlay: {
            backgroundColor: 'transparent',
          },
        }}
      >
        <CommonSpinner visible={loading} />

        <div
          onClick={creditClose}
          className="modal-close icon-close mb20"
        ></div>

        <span
          title="Back"
          className="modal-close icon-down-arrow"
          style={{
            left: '10px',
            right: 'auto',
            transform: 'rotate(90deg)',
            cursor: 'pointer',
          }}
          onClick={() => setAddCreditModel(false)}
        ></span>

        <CreditNote
          invData={
            accpayFormControls?.invFormResponse?.data?.value
              ?.formDetailsDtoList || []
          }
          addCreditHandler={addCreditHandler}
          active={true}
          back={() => setAddCreditModel(false)}
          tittle={'Add Credit Note'}
        />
      </Modal>
    </>
  );
}

export default CreditModel;
