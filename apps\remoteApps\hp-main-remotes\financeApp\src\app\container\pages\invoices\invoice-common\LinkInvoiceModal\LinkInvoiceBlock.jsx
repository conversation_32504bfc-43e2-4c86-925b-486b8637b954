/* eslint-disable react-hooks/exhaustive-deps */
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import { utilConstants } from '@hp/mainstore';
import {
  Button,
  DataTableDynamicPagination,
  dataTableServiceProvider,
  SearchInput,
} from '@hp/components';
import {
  resetDynamicPagination,
  dynamicListlimitAndOffset,
} from '@hp/mainstore';

function LinkInvoiceBlock({
  tableData,
  serachApiCall,
  rowLink,
  linkButtonName,
  active,
}) {
  const { dynamicListLimitAndOffset, resetDataTable } = useSelector(
    (store) => store.util
  );

  const dispatch = useDispatch();
  const [tableHeader, setTableHeader] = useState([]);
  const [tableValue, setTableValue] = useState([]);
  const [conditionalStyles, setConditionalStyles] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [rowDetails, setRowDetails] = useState('');

  useEffect(() => {
    if (tableData) {
      setTableHeader(tableData.invHeadingDto);
      setTableValue(tableData.invTableDataDtoList);

      setResponse(tableData.invHeadingDto, tableData.invTableDataDtoList);

      dispatch({
        type: utilConstants.DYNAMIC_TABLE_LISTING_TOTAL_COUNT,
        payload: { value: tableData.totalCount },
      });
    } else {
      setTableHeader([]);
      setTableValue([]);
    }
  }, [tableData]);

  useEffect(() => {
    if (dynamicListLimitAndOffset) {
      searchHandler(
        dynamicListLimitAndOffset.limit,
        dynamicListLimitAndOffset.offset - 1
      );
    }
  }, [dynamicListLimitAndOffset]);

  useEffect(() => {
    if (searchValue === '') {
      searchHandler(10, 0);
      setConditionalStyles([]);
      dispatch(resetDynamicPagination(!resetDataTable));
    }
  }, [searchValue]);

  useEffect(() => {
    if (!active) {
      let obj = {
        limit: 10,
        offset: 1,
      };
      dispatch(dynamicListlimitAndOffset(obj, 'subscribe'));
    }
  }, [active]);

  const DataTableEventHandler = (data) => {
    setRowDetails(data);
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      data.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  };

  const setResponse = (head, data) => {
    if (head && data) {
      const DisplayTitle = (row, key) => {
        return (
          <div className="display-title" title={row[key]}>
            {row[key]}
          </div>
        );
      };
      const TableHead = Object.entries(head).map(([key, value]) => {
        let Width = '';
        let Right = '';

        return {
          selector: key,
          name: value,
          width: Width,
          right: Right,
          sortable: true,
          cell: (row) => DisplayTitle(row, key),
        };
      });
      setTableHeader(TableHead);
    }
  };

  const handleChange = (event) => {
    setSearchValue(event.target.value);
  };

  const searchHandler = (limit, offset) => {
    let obj = {
      limit,
      offset,
      searchItem: searchValue,
    };
    setTableHeader([]);

    serachApiCall(obj);
  };

  const linkHandler = () => {
    if (!rowDetails) return;
    rowLink(rowDetails);
  };

  return (
    <>
      <div className="page-title">Invoices</div>

      <div className="two-col-layout mb20">
        <SearchInput value={searchValue} onChange={handleChange} />
        <Button
          className="default small"
          style={{
            width: 'min-content',
          }}
          onClick={() => {
            searchHandler(10, 0);
            dispatch(resetDynamicPagination(!resetDataTable));
          }}
        >
          Search
        </Button>
      </div>

      <DataTableDynamicPagination
        theme={'solarized'}
        type={'DataTableDynamicPagination'}
        conditionalRowStyles={conditionalStyles}
        diableDynamicLoading={true}
        tableHeight={'290px'}
        persistTableHead
        dense={false}
        highlightOnHover={true}
        clickRow={DataTableEventHandler}
        striped={true}
        columns={tableHeader}
        data={tableValue}
        pagination={true}
        paginationPerPage={10}
      />

      <div className=" content-button flex-right ">
        <Button
          className="small  secondary mr20"
          onClick={() => {
            setSearchValue('');
          }}
        >
          Reset
        </Button>

        <Button className="small default primary  mr20" onClick={linkHandler}>
          {linkButtonName}
        </Button>
      </div>
    </>
  );
}

export { LinkInvoiceBlock };
