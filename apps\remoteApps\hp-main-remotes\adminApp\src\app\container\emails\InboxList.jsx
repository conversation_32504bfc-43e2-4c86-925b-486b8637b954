import React from 'react';
import DataTable from 'react-data-table-component';

function InboxList(props) {
  const { onRowClicked, conditionalRowStyles, columns, TableListing } = props;
  return (
    <>
      {/* <Select
            label="Client"
            className="mb16"
            style={{ width: 130 + "px" }}
            onChange={clientComboChange}
            options={clientCombo}
          />
          <Select
           className="mb16"
            label="Email"
            style={{ width: 250 + "px" }}
            onChange={emailComboChange}
            options={emailCombo}
        /> */}
      <div className="force-full-width">
        {/* <ul className="flex-row">
             {
           columns.map((data,index)=>{
            return (
                <li key={index}>{data.name}</li>
              );
           })
                   
              
            }
        </ul>
         <ul >
             {
           TableListing.map((data,index)=>{
            return (
                <div key={index}>
                <li >{data.fromAddress}</li>
                <li>{data.subject}</li>
                <li>{data.toAddress}</li>
                <li>{data.status}</li>
                </div>
              );
           })
                   
              
            }
        </ul>*/}
        <div className="email-widget home-widget col-2">
          {/* <h3>Inbox</h3> */}
          <div className="mb24 styledDatatable">
            <DataTable
              persistTableHead
              noHeader={true}
              dense={true}
              columns={columns}
              onRowClicked={onRowClicked}
              data={TableListing}
              conditionalRowStyles={conditionalRowStyles}
              pagination={true}
              paginationDefaultPage={1}
              paginationResetDefaultPage={true}
              paginationPerPage={10}
            ></DataTable>
          </div>
        </div>

        {/*<div className="mb24 styledDatatable">
        <DataTable
            persistTableHead
            highlightOnHover={true}
            noHeader={true}
            striped={true}
            dense={false}
            columns={columns}
            progressPending={isLoading}
            progressComponent={<Ellipsis color={"#3b73b9"} />}
            pagination={true}
            paginationDefaultPage={1}
            paginationResetDefaultPage={true}
            //paginationTotalRows={TableListinglength}
            paginationPerPage={10}
            defaultSortAsc={false}
            data={TableListing}
            theme={"solarized"}
            onRowClicked={onRowClicked}
            conditionalRowStyles={conditionalRowStyles}
          />
        </div> 
         */}
      </div>
    </>
  );
}

export { InboxList };
