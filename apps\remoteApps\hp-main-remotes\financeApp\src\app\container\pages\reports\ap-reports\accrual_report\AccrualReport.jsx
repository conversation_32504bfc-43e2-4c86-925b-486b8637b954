/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from 'react';
import {
  Button,
  DateRangeInput,
  CommonSpinner,
  globalutils,
} from '@hp/components';

import '../ApReportStyle.scss';
import { useDispatch, useSelector } from 'react-redux';
import DataTable from 'react-data-table-component';
import { Badge } from 'antd';
import { getAPReports, accPayConstants } from '@hp/mainstore';
import { useAppRouterDom } from '@hp/utils';
const AccrualReport = () => {
  const { apReportDTO, loading } = useSelector((state) => state.accpay);
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();

  const urlParam = domParameters?.parameters || '';

  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user.userId);
  const clientId = user.clientId;

  const [tableData, setTableData] = useState();
  const [columns, setColumns] = useState();

  const [reportDate, setReportDate] = useState();
  const [expandedKeys, setExpandedKeys] = useState([]);

  const [toSearchDate, setToSearchDate] = useState();
  const [fromSearchDate, setFromSearchDate] = useState();

  let endDate = new Date();
  let startDate = new Date();
  startDate.setDate(endDate.getDate() - 30);

  // Function to format the date as "MM-DD-YYYY"
  const formatDateMMDDYYYY = (date) => {
    let month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
    let day = date.getDate().toString().padStart(2, '0');
    let year = date.getFullYear();
    return `${month}-${day}-${year}`;
  };

  let formattedEndDate = formatDateMMDDYYYY(endDate);
  let formattedStartDate = formatDateMMDDYYYY(startDate);

  const [dateChange, setDateChange] = useState({
    fromDate: formattedStartDate,
    toDate: formattedEndDate,
  });

  useEffect(() => {
    dispatch(
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'subscribe',
        fromDate: dateChange.fromDate,
        toDate: dateChange.toDate,
      })
    );
  }, [
    clientId,
    dateChange.fromDate,
    dateChange.toDate,
    dispatch,
    urlParam,
    userId,
  ]);

  useEffect(() => {
    return () => {
      dispatch({
        type: accPayConstants.AP_REPORT_FETCHED_SUCCESS,
        apReportDTO: null,
      });
    };
  }, []);

  useEffect(() => {
    if (
      apReportDTO?.value?.columns &&
      Array.isArray(apReportDTO.value.columns)
    ) {
      const columnsData = apReportDTO.value.columns.map((data) => data || []);
      setColumns(columnsData);
    }

    setTableData(apReportDTO?.value?.values || []);
    setReportDate(apReportDTO?.value);
  }, [apReportDTO]);

  const onDateChange = (e) => {
    const startDate = globalutils.formatDate(e.fromDate, 'mm-dd-yyyy');
    const endDate = globalutils.formatDate(e.toDate, 'mm-dd-yyyy');
    setFromSearchDate(startDate.replaceAll('/', '-'));
    setToSearchDate(endDate.replaceAll('/', '-'));
    setDateChange(e);
  };
  const searchHandler = () => {
    dispatch(
      getAPReports(
        urlParam,
        clientId,
        userId,
        fromSearchDate,
        toSearchDate,
        'subscribe'
      )
    );
  };

  return (
    <>
      <div className="page-sub-title">Accrual Report</div>
      <CommonSpinner visible={loading} />
      {apReportDTO ? (
        <>
          {reportDate?.periodCovered ? (
            <Badge.Ribbon
              text={reportDate?.periodCovered}
              style={{ fontFamily: 'Roboto' }}
            >
              {' '}
            </Badge.Ribbon>
          ) : (
            ''
          )}
          <div className="header-nav card  mb20">
            <div className="three-col-layout mb20">
              <div>
                <DateRangeInput
                  className="mr20"
                  style={{ width: 100 + '%' }}
                  label="Duration"
                  formType={{
                    value: dateChange,
                  }}
                  onChange={(e) => onDateChange(e)}
                />
              </div>
              <div>
                {' '}
                <Button
                  className=" mr20 mt20 small default "
                  style={{ width: '10%' }}
                  onClick={searchHandler}
                >
                  Search
                </Button>
              </div>
              <div></div>
            </div>
          </div>
          <div className="mb20 apreport-main-container">
            {' '}
            <div className="styledDatatable mb20">
              <DataTable
                persistTableHead
                noHeader={true}
                dense={false}
                highlightOnHover={true}
                striped={true}
                columns={columns}
                data={tableData}
                pagination={true}
                paginationDefaultPage={1}
                paginationResetDefaultPage={true}
              />
            </div>
          </div>
        </>
      ) : (
        ''
      )}
    </>
  );
};
export { AccrualReport };
