// ... existing code ...
module.exports = {
  // ... other configurations ...
  overrides: [
    // ... other overrides ...
    {
      files: ['cypress/**/*.js'],
      parserOptions: {
        project: null, // Disable TypeScript checking for Cypress files
      },
      env: {
        'cypress/globals': true,
      },
      plugins: ['cypress'],
      rules: {
        '@typescript-eslint/no-unsafe-assignment': 'off',
        '@typescript-eslint/no-unsafe-member-access': 'off',
        '@typescript-eslint/no-unsafe-call': 'off',
        'no-undef': 'off', // Turn off no-undef since Cypress globals are handled by the env
        'no-console': 'warn', // Change to warning instead of error for console statements
        // Add other TypeScript-specific rules that might cause issues
      },
    },
  ],
};
