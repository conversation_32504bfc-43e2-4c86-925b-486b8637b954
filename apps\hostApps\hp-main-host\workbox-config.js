module.exports = {
  globDirectory: "dist/apps/hostApps/hp-main-host",
  globPatterns: ["**/*.{txt,ico,html,js}"],
  swDest: "dist/apps/hostApps/hp-main-host/service-worker.js",
  runtimeCaching: [
    {
      // runtime cache for images
      urlPattern: /\.(?:png|jpg|jpeg|svg)$/,
      handler: 'CacheFirst',
      options: {
        expiration: { maxEntries: 10 },
        cacheName: 'images',
      },
    },
  ]
};
