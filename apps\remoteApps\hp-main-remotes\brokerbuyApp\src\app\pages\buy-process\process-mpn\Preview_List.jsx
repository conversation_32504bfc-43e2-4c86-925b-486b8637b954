/* eslint-disable react-hooks/rules-of-hooks */

import { Button, Input, globalutils } from '@hp/components';
import { generateEmail } from '@hp/mainstore';
import { useDispatch } from 'react-redux';
import { memo, useState, useLayoutEffect } from 'react';
import { Ellipsis } from 'react-awesome-spinners';

function PreviewList(props) {
  const { form, setSelectedTab, excelPath, productFileMasterId, clientId } =
    props;
  const dispatch = useDispatch();
  const [isLoadingSpinner, setisLoadingSpinner] = useState(true);
  const [isLoading, setisLoading] = useState({
    spin: false,
    buttonDisable: false,
  });

  useLayoutEffect(() => {
    var timerId = setTimeout(() => {
      setisLoadingSpinner(false);
    }, 1500);

    return () => {
      clearTimeout(timerId);
    };
  }, []);

  const onGenerateEmail = () => {
    setisLoading({ ...isLoading, spin: true });
    dispatch(
      generateEmail({
        productFileMasterId: productFileMasterId,
        bbClientId: clientId,
      })
    );
  };

  return (
    <div>
      {isLoadingSpinner ? (
        <div className="align-spinner">
          <Ellipsis
            color={globalutils.spinnerColor()}
            size={100}
            sizeUnit={'px'}
          />
        </div>
      ) : (
        ''
      )}
      {form.formSubDetailsInternalDTOList.map((items, index) => {
        return <Input key={index} formType={items} excelPath={excelPath} />;
      })}

      <Button
        className=" secondary small mr20 "
        style={{ marginTop: 30 + 'px' }}
        onClick={() => setSelectedTab((prev) => prev - 1)}
      >
        Previous
      </Button>
      <Button
        className="small default "
        style={{ marginTop: 30 + 'px' }}
        onClick={() => setSelectedTab((prev) => prev + 1)}
      >
        Next
      </Button>

      {/* <LoadingButton
        label={"Generate Email"}
        disabled={isLoading.buttonDisable}
        isLoading={isLoading.spin}
        className="small default"
        style={{ float: "right", marginTop: 30 + "px" }}
        onClick={onGenerateEmail}
      /> */}

      <Button
        className="small default"
        style={{ float: 'right', marginTop: 30 + 'px' }}
        onClick={onGenerateEmail}
      >
        Generate Email
      </Button>
    </div>
  );
}

export default memo(PreviewList);
