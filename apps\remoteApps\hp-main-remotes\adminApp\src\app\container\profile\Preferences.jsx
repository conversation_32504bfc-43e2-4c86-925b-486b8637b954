/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:39:36
 * @modify date 27-05-2021 15:39:39
 * @desc [description]
 */
import React, { useState, useEffect } from 'react';
import {
  Select,
  ButtonCommon,
  Imageupload,
  Button,
  globalutils,
} from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';

import { AP_file_url, AP_USER } from '@hp/constants';
import { Tooltip } from 'react-tooltip';
import cloneDeep from 'lodash';
import {
  getdataforFields,
  getdefEntityLists,
  getLanguageCombo,
  getProfileImage,
  // savedDetailsClear,
  resetsavedDetailsClear,
  savePreferenceDetails,
  showDialog,
} from '@hp/mainstore';
import { sharedAsset } from '@hp/assets';
const Preferences = (props) => {
  var themeColors = globalutils.themeColors;
  let user = globalutils.getDataFromStorage('all');
  const dispatch = useDispatch();
  const { languagelist, savedDetails, getDetails, defEntityList } = useSelector(
    (store) => store.admin
  );
  const subscribe = 'subscribe';
  const [userDP, setUserDP] = useState('');
  const [upload, setUpload] = useState(false);
  const [languageCombo, setLanguageCombo] = useState();
  const [data, setData] = useState({ form: null });
  const [defEntityCombo, setDefEntityCombo] = useState();
  const [state, setState] = useState({
    DetailsData: {
      langCode: '',
      defEntity: '',
      dateFormat: 'MM-dd-yyyy',
      theme: 'default',
    },
  });
  useEffect(() => {
    getLanguageComboFunc();
    getPreferenceValue();
    getdefEntityList();
  }, [props]);

  useEffect(() => {
    if (languagelist && languagelist.value) {
      const languageOptions = languagelist.value.map((value) => {
        return {
          value: value.langCode,
          display: value.language,
        };
      });
      setLanguageCombo(languageOptions);
    }
  }, [languagelist]);

  useEffect(() => {
    if (defEntityList && defEntityList.value) {
      const defEntityOptions = defEntityList.value.map((value) => {
        return { value: value.entityId, display: value.entityName };
      });
      setDefEntityCombo(defEntityOptions);
    }
  }, [defEntityList]);

  useEffect(() => {
    return () => {
      // let saveDetails = null;
      // dispatch(savedDetailsClear(saveDetails));
      dispatch(resetsavedDetailsClear());
    };
  }, []);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };
  useEffect(() => {
    if (savedDetails && savedDetails.value) {
      funcToSetResponseMessage(
        savedDetails.value.type,
        savedDetails.value.text
      );
      dispatch(getProfileImage(subscribe));
      getPreferenceValue();
      // window.location.reload();
    }
  }, [savedDetails]);

  useEffect(() => {
    if (getDetails && getDetails.value) {
      const url = getDetails?.value?.userPictureDtlsDto?.filePath;
      const userDp = cloneDeep(AP_file_url + url);
      if (url) {
        setUserDP(userDp);
      } else setUserDP(sharedAsset.userAsset.userLogo);
      setState({
        DetailsData: {
          ...state.DetailsData,
          langCode: getDetails.value.langCode,
          defEntity: getDetails.value.defaultEntityId,
          theme: getDetails.value.theme,
        },
      });
    }
  }, [getDetails]);

  const getLanguageComboFunc = () => {
    dispatch(getLanguageCombo());
  };

  const getdefEntityList = () => {
    dispatch(getdefEntityLists(user.userId));
  };

  const handleOnChange = (e) => {
    setState({
      DetailsData: {
        ...state.DetailsData,
        [e.target.name]: e.target.value,
      },
    });
  };

  const getPreferenceValue = () => {
    let userid = user.userId;
    dispatch(getdataforFields(userid));
  };

  const handleSave = () => {
    let saveObject = {
      langCode: state.DetailsData.langCode,
      defaultEntityId: state.DetailsData.defEntity,
      theme: state.DetailsData.theme,
      userId: globalutils.getDataFromStorage('userId'),
    };

    dispatch(
      savePreferenceDetails({
        saveObj: saveObject,
        formData: data.form,
        type: subscribe,
      })
    );
  };

  const functionsName = {
    handleSave,
  };
  let mesg = ' Size should be 32 x 32 or below';
  let imgSize = { height: 32, width: 32, radius: 100 };

  return (
    <>
      <div className="page-title">Preferences</div>
      <div className="three-col-layout mb20">
        <div className="col">
          <Select
            label="Language"
            className="mb20"
            name="langCode"
            options={languageCombo}
            onChange={(e) => handleOnChange(e)}
            value={state.DetailsData ? state.DetailsData.langCode : ''}
          />
          <Select
            label="Default Entity"
            className="mb20"
            name="defEntity"
            options={defEntityCombo}
            onChange={(e) => handleOnChange(e)}
            value={state.DetailsData ? state.DetailsData.defEntity : ''}
          />
          <div className="two-col-layout">
            <div className="col">
              <Select
                label="Theme"
                className="mb20"
                name="theme"
                options={[
                  {
                    value: 'default',
                    display: 'Default',
                  },
                  {
                    value: 'gray',
                    display: 'Gray',
                  },
                  {
                    value: 'red',
                    display: 'Red',
                  },
                  {
                    value: 'cyan',
                    display: 'Cyan',
                  },
                  {
                    value: 'purple',
                    display: 'Purple',
                  },
                ]}
                onChange={(e) => handleOnChange(e)}
                value={state.DetailsData ? state.DetailsData.theme : ''}
              />
            </div>
            <div className="col">
              <div
                title="Selected Theme"
                style={{
                  backgroundColor: themeColors[state.DetailsData.theme],
                  borderRadius: '15%',
                  height: '33px',
                  width: '60px',
                  border: 'solid 1px gray',
                  marginTop: '24px',
                  cursor: 'pointer',
                  boxShadow:
                    'rgba(0, 0, 0, 0.17) 0px -23px 25px 0px inset, rgba(0, 0, 0, 0.15) 0px -36px 30px 0px inset, rgba(0, 0, 0, 0.1) 0px -79px 40px 0px inset, rgba(0, 0, 0, 0.06) 0px 2px 1px, rgba(0, 0, 0, 0.09) 0px 4px 2px, rgba(0, 0, 0, 0.09) 0px 8px 4px, rgba(0, 0, 0, 0.09) 0px 16px 8px, rgba(0, 0, 0, 0.09) 0px 32px 16px',
                }}
              ></div>
            </div>
          </div>

          <div className="userImage-wrap mb20">
            <img
              src={userDP ? userDP : sharedAsset.userAsset.userLogo}
              // src={sharedAsset.userAsset.userLogo}
              alt=""
            />
          </div>
          <a className="my-anchor-element">
            <Button
              className="small mb20 outline add-button-custom flex-row vam "
              onClick={() => setUpload(!upload)}
            >
              <i className="icon-add-button "> </i>Upload
              <Tooltip
                anchorSelect=".my-anchor-element"
                place="Right Top"
                style={{
                  width: '220px',
                  height: '70px',
                  marginLeft: '115px',
                  backgroundColor: 'rgb(132 198 83)',
                }}
              >
                <b>
                  Accepted Formats: JPG, PNG, JPEG <br></br>
                  <br></br>Size should be 32 x 32 or below <br></br>
                  <br></br>Memory Size Max: 20KB
                </b>
              </Tooltip>
            </Button>
          </a>

          {upload && (
            <Imageupload
              userImage={true}
              labels={{
                headLbl: 'Drag Images or Click to upload',
                formatLbl: 'Accepted Formats: JPG, PNG, JPEG',
                sizeLbl: 'Memory Size Max: 20KB',
              }}
              imgSize={imgSize}
              errorMesg={mesg}
              onCancel={() => setUpload(false)}
              isShow={upload}
              clientId={user.clientId}
              imageDetailsDTO={(formData) => setData({ form: formData })}
            />
          )}
        </div>
        <div className="col">
          <div className="col"></div>
        </div>
      </div>
      <ButtonCommon functionsName={functionsName} />
    </>
  );
};

export { Preferences };
