/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-case-declarations */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-key */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 31-01-2023 15:40:13
 * @modify date 09-02-2023 15:40:20
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { accPayConstants } from '@hp/mainstore';
import { AP_file_url } from '@hp/constants';
import {
  DocumentViewer,
  formValidationUtil,
  globalutils,
  Input,
} from '@hp/components';
import {
  getAgingReportDetails,
  getAgingTableTotalCount,
  getAgingTableDetails,
  getSubStatusList,
  downloadAsExcel,
} from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import { currentLimtAndOffset } from '@hp/mainstore';

const AgingReport = ({ statusVal, payload }) => {
  const dispatch = useDispatch();
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;
  let todaysDate = globalutils.formatDateMonth(new Date());
  const {
    agingReportDetails,
    agingTableDetails,
    subStatusList,
    generatedExcelFile,
  } = useSelector((store) => store.accpay);
  const { dynamicListLimitAndOffset } = useSelector((store) => store.util);

  const [formDetails, setFormDetails] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [filePath, setFilePath] = useState(false);
  const [cleared, setCleared] = useState(false);
  const [selected, setSelected] = useState({
    userId: userId,
    clientId: clientId,
    status: null,
    subStatus: null,
    dateRangeDTO: { fromDate: todaysDate, toDate: todaysDate },
    limit: null,
    offset: null,
  });
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (statusVal && payload) {
      let tempObj = {
        ...selected,
        ...payload,
        status: statusVal,
        dbFlag: true,
        limit: dynamicListLimitAndOffset?.limit
          ? dynamicListLimitAndOffset.limit
          : 10,
        offset: dynamicListLimitAndOffset?.offset
          ? dynamicListLimitAndOffset.offset
          : 1,
      };
      dispatch(getAgingTableTotalCount(tempObj));
      dispatch(getAgingTableDetails(tempObj));
      setSelected(tempObj);
    } else dispatch(getAgingReportDetails(userId));
    return () => {
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (dynamicListLimitAndOffset) {
      let tempObj = {
        ...selected,
        dbFlag: false,
        limit: dynamicListLimitAndOffset.limit,
        offset: dynamicListLimitAndOffset.offset,
      };

      if (statusVal && payload && !cleared) {
        tempObj = {
          ...tempObj,
          ...payload,
          dbFlag: true,
          status: statusVal,
        };
      }
      dispatch(getAgingTableDetails(tempObj));
      setSelected(tempObj);
    }
  }, [dynamicListLimitAndOffset]);

  useEffect(() => {
    if (agingReportDetails && agingReportDetails.value) {
      let tempArray = cloneDeep(agingReportDetails.value);
      tempArray.map((entry) => {
        entry.uniqueKey === 'group1' &&
          entry.formSubDetailsInternalDTOList.map(
            (element) =>
              (element.value =
                element.uniqueKey === 'period'
                  ? { fromDate: todaysDate, toDate: todaysDate }
                  : element.value)
          );
      });
      setFormDetails(tempArray);
      setTableData(
        agingReportDetails.value.find(
          (val) =>
            val.uniqueKey === 'mainTable' || val.uniqueKey === 'subStatusTable'
        )?.value
      );
    }
  }, [agingReportDetails]);

  useEffect(() => {
    if (agingTableDetails && agingTableDetails.value) {
      let tempArray = cloneDeep(agingTableDetails.value);
      setFormDetails(tempArray);
      setTableData(
        agingTableDetails.value.find(
          (val) =>
            val.uniqueKey === 'mainTable' || val.uniqueKey === 'subStatusTable'
        )?.value
      );
    }
  }, [agingTableDetails]);

  useEffect(() => {
    if (subStatusList && subStatusList.value) {
      let tempForm = cloneDeep(formDetails);
      tempForm.map((item) =>
        item.uniqueKey === 'group1'
          ? item?.formSubDetailsInternalDTOList.map((entry) => {
              if (entry.uniqueKey === 'subStatus') {
                entry.comboBoxOptions = subStatusList.value;
                entry.value = '';
              }
            })
          : ''
      );
      setFormDetails(tempForm);
    }
  }, [subStatusList]);

  useEffect(() => {
    if (generatedExcelFile?.value?.filePath) {
      let url = `${AP_file_url}${generatedExcelFile.value.filePath}`;
      let filename = generatedExcelFile?.value?.fileName;
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      link.click();
    }
    return () => {
      let clearData = null;
      dispatch({
        type: accPayConstants.DOWNLOAD_AS_EXCEL_SUCCESS,
        clearData,
      });
    };
  }, [generatedExcelFile]);

  const handleAttachment = (obj) => {
    setFilePath(obj?.filePath ? obj?.filePath : '');
    setShowModal(true);
  };

  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'group1' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            if (uniqueKey === 'status') {
              element.value = event.target.value;
              setSelected({
                ...selected,
                status: event.target.value,
                subStatus: null,
              });
              dispatch(getSubStatusList(event.target.value));
            } else {
              element.value = event.target.value;
              setSelected({
                ...selected,
                [uniqueKey]: event.target.value,
              });
            }
          }
        });
    });
    setFormDetails(tempArray);
  };

  const handleDateRange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'group1' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event;
            setSelected({ ...selected, dateRangeDTO: event });
          }
        });
    });
    setFormDetails(tempArray);
  };

  const handleButtonClick = (event, uniqueKey) => {
    let data = {
      limit: 10,
      offset: 1,
    };
    dispatch(currentLimtAndOffset({ ...data }));
    switch (uniqueKey) {
      case 'searchButton':
        let isValid = formValidationUtil.validateForm(formDetails);
        if (isValid.validSuccess) {
          let tempObj = { ...selected, dbFlag: false, limit: 10, offset: 1 };
          dispatch(getAgingTableDetails(tempObj));
          dispatch(getAgingTableTotalCount(tempObj));
        } else {
          let tempArray = cloneDeep(isValid.formList);
          setFormDetails(tempArray);
        }
        break;
      case 'clearButton':
        let tempArray = cloneDeep(formDetails);
        tempArray.map((entry) => {
          if (entry.uniqueKey === 'group1') {
            entry.formSubDetailsInternalDTOList.map((element) => {
              element.value =
                element.uniqueKey === 'period'
                  ? { fromDate: todaysDate, toDate: todaysDate }
                  : null;
              element.errorFlag = false;
            });
          } else if (
            entry.uniqueKey === 'mainTable' ||
            entry.uniqueKey === 'subStatusTable'
          ) {
            entry.value = null;
          }
        });
        setCleared(true);
        setTableData([]);
        setFormDetails(tempArray);
        setSelected({
          ...selected,
          status: null,
          subStatus: null,
          dateRangeDTO: { fromDate: todaysDate, toDate: todaysDate },
        });
        break;
      case 'downloadButton':
        dispatch(downloadAsExcel(tableData));
        break;
    }
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleDateRange,
    handleButtonClick,
  };

  const handleFilterData = (filterData) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) =>
      element.uniqueKey === 'mainTable' ||
      element.uniqueKey === 'subStatusTable'
        ? (element.value = filterData)
        : ''
    );
    setFormDetails(tempArray);
  };

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey) {
            if (
              element.uniqueKey === 'mainTable' ||
              element.uniqueKey === 'subStatusTable'
            ) {
              let columns =
                element.formSubDetailsInternalDTOList &&
                element.formSubDetailsInternalDTOList
                  .filter((item) => item?.type === 'tableColumn')
                  .map((val) => {
                    return {
                      sortable: true,
                      selector: val.selector,
                      width:
                        val.selector === 'serialNo'
                          ? '5%'
                          : val.selector === 'attachment'
                          ? '4%'
                          : val.displayWidth,
                      right: val.selector === 'amountConverted' ? true : false,
                      name: val.displayName,
                      cell:
                        val.selector === 'attachment'
                          ? function displayCell(row) {
                              return (
                                <div
                                  title="click to show the attachment"
                                  className="icon icon-attachment1"
                                  onClick={() => handleAttachment(row)}
                                ></div>
                              );
                            }
                          : function displayTitle(row) {
                              return (
                                <div title={row[val.selector]}>
                                  {row[val.selector]}
                                </div>
                              );
                            },
                    };
                  });
              return (
                <Input
                  key={index}
                  formType={element}
                  expandOnRowClicked={true}
                  dataTableColumn={columns}
                  dataTableEventHandler={() => null}
                />
              );
            } else if (element.uniqueKey === 'group1') {
              return (
                <div className=" flex-row ">
                  {formControlsBinding(element.formSubDetailsInternalDTOList)}
                </div>
              );
            } else if (element.uniqueKey === 'downloadButton') {
              return tableData?.length > 0 ? (
                <div
                  style={{ width: 'min-content', margin: '0px 0px 0px auto' }}
                >
                  <Input
                    key={index}
                    formType={element}
                    onChangeHandler={(element, event) => {
                      onChangeHandlingFunctions[element.onChangeFunction](
                        event,
                        element.uniqueKey,
                        element
                      );
                    }}
                  />
                </div>
              ) : null;
            } else if (
              element.uniqueKey === 'searchButton' ||
              element.uniqueKey === 'clearButton'
            ) {
              return (
                <div style={{ marginTop: '24px', marginLeft: '20px' }}>
                  <Input
                    key={index}
                    formType={element}
                    onChangeHandler={(element, event) => {
                      onChangeHandlingFunctions[element.onChangeFunction](
                        event,
                        element.uniqueKey,
                        element
                      );
                    }}
                  />
                </div>
              );
            } else if (element.uniqueKey === 'search') {
              return (
                <div
                  style={{
                    display: 'inline-block',
                    width: '200px',
                    marginTop: '20px',
                    marginBottom: '8px',
                  }}
                >
                  <Input
                    key={index}
                    formType={element}
                    isEditable="notShowing"
                    tableListData={tableData}
                    getFilteredData={(filterData) =>
                      handleFilterData(filterData)
                    }
                  />
                </div>
              );
            } else {
              return (
                <div
                  style={
                    element.uniqueKey === 'period'
                      ? { width: '100%' }
                      : { width: '100%', marginLeft: '20px' }
                  }
                >
                  <Input
                    key={index}
                    formType={element}
                    isEditable="notShowing"
                    onChangeHandler={(element, event) => {
                      onChangeHandlingFunctions[element.onChangeFunction](
                        event,
                        element.uniqueKey,
                        element
                      );
                    }}
                  />
                </div>
              );
            }
          }
        })
      : null;
  };

  return (
    <div>
      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={showModal}
      >
        <div
          onClick={() => setShowModal(false)}
          className="modal-close icon-close"
        ></div>
        <DocumentViewer
          fileURL={filePath}
          fileType={'iframe'}
          zoom={'#zoom=100'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
        />
      </Modal>
    </div>
  );
};
export { AgingReport };
