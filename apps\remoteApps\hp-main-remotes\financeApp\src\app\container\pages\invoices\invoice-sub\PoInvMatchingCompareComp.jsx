/* eslint-disable eqeqeq */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @create date 13-07-2021 10:13:11
 * @modify date 2024-08-02 23:12:20
 * @desc [description]
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { InvDatatable } from './inv-table/INV-DataTable';
import { PoDataTable } from './PO-DataTable';
import { AP_USER } from '@hp/constants';
import Modal from 'react-modal';
import cloneDeep from 'lodash.clonedeep';
import { dataTableServiceProvider, globalutils } from '@hp/utils';
import Docviewer from './docviewer';
import { invoiceService } from '../invoice-common/invoice.service';
import { useEffectOnce } from 'react-use';
import { useLayoutEffect } from 'react';
import { Tabpanels } from './Tabpanels';
import { Tabs } from 'antd';
import { format } from 'date-fns';
import {
  DocumentViewer,
  Input,
  MultiRowInput,
  TextInput,
  useConfirm,
  UploadDoc,
} from '@hp/components';
import {
  getCoordsList,
  changedInputsAction,
  extractData,
  passINVEditedDetails,
  sendCoordsList,
  uploadAdditionalFilesDetails,
  getInvAdditionalDocuments,
} from '@hp/mainstore';
import { passInvEventTime } from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import { inputTextDescription } from '@hp/mainstore';
import '@hp/styles/InvoiceCompare.scss';

import {
  getDueDateCalculated,
  getSupplierFilteredByClientWithCode,
  getEntityChangeRes,
  ACCPAY_FORM_PO_INV_DETAILS_SUCCESS,
  accpayEntityChangeReset,
  accpayFormControlsReset,
} from '@hp/mainstore';
import { useLocation } from 'react-router-dom';
const PoInvMatchingCompareComp = (props) => {
  const {
    headerCheckBoxes,
    disabled,
    tableName,
    className,
    onSaveClick,
    onSaveDisable,
    addCommentsValue,
    notificationBar,
    onSubmittedClick,
    onSubmittedClickDisable,
    onSubmitvalidationSuccess,
    setisLoading,
    invId,
    suppId,
    poId,
    uploadAddFileData,
    fileVersionDisable,
    setDisToolTip,
    setDisabled,
  } = props;
  const {
    accpayFormControls,
    accpayRowIcons,
    dueDateFromBaselineDate,
    dueDateFromBaselineDateError,
    multiselectFilterWithCodeClientData,
    Extraction,
    ExtractData,

    changedIPRes,
    apCachedData,
    accpayEntityChange,
    apListingFilteredData,
  } = useSelector((store) => store.accpay);
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const { TabPane } = Tabs;
  const { inputDescription } = useSelector((store) => store.util);
  let history = useLocation().pathname;
  //=============================================================Local States Start=================================================//
  const [expandableInv, setExpandableInv] = useState(false);
  const [distoolTip, setDistoolTip] = useState(true);
  const [disabledFlag, setDisabledFlag] = useState(true);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [fileViewer, setfileViewer] = useState('iframe');
  const [baselineDate, setBaselineDate] = React.useState();

  const [dueDateList, setDueDateList] = React.useState();
  const [paymentTermsId, setPaymentTermsId] = useState();
  const imgHandle = React.useState(['hide', 'hide']);
  const [invImageFilePath, setInvImageFilePath] = React.useState([]);
  const [attachmentTabs, setAttachmentTabs] = React.useState([]);
  const [primaryFormElements, setPrimaryFormElements] = React.useState([]);
  const [secondaryFormElements, setSecondaryFormElements] = React.useState([]);
  const [otherChargesFormData, setOtherChargesFormData] = useState();

  const [classToDisabled, setClassToDisabled] =
    React.useState('is-not-editing');
  const [invFormControls, setInvFormControls] = React.useState();
  const invFormControlsRef = useRef();
  const docRef = useRef(null);

  invFormControlsRef.current = invFormControls;

  const [poFormControls, setPoFormControls] = React.useState();

  const [errorFlagCount, setErrorFlagCount] = React.useState();
  const [itemListMessage, setItemListMessage] = React.useState('');
  const [invoiceFormData, setInvoiceFormData] = useState([]);
  const [poFormData, setPoFormData] = useState();
  const [uploadFileData, setUploadFileData] = useState([]);
  const [isModal, setIsModal] = useState(false);
  const [filePath, setFilePath] = useState();
  const [checkFlag, setcheckFlag] = useState({
    invNumberFlag: false,
    poNumberFlag: false,
    supplierNameFlag: false,
    invDateFlag: false,
    dueDateFlag: false,
    amountFlag: false,
    currencyFlag: false,
    itemListFlag: false,
  });
  const [focusedInput, setFocusedInput] = useState('');
  const focusInputRef = useRef();
  focusInputRef.current = focusedInput;
  const [changedInput, setchangedInput] = useState({});
  const [reactDocViewerProp, setReactDocViewerProp] = useState(null);
  const [extractionSaveObject, setextractionSaveObject] = useState(null);
  const [extractedCoordsVerification, setextractedCoordsVerification] =
    useState([]);
  const inputArray = cloneDeep(invFormControls?.formDetailsDtoList);
  const [modelClose, setModelClose] = useState(false);

  const [previousAnnoListLength, setPreviousAnnoListLength] = useState(0);
  const [showInvoiceToggle, setShowInvoiceToggle] = useState(true);
  const [activeTabKey, setActiveTabKey] = useState('0');

  const subscribe = 'subscribe';
  const unSubscribe = 'unSubscribe';
  let user = globalutils.getDataFromStorage('all');
  const clientId = user.clientId;

  //=============================================================Local States End=================================================//
  useEffect(() => {
    let timerId = setTimeout(() => {
      setDistoolTip(false);
      return () => {
        clearTimeout(timerId);
      };
    }, 20000);
  }, [distoolTip]);

  useEffect(() => {
    //To show next and previous button for 3 Seconds
    setShowInvoiceToggle(true);
    const timeout = setTimeout(() => {
      setShowInvoiceToggle(false);
    }, 3000);
    return () => {
      clearTimeout(timeout);
      let removeNotification = null;
      dispatch(accpayEntityChangeReset(removeNotification));
      dispatch(ACCPAY_FORM_PO_INV_DETAILS_SUCCESS(null));
    };
  }, []);

  useEffect(() => {
    if (uploadAddFileData.length != 0) {
      if (uploadAddFileData.value.length != 0) {
        let tempArray1 = cloneDeep(uploadAddFileData);
        let filedata = [];
        tempArray1.value.map((entry) => {
          filedata.push(entry);
          setUploadFileData(filedata);
        });
      } else {
        setUploadFileData([]);
      }
    }
  }, [uploadAddFileData]);

  useEffect(() => {
    if (invoiceFormData) {
      invoiceFormData.map((entry) => {
        if (entry.uniqueKey === 'totalcharges') {
          setOtherChargesFormData(entry?.formSubDetailsInternalDTOList[0]);
        }
      });
    }
  }, [invoiceFormData]);

  useEffect(() => {
    if (accpayFormControls) {
      setInvFormControls(accpayFormControls.invFormResponse.data.value);
      setPoFormControls(
        accpayFormControls.poFormResponse
          ? accpayFormControls.poFormResponse.data.value
          : []
      );
      setInvImageFilePath(
        accpayFormControls.invFormResponse.data.value.filePath
      );

      setAttachmentTabs(accpayFormControls.invFormResponse.data.value);
      dispatch(
        passInvEventTime(
          accpayFormControls.invFormResponse.data.value.eventTime
        )
      );
      dispatch(getCoordsList({ supplierId: suppId, invId: invId }));
    }

    return () => {
      setInvImageFilePath(null);
      setAttachmentTabs([]);
    };
  }, [accpayFormControls]);

  useEffect(() => {
    if (accpayEntityChange?.value && invoiceFormData?.length) {
      let tempForm = [...invoiceFormData];
      tempForm?.map((item, index) => {
        if (item.uniqueKey === 'itemList') {
          let itemList = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'itemList'
          );
          tempForm.splice(index, 1, itemList);
        } else if (item.uniqueKey === 'location') {
          let location = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'location'
          );
          tempForm.splice(index, 1, location);
          let tempArray = secondaryFormElements;
          tempArray.map((val) => {
            if (Object.keys(val)[0] === 'Location') {
              val['Location'].invForm[0] = location;
            }
            return val;
          });
          setSecondaryFormElements(tempArray);
        } else if (item.uniqueKey === 'totalcharges') {
          let totalcharges = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'totalcharges'
          );
          tempForm.splice(index, 1, totalcharges);
          setOtherChargesFormData(
            totalcharges?.formSubDetailsInternalDTOList[0]
          );
        }
      });
      setInvoiceFormData(tempForm);
    }
  }, [accpayEntityChange]);

  useEffectOnce(() => {
    setReactDocViewerProp(invoiceService.annotationConfigs);
    return () => {
      invoiceService.annotationConfigs.defaultAnnotations = [];
      setReactDocViewerProp(invoiceService.annotationConfigs);
      dispatch(
        changedInputsAction({
          changedInputs: null,
          invID: invId,
          reqType: 'unsubscribe',
        })
      );
      setFocusedInput('');
      setReactDocViewerProp(null);
      invoiceService.annotationConfigs.entity = {};
    };
  }, []);

  const fileVersionUpload = (fileWithDescription) => {
    dispatch(
      uploadAdditionalFilesDetails({
        invId: invId,
        poId: poId,
        file: fileWithDescription.file,
      })
    );
    dispatch(getInvAdditionalDocuments(invId));
  };

  useLayoutEffect(() => {
    if (changedIPRes && changedIPRes?.value) {
      const defaultAnno = invoiceService.extractedValuesMapping(
        changedIPRes.value
      );
      invoiceService.annotationConfigs.defaultAnnotations = defaultAnno;
      invoiceService.annotationConfigs.initialScale =
        changedIPRes?.value?.scale;
      setReactDocViewerProp(invoiceService.annotationConfigs);
      let AISelectionCoords = [];
      changedIPRes?.value?.boundingBoxList?.map((coordsSelectedByAI) => {
        AISelectionCoords.push({
          ...coordsSelectedByAI,
          allCoords: changedIPRes?.value,
        });
      });
      setextractedCoordsVerification(AISelectionCoords);

      return () => {
        invoiceService.annotationConfigs.defaultAnnotations = [];
        setReactDocViewerProp(invoiceService.annotationConfigs);
      };
    }
  }, [changedIPRes]);

  /**Invoice Extraction Controls.
   * <AUTHOR> Wani, <AUTHOR> <AUTHOR>
   */

  const updateFocusedInput = useCallback(
    (uniqueKey) => {
      if (uniqueKey && uniqueKey !== focusedInput) {
        const entity = invoiceService.entities.find(
          (item) => item.uniquekey === uniqueKey
        );
        invoiceService.annotationConfigs.entity = entity;
        invoiceService.annotationConfigs.config.disableOCR = false;
        invoiceService.annotationConfigs.config.hideAnnotateableBoxes = false;
        invoiceService.annotationConfigs.config.hideAnnotatingEntityVisualizations = false;
        invoiceService.annotationConfigs.config.readonly = false;
        invoiceService.annotationConfigs.config.hideAnnotatingTooltips = false;
        let updatedState = { ...invoiceService.annotationConfigs };
        setFocusedInput(uniqueKey);
        setReactDocViewerProp(updatedState);
      }
    },
    [focusedInput]
  );

  useEffect(() => {
    if (ExtractData && ExtractData !== undefined) {
      poInvFormAndIconSetter();
    }

    return () => {
      dispatch(extractData({ data: null, reqType: 'unsubscribe' }));
    };
  }, [ExtractData]);

  useEffect(() => {
    if (inputDescription && inputDescription !== undefined) {
      let tempForm = secondaryFormElements;

      tempForm.map((val) => {
        if (Object.keys(val)[0] === 'Other Charges') {
          if (inputDescription.length === 1) {
            if (inputDescription[0].taxType.id === '') {
              val[
                'Other Charges'
              ].invForm[0].formSubDetailsInternalDTOList[0].value = null;
            } else {
              val[
                'Other Charges'
              ].invForm[0].formSubDetailsInternalDTOList[0].value =
                inputDescription;
            }
          } else {
            val[
              'Other Charges'
            ].invForm[0].formSubDetailsInternalDTOList[0].value =
              inputDescription;
          }
        }
      });
      setSecondaryFormElements(tempForm);
    }
  }, [inputDescription]);

  useEffect(() => {
    setDisabledFlag(disabled);

    if (!disabled) {
      setClassToDisabled('is-editing');
      setfileViewer('reactDocViewer');
    } else {
      setClassToDisabled('is-not-editing');
      setfileViewer('iframe');
    }
  }, [disabled]);

  useEffect(() => {
    poInvFormAndIconSetter();
  }, [accpayRowIcons]);

  useEffect(() => {
    let option =
      multiselectFilterWithCodeClientData &&
      multiselectFilterWithCodeClientData.value &&
      multiselectFilterWithCodeClientData.value.length
        ? multiselectFilterWithCodeClientData.value.map((item) => {
            const container = {};
            container.commonId = item.supplierId;
            container.commonName = item.supplierName;
            return container;
          })
        : [];

    let primaryDetailsMap = cloneDeep(primaryFormElements);

    primaryDetailsMap.map((formControl) => {
      let primaryData = formControl[Object.keys(formControl)];
      if (primaryData && primaryData.uniqueKey === 'supplier') {
        primaryData.invForm[0].comboBoxOptions = option;
        if (
          multiselectFilterWithCodeClientData?.value?.length &&
          focusInputRef.current === 'supplier' &&
          ExtractData?.outputValue
        ) {
          var optionsv1 = multiselectFilterWithCodeClientData?.value.filter(
            (item) =>
              item.supplierName.toLowerCase() ===
              ExtractData?.outputValue.toLowerCase()
          );
          if (optionsv1 && optionsv1.length) {
            primaryData.invForm[0].value = {
              id: optionsv1[0].supplierId,
              value: optionsv1[0].supplierName,
            };
          }
        }
      }
    });
    setPrimaryFormElements(primaryDetailsMap);
  }, [multiselectFilterWithCodeClientData]);

  useEffect(() => {
    if (onSubmittedClick) {
      checkValidation();
    }
    if (onSaveClick) {
      let formDetails = cloneDeep(invFormControlsRef.current);
      formDetails.userComments = addCommentsValue ? addCommentsValue : '';

      let formDetailsDtoList = [];

      let primaryDetailsMap = cloneDeep(primaryFormElements);
      let secondaryDetailsMap = cloneDeep(secondaryFormElements);

      primaryDetailsMap.map((formControl) => {
        let mapValue = formControl[Object.keys(formControl)];
        if (mapValue && mapValue.invForm && mapValue.invForm[0])
          formDetailsDtoList.push(mapValue.invForm[0]);
        return null;
      });

      secondaryDetailsMap.map((formControl) => {
        let mapValue = formControl[Object.keys(formControl)];
        if (mapValue && mapValue.invForm && mapValue.invForm[0])
          formDetailsDtoList.push(mapValue.invForm[0]);
        return null;
      });

      formDetails.formDetailsDtoList = formDetailsDtoList;
      let invForms = cloneDeep(invoiceFormData);
      if (invForms) {
        var newArray = invForms.filter(function (el) {
          return el.uniqueKey && el.uniqueKey === 'itemList';
        });
        newArray && newArray.length > 0
          ? (formDetails.formDetailsDtoList.push(newArray[0]),
            (formDetails.invItemsDtoList = newArray[0].value))
          : '';
      }

      dispatch(
        passINVEditedDetails({
          clientId: globalutils.getDataFromStorage('clientId'),
          userId: globalutils.getDataFromStorage('userId'),
          detailsData: formDetails,
        })
      );
      dispatch(inputTextDescription([]));

      if (Object.keys(changedInput).length !== 0) {
        if (document.getElementsByClassName('page__container').length) {
          var width =
            document.getElementsByClassName('page__container')[0].clientWidth;
          var height =
            document.getElementsByClassName('page__container')[0].clientHeight;
        }

        let scle = invoiceService.annotationConfigs.initialScale
          ? invoiceService.annotationConfigs.initialScale
          : 2;

        let inputsBody = {
          invoiceInputFieldsDto: changedInput,
          pageHeight: height,
          pageWidth: width,
          scale: scle,
        };

        /**<AUTHOR> R B commented this api which is causing error for time being. */
        // dispatch(
        //   accPayAction.changedInputsAction(inputsBody, invId, "subscribe")
        // );
      }
      if (extractionSaveObject) {
        saveCordsHandler(null, null);
      }
    }
  }, [onSaveClick, onSubmittedClick]);

  const saveCordsHandler = (key, userConfirmedOject) => {
    if (!suppId) {
      dispatch(
        showDialog({
          showPopup: true,
          type: 'error',
          responseMessage: 'Extraction failed! Supplier not found.',
          canClose: true,
          autoHide: false,
        })
      );
    } else if (extractionSaveObject?.invCordinates?.length && !key) {
      dispatch(sendCoordsList(extractionSaveObject));
    }

    if (key === 'verfiedByUser' && userConfirmedOject?.invCordinates?.length) {
      dispatch(sendCoordsList(userConfirmedOject));
      dispatch(
        changedInputsAction({
          changedInputs: null,
          invID: invId,
          reqType: 'unsubscribe',
        })
      );
    }
  };

  useEffect(() => {
    dueDateFromBaselineDate && dueDateFromBaselineDate !== undefined
      ? setDueDateList(dueDateFromBaselineDate.value)
      : '';
  }, [dueDateFromBaselineDate, dueDateFromBaselineDateError]);

  //set baselinedate and duedate on duedate calculate func response (getDueDateFromBaselineDate).
  useEffect(() => {
    if (dueDateList && secondaryFormElements) {
      let secondaryDetailsMap = cloneDeep(secondaryFormElements);

      secondaryDetailsMap.map((formControl) => {
        let secData = formControl[Object.keys(formControl)];

        if (secData && secData.uniqueKey === 'dueDate') {
          if (dueDateList && dueDateList.dueDate) {
            secData.invForm.length
              ? (secData.invForm[0].value = dueDateList.dueDate)
              : null;
          }
          setSecondaryFormElements(secondaryDetailsMap);
          return null;
        }
        return null;
      });
    }
  }, [dueDateList]);

  //set baselinedate and paymentterms to state on initial response for calculation of duedate.
  const setBaselineAndPymtTerms = () => {
    var paymentTermsId = null;
    var invDate = '';

    let primaryDetailsMap = cloneDeep(primaryFormElements);
    let secondaryDetailsMap = cloneDeep(secondaryFormElements);

    secondaryDetailsMap.map((formControl) => {
      let secData = formControl[Object.keys(formControl)];
      if (secData && secData.uniqueKey === 'invDate') {
        if (secData.invForm.length) {
          secData.invForm[0].value ? (invDate = secData.invForm[0].value) : '';
        }
      }
    });

    primaryDetailsMap.map((formControl) => {
      let primaryData = formControl[Object.keys(formControl)];
      if (primaryData && primaryData.uniqueKey === 'pymtTerms') {
        if (primaryData.invForm.length) {
          primaryData.invForm[0].value
            ? (paymentTermsId = parseInt(primaryData.invForm[0].value?.id))
            : '';
        }
      }
    });

    return { paymentTermsId: paymentTermsId, invDate: invDate };
  };

  //function to calculate due date on baselinedate and paymentterms.
  const getDueDateFromBaselineDate = (uniqueName, targetValue) => {
    let formDetails = cloneDeep(invFormControlsRef.current);

    var obj = setBaselineAndPymtTerms();

    let invId = formDetails.inv_id;
    let clientId = formDetails.clientId;
    if (uniqueName === 'invDate') {
      let invoiceDate = targetValue;
      invoiceDate && obj.paymentTermsId
        ? dispatch(
            getDueDateCalculated({
              invId,
              paymentTermsId: obj.paymentTermsId,
              clientId,
              invoiceDate,
              baselineDate,
            })
          )
        : '';
    }

    if (uniqueName === 'pymtTerms') {
      targetValue && obj.invDate
        ? dispatch(
            getDueDateCalculated({
              invId,
              paymentTermsId: targetValue,
              clientId,
              invoiceDate: obj.invDate,
              baselineDate,
            })
          )
        : '';
    }
  };

  const onChangeStateHandler = (value, element) => {
    primaryFormElements.map((formControl) => {
      let primaryData = formControl[Object.keys(formControl)];
      if (primaryData && primaryData.uniqueKey === element.uniqueKey) {
        if (primaryData.invForm.length) {
          inputChangeCache(element, value);
          primaryData.invForm[0].value = value;
          primaryData.invForm[0].errorFlag = false;
        }
        return null;
      }

      return null;
    });

    secondaryFormElements.map((formControl) => {
      let secondaryData = formControl[Object.keys(formControl)];
      if (secondaryData && secondaryData.uniqueKey === element.uniqueKey) {
        if (secondaryData.invForm.length) {
          inputChangeCache(element, value);
          secondaryData.invForm[0].value = value;
          secondaryData.invForm[0].errorFlag = false;
        }
        return null;
      }
      return null;
    });
  };

  const inputChangeCache = (element, value) => {
    let cachedInputs = { ...changedInput };
    const input = inputArray.find(
      (input) => input.uniqueKey === element.uniqueKey
    );
    if (!input) {
      return cachedInputs;
    }

    if (input.type === 'Multiselect') {
      if (input?.value?.length && value?.length) {
        cachedInputs[input.uniqueKey] = value[0].label;
      } else {
        delete cachedInputs[input.uniqueKey];
      }
    } else {
      if (input.value !== value) {
        cachedInputs[input.uniqueKey] = value;
      } else {
        delete cachedInputs[input.uniqueKey];
      }
    }

    setchangedInput(cachedInputs);
  };

  const handleINVOnChange = (event, element) => {
    onChangeStateHandler(event.target.value, element);
  };

  //handle dates according to client's date format.
  const handleDateChange = (date, element) => {
    //event is formatted date string

    onChangeStateHandler(date, element);
    getDueDateFromBaselineDate(element.uniqueKey, date);
  };

  const handleComboBoxValue = (event, element) => {
    if (element.uniqueKey === 'pymtTerms') {
      getDueDateFromBaselineDate(element.uniqueKey, event);
      return;
    }
    let targetValue = parseInt(event.target.value);
    onChangeStateHandler(targetValue, element);
  };
  //if paymentterms, handle combobox to calculate duedate by calling func "getDueDateFromBaselineDate".
  const handleMultiselectComboBoxValue = (value, element, key) => {
    if (key === 'not-present') {
      onChangeStateHandler(value, element);
      // if (element.uniqueKey === "pymtTerms") {
      //   getDueDateFromBaselineDate(element.uniqueKey, value?.id);
      //   setPaymentTermsId(value?.id);
      // }
    }
    if (key === 'present') {
      handleSearchInputValue(element, value);
    }
  };

  const handleSearchInputValue = (element, event) => {
    multiselectFilter(event?.target?.value, element?.uniqueKey);
    if (!event?.target?.value) {
      onChangeStateHandler({ id: null, value: '' }, element);
    }
  };

  /**
   * <AUTHOR>
   */

  var count = 0;
  var itemListErrorCount = 0;

  const validationCounter = (uniqueKey, data) => {
    if (uniqueKey)
      switch (uniqueKey) {
        case 'invNumber': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, invNumberFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'poNumber': {
          count = count + 1;
          return tableName[1] === 'non-po' ||
            tableName[1] === 'cost-inv' ||
            tableName[1] === 'credit_note'
            ? (setcheckFlag((prevState) => {
                return { ...prevState, poNumberFlag: true };
              }),
              (data.errorFlag = false))
            : data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, poNumberFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'supplier': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, supplierNameFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'invDate': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, invDateFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'dueDate': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, dueDateFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'amountConverted': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, amountFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'currency': {
          count = count + 1;
          return data.value
            ? (setcheckFlag((prevState) => {
                return { ...prevState, currencyFlag: true };
              }),
              (data.errorFlag = false))
            : (data.errorFlag = true);
        }
        case 'itemList': {
          count = count + 1;
          return (
            data.value.length &&
            data.value.map((item) => {
              item.serialNo && item.description && item.amountConverted
                ? setcheckFlag((prevState) => {
                    return { ...prevState, itemListFlag: true };
                  })
                : (itemListErrorCount = itemListErrorCount + 1);
            })
          );
        }
      }
    return null;
  };

  //check if all mandatory fields are filled on save click.
  const checkValidation = () => {
    // validate itemlist seperately
    let primaryDetailsMap = cloneDeep(primaryFormElements);
    let secondaryDetailsMap = cloneDeep(secondaryFormElements);

    primaryDetailsMap.map((formControl) => {
      let primaryData = formControl[Object.keys(formControl)];
      validationCounter(primaryData.uniqueKey, primaryData.invForm[0]);
    });

    secondaryDetailsMap.map((formControl) => {
      let secData = formControl[Object.keys(formControl)];
      validationCounter(secData.uniqueKey, secData.invForm[0]);
    });

    if (invFormControlsRef.current) {
      let formDetails = cloneDeep(invFormControlsRef.current);
      formDetails.formDetailsDtoList.map((data) => {
        if (data && data.uniqueKey === 'itemList') {
          validationCounter(data.uniqueKey, data);
        }
      });
    }

    itemListErrorCount > 0
      ? (setcheckFlag((prevState) => {
          return { ...prevState, itemListFlag: false };
        }),
        setItemListMessage('Data missing in item list!'))
      : null;

    setErrorFlagCount(count);
    setPrimaryFormElements(primaryDetailsMap);
    setSecondaryFormElements(secondaryDetailsMap);
  };

  //function to dispatch edited details after validation.
  //if results of checkFlag(checkFlagresult) is true proceed dispatch else alert error.
  useEffect(() => {
    if (errorFlagCount) {
      if (errorFlagCount === 8) {
        let checkFlagresult = true;
        try {
          for (var i in checkFlag) {
            if (i && checkFlag[i] === false) {
              checkFlagresult = false;
              break;
            }
          }
        } catch (err) {
          notificationBar('error', 'Something went wrong!..');
        }

        checkFlagresult === true
          ? onSubmittedClick
            ? onSubmitvalidationSuccess(true)
            : null
          : (setisLoading(false),
            notificationBar(
              'error',
              'Process failed! Mandatory fields missing. ' + itemListMessage
            ));
        onSaveDisable(false);
        onSubmittedClickDisable(false);
        setErrorFlagCount(0);
      } else {
        setisLoading(false);
        notificationBar('error', 'Something went wrong!..');
      }
    }
  }, [errorFlagCount]);

  const handleAdvancedSelect = (event, element) => {
    if (element.type === 'AdvancedSelect') {
      element.value = event;
      onChangeStateHandler(event, element);
      if (element.uniqueKey === 'pymtTerms') {
        getDueDateFromBaselineDate(element.uniqueKey, event?.id ?? event);
        setPaymentTermsId(event?.id ?? event);
      } else if (event?.id && element.uniqueKey === 'invEntity') {
        let payload = invoiceFormData?.filter(
          (item) =>
            item.uniqueKey === 'itemList' ||
            item.uniqueKey === 'totalcharges' ||
            item.uniqueKey === 'location'
        );
        dispatch(
          getEntityChangeRes({ clientId, entityId: event.id, payload, invId })
        );
      }
    }
  };

  const onChangeHandlingFunctions = {
    handleINVOnChange,
    handleDateChange,
    handleComboBoxValue,
    handleMultiselectComboBoxValue,
    handleAdvancedSelect,
  };

  const handleAdvancedSelectSearch = (filter, element) => {
    if (element?.type === 'AdvancedSelect') {
      if (element?.uniqueKey === 'supplier') {
        dispatch(
          getSupplierFilteredByClientWithCode({
            filterField: filter,
            clientId,
            type: filter ? subscribe : unSubscribe,
          })
        );
      }
    }
  };

  const multiselectFilter = (filter, uniqueKey) => {
    if (uniqueKey && uniqueKey === 'supplier') {
      if (filter) {
        // setMultiselectFilterValue(filter);
        dispatch(
          getSupplierFilteredByClientWithCode({
            filterField: filter,
            clientId,
            type: subscribe,
          })
        );
      } else {
        dispatch(
          getSupplierFilteredByClientWithCode({
            filterField: null,
            clientId: null,
            type: unSubscribe,
          })
        );
      }
    }
  };

  // useEffect(() => {
  //   if (multiselectFilterValue) {
  //     dispatch(accPayAction.getMultiselectFilterData(multiselectFilterValue, subscribe));
  //   }
  // }, [multiselectFilterValue]);

  /**Function returns a list of labels from incoming form array list.
   * <AUTHOR> R B
   */
  const getLabelListFromFormControls = (formList) => {
    if (formList) {
      return formList.map((obj) =>
        obj.displayName && obj.type !== 'DataTable' ? obj.displayName : null
      );
    }
  };
  /**Function returns list of labels to form array list.
   * <AUTHOR> Sherlin
   */
  function getLabelList(invLabelList, poLabelList) {
    if (invLabelList && poLabelList) {
      return invLabelList.concat(poLabelList);
    } else if (invLabelList) {
      return invLabelList;
    } else if (poLabelList) {
      return poLabelList;
    } else {
      return null;
    }
  }

  const setValueOfDataTable = (invItemsOnAdd) => {
    var tempArray = cloneDeep(invoiceFormData);

    tempArray?.map((item) => {
      item.type === 'DataTable' ? (item.value = invItemsOnAdd) : null;
    });

    tempArray.invItemsDtoList = invItemsOnAdd;
    setInvoiceFormData(tempArray);
  };

  const onDocPointer = () => {
    if (docRef.current) {
      docRef.current.scrollIntoView({ behavior: 'smooth' });
      setDisToolTip(true);
      setDisabled(false);
    }
  };
  const poInvFormAndIconSetter = () => {
    /**Function returns Icon class name to form array list.
     * <AUTHOR> Sherlin
     */
    function getIcons(filteredLabelList, i) {
      if (
        tableName &&
        tableName[1] !== 'approved' &&
        tableName[1] !== 'for-approve'
      ) {
        if (accpayRowIcons && accpayRowIcons.data.value) {
          let formIconData = accpayRowIcons.data.value;
          const findIndex = formIconData.findIndex(
            (item) => item.fieldName === filteredLabelList[i]
          );
          if (findIndex !== -1) {
            return formIconData[findIndex].fieldClassName;
          } else {
            return 'ico-';
          }
        }
      }
      return '';
    }
    function getIconTitle(filteredLabelList, i) {
      if (accpayRowIcons && accpayRowIcons.data.value) {
        let formIconData = accpayRowIcons.data.value;
        const findIndex = formIconData.findIndex(
          (item) => item.fieldName === filteredLabelList[i]
        );
        if (findIndex !== -1) {
          return formIconData[findIndex].fieldDisplayTitle;
        } else {
          return '';
        }
      }
    }

    var invoiceFormControlsData = [];
    var poFormControlsData = [];
    var invLabelList = [];
    var poLabelList = [];

    if (invFormControlsRef.current && poFormControls) {
      if (invFormControlsRef.current.formDetailsDtoList) {
        invoiceFormControlsData =
          invFormControlsRef.current.formDetailsDtoList.filter(
            (e) => e.type !== 'div'
          );
        invLabelList = getLabelListFromFormControls(
          invoiceFormControlsData
        ).filter((e) => e != null);
      }
      if (poFormControls.formDetailsDtoList) {
        poFormControlsData = poFormControls.formDetailsDtoList;
        poLabelList = getLabelListFromFormControls(poFormControlsData).filter(
          (e) => e != null
        );
      }
      var combinedArrayList = null;
      combinedArrayList = getLabelList(invLabelList, poLabelList);

      let primaryArray = [];
      let secondaryArray = [];
      if (combinedArrayList != null) {
        //Removing Duplicates from Array
        let filteredLabelList = [...new Set(combinedArrayList)];

        filteredLabelList.some((labelName, i) => {
          let labelAndValueMap = [];
          let poForm = [];
          let invForm = [];
          let invFlag = false;
          let poFlag = false;

          if (invoiceFormControlsData) {
            invoiceFormControlsData.forEach((invoice) => {
              if (
                invoice.displayName &&
                invoice.displayName === labelName &&
                invoice.type !== 'DataTable'
              ) {
                // Clone the invoice object to avoid modifying state directly
                let clonedInvoice = { ...invoice };

                if (
                  ExtractData?.outputValue &&
                  clonedInvoice.uniqueKey === ExtractData?.key
                ) {
                  if (clonedInvoice.type === 'SearchInput') {
                    let optionsv1 = [];
                    let data = { id: null, value: '' };

                    if (clonedInvoice.uniqueKey === 'supplier') {
                      multiselectFilter(ExtractData?.outputValue, 'supplier');
                    } else if (clonedInvoice.comboBoxOptions) {
                      optionsv1 = clonedInvoice.comboBoxOptions?.filter(
                        (item) =>
                          item.commonName.toLowerCase() ===
                          ExtractData?.outputValue.toLowerCase()
                      );
                      if (optionsv1.length) {
                        clonedInvoice = {
                          ...clonedInvoice,
                          comboBoxOptions: optionsv1,
                          value: {
                            id: optionsv1[0].commonId,
                            value: optionsv1[0].commonName,
                          },
                        };
                      }
                    }
                  } else if (clonedInvoice.type === 'DatePickerInput') {
                    const NaNDate = isNaN(ExtractData?.outputValue);

                    if (NaNDate) {
                      const date = globalutils.parseExtractedDate(
                        ExtractData?.outputValue
                      );
                      try {
                        let formattedDate = format(
                          date,
                          user?.clientDateFormat
                        );
                        clonedInvoice = {
                          ...clonedInvoice,
                          value: formattedDate,
                        };

                        dispatch(
                          showDialog({
                            showPopup: true,
                            type: 'success',
                            responseMessage: `Extracted value : ${ExtractData?.outputValue}`,
                            canClose: true,
                            autoHide: true,
                          })
                        );
                      } catch (error) {
                        dispatch(
                          showDialog({
                            showPopup: true,
                            type: 'error',
                            responseMessage: `${date} : ${ExtractData?.outputValue}`,
                            canClose: true,
                            autoHide: false,
                          })
                        );
                      }
                    } else {
                      dispatch(
                        showDialog({
                          showPopup: true,
                          type: 'error',
                          responseMessage: `Invalid Date: ${ExtractData?.outputValue}`,
                          canClose: true,
                          autoHide: false,
                        })
                      );
                    }
                  } else {
                    clonedInvoice = {
                      ...clonedInvoice,
                      value: ExtractData?.outputValue,
                    };
                  }
                }

                invForm.push(clonedInvoice);
                invFlag = true;
              }
            });
          }

          if (poFormControlsData) {
            poFormControlsData.forEach((purchase) => {
              if (
                purchase.displayName &&
                purchase.displayName === labelName &&
                purchase.type !== 'DataTable'
              ) {
                poForm.push(purchase);
                poFlag = true;
              }
            });
          }

          labelAndValueMap.push({
            [labelName]: {
              poForm: poForm,
              invForm: invForm,
              iconClassName:
                tableName[0] === 'INV'
                  ? getIcons(filteredLabelList, i)
                  : 'ico-',
              iconTitle:
                tableName[0] === 'INV'
                  ? getIconTitle(filteredLabelList, i)
                  : '',
              isEditable: poForm.length ? 'row-not-editable' : 'row-editable',
              required: invForm[0]?.required || '',
              uniqueKey: invForm[0]?.uniqueKey || '',
            },
          });

          if (invFlag && poFlag) {
            if (poForm.length && invForm.length) {
              primaryArray.push(...labelAndValueMap);
            }
          } else {
            secondaryArray.push(...labelAndValueMap);
          }
        });
      }

      const primary = cloneDeep(primaryArray);
      const secondary = cloneDeep(secondaryArray);
      invoiceService.extractionLabels(primary, secondary);
      setPrimaryFormElements(primary);
      setSecondaryFormElements(secondary);
      setInvoiceFormData(invoiceFormControlsData);
      setPoFormData(poFormControlsData);
    }
  };
  /**This hook returns a map with label as key and list of po and inv matched result as value .
   * <AUTHOR> R B
   */

  const updatePrimarySecondaryStates = useCallback(() => {
    poInvFormAndIconSetter();
  }, [accpayRowIcons, dispatch, poFormControls, tableName]);

  useEffect(() => {
    updatePrimarySecondaryStates();
    // setBaselineAndPymtTerms();
  }, [invFormControlsRef.current, poFormControls]);

  /**Function returns Form Controls.
   * <AUTHOR> Sherlin
   */
  function getForms(getParameter, largeWidth = false) {
    return getParameter.map((formControl, index) => {
      /**Logic returns Title Updated.
       * <AUTHOR> Paul J
       */
      let formData = formControl[Object.keys(formControl)];
      let titleValue = '';
      if (
        formControl[Object.keys(formControl)].invForm[0]?.uniqueKey ===
        'supplier'
      ) {
        titleValue =
          formControl[Object.keys(formControl)].invForm[0]?.value?.[0]?.label;
      } else if (
        formControl[Object.keys(formControl)].invForm[0]?.value?.value
      ) {
        titleValue =
          formControl[Object.keys(formControl)].invForm[0]?.value?.value;
      } else {
        titleValue = formControl[Object.keys(formControl)].invForm[0]?.value;
      }
      return (
        <div className="single" key={index}>
          <div className="td label">
            {formData.iconClassName !== 'ico-' ? (
              <i className={formData.iconClassName} title={formData.iconTitle}>
                <span className="path1"></span>
                <span className="path2"></span>
                <span className="path3"></span>
              </i>
            ) : (
              <i className={'ico-'}></i>
            )}
            <p
              className={
                formData.required === 'Y' ? 'required-asterisk-field' : ''
              }
            >
              {Object.keys(formControl)}
            </p>
          </div>

          <div
            className={[
              'td invoice',
              formControl &&
              formControl[Object.keys(formControl)].invForm.length
                ? ''
                : 'this-is-empty',
            ].join(' ')}
            title={titleValue}
          >
            {formControl &&
            formControl[Object.keys(formControl)].invForm.length ? (
              <Input
                getLabel
                useIdasValue
                disabledFlag={props.disabled}
                formType={formControl[Object.keys(formControl)].invForm[0]}
                onChangeHandler={(element, event, key) =>
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element,
                    key
                  )
                }
                styleSheet={{ maxWidth: largeWidth ? '409px' : '200px' }}
                onSearchHandler={(element, event) =>
                  handleAdvancedSelectSearch(event, element)
                }
                onFocusHandler={(formType) =>
                  updateFocusedInput(formType?.uniqueKey)
                }
                disToolTip={props.disToolTip}
              />
            ) : (
              ''
            )}
          </div>

          {!props.disabledPO ? (
            <div
              className={[
                'td po ',
                formControl &&
                formControl[Object.keys(formControl)].poForm.length
                  ? ''
                  : 'this-is-empty',
              ].join(' ')}
            >
              {formControl &&
              formControl[Object.keys(formControl)].poForm.length ? (
                formData.isEditable === 'row-not-editable' ? (
                  <p
                    title={
                      formControl[Object.keys(formControl)].poForm[0]?.value
                    }
                  >
                    {formControl[Object.keys(formControl)].poForm[0]?.value}
                  </p>
                ) : (
                  <TextInput
                    name=""
                    label=""
                    autoComplete="off"
                    disabledFlag={props.disabled}
                    value={
                      formControl[Object.keys(formControl)].poForm[0].value
                    }
                  />
                )
              ) : (
                ''
              )}{' '}
            </div>
          ) : (
            ''
          )}
        </div>
      );
    });
  }

  const rowClickCondition = (event) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      event.poItemId,
      'poItemId'
    );
    setConditionalStyles(styleAttribute);
  };

  const handleConfirm = async (msg, anno, indexToReplace) => {
    const isConfirmed = await confirm(msg);
    if (isConfirmed) {
      anno.splice(indexToReplace, 1);
    } else {
      anno.splice(-1);
    }
    textMapping(anno);
  };

  const textMapping = (anno) => {
    if (!anno || anno.length === 0) return;

    const lastanno = anno.at(-1);
    const boundingBox = lastanno?.areaAnnotation?.boundingBox;
    const pdfInfo = lastanno?.areaAnnotation?.pdfInformation;
    const updatedCoords = invoiceService.mapDisplayNameToTextBox(anno);
    invoiceService.annotationConfigs.defaultAnnotations = updatedCoords;
    let updatedConfig = cloneDeep(invoiceService.annotationConfigs);

    if (Extraction && suppId) {
      const updatedInvCoordinates = Extraction?.invCordinates?.map(
        (element) => {
          if (element.invFieldKey === focusedInput) {
            return {
              ...element,
              cordinateFound: true,
              cordinateMapped: true,
              rectangle: {
                ...element.rectangle,
                x: boundingBox.left,
                y: boundingBox.top,
                width: boundingBox.width,
                height: boundingBox.height,
                pageHeight: pdfInfo.height,
                pageWidth: pdfInfo.width,
                pageNumber: lastanno.page,
              },
            };
          }
          return element;
        }
      );

      let updatedValues = {
        ...Extraction,
        invCordinates: updatedInvCoordinates,
        supplierId: suppId,
      };

      setextractionSaveObject(updatedValues);
      dispatch(
        extractData({
          data: {
            fileFullPath: Extraction?.filePath,
            rectangle: {
              x: boundingBox.left,
              y: boundingBox.top,
              width: boundingBox.width,
              height: boundingBox.height,
              pageWidth: pdfInfo.width,
              pageHeight: pdfInfo.height,
              pageNumber: lastanno.page,
            },
            key: focusedInput,
            clientId: globalutils.getDataFromStorage('clientId'),
          },
          reqType: 'subscribe',
        })
      );
    } else {
      var msg = 'Extraction failed!';
      if (!suppId) {
        msg = 'Extraction failed! Supplier not found.';
      }
      dispatch(
        showDialog({
          showPopup: true,
          type: 'error',
          responseMessage: msg,
          canClose: true,
          autoHide: false,
        })
      );
    }

    setReactDocViewerProp(updatedConfig);
  };

  const getAnnotations = (anno, childRef) => {
    if (
      previousAnnoListLength &&
      anno.length &&
      previousAnnoListLength > anno.length
    )
      return;

    if (anno && anno.length) {
      setPreviousAnnoListLength(anno.length);
      var text = 'Text';
      var indexToReplace = null;
      var valueArr = anno.map(function (item) {
        return item.entity.uniquekey;
      });

      var isDuplicate = valueArr.some(function (item, idx) {
        if (valueArr.indexOf(item) !== idx) {
          indexToReplace = valueArr.indexOf(item);
          text = anno[valueArr.indexOf(item)]?.entity?.name;
          return true;
        }
      });

      if (isDuplicate) {
        handleConfirm(
          text + ' was already marked. Are you sure you want to replace it.',
          anno,
          indexToReplace
        );
      } else {
        textMapping(anno);
      }
      setModelClose(false);
    }
  };

  const zoomHandler = (scaleValue) => {
    invoiceService.annotationConfigs.initialScale = scaleValue;
  };
  const confirmVerification = (verificationObject) => {
    if (Extraction) {
      Extraction?.invCordinates?.forEach((element) => {
        verificationObject.map((verfiedElement) => {
          if (verfiedElement?.uniqueKey.includes(element.invFieldKey)) {
            if (verfiedElement.matchedCheck) {
              element.cordinateFound = true;
              element.cordinateMapped = true;
              element.rectangle.x =
                verfiedElement.left * verfiedElement.allCoords.scale;
              element.rectangle.y =
                verfiedElement.top * verfiedElement.allCoords.scale;
              element.rectangle.width =
                verfiedElement.width * verfiedElement.allCoords.scale;
              element.rectangle.height =
                verfiedElement.height * verfiedElement.allCoords.scale;
              element.rectangle.pageHeight =
                verfiedElement.allCoords.pageHeight;
              element.rectangle.pageWidth = verfiedElement.allCoords.pageWidth;
              element.rectangle.pageNumber =
                verfiedElement.allCoords.currentPage;
            }
          }
        });
      });
      var updatedValues = {
        ...Extraction,
        supplierId: suppId,
      };
      saveCordsHandler('verfiedByUser', updatedValues);
      clearChangedInputAction();
    }
  };
  // eslint-disable-next-line no-lone-blocks
  const clearChangedInputAction = () => {
    dispatch(
      changedInputsAction({
        changedInputs: null,
        invID: null,
        reqType: 'unsubscribe',
      })
    );
  };
  const handlereactDocViewer = () => {
    if (classToDisabled === 'is-editing') {
      setModelClose(true);
    }
  };
  const expandableOnChange = () => {
    setExpandableInv(!expandableInv);
  };

  function MouseOver(event) {
    event.target.style.color = '#3c75bc';
    document.body.style.cursor = 'pointer';
  }
  function MouseOut(event) {
    event.target.style.color = '';
    document.body.style.cursor = 'default';
  }
  const disableToolTip = () => {
    setDistoolTip(false);
  };
  const navigateRoute = (id, e) => {
    dispatch(ACCPAY_FORM_PO_INV_DETAILS_SUCCESS(null));
    e.preventDefault();
    invoiceService.previousNext(
      id,
      dispatch,
      apCachedData,
      apListingFilteredData
    );
  };

  return (
    <>
      {/* /**
       * <AUTHOR> R B
       * commented this code for time being.
       */}
      {/* {changedIPRes && changedIPRes?.value?.boundingBoxList?.length ? (
        <DraggableComponent
          confirmVerification={confirmVerification}
          verificationProp={extractedCoordsVerification}
          clearChangedInputAction={clearChangedInputAction}
        />
      ) : (
        ''
      )} */}
      <div
        className={[
          className,
          'edit-only-half',
          tableName[0] === 'INV' ? classToDisabled : 'is-not-editing',
        ].join(' ')}
      >
        <div className="inv-div">
          <div className="inv-prev-next-icons">
            <div
              className="prev-wrap"
              onMouseEnter={() => setShowInvoiceToggle(true)}
              onMouseLeave={() => setShowInvoiceToggle(false)}
            >
              <div
                className="prev-container"
                style={{ display: showInvoiceToggle ? 'block' : 'none' }}
              >
                <div
                  title="Previous"
                  data-tooltip-id="invoice-prev"
                  onClick={(e) => navigateRoute('prev', e)}
                  className="icon-arrow-left2 fl prev"
                  style={{ display: showInvoiceToggle ? 'block' : 'none' }}
                ></div>
              </div>
            </div>
          </div>
          <div className="inv-table-view">
            <Tabs
              activeKey={activeTabKey}
              onChange={(key) => setActiveTabKey(key)}
              tabBarStyle={{ marginTop: '-24px', fontFamily: 'Roboto' }}
            >
              <TabPane className="th " tab={'Invoice'} key={0}>
                <div className="inv-table-body">
                  <div
                    className={[
                      'primary-secondary-table-wrap',
                      'data-full-view-wrap',
                    ].join(' ')}
                    style={props.disabledPO ? { width: '30%' } : {}}
                  >
                    <div className="inv-table-body-format">
                      {' '}
                      <div className="inv-table-header">
                        <div
                          className="inv-table-format"
                          style={
                            expandableInv
                              ? {
                                  backgroundColor: 'rgb(255 255 255)',
                                  WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
                                  border: '1px solid 255 255 255',
                                }
                              : {}
                          }
                        >
                          <div className="th label"></div>
                          <div className="th invoice">Invoice</div>
                          {!props.disabledPO ? (
                            <div className="th po">Purchase Order</div>
                          ) : (
                            ''
                          )}
                          <div
                            onClick={() => expandableOnChange()}
                            onMouseOver={MouseOver}
                            onMouseOut={MouseOut}
                          >
                            {expandableInv ? (
                              <i className="icon-tree-arrow"></i>
                            ) : (
                              <i className="icon-tree-arrow active"></i>
                            )}
                          </div>
                        </div>
                      </div>
                      {!expandableInv
                        ? primaryFormElements && primaryFormElements.length
                          ? getForms(primaryFormElements)
                          : ''
                        : ''}
                    </div>

                    <h3
                      className="reference-title"
                      style={
                        expandableInv
                          ? { marginTop: '8px', transition: '200ms' }
                          : {}
                      }
                    >
                      Reference
                    </h3>
                    <div className="inv-table-body-format reference-table">
                      {secondaryFormElements && secondaryFormElements.length
                        ? getForms(secondaryFormElements, true)
                        : ''}
                    </div>
                  </div>
                  {headerCheckBoxes && headerCheckBoxes.headerInvImage ? (
                    <div
                      ref={docRef}
                      className={['inv-image', imgHandle[0]].join(' ')}
                      style={
                        props.disabledPO
                          ? { position: 'relative', minWidth: '69%' }
                          : {}
                      }
                    >
                      <Tabpanels
                        tabsData={{
                          reactDocViewerProp,
                          getAnnotations,
                          zoomHandler,
                          invImageFilePath,
                          fileViewer,
                          setModelClose,
                          attachmentTabs,
                        }}
                      />
                    </div>
                  ) : null}
                </div>
                {!['RF', 'OF', 'YF'].includes(tableName[1]) ? (
                  <UploadDoc
                    uploadedList={uploadFileData}
                    invId={invId}
                    // invAdditionalDocuments={invAdditionalDocuments}
                    fileVersionUpload={fileVersionUpload}
                    fileVersionDisable={fileVersionDisable}
                  />
                ) : (
                  ''
                )}
                <div className="force-full-width">
                  <div className={'data-table-wrap'}>
                    <InvDatatable
                      disabledFlag={disabledFlag}
                      conditionalRowStyles={conditionalRowStyles}
                      dataTableClick={rowClickCondition}
                      setValueOfDataTable={setValueOfDataTable}
                      invData={invoiceFormData}
                      onSaveClick={onSaveClick}
                      onDocPointer={onDocPointer}
                      status={tableName[1]}
                    />
                    <PoDataTable
                      conditionalRowStyles={conditionalRowStyles}
                      dataTableClick={rowClickCondition}
                      disabledFlag={disabledFlag}
                      setValueOfDataTable={setValueOfDataTable}
                      poData={poFormData}
                      invData={invoiceFormData}
                    />
                  </div>
                </div>
              </TabPane>
              <TabPane className="th " tab={'Invoice Items'} key={1}>
                <div className="force-full-width">
                  <InvDatatable
                    fullWidth
                    disabledFlag={disabledFlag}
                    conditionalRowStyles={conditionalRowStyles}
                    dataTableClick={rowClickCondition}
                    setValueOfDataTable={setValueOfDataTable}
                    invData={invoiceFormData}
                    onSaveClick={onSaveClick}
                    onDocPointer={onDocPointer}
                  />
                </div>
              </TabPane>
              <TabPane className="th " tab={'PO Items'} key={2}>
                <div className="force-full-width">
                  <PoDataTable
                    fullWidth
                    conditionalRowStyles={conditionalRowStyles}
                    dataTableClick={rowClickCondition}
                    disabledFlag={disabledFlag}
                    setValueOfDataTable={setValueOfDataTable}
                    poData={poFormData}
                    invData={invoiceFormData}
                  />
                </div>
              </TabPane>
              <TabPane className="th " tab={'Other Charges'} key={3}>
                <div className="force-full-width">
                  <MultiRowInput
                    formType={otherChargesFormData}
                    disabled={disabledFlag}
                    invId={invId}
                    poFormData={poFormControls}
                  />
                </div>
              </TabPane>
            </Tabs>
          </div>
        </div>
      </div>
      <Modal
        className="ModelFullScreen"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={modelClose}
      >
        <div
          onClick={() => {
            setModelClose(false);
          }}
          className="modal-closeTop-1 icon-close"
          style={{ color: 'white', fontSize: 20 + 'px' }}
        ></div>
        {headerCheckBoxes && headerCheckBoxes.headerInvImage ? (
          <div className={['inv-image', imgHandle[0]].join(' ')}>
            <Docviewer
              reactDocprop={reactDocViewerProp}
              actionHandlers={{ getAnnotations, zoomHandler }}
              fileURL={invImageFilePath}
              fileType={'reactDocViewer'}
              zoom={'#zoom=100'}
              disableClick={false}
              fullScreen={false}
            />
          </div>
        ) : null}
      </Modal>
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
        style={{
          content: {
            width: '50%',
            height: '80%',
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            overflow: 'hidden',
          },
        }}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close mb20"
        ></div>
        <DocumentViewer
          fileURL={filePath}
          fileType={'iframe'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
        />
      </Modal>
    </>
  );
};
export { PoInvMatchingCompareComp };
