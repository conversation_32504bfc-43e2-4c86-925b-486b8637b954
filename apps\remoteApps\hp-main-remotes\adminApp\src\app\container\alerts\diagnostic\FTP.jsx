/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-10-21 10:47:32
 * @modify date 01-06-2023 21:31:32
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './diagnostic.scss';
import { dataTableServiceProvider, Input, Button } from '@hp/components';
import {
  getAllFtpForStatusCheck,
  getAllFtpForStatusCheckByInOut,
  getAllFtpForStatusCheckForClient,
  getAllFtpForStatusCheckForClientAndInOut,
  getFtpForSingleConnection,
  showDialog,
} from '@hp/mainstore';

const FTP = () => {
  const [selectionfield, setSelectionfield] = useState(null);
  const [datatable, setDatatable] = useState({ value: [] });
  const [clientId, setClientId] = useState('');
  const [inOut, setInOut] = useState('');
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const dispatch = useDispatch();

  const { ftpRes, SingleConnectionStatus } = useSelector(
    (store) => store.admin
  );
  const { innerMenuName } = useSelector((store) => store.menu);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  //initial api call ....

  useEffect(() => {
    dispatch(getAllFtpForStatusCheck());
  }, []);

  // reducer value handler

  useEffect(() => {
    if (ftpRes && ftpRes.value) {
      if (!selectionfield) {
        setSelectionfield(ftpRes.value[0]);
        setDatatable(ftpRes.value[1]);
      } else {
        setDatatable(ftpRes.value[1]);
      }
      // handleAutoRefreshForEachRows(ftpRes.value[1]?.value);
    }
  }, [ftpRes]);

  // useEffect(() => {
  //   if (SingleConnectionStatus?.value) {
  //     let resVal = SingleConnectionStatus.value;
  //     let tempVal = datatable.value;
  //     tempVal.splice(resVal.serialNo - 1, 1, resVal);
  //     setDatatable((prev) => {
  //       return { ...prev, value: tempVal };
  //     });
  //   }
  // }, [SingleConnectionStatus]);

  useEffect(() => {
    if (SingleConnectionStatus?.value) {
      let resVal = SingleConnectionStatus.value;
      const updatedData = datatable.value.map((item, index) =>
        index === resVal.serialNo - 1 ? resVal : item
      );
      setDatatable((prev) => {
        return { ...prev, value: updatedData };
      });
    }
  }, [SingleConnectionStatus]);

  // function delay(time) {
  //   return new Promise((resolve) => setTimeout(resolve, time));
  // }

  // async function handleAutoRefreshForEachRows(rowsList) {
  //   for (let i = 0; i < rowsList.length; i++) {
  //     dispatch(getFtpForSingleConnection(rowsList[i]));
  //     await delay(1000);
  //   }
  // }

  const SingleConnectionHandler = (row) => {
    dispatch(getFtpForSingleConnection(row));
  };

  const selectionHandler = (element, event) => {
    const updatedElement = { ...element, value: event.target.value };

    if (updatedElement.displayName === 'Select Client') {
      setClientId(event.target.value);
    } else {
      setInOut(event.target.value);
    }

    const updatedFormSubDetails =
      selectionfield.formSubDetailsInternalDTOList.map((item) => {
        if (item.formSubDetailsId === updatedElement.formSubDetailsId) {
          return { ...item, value: event.target.value };
        }
        return item;
      });

    setSelectionfield({
      ...selectionfield,
      formSubDetailsInternalDTOList: updatedFormSubDetails,
    });
  };

  //search....../

  const searchHandler = () => {
    if (clientId && inOut) {
      dispatch(
        getAllFtpForStatusCheckForClientAndInOut({
          clientId,
          inOut,
        })
      );
    } else if (clientId) {
      dispatch(getAllFtpForStatusCheckForClient(clientId));
    } else if (inOut) {
      dispatch(getAllFtpForStatusCheckByInOut(inOut));
    } else {
      funcToSetResponseMessage('info', 'select one field');
    }
  };

  const clearHandler = () => {
    const Clearall = selectionfield.formSubDetailsInternalDTOList.map(
      (item) => {
        return { ...item, value: null };
      }
    );
    setSelectionfield({
      ...selectionfield,
      formSubDetailsInternalDTOList: Clearall,
    });
    setClientId('');
    setInOut('');
  };

  const onRowClick = (row) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  };

  return (
    <>
      <div className="page-title">{innerMenuName}</div>

      {selectionfield && selectionfield.formSubDetailsInternalDTOList.length ? (
        <div className={selectionfield.className}>
          {selectionfield.formSubDetailsInternalDTOList.map((item) => {
            return (
              <Input
                indexKey={item.formSubDetailsId}
                formType={item}
                onChangeHandler={(element, event) =>
                  selectionHandler(element, event)
                }
                isEditable={'notShowing'}
              />
            );
          })}

          <div>
            <Button
              className="small default mr4 "
              style={{ marginTop: '24px' }}
              onClick={clearHandler}
            >
              {' '}
              Clear
            </Button>
            <Button
              className="small default"
              style={{ marginTop: '24px' }}
              onClick={searchHandler}
            >
              {' '}
              Search
            </Button>
          </div>
        </div>
      ) : (
        ''
      )}

      {datatable ? (
        <div className={datatable.className}>
          <Input
            formType={datatable}
            conditionalRowStyles={conditionalRowStyles}
            dataTableColumn={
              datatable.formSubDetailsInternalDTOList &&
              Array.isArray(datatable.formSubDetailsInternalDTOList) &&
              datatable.formSubDetailsInternalDTOList.length
                ? datatable.formSubDetailsInternalDTOList.map((value) => {
                    return {
                      width:
                        value.selector === 'serialNo'
                          ? '6%'
                          : value.width
                          ? value.width
                          : '',
                      name: value.displayName ? value.displayName : '',
                      selector: value.selector ? value.selector : '',
                      cell:
                        value.selector === 'refreshIcon'
                          ? function displayCell(row) {
                              return (
                                <span
                                  className="icon-refresh"
                                  style={{ fontSize: 'medium' }}
                                  onClick={() => {
                                    SingleConnectionHandler(row);
                                    onRowClick(row);
                                  }}
                                ></span>
                              );
                            }
                          : value.selector === 'fileAccessURL'
                          ? function displayCell(row) {
                              return row.fileAccessURL === 'Ok' ? (
                                <span
                                  style={{ color: '#6fcf97' }}
                                  className="icon-new-checkbox"
                                ></span>
                              ) : (
                                <span
                                  style={{ color: '#FF0000' }}
                                  className="icon-error"
                                ></span>
                              );
                            }
                          : '',
                    };
                  })
                : []
            }
          />
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export { FTP };
