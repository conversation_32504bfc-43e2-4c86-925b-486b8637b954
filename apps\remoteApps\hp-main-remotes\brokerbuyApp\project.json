{"name": "brokerbuyApp", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/remoteApps/hp-main-remotes/brokerbuyApp/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "dist/apps/remoteApps/hp-main-remotes/brokerbuyApp", "main": "apps/remoteApps/hp-main-remotes/brokerbuyApp/src/main.jsx", "tsConfig": "apps/remoteApps/hp-main-remotes/brokerbuyApp/tsconfig.app.json", "rspackConfig": "apps/remoteApps/hp-main-remotes/brokerbuyApp/rspack.config.js", "assets": ["apps/remoteApps/hp-main-remotes/brokerbuyApp/src/favicon.ico", "apps/remoteApps/hp-main-remotes/brokerbuyApp/src/assets"]}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "optimization": true, "sourceMap": false, "rspackConfig": "apps/remoteApps/hp-main-remotes/brokerbuyApp/rspack.config.prod.js"}}}, "serve": {"executor": "@nx/rspack:module-federation-dev-server", "options": {"buildTarget": "brokerbuyApp:build:development", "port": 4208}, "configurations": {"development": {}, "production": {"buildTarget": "brokerbuyApp:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/rspack:module-federation-static-server", "defaultConfiguration": "production", "options": {"serveTarget": "brokerbuyApp:serve"}, "configurations": {"development": {"serveTarget": "brokerbuyApp:serve:development"}, "production": {"serveTarget": "brokerbuyApp:serve:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/remoteApps/hp-main-remotes/brokerbuyApp/jest.config.js"}}}}