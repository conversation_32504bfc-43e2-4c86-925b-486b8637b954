import { Input } from '@hp/components';
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  pdmConstants,
  setPage,
  setRowsPerPage,
  showDialog,
} from '@hp/mainstore';
import { ECRECOPartNoSelect } from './ECRECOPartNoSelect';
import { Tooltip } from 'antd';
// eslint-disable-next-line react/display-name
const ECRECOPartNoSection = forwardRef(
  ({ parameter, formData, partNoChanged, ...props }, ref) => {
    const dispatch = useDispatch();
    const { pageNumber, pageRowCount } = useSelector((store) => store.table);

    const [tableData, setTableData] = useState(null);
    const [selectedPartNo, setSelectedPartNo] = useState(null);
    const [partNoList, setPartNoList] = useState([]);

    useEffect(() => {
      return () => {
        handleClearPartNo();
        dispatch(setRowsPerPage(10));
        let removeNotification = null;
        dispatch({
          type: pdmConstants.GET_ECR_ECO_PART_NO_SUCCESS,
          removeNotification,
        });
      };
    }, [parameter]);

    useEffect(() => {
      if (formData?.length)
        formData.forEach((item) => {
          if (item.uniqueKey === 'partNoTable') {
            setTableData(item);
            if (item?.value?.length && parameter !== 'view') {
              let tempPartNoList = item.value.map((entry) => entry.value);
              setPartNoList(tempPartNoList);
            }
          }
        });
    }, [formData]);

    function handleClearPartNo() {
      setPartNoList([]);
      setSelectedPartNo(null);
      setTableData((prev) => ({ ...prev, value: [] }));
    }

    useImperativeHandle(ref, () => ({
      handleClearPartNo,
    }));

    const handlePartNoChange = (event, uniqueKey, element) => {
      setSelectedPartNo(event);
      handleAddPartNo(event);
    };

    const funcToSetResponseMessage = (type, resMessage) => {
      let showPopup = true;
      let canClose = true;
      let autoHide = true;
      dispatch(
        showDialog({
          showPopup: showPopup,
          type: type,
          responseMessage: resMessage,
          canClose: canClose,
          autoHide: autoHide,
        })
      );
    };

    const handleAddPartNo = (selectedPartNo) => {
      if (selectedPartNo?.value) {
        let res = partNoList.find((val) => val === selectedPartNo?.value);
        if (res) {
          funcToSetResponseMessage('info', 'Part Number is already selected');
          return;
        }

        let tempPartNoList = [...partNoList, selectedPartNo.value];
        let tempTableValues = [
          ...(tableData.value ?? []),
          {
            partNo: selectedPartNo.label,
            value: selectedPartNo.value,
          },
        ];
        setSelectedPartNo(null);
        setPartNoList(tempPartNoList);
        setTableData((prev) => ({
          ...prev,
          value: tempTableValues,
        }));
        partNoChanged(
          tempPartNoList,
          tempTableValues.map((entry) => ({
            commonId: entry.value,
            commonName: entry.partNo,
          }))
        );
      }
    };

    const handleRemovePartNo = (row) => {
      let tempPartNoList = partNoList.filter((item) => item !== row.value);
      let tempTableValues = tableData.value.filter(
        (item) => item.value !== row.value
      );
      setPartNoList(tempPartNoList);
      setTableData((prev) => ({
        ...prev,
        value: tempTableValues,
      }));
      partNoChanged(
        tempPartNoList,
        tempTableValues.map((entry) => ({
          commonId: entry.value,
          commonName: entry.partNo,
        }))
      );
    };

    const handlePartNoRender = (tableData) => {
      let tableColumns =
        tableData?.formSubDetailsInternalDTOList?.map((val, index) => {
          return {
            selector: val.selector,
            name: val.displayName,
            width: val.displayWidth,
            cell:
              val.selector === 'partNo'
                ? function displayCell(row) {
                    return row.addButton ? (
                      <ECRECOPartNoSelect
                        style={{
                          width: '600px',
                          placeholder: { color: 'black' },
                          fontFamily:
                            'Roboto, Verdana, Arial, Tahoma, sans-serif',
                        }}
                        value={selectedPartNo}
                        selectedValue={(val) => handlePartNoChange(val)}
                        placeholder={'Search & Add a Part #'}
                      />
                    ) : (
                      <Tooltip
                        classNames={{ root: 'ant-tooltip-container' }}
                        title={row[val.selector]}
                      >
                        <div className="display-title custom-overflow">
                          {row[val.selector]}
                        </div>
                      </Tooltip>
                    );
                  }
                : val.selector === 'addButton'
                ? function displayCell(row) {
                    return parameter !== 'view' ? (
                      row.addButton ? (
                        ''
                      ) : (
                        <span
                          key={index}
                          style={{ fontSize: '16px', marginRight: '8px' }}
                          className="icon-cancel-circle"
                          title="Remove Part Number"
                          onClick={() => {
                            handleRemovePartNo(row);
                          }}
                        ></span>
                      )
                    ) : (
                      ''
                    );
                  }
                : val.selector === 'serialNo'
                ? function displayCell(row, tableIndex) {
                    return (
                      <div className="display-title custom-overflow">
                        {((pageNumber ?? 1) - 1) * (pageRowCount ?? 10) +
                          (tableIndex + 1)}
                      </div>
                    );
                  }
                : '',
          };
        }) ?? [];
      return (
        <Input
          dataTablePersistHead
          dataTableSearchDisable
          formType={
            parameter === 'view'
              ? tableData
              : {
                  ...tableData,
                  value: [
                    ...(tableData?.value ?? []),
                    {
                      partNo: null,
                      addButton: true,
                    },
                  ],
                }
          }
          dataTableColumn={tableColumns}
        />
      );
    };

    return (
      <div
        style={{
          fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
        }}
      >
        {tableData ? (
          <div>
            <div>{handlePartNoRender(tableData)}</div>
          </div>
        ) : (
          ''
        )}
      </div>
    );
  }
);

export default ECRECOPartNoSection;
