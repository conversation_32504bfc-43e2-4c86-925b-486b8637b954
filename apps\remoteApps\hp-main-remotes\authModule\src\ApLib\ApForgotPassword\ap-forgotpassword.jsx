/* eslint-disable @nx/enforce-module-boundaries */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// import { adminActions, alertActions } from '@hp/mainstore/_actions';
import {
  adminConstants,
  forgotPasswordSaveFunc,
  showDialog,
} from '@hp/mainstore';
// import { Button, LoginInput } from '@hp/components';
import { sharedAsset } from '@hp/assets';
import { Link } from 'react-router-dom';
import { Button, LoginInput } from '@hp/components';

const MainForgotPassword = () => {
  const dispatch = useDispatch();

  const { forgotPasswordResponse } = useSelector((store) => store.admin);

  const [showMsg, setShowMsg] = useState(false);
  const [buttonDisable, setButtonDisable] = useState(false);
  const [message, setMessage] = useState({ type: '', response: '' });
  const [isError, setIsError] = useState({ userName: false, email: false });
  const [state, setState] = useState({
    userName: '',
    email: '',
  });

  useEffect(() => {
    return () => {
      let clrNotification = null;
      dispatch({
        type: adminConstants.FORGOT_PASSWORD_SUCCESS,
        clrNotification,
      });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, [dispatch]);

  useEffect(() => {
    if (showMsg) {
      const clearTimer = setTimeout(() => {
        setShowMsg(false);
        setMessage({ type: '', response: '' });
        handleClearTimeout();
      }, 3200);
      const handleClearTimeout = () => {
        clearTimeout(clearTimer);
      };
    }
  }, [showMsg]);

  useEffect(() => {
    if (forgotPasswordResponse?.value) {
      setMessage({
        type: forgotPasswordResponse?.value?.type,
        response: forgotPasswordResponse.value?.text,
      });
      setShowMsg(true);
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      forgotPasswordResponse?.value?.type === 'success'
        ? setButtonDisable(true)
        : '';
    }
  }, [forgotPasswordResponse]);

  const onChangeHandler = (e) => {
    setState({
      ...state,
      [e.target.name]: e.target.value,
    });
  };

  const onSubmitClick = (e) => {
    e.preventDefault();
    let userName = false;
    let email = false;
    var validRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
    if (state.userName?.trim().length > 0) userName = true;
    if (state.email?.trim().match(validRegex)) email = true;
    if (userName && email) dispatch(forgotPasswordSaveFunc(state));
    setIsError({ userName: !userName, email: !email });
  };

  return (
    <div className="login-page-wrap">
      <div
        className="login-form-wrap flex-column"
        style={{ margin: 'auto', width: '600px' }}
      >
        <a
          rel="noopener noreferrer"
          target="_blank"
          href="http://pinnacle-solutionsllc.com/"
          className="client-logo-wrap"
        >
          <img
            style={{ width: 210 + 'px', height: 50 + 'px' }}
            src={sharedAsset.Login.companyLogo}
            alt="Pinnacle Solutions LLC"
          />
          {/* <img
            style={{ width: 80 + 'px', height: 50 + 'px' }}
            src={sharedAsset.Login.clientLogo}
            alt="Client Logo"
          /> */}
        </a>

        <form
          className="form-wrap"
          style={{
            border: '1px solid #000',
            borderRadius: '5px',
            padding: '60px 50px 60px',
            margin: 'auto',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <h1 style={{ marginBottom: '80px' }}>Forgot Password</h1>
          <LoginInput
            label="User Name"
            type="text"
            placeholder="user name"
            name="userName"
            onChange={(e) => onChangeHandler(e)}
            error={{
              flag: isError.userName,
              message: 'Please enter user name correctly',
            }}
          />

          <LoginInput
            label="Email"
            type="email"
            placeholder="email"
            name="email"
            onChange={(e) => onChangeHandler(e)}
            error={{
              flag: isError.email,
              message: 'Please enter email correctly',
            }}
          />
          <div style={{ paddingTop: '40px' }}></div>
          <Button onClick={onSubmitClick} disabled={buttonDisable}>
            Submit
          </Button>
          <Link className="forgotPassword" to="/">
            Back to Home
          </Link>
          {showMsg ? (
            <div
              style={{
                fontSize: '12px',
                textAlign: 'center',
                lineHeight: '16px',
                whiteWpace: 'pre-line',
                color: '#fff',
                background: message.type === 'error' ? '#c74646' : '#7bb216',
                width: '100%',
                marginTop: '40px',
                padding: '10px 20px',
                boxShadow: '0 0 3px rgb(32 32 42 / 50%)',
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              <span>{message.response}</span>
              <i
                className="icon-close"
                style={{ marginRight: '0px' }}
                onClick={() => setShowMsg(false)}
              ></i>
            </div>
          ) : (
            ''
          )}
        </form>

        {/* <div className="login-footer">
          <a
            href="http://pinnacle-solutionsllc.com/"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src={sharedAsset.Login.companyLogo}
              alt="Pinnacle Solutions LLC"
            />
          </a>
        </div> */}
      </div>
    </div>
  );
};
export { MainForgotPassword };
