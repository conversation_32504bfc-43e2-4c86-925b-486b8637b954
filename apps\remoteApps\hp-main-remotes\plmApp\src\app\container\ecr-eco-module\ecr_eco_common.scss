@import '@hp/styles/variables.scss';

// .fixed-width-table {
//   .styledDatatable .rdt_Table {
//     max-width: 1230px;
//   }
// }

// div.eco-related-docs-container {
//   display: flex;

//   div.eco-related-docs-item-left {
//     position: relative;
//     width: 40%;
//     margin-right: 20px;
//     .card {
//       height: 100% !important;
//     }
//   }
//   div.eco-related-docs-item-right {
//     position: relative;
//     width: 60%;
//   }
// }
// .ecr-eco-reject-reason {
//   textarea {
//     height: 120px;
//   }
// }

//antd style
.ecr-eco-main-container {
  .ant-tree
    .ant-tree-treenode:not(.ant-tree-treenode-disabled).filter-node
    .ant-tree-title {
    color: inherit;
  }
  .ant-tree .ant-tree-treenode .ant-tree-title {
    font-size: 13px;
  }

  //eco settings module
  .ecr-eco-no-affected-items {
    font-family: 'Roboto';
  }

  .ecr-eco-settings {
    display: flex;
    flex-direction: row;
    align-items: center;
    & > :first-child {
      margin-right: 30px;
      width: 25%;
    }

    & > :nth-child(2) {
      margin-top: 10px;
    }
  }
  .ecr-process-flow {
    .process-flow-wrapper {
      max-height: 520px;
      // .react-flow {
      //   .react-flow__renderer {
      //     max-height: 540px;
      //     .react-flow__pane {
      //       max-height: 540px;
      //     }
      //   }
      // }
    }
  }

  //eco styles
  .ecr-eco-docs-container {
    overflow: hidden;
    background-color: white;
    .ecr-eco-view-icon {
      position: absolute;
      right: 0;
      width: 30px;
      height: 30px;
      margin: 5px;
      font-size: 25px;
      span {
        cursor: pointer;
      }
    }

    .ecr-eco-section-hide {
      .ecr-eco-minimized-title {
        position: relative;
        bottom: 35px;
        margin: 0px 40px 0px 20px;
        // font-size: 12px;
        // font-weight: 500;
        // line-height: 16px;
        // color: $text_color;
        // letter-spacing: -0.02em;
        // border-bottom: 1px solid rgba($text_color_rgb, 0.1);
      }
      transition: 500ms;
      max-height: 50px;
      overflow: hidden;
      padding-top: 50px;
    }

    .ecr-eco-section-show {
      transition: 500ms;
      max-height: 1200px;
      overflow: visible;
      padding-top: 0px;
    }
    p.ecr-eco-no-affected-items {
      text-align: center;
    }
  }
  .boxed {
    padding: 20px;
    margin-right: 20px;
    background: #fff;
    transition: 300ms;
    box-shadow: 0 1px 3px #c4bebe;

    &.low-contrast {
      background-color: #fffff0;
    }

    &.disable-form,
    &.low-contrast.disable-form {
      background-color: #fff;
    }

    &:nth-child(2n + 0) {
      margin-right: 0;
    }
  }
}

//header style code

.ecr-eco-header {
  pointer-events: none;
  border-radius: 5px;
  background-color: #fff;
  padding: 9px 18px;
  border: $grey_color;
  position: sticky;
  z-index: 99;

  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    font-size: 12px;
    color: #000;
    font-weight: 400;
  }
  // .ecr-eco-header-height-control {
  //   &.short {
  //     transition: 2000ms;
  //     max-height: 20px;
  //     overflow-y: hidden;
  //   }
  //   &.long {
  //     transition: 2000ms;
  //     max-height: 60px;
  //     overflow-y: hidden;
  //   }
  // }
  overflow: hidden;
  max-height: 3rem;
  // transition: max-height 3s ease;
  .ecr-eco-header-item {
    pointer-events: auto;
    white-space: nowrap;
    // max-height: 1.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    font-weight: 400;
    font-size: 14px;
    color: rgb(0, 0, 0);
    // transition: max-height 0.3s ease, white-space 0.3s ease;
  }
  .ecr-eco-header-item:hover {
    white-space: normal;
  }
  &:has(.ecr-eco-header-item:hover) {
    max-height: 300px;
    transition: max-height 3s ease;
  }
}

.ecr-eco-approver-container {
  max-height: 600px;
  min-height: 250px;

  .name-tag {
    font-size: 12px;
    position: absolute;
    right: 12%;
    top: 33px;
    white-space: nowrap;
    overflow: hidden;
    max-width: 160px;
  }

  .close-btn {
    position: absolute;
    right: -15px;
    top: 13px;
    font-size: 12px;
    cursor: pointer;
  }
}
