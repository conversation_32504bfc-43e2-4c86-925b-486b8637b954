/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> <PERSON><PERSON>tian
 * @email <EMAIL>
 * @create date 16-09-2022 10:40:13
 * @modify date 2024-08-01 15:19:46
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
// import { Input, ButtonCommon, CheckBoxInput } from '@hp/components';

import { useDispatch, useSelector } from 'react-redux';

import { settingsConstants } from '@hp/mainstore';
import { CheckBoxInput } from '@hp/components';
import { ButtonCommon, Input } from '@hp/components';
import { showDialog, resetlogoutUserSession } from '@hp/mainstore';
import { getUserSessionFormDetails, logoutUserSession } from '@hp/mainstore';

const UserSession = () => {
  const dispatch = useDispatch();
  const { userSessionDetails, userSessionDetailsResponse } = useSelector(
    (store) => store.settings
  );

  const [formDetails, setFormDetails] = useState([]);
  const [selectedClient, setSelectedClient] = useState(0);
  const [selectedUser, setSelectedUser] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [sessionIdList, setSessionIdList] = useState([]);

  // Clear Notification on exit
  useEffect(() => {
    return () => {
      dispatch(resetlogoutUserSession());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (userSessionDetails && userSessionDetails.value) {
      setFormDetails(userSessionDetails.value);
      setTableData(
        userSessionDetails.value.filter(
          (val) => val.uniqueKey === 'dataTable'
        )[0].value
      );
    }
  }, [userSessionDetails]);

  useEffect(() => {
    if (userSessionDetailsResponse) {
      funcToSetResponseMessage('success', userSessionDetailsResponse.value);
    }
    getUserSessionDetails(selectedClient, selectedUser);
  }, [userSessionDetailsResponse]);

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    // let showPopup = true;
    // let canClose = true;
    // let autoHide = true;
    dispatch(
      showDialog({
        showPopup: true,
        type: type,
        responseMessage: resMessage,
        canClose: true,
        autoHide: true,
      })
    );
  };

  // Fetch User Session Form
  const getUserSessionDetails = (clientId, userId) => {
    dispatch(getUserSessionFormDetails({ clientId: clientId, userId: userId }));
  };

  const handleLogout = () => {
    if (sessionIdList.length > 0) {
      let sessionPayload = {
        sessionList: sessionIdList,
        logoutType: 'BATCH_SESSION',
      };
      dispatch(logoutUserSession(sessionPayload));
    }
  };

  const functionsName = {
    handleLogout,
  };

  const handleClientSelect = (event, uniqueKey) => {
    getUserSessionDetails(event.target.value, 0);
    setSelectedClient(event.target.value);
  };

  const handleUserSelect = (event, uniqueKey) => {
    getUserSessionDetails(selectedClient, event.target.value);
    setSelectedUser(event.target.value);
  };

  const onChangeHandlingFunctions = {
    handleClientSelect,
    handleUserSelect,
  };

  // const handleFilterData = (filterData) => {
  //   let tempArray = cloneDeep(formDetails);
  //   tempArray.map((element) =>
  //     element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
  //   );
  //   setFormDetails(tempArray);
  // };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val, idx) => {
                return {
                  selector: val.selector,
                  width: val.displayWidth,
                  name: val.displayName,
                  cell:
                    val.selector === 'checkBox'
                      ? function displayCell(row) {
                          return (
                            <CheckBoxInput
                              checked={sessionIdList.includes(row.sessionId)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSessionIdList((prev) => [
                                    ...prev,
                                    row.sessionId,
                                  ]);
                                } else {
                                  setSessionIdList((prev) =>
                                    prev.filter(
                                      (item) => item !== row.sessionId
                                    )
                                  );
                                }
                              }}
                              key={idx}
                            />
                          );
                        }
                      : '',
                };
              });
            return (
              <Input key={index} formType={element} dataTableColumn={columns} />
            );
          } else if (element.uniqueKey === 'clientName') {
            return (
              <div className="three-col-layout" key={index}>
                <div style={{ width: 300 }}>
                  <Input
                    formType={element}
                    isEditable="notShowing"
                    onChangeHandler={(element, event) => {
                      onChangeHandlingFunctions[element.onChangeFunction](
                        event,
                        element.uniqueKey
                      );
                    }}
                    key={index}
                  />
                </div>
                <div style={{ width: 300 }}>
                  {data.map(
                    (element, id) =>
                      element.uniqueKey === 'userName' && (
                        <Input
                          formType={element}
                          isEditable="notShowing"
                          onChangeHandler={(element, event) => {
                            onChangeHandlingFunctions[element.onChangeFunction](
                              event,
                              element.uniqueKey
                            );
                          }}
                          key={id}
                        />
                      )
                  )}
                </div>
              </div>
            );
          } else if (element.uniqueKey === 'search') {
            return;
          } else if (element.type === 'PageTitle') {
            return (
              <Input key={index} formType={element} isEditable="notShowing" />
            );
          }
        })
      : null;
  };

  return (
    <>
      <div className="">
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
      {sessionIdList.length > 0 ? (
        <ButtonCommon functionsName={functionsName} />
      ) : (
        ''
      )}
    </>
  );
};
export { UserSession };
