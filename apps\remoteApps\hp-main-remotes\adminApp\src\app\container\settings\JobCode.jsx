/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 24-03-2022 12:10:13
 * @modify date 25-03-2022 15:40:20
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';

import { AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { settingsConstants } from '@hp/mainstore';
import {
  dataTableServiceProvider,
  formValidationUtil,
  Input,
  useConfirm,
} from '@hp/components';
import { Button } from '@hp/components';
import {
  addJobCodeFormDetails,
  deleteJobCodeFormDetails,
  editJobCodeFormDetails,
  getJobCodeFilteredList,
  getJobCodeFormDetails,
  showDialog,
  jobCodeFormDetailsResponseClear,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';
const JobCode = () => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { jobCodeFormDetails, jobCodeFormDetailsResponse, jobCodeList } =
    useSelector((store) => store.settings);

  let user = globalutils.getDataFromStorage('all');
  const clientIdVal = user.clientId;
  const [clientId, setClientId] = useState(clientIdVal);
  const [formDetails, setFormDetails] = useState([]);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [selected, setSelected] = useState({});
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [notify, setnotify] = useState(null);
  const [search, setSearch] = useState('');
  const [searchInputOptions, setSearchInputOptions] = useState({});

  // Fetch JOb Code details form
  const getJobCodeFormDetailsFn = (clientId) => {
    dispatch(getJobCodeFormDetails(clientId));
  };

  useEffect(() => {
    if (search.length > 0) {
      const timeout = setTimeout(() => {
        dispatch(getJobCodeFilteredList(search));
      }, 250);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [search]);

  useEffect(() => {
    if (jobCodeList && jobCodeList.value) {
      setSearchInputOptions({ comboBoxOptions: jobCodeList.value });
    }
  }, [jobCodeList]);

  useEffect(() => {
    if (jobCodeFormDetails && jobCodeFormDetails.value) {
      setFormDetails(jobCodeFormDetails.value);
      setTableData(
        jobCodeFormDetails.value.find((val) => val.uniqueKey === 'dataTable')
          .value
      );
    }
  }, [jobCodeFormDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch(jobCodeFormDetailsResponseClear(removeNotification));
      // dispatch({
      //   type: settingsConstants.EDIT_JOB_CODE_FORM_SUCCESS,
      //   removeNotification,
      // });
      // dispatch({
      //   type: settingsConstants.DELETE_JOB_CODE_FORM_SUCCESS,
      //   removeNotification,
      // });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (
      jobCodeFormDetailsResponse &&
      jobCodeFormDetailsResponse?.value &&
      jobCodeFormDetailsResponse?.value !== undefined
    ) {
      funcToSetResponseMessage(
        jobCodeFormDetailsResponse?.value.type,
        jobCodeFormDetailsResponse?.value.text
      );
    }
    getJobCodeFormDetailsFn(clientId);
  }, [jobCodeFormDetailsResponse]);

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let jobNameValid = true;
    let isValid = formValidationUtil.validateForm(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'name') {
            tempArray.map((item) => {
              if (item.uniqueKey === 'dataTable') {
                item.value.map((val) => {
                  if (val.jobCodeName === element.value) {
                    if (selected.jobCodeName === element.value) {
                      return;
                    } else {
                      jobNameValid = false;
                      funcToSetResMessageInModal(
                        'error',
                        'Job Name already exists'
                      );
                    }
                  }
                });
              }
            });
          }
        });
    });
    if (isValid.validSuccess && jobNameValid) {
      edit
        ? dispatch(
            editJobCodeFormDetails({
              formDetails: formDetails,
              jobCodeId: selected.jobCodeId,
            })
          )
        : dispatch(addJobCodeFormDetails(formDetails));
      setShowModal(false);
    } else {
      let tempArray = cloneDeep(isValid.formList);
      setFormDetails(tempArray);
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    setEdit(false);
    setSelected({});
    setConditionalRowStyles([]);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.disableFlag = 'F';
          element.value = '';
          element.errorFlag = false;
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setEdit(true);
    setSelected(obj);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          entry.uniqueKey === 'name' ? (entry.value = obj.jobCodeName) : '';
          entry.uniqueKey === 'code' ? (entry.value = obj.jobCode) : '';
          entry.uniqueKey === 'description'
            ? (entry.value = obj.jobCodeDesc)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.jobCodeId,
      'jobCodeId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.jobCodeId
      ? handleConfirm(obj.jobCodeId)
      : funcToSetResponseMessage('error', 'Please Select a Job Code to Delete');
  };

  //Function to confirm delete
  const handleConfirm = async (jobCodeId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteJobCodeFormDetails(jobCodeId));
      setDisable(false);
      setShowModal(false);
      setSelected({});
    }
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event.target.value;
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Select a value in the Client select combo-box
  const handleClientSelect = (event) => {
    getJobCodeFormDetailsFn(event.target.value);
    setClientId(event.target.value);
    setDisable(false);
    setConditionalRowStyles([]);
  };

  //Search and Select a value in the Job Code Field
  const handleSearchInput = (event, uniqueKey, key) => {
    let tempArray = cloneDeep(formDetails);
    if (key === 'not-present') {
      tempArray.map((element) => {
        if (element.uniqueKey === 'pageSubtitle') {
          element.formSubDetailsInternalDTOList.map((item) => {
            if (item.uniqueKey === uniqueKey) {
              item.value = event.value;
            }
          });
        }
      });
      setFormDetails(tempArray);
    } else {
      setSearch(event.target.value);
      tempArray.map((element) => {
        if (element.uniqueKey === 'pageSubtitle') {
          element.formSubDetailsInternalDTOList.map((item) => {
            if (item.uniqueKey === uniqueKey) {
              item.value = event.target.value;
            }
          });
        }
      });
      setFormDetails(tempArray);
    }
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleClientSelect,
    handleSearchInput,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelected(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'T';
          entry.uniqueKey === 'name' ? (entry.value = obj.jobCodeName) : '';
          entry.uniqueKey === 'code' ? (entry.value = obj.jobCode) : '';
          entry.uniqueKey === 'description'
            ? (entry.value = obj.jobCodeDesc)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.jobCodeId,
      'jobCodeId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  // const handleFilterData = (filterData) => {
  //   let tempArray = cloneDeep(formDetails);
  //   tempArray.map((element) =>
  //     element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
  //   );
  //   setFormDetails(tempArray);
  // };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'pageSubtitle') {
            while (showModal) {
              return (
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                >
                  <div
                    onClick={() => {
                      handleCancel();
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div className="boxed" style={{ margin: 0 }}>
                    <Input key={index} formType={element} />
                    <div>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList
                      )}
                    </div>
                    {showButtons && (
                      <div>
                        <Button
                          onClick={() => handleSave()}
                          className="default mr20"
                        >
                          Save
                        </Button>
                        <Button
                          onClick={() => handleCancel()}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-bar',
                        'type-' + notify.type,
                      ].join(' ')}
                      style={{ position: 'sticky !important', width: 90 + '%' }}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'add_button') {
            return (
              <div style={{ float: 'right', marginBottom: 10 + 'px' }}>
                <Button
                  onClick={() => {
                    handleCancel();
                    setShowModal(true);
                    setShowButtons(true);
                  }}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-add-button "> </i>Add
                </Button>
              </div>
            );
          } else if (element.uniqueKey === 'clientSelect') {
            return (
              <div className="three-col-layout">
                {/* <div style={{ width: '200px', marginTop: '25px' }}>
                  <Input
                    formType={data.find((item) => item.uniqueKey === 'search')}
                    isEditable="notShowing"
                    tableListData={tableData}
                    getFilteredData={(filterData) =>
                      handleFilterData(filterData)
                    }
                  />
                </div> */}
                {clientIdVal === 1 && (
                  <div className="col">
                    <Input
                      key={index}
                      formType={element}
                      isEditable={'notShowing'}
                      onChangeHandler={(element, event) => {
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey
                        );
                      }}
                    />
                  </div>
                )}
              </div>
            );
          } else if (element.uniqueKey === 'search') {
            return;
          } else if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else {
            return (
              <Input
                key={index}
                disabledFlag={disable}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event, key) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    key
                  );
                }}
                searchOptions={searchInputOptions}
              />
            );
          }
        })
      : null;
  };

  return (
    <div className="">
      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}
    </div>
  );
};
export { JobCode };
