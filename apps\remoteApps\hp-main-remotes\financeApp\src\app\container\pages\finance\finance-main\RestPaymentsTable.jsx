/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-case-declarations */
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { Button, CheckBoxInput } from '@hp/components';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'antd';

const RestPaymentsTable = ({
  formData,
  MsgShow,
  onCloseModal,
  onTriggerUpdate,
}) => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(1);
  const [toolTipEnable, setToolTipEnable] = useState(false);
  const tooltipTimer = useRef(null);

  useEffect(() => {
    return () => {
      setSelectedRows([]);
    };
  }, []);

  useEffect(() => {
    if (MsgShow && formData?.defaultValue) {
      setToolTipEnable(true);
      tooltipTimer.current = setTimeout(() => {
        setToolTipEnable(false);
      }, 3000);
    }

    return () => {
      if (tooltipTimer.current) {
        clearTimeout(tooltipTimer.current);
      }
    };
  }, []);

  const handleSave = () => {
    if (!selectedRows.length) return;

    const paymentId = selectedRows[0].intInTxnDetailsId;

    if (onTriggerUpdate) {
      onTriggerUpdate(paymentId);
    }

    if (onCloseModal) {
      onCloseModal();
    }

    setSelectedRows([]);
  };

  const DisplayTitle = (row, key) => {
    const value = row[key];

    if (value === null || value === undefined) {
      return <div className="display-title custom-overflow"></div>;
    }

    if (key === 'amount' && typeof value === 'number') {
      const formattedValue = value.toLocaleString();
      return (
        <Tooltip
          classNames={{ root: 'ant-tooltip-container' }}
          title={formattedValue}
        >
          <div
            className="display-title custom-overflow"
            style={{ textAlign: 'right', width: '100%' }}
          >
            {formattedValue}
          </div>
        </Tooltip>
      );
    }

    return (
      <Tooltip title={value}>
        <div className="display-title custom-overflow">{value}</div>
      </Tooltip>
    );
  };

  const calculateColumnWidth = (field, data) => {
    const selector = field.selector;
    const headerText = field.displayName || '';
    if (field.displayWidth && field.displayWidth !== '')
      return field.displayWidth;

    switch (selector) {
      case 'serialNo':
        return '80px';

      case 'accountName':
        return '210px';

      case 'bankName':
        return '170px';

      case 'currency':
        return '80px';

      case 'dueDate':
        return '100px';

      case 'amount':
        return '100px';

      default:
        // Dynamic calculation for any other fields
        const contentLengths = data.map((row) => {
          const value = row[selector];
          if (value === null || value === undefined) return 0;
          return String(value).length;
        });

        const maxContentLength = Math.max(headerText.length, ...contentLengths);

        const baseWidth = maxContentLength * 8 + 40; // Increased padding
        const minWidth = 100;
        const maxWidth = 300;

        return Math.min(Math.max(baseWidth, minWidth), maxWidth) + 'px';
    }
  };

  const columns = useMemo(() => {
    const data = formData?.value || [];
    const baseColumns = (formData?.formSubDetailsInternalDTOList || []).map(
      (field) => ({
        name: field.displayName || '',
        selector: field.selector || '',
        width: calculateColumnWidth(field, data),
        sortable: true,
        cell: (row) => DisplayTitle(row, field.selector),
      })
    );

    const receivingAcctIndex = baseColumns.findIndex(
      (col) => col.selector === 'accountName'
    );
    const hashIndex = baseColumns.findIndex(
      (col) => col.selector === 'serialNo'
    );
    const insertIndex =
      receivingAcctIndex > -1 ? receivingAcctIndex : hashIndex + 1;

    const checkboxColumn = {
      name: '',
      cell: (row) => {
        const isSelected =
          selectedRows[0]?.intInTxnDetailsId === row.intInTxnDetailsId;
        return (
          <CheckBoxInput
            checked={isSelected}
            onChange={(e) => handleCheckboxChange(e, row)}
          />
        );
      },
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
      width: '50px',
    };

    const finalColumns = [...baseColumns];
    finalColumns.splice(insertIndex, 0, checkboxColumn);
    return finalColumns;
  }, [formData, selectedRows]);

  const handleCheckboxChange = (e, row) => {
    const isChecked = e.target.checked;
    setSelectedRows(isChecked ? [row] : []);
  };

  const handlePageChange = (page) => setOffset(page);
  const handlePerRowsChange = (newLimit, page) => {
    setLimit(newLimit);
    setOffset(page);
  };

  return (
    <div className="rest-payments-wrapper modal-flex-container">
      <h2 className="page-title">Manual Reconciliation</h2>
      {MsgShow && formData?.defaultValue && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            position: 'relative',
          }}
        >
          <div
            className="mr8 fr"
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'absolute',
              bottom: '20px',
              right: 0,
            }}
          >
            <Tooltip
              overlayClassName="tooltip-reconcilliation-custom"
              title={
                <div
                  style={{
                    color: '#20202a',
                    backgroundColor: '#FFFF99',
                    fontWeight: 'normal',
                    fontSize: '13px',
                    padding: '5px 8px',
                    whiteSpace: 'pre-wrap',
                    maxWidth: '250px',
                    wordBreak: 'break-word', // ✅ avoid overflow
                    margin: 0,
                    WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
                    borderRadius: '2px',
                  }}
                >
                  {formData?.defaultValue || ''}
                </div>
              }
              open={toolTipEnable ? true : undefined} // Show for 3s initially
              placement="bottom"
              overlayStyle={{
                zIndex: 1100,
                maxWidth: '250px',
                overflowWrap: 'break-word',
              }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode} // ✅ Keep inside modal
            >
              <span
                className="icon-check-alert-light"
                style={{
                  cursor: 'pointer',
                  fontSize: '16px',
                }}
                onMouseEnter={() => setToolTipEnable(true)}
                onMouseLeave={() => setToolTipEnable(false)}
              ></span>
            </Tooltip>
          </div>
        </div>
      )}

      <div
        className="data-table-scrollable styledDatatable"
        style={{
          height: '400px',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <DataTable
          persistTableHead
          highlightOnHover
          noHeader
          striped
          pagination
          paginationPerPage={limit}
          paginationDefaultPage={offset}
          paginationTotalRows={formData?.value.length || 0}
          onChangeRowsPerPage={handlePerRowsChange}
          onChangePage={handlePageChange}
          data={formData?.value || []}
          columns={columns}
          customStyles={{
            tableWrapper: {
              style: {
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
              },
            },
            table: {
              style: {
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
              },
            },
            headCells: {
              style: {
                position: 'sticky',
                top: 0,
                zIndex: 10,
                backgroundColor: '#f5f5f5',
                fontWeight: 600,
                fontSize: '14px',
                padding: '8px 12px',
              },
            },
            rows: {
              style: {
                minHeight: '48px',
              },
            },
            cells: {
              style: {
                fontSize: '14px',
                padding: '8px 12px',
                whiteSpace: 'nowrap',
              },
            },
            pagination: {
              style: {
                position: 'sticky',
                bottom: 0,
                backgroundColor: '#fff',
                zIndex: 9,
                borderTop: '1px solid #eee',
              },
            },
          }}
        />
      </div>

      <div className="modal-footer">
        <Button onClick={handleSave} disabled={selectedRows.length !== 1}>
          Update
        </Button>
      </div>
    </div>
  );
};

export default RestPaymentsTable;
