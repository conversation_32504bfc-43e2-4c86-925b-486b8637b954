/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable no-dupe-else-if */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR>
 * @email <EMAIL>
 */
import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Input, Button, formValidationUtil } from '@hp/components';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { CommonSpinner, useConfirm, globalutils } from '@hp/components';
import { dataTableServiceProvider } from '@hp/components';
import {
  bbForms,
  MpnDistributorClients,
  DistributorUploadMPN,
  addDistributor,
  editDistributor,
  bbGetState,
  bbDistributorDelete,
} from '@hp/mainstore';
const Distributor = () => {
  const [formDetails, setFormDetails] = useState([]);
  const [clientId, setclientId] = useState();
  const [showUploadModal, setshowUploadModal] = useState(false);
  const [showAddModal, setshowAddModal] = useState(false);
  const [distributorId, setDistributorId] = useState();
  const [action, setaction] = useState('');
  const [notify, setnotify] = useState(null);
  const [searchBox, setSearchBox] = useState();
  const [TableListing, setTableList] = useState();
  const [tempObj, setTempObj] = useState();
  const [tempFile, setTempFile] = useState();
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };
  const { confirm } = useConfirm();
  const dispatch = useDispatch();

  const {
    bbFormDetails,
    loading,
    MPNDistributorClient,
    distributorMPNSuccess,
    distributorAddResponse,
    distributorEditResponse,
    distributorDeleteResponse,
    stateData,
  } = useSelector((store) => store.bb);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: Component did mount First call of the component.
     */

    dispatch(bbForms('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
    };
  }, []);

  useEffect(() => {
    setnotify(null);
  }, [showAddModal]);

  useEffect(() => {
    dispatch(bbForms('subscribe'));
  }, [
    distributorAddResponse,
    distributorEditResponse,
    distributorDeleteResponse,
  ]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: form client response.
     */
    if (bbFormDetails && bbFormDetails !== undefined && bbFormDetails.value) {
      setFormDetails(bbFormDetails.value);
      bbFormDetails.value.map((element) => {
        if (element.uniqueKey === 'fileTable') {
          setTableList(element.value);
          setSearchBox(element.value);
        }
      });
    }
  }, [bbFormDetails]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;
      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Component Refresh.
     */
    if (
      (distributorAddResponse &&
        distributorAddResponse.httpStatus &&
        distributorAddResponse.httpStatus === 'OK') ||
      (distributorEditResponse &&
        distributorEditResponse.httpStatus &&
        distributorEditResponse.httpStatus === 'OK') ||
      (distributorDeleteResponse &&
        distributorDeleteResponse.httpStatus &&
        distributorDeleteResponse.httpStatus === 'OK')
    ) {
      var timerId = setTimeout(() => {
        setshowAddModal(false);
      }, 3000);
      dispatch(bbForms('subscribe'));

      return () => {
        clearTimeout(timerId);
      };
    }
  }, [
    distributorAddResponse,
    distributorEditResponse,
    distributorDeleteResponse,
  ]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Component Refresh.
     */
    if (
      distributorMPNSuccess &&
      distributorMPNSuccess.httpStatus &&
      distributorMPNSuccess.httpStatus === 'OK'
    ) {
      var timerId = setTimeout(() => {
        setshowUploadModal(false);
      }, 3000);
      dispatch(bbForms('subscribe'));

      return () => {
        clearTimeout(timerId);
      };
    }
  }, [distributorMPNSuccess]);

  useEffect(() => {
    if (
      distributorMPNSuccess &&
      distributorMPNSuccess.httpStatus &&
      distributorMPNSuccess.httpStatus === 'OK'
    ) {
      funcToSetResMessageInModal('success', distributorMPNSuccess.value);
    } else if (
      distributorMPNSuccess &&
      distributorMPNSuccess.response &&
      distributorMPNSuccess.response.data &&
      distributorMPNSuccess.response.data.httpStatus
    ) {
      sameFileUpload(distributorMPNSuccess.response.data);
    }
  }, [distributorMPNSuccess]);

  useEffect(() => {
    if (distributorAddResponse) {
      if (distributorAddResponse.httpStatus === 'OK')
        funcToSetResMessageInModal('success', distributorAddResponse.value);
      else if (
        distributorAddResponse &&
        distributorAddResponse.httpStatus !== 'OK'
      ) {
        funcToSetResMessageInModal('error', 'Something went wrong');
      }
    } else if (distributorEditResponse) {
      if (distributorEditResponse.httpStatus === 'OK')
        funcToSetResMessageInModal('success', distributorEditResponse.value);
      else if (
        distributorEditResponse &&
        distributorEditResponse.httpStatus !== 'OK'
      ) {
        funcToSetResMessageInModal('error', 'Something went wrong');
      }
    }
  }, [distributorAddResponse, distributorEditResponse]);

  useEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: form response.
     */
    if (
      MPNDistributorClient &&
      MPNDistributorClient !== undefined &&
      MPNDistributorClient.value
    ) {
      let formControl = cloneDeep(formDetails);
      formControl &&
        formControl !== undefined &&
        formControl.map((element) => {
          if (element.uniqueKey === 'fileTable') {
            element.value = MPNDistributorClient.value;
          }
        });
      setFormDetails(formControl);
    }
  }, [MPNDistributorClient]);

  useEffect(() => {
    dispatch(MpnDistributorClients(clientId));
  }, [clientId, distributorMPNSuccess]);

  useEffect(() => {
    if (stateData && stateData !== undefined) {
      let tempArray = cloneDeep(formDetails);
      tempArray.map((element) => {
        element.uniqueKey === 'add-distributor' &&
          element.formSubDetailsInternalDTOList.map((entry) => {
            entry.disableFlag = 'F';
            switch (entry.uniqueKey) {
              case 'state':
                entry.comboBoxOptions = stateData.value;
                break;
              default:
                break;
            }
          });
      });
      setFormDetails(tempArray);
    }
  }, [stateData]);

  useEffect(() => {
    if (searchBox && searchBox.length && searchBox !== undefined) {
      const tempArray = cloneDeep(formDetails);
      tempArray
        ? tempArray.map((element) => {
            if (element.uniqueKey === 'fileTable') {
              element.value = searchBox;
            }
            setFormDetails(tempArray);
          })
        : null;
    }
  }, [searchBox]);
  const clientCancel = () => {
    setshowAddModal(false);
  };

  const handleRowClick = (obj) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.serialNo,
      'serialNo'
    );
    setConditionalRowStyles(styleAttribute);
  };

  const handleOnChangeSearch = (type, event) => {
    setFilterString({ filterStr: event.target.value });
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'page-title' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'search') {
            element.value = event.target.value;
          }
        });
    });
    setFormDetails(tempArray);
  };
  const sameFileUpload = async (data) => {
    const isConfirmed = await confirm(data.message);
    if (isConfirmed) {
      dispatch(
        DistributorUploadMPN({
          productFileMasterDTO: tempObj,
          UploadedFile: tempFile,
          reqType: 'subscribe',
          bbClientId: clientId,
          flag: false,
        })
      );
    }
  };
  const addEditDistributor = () => {
    let validatedElement = formValidationUtil.validateForm(formDetails);

    // let validate = formValidationUtil.checkMandatoryField(formDetails);
    let validatedFc = cloneDeep(validatedElement.formList);
    setFormDetails(validatedFc);
    var distributorDetails;
    formDetails.map((element) => {
      if (element.uniqueKey === 'add-distributor') {
        element.formSubDetailsInternalDTOList.map((dForm) => {
          if (
            dForm.uniqueKey === 'country' &&
            dForm.value !== null &&
            typeof dForm.value === 'string'
          ) {
            dForm.value = parseInt(dForm.value);
          }

          if (
            dForm.uniqueKey === 'state' &&
            dForm.value !== null &&
            typeof dForm.value === 'string'
          ) {
            dForm.value = parseInt(dForm.value);
          }
        });

        distributorDetails = element.formSubDetailsInternalDTOList;
      }
    });

    if (validatedElement.validSuccess) {
      if (action === 'edit') {
        dispatch(
          editDistributor({
            formDetails: distributorDetails,
            distributorId: distributorId,
          })
        );
      } else {
        dispatch(addDistributor(distributorDetails));
      }
    }
  };

  const addDistributorFile = async () => {
    setshowAddModal(true);
    setaction('add');
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'add-distributor' &&
        element.formSubDetailsInternalDTOList.map((items) => {
          items.value = '';
        });
    });
    setFormDetails(tempArray);
  };

  const handleDelete = async (client) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(bbDistributorDelete(client.brokerDistributorId));
    }
  };

  const handleEdit = (row) => {
    setaction('edit');
    if (row.countryId) {
      dispatch(bbGetState(row.countryId));
    }

    setDistributorId(row.brokerDistributorId);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'add-distributor' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          switch (entry.uniqueKey) {
            case 'vendor':
              entry.value = row.vendorName;
              break;
            case 'classification':
              entry.value = row.classification;
              break;
            case 'website':
              entry.value = row.websiteAddress;
              break;
            case 'contact_name':
              entry.value = row.contactName;
              break;
            case 'contact_number':
              entry.value = row.contactNumber;
              break;
            case 'description':
              entry.value = row.description;
              break;
            case 'address':
              entry.value = row.address;
              break;
            case 'email':
              entry.value = row.email;
              break;
            case 'country':
              entry.value = row.countryId;
              break;
            case 'state':
              entry.value = row.stateId;
              break;
            case 'designation':
              entry.value = row.designation;
              break;
            default:
              break;
          }
        });
    });
    handleRowClick(row);
    setFormDetails(tempArray);
    setshowAddModal(true);
  };

  const onChangeHandler = (event, element, uniqueKey) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for user input content change.
     */

    let tempArray = cloneDeep(formDetails);
    if (uniqueKey === 'country') {
      dispatch(bbGetState(event.target.value));
    }
    tempArray.map((entry) => {
      entry.uniqueKey === 'add-distributor' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (
            element.uniqueKey === uniqueKey &&
            element.type !== 'SearchInput'
          ) {
            element.value = event.target.value;
          } else if (
            element.uniqueKey === uniqueKey &&
            element.type === 'SearchInput'
          ) {
            element.value = event.value;
          }
        });
    });
    setFormDetails(tempArray);

    // let validatedElement = formValidationUtil.isFormcontrolValidDynamic(
    //   event,
    //   element
    // );
    // let updateFormElement = cloneDeep(formDetails);

    // updateFormElement.map((entry) => {
    //   entry.uniqueKey === 'add-distributor' &&
    //     entry.formSubDetailsInternalDTOList.map((element, elementIndex) => {
    //       if (element.uniqueKey === uniqueKey) {
    //         entry.formSubDetailsInternalDTOList.splice(
    //           elementIndex,
    //           1,
    //           validatedElement
    //         );
    //       }
    //     });
    // });
    // setFormDetails(updateFormElement);
    // seterrorFlag(validatedElement.errorFlag);
  };

  const handlers = {
    onChangeHandler,
    handleRowClick,
    handleOnChangeSearch,
  };

  const fileUpload = (UploadedFile) => {
    const userId = globalutils.getDataFromStorage('userId');
    let obj = {
      clientId: clientId,
      description: UploadedFile.Description,
      fileName: UploadedFile.file.name,
      distributerFileMasterId: 0,
      serialNo: 0,
      uploadedBy: userId,
      uploadedOn: null,
      userId: userId,
    };
    const productFileMasterDTO = JSON.stringify(obj);
    setTempObj(productFileMasterDTO);
    setTempFile(UploadedFile);
    dispatch(
      DistributorUploadMPN({
        productFileMasterDTO: productFileMasterDTO,
        UploadedFile: UploadedFile,
        reqType: 'subscribe',
        bbClientId: clientId,
        flag: true,
      })
    );
  };
  return (
    <>
      <CommonSpinner visible={loading} />
      <div className="">
        {formDetails && formDetails.length > 0
          ? formDetails.map((element, index) => {
              if (element.uniqueKey === 'page-title') {
                return (
                  <React.Fragment key={index}>
                    <Input formType={element} />
                    <div
                      className="flex-row"
                      style={{
                        position: 'relative',
                      }}
                    >
                      {/* <div>
                        {element.formSubDetailsInternalDTOList &&
                          element.formSubDetailsInternalDTOList.length > 0 &&
                          element.formSubDetailsInternalDTOList.map(
                            (element, subIndex) => {
                              return (
                                <Input
                                  key={subIndex}
                                  onChangeHandler={(type, event) =>
                                    handlers[element.onChangeFunction](
                                      type,
                                      event,
                                      'search'
                                    )
                                  }
                                  isEditable="notShowing"
                                  formType={element}
                                />
                              );
                            }
                          )}
                      </div> */}
                      <div
                        style={{
                          marginTop: '10px',
                          position: 'absolute',
                          right: 0,
                          display: 'flex',
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div className="mr20">
                          <Button
                            onClick={() => {
                              setshowUploadModal(true);
                            }}
                            className="small mb8 outline add-button-custom flex-row vam"
                          >
                            <i className="icon-upload" />
                            Upload
                          </Button>
                        </div>

                        <div className="mr20">
                          <Button
                            onClick={() => {
                              addDistributorFile();
                            }}
                            className="small mb8 outline add-button-custom flex-row vam"
                          >
                            <i className="icon-add-button" />
                            Add Distributor
                          </Button>
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                );
              } else if (element.uniqueKey === 'fileTable') {
                let columns =
                  element.formSubDetailsInternalDTOList &&
                  element.formSubDetailsInternalDTOList.map((val) => {
                    return {
                      selector: val.selector,
                      width:
                        val.selector === 'edit-icon' ||
                        val.selector === 'delete-icon'
                          ? '4%'
                          : val.displayWidth,
                      name: val.displayName,
                      cell:
                        val.selector === 'edit-icon'
                          ? function displayCell(row) {
                              return (
                                <div
                                  className="icon-edit-button"
                                  onClick={() => handleEdit(row, 'edit')}
                                ></div>
                              );
                            }
                          : val.selector === 'delete-icon'
                          ? function displayCell(row) {
                              return (
                                <div
                                  className="icon-2-trash"
                                  onClick={() => handleDelete(row)}
                                ></div>
                              );
                            }
                          : '',
                    };
                  });
                return (
                  <Input
                    formType={element}
                    dataTableEventHandler={(e) => handleRowClick(e)}
                    conditionalRowStyles={conditionalRowStyles}
                    dataTableColumn={columns}
                    linkPath={'/'}
                    isEditable={'notShowing'}
                    onsubmitHandler={fileUpload}
                    key={index}
                  />
                );
              } else if (element.uniqueKey === 'disSubtitle') {
                while (showUploadModal) {
                  return (
                    <Modal
                      className="Modal"
                      overlayClassName="ModalOverlay"
                      ariaHideApp={false}
                      isOpen={showUploadModal}
                      key={index}
                    >
                      <div
                        onClick={() => setshowUploadModal(false)}
                        className="modal-close icon-close"
                        style={{ fontSize: 20 + 'px' }}
                      ></div>
                      <div>
                        <Input formType={element} />
                        <div>
                          {element.formSubDetailsInternalDTOList.map(
                            (element, subIndex) => {
                              return (
                                <Input
                                  key={subIndex}
                                  formType={element}
                                  // dataTableEventHandler={(e) => onRowClick(e)}
                                  conditionalRowStyles={conditionalRowStyles}
                                  // dataTableColumn={editDeleteIcon}
                                  disabled={false}
                                  linkPath={'/'}
                                  downloadLink={
                                    'https://dev.highpointsuite.com/inpinn/sample/bb/broker_distributor_sample.xlsx'
                                  }
                                  isEditable={'notShowing'}
                                  onsubmitHandler={fileUpload}
                                  handleCancel={(isClose) =>
                                    setshowUploadModal(isClose)
                                  }
                                />
                              );
                            }
                          )}
                        </div>
                      </div>
                      {notify != null ? (
                        <div
                          className={[
                            'notification-bar',
                            'type-' + notify.type,
                          ].join(' ')}
                          style={{
                            position: 'sticky !important',
                            width: 90 + '%',
                          }}
                        >
                          <i
                            className="icon-close"
                            onClick={() => setnotify(null)}
                          ></i>
                          {notify.resMessage}
                        </div>
                      ) : (
                        ''
                      )}
                    </Modal>
                  );
                }
              } else if (element.uniqueKey === 'add-distributor') {
                if (showAddModal) {
                  return (
                    <Modal
                      className="Modal"
                      overlayClassName="ModalOverlay"
                      ariaHideApp={false}
                      isOpen={showAddModal}
                      key={index}
                    >
                      <div
                        onClick={() => {
                          setshowAddModal(false);
                          formValidationUtil.clear(formDetails);
                        }}
                        className="modal-close icon-close"
                        style={{ fontSize: 20 + 'px' }}
                      ></div>
                      <div>
                        <Input formType={element} />
                        <div className="two-col-layout">
                          {element.formSubDetailsInternalDTOList.map(
                            (element, subIndex) => {
                              return (
                                <Input
                                  key={subIndex}
                                  formType={element}
                                  isEditable={'notShowing'}
                                  onChangeHandler={(element, event) =>
                                    handlers[element.onChangeFunction](
                                      event,
                                      element,
                                      element.uniqueKey,
                                      index
                                    )
                                  }
                                />
                              );
                            }
                          )}
                        </div>
                        <Button
                          onClick={() => addEditDistributor()}
                          className="default mr20"
                          style={{ marginTop: 30 + 'px' }}
                        >
                          Save
                        </Button>
                        <Button
                          onClick={() => {
                            clientCancel();
                            formValidationUtil.clear(formDetails);
                          }}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                      {notify != null ? (
                        <div
                          className={[
                            'notification-bar',
                            'type-' + notify.type,
                          ].join(' ')}
                          style={{
                            position: 'sticky !important',
                            width: 90 + '%',
                          }}
                        >
                          <i
                            className="icon-close"
                            onClick={() => setnotify(null)}
                          ></i>
                          {notify.resMessage}
                        </div>
                      ) : (
                        ''
                      )}
                    </Modal>
                  );
                }
              }
            })
          : null}
      </div>
    </>
  );
};

export { Distributor };
