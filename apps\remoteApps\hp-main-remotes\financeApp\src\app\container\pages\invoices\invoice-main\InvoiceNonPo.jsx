/* eslint-disable eqeqeq */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
import React, {
  useState,
  useEffect,
  useRef,
  useLayoutEffect,
  useCallback,
} from 'react';
import {
  getINVDetailsForAccPay,
  getNonPOApproverEmailTemplate,
  traceEvent,
  showDialog,
  linkCreditNoteWithInvoice,
  getInvoiceList,
  poInvStatusChange,
  extractData,
  submitCreditNote,
  changedInputsAction,
  nonPoInvoiceValidatorAndSubmitForApproval,
  getSuppliersById,
  selectedPoDetails,
  getInvEmailTree,
  emailResetStateField,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';

import {
  AP_file_url,
  AP_invIdConstant,
  AP_USER,
  AP_emailSubmitId,
  AP_onHold,
} from '@hp/constants';
import { accPayConstants, emailConstants } from '@hp/mainstore';
import { EmailModal } from '../invoice-common/EmailModal';
import { InvMatchingComponent } from '../invoice-sub/InvMatchingComponent';
import cloneDeep from 'lodash.clonedeep';
import { Tabs } from 'antd';
import { invoiceService } from '../invoice-common/invoice.service';
import { Empty } from 'antd';
import Docviewer from '../invoice-sub/docviewer';
import { useEffectOnce } from 'react-use';
import { UnlinkAndMatchPO } from '../invoice-common/PO-INV-Modal';
import { LinkInvoiceModal } from '../invoice-common/LinkInvoiceModal/LinkInvoiceModal';
import { InvDatatable } from '../invoice-sub/inv-table/INV-DataTable';
import INVEmailList from '../invoice-sub/INV-Email-List';
import { useAppRouterDom, utils } from '@hp/utils';
import {
  ButtonCommon,
  CommonSpinner,
  DocumentViewer,
  globalutils,
  MultiRowInput,
  TraceEvents,
  useConfirm,
} from '@hp/components';
import { InvPageHeader } from './InvPageHeader';
const InvoiceNonPO = () => {
  const dispatch = useDispatch();

  const { domParameters, navigate } = useAppRouterDom();
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';
  const parameters = domParameters?.parameters || '';
  const [extractionSaveObject, setextractionSaveObject] = useState(null);
  const { confirm } = useConfirm();

  const [getTraceEvents, setTraceEvents] = React.useState([]);
  const [previousAnnoListLength, setPreviousAnnoListLength] = useState(0);

  const [addCommentsValue, setAddCommentsValue] = useState('');

  const [emailModalOpen, setEmailModalOpen] = React.useState(false);
  const [otherChargesFormData, setOtherChargesFormData] = useState();

  const [disabledFlag, setDisabledFlag] = useState(true);

  const [onSaveClicked, setOnSaveClicked] = useState(false);
  const [modelClose, setModelClose] = useState(false);

  const [onSubmittedClick, setOnSubmittedClick] = useState(false);
  const [reactDocViewerProp, setReactDocViewerProp] = useState(null);
  const [extractedCoordsVerification, setextractedCoordsVerification] =
    useState([]);

  const [onSubmitvalidationSuccess, setOnSubmitvalidationSuccess] =
    useState(false);
  const [activeTabKey, setActiveTabKey] = useState('0');

  const [isLoading, setisLoading] = useState(false);
  const [modalLoadng, setModalLoadng] = useState(false);

  const [alertMsgFlag, setAlertMsgFlag] = useState(false);

  const [showInvoiceToggle, setShowInvoiceToggle] = useState(true);

  const [emailTemplateDetails, setEmailTemplate] = useState({
    toAddress: [],
    subject: '',
    content: '',
  });

  const [filePath, setFilePath] = useState('');

  const [invoiceList, setInvoiceList] = useState([]);

  const [rtPoFlag, setRtPoFlag] = useState(false);
  const [invoiceModal, setInvoiceModal] = useState(false);
  const [actionButtons, setActionButtons] = useState([]);
  const [poOrInvResponse, setPoOrInvResponse] = useState([]);
  const [invEmailListPresent, setInvEmailListPresent] = useState(false);
  const [invEmailTreeList, setInvEmailTreeList] = useState([]);

  const {
    accpayINVDetails,
    accPaytraceEvents,
    accpayBreadCrumb,
    invEventTime,
    ApListingOnRowClickData,
    invDetailsEditResponse,
    invDetailsEditError,
    apCachedData,
    nonPoInvApprovalSucess,
    Extraction,
    changedIPRes,
    linkRtPoRes,
    creditNoteSubmitRes,
    invoiceListData,
    linkCreditNoteWithInvRes,
    selectedPoData,
    accpayEntityChange,
    apListingFilteredData,
  } = useSelector((state) => state.accpay);

  const buttonData = useSelector((state) => state.buttons.buttons || null);

  const { innerMenuName } = useSelector((store) => store.menu);
  const { TabPane } = Tabs;

  const [focusedInput, setFocusedInput] = useState('');
  const focusInputRef = useRef();
  focusInputRef.current = focusedInput;

  const { emailTree, nonPoEmailTemplate } = useSelector((store) => store.email);

  const suppId = ApListingOnRowClickData
    ? ApListingOnRowClickData.supplierId
    : '';
  const suppName = ApListingOnRowClickData
    ? ApListingOnRowClickData?.supplierIdAndNameDTO?.supplierName
    : '';
  const invId = domParameters?.invId || '';
  const table_name = domParameters?.parameters || '';
  const profileId = ApListingOnRowClickData
    ? ApListingOnRowClickData.profileId
    : '';
  const invGrossAmount = ApListingOnRowClickData
    ? ApListingOnRowClickData.amountConverted
    : '';
  const mainMenu = domParameters?.menuData || '';
  const subMenu = domParameters?.submenu || '';

  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user?.userId);
  const clientId = user?.clientId;
  let tableName = [];
  if (table_name.indexOf(',') > -1) {
    tableName = table_name.split(',');
  }

  const approvalObj = {
    clientId: clientId,
    invId: invId,
    commonTableId: invId,
    status: tableName[1],
    invStatus: tableName[1],
    previousEventTime: invEventTime,
    invEventTime: invEventTime,
    userId: userId,
    userComments: addCommentsValue,
    profileId: profileId,
    invoiceGrossAmount: invGrossAmount,
    emailTempId: null,
  };

  const onEnterKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      invNonPoSave();
    }
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    //To show next and previous button for 3 Seconds
    setShowInvoiceToggle(true);
    const timeout = setTimeout(() => {
      setShowInvoiceToggle(false);
    }, 3000);

    dispatch({
      type: accPayConstants.SUBMIT_CREDIT_NOTE_RESET,
    });

    dispatch({
      type: accPayConstants.NON_PO_INV_SUBMIT_FOR_APPROVAL_RESET,
    });
    return () => {
      clearTimeout(timeout);
    };
  }, []);

  useEffect(() => {
    if (accpayINVDetails && accpayINVDetails.value) {
      accpayINVDetails.value.filePath
        ? setFilePath(accpayINVDetails.value.filePath)
        : null;

      setAlertMsgFlag(accpayINVDetails.value.autoSubmitAlertFlag);
      if (accpayINVDetails?.value?.formDetailsDtoList) {
        accpayINVDetails?.value?.formDetailsDtoList.map((items) => {
          if (items.uniqueKey === 'totalcharges') {
            setOtherChargesFormData(items?.formSubDetailsInternalDTOList[0]);
          }
        });
      }
      setPoOrInvResponse(accpayINVDetails.value);
    }
  }, [accpayINVDetails]);

  useLayoutEffect(() => {
    if (invId) {
      invId && user?.clientId && user?.clientId != undefined
        ? dispatch(
            getInvEmailTree({
              clientId: user?.clientId,
              invId: invId,
            })
          )
        : '';
    }

    return () => {
      dispatch(emailResetStateField({ fieldNames: ['emailTree'] }));
    };
  }, []);

  useEffect(() => {
    if (emailTree?.value && emailTree?.value?.length > 0) {
      setInvEmailTreeList(emailTree?.value);
      setInvEmailListPresent(true);
    }
  }, [emailTree]);

  useEffect(() => {
    if (accpayEntityChange?.value) {
      let tempForm = { ...poOrInvResponse };
      tempForm?.formDetailsDtoList?.map((item, index) => {
        if (item.uniqueKey === 'itemList') {
          let itemList = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'itemList'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, itemList);
        } else if (item.uniqueKey === 'location') {
          let location = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'location'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, location);
        } else if (item.uniqueKey === 'totalcharges') {
          let totalcharges = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'totalcharges'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, totalcharges);
          setOtherChargesFormData(
            totalcharges?.formSubDetailsInternalDTOList[0]
          );
        }
      });
      setPoOrInvResponse(tempForm);
    }
  }, [accpayEntityChange]);

  useEffect(() => {
    if (suppId) {
      dispatch(getSuppliersById(suppId));
    }

    if (invId) {
      getInvAccPayFormResponse();
      getEmailTemplateFunc(AP_emailSubmitId);
    }

    dispatch(traceEvent({ tableName: 'INV', tableId: invId }));
    return () => {
      var emailTemplate = null;
      dispatch({
        type: emailConstants.EMAIL_TEMPLATE,
        emailTemplate,
      });
    };
  }, []);

  useEffect(() => {
    setisLoading(false);
    setOnSubmittedClick(false);
    if (nonPoInvApprovalSucess) {
      if (nonPoInvApprovalSucess?.value?.validationSuccess) {
        navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
      } else {
        amountAndInvDuplicateMessageFunc(nonPoInvApprovalSucess);
      }
    }
    return () => {
      dispatch({
        type: accPayConstants.NON_PO_INV_SUBMIT_FOR_APPROVAL_RESET,
      });
    };
  }, [nonPoInvApprovalSucess]);

  useEffect(() => {
    if (buttonData && buttonData.length) {
      const parentInvId = ApListingOnRowClickData?.parentInvId;

      if (parentInvId && tableName[1] === 'credit_note') {
        let buttons = buttonData.filter((btn) => btn.funcPath !== 'linkInv');
        setActionButtons(buttons);
        return;
      }
      setActionButtons(buttonData);
    }
  }, [buttonData, ApListingOnRowClickData]);

  useEffect(() => {
    if (invoiceListData && invoiceListData.value) {
      setInvoiceList(invoiceListData.value);
      setModalLoadng(false);
    }

    return () => {
      clearList(accPayConstants.GET_INVOICE_LIST_SUCCESS);
    };
  }, [invoiceListData]);

  useEffect(() => {
    if (linkCreditNoteWithInvRes) {
      setInvoiceModal(false);
      setModalLoadng(false);
    }
    if (linkCreditNoteWithInvRes?.value) {
      funcToSetResponseMessage('success', linkCreditNoteWithInvRes.value);
    }

    return () => {
      clearList(accPayConstants.LINK_CREDIT_NOTE_WITH_INVOICE_SUCCESS);
    };
  }, [linkCreditNoteWithInvRes]);

  const clearList = (constant) => {
    dispatch({
      type: constant,
      payload: null,
    });
  };

  const amountAndInvDuplicateMessageFunc = (validationResponse) => {
    var errorMessageBuilder = '';
    if (validationResponse) {
      if (validationResponse.value.amountExceededFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.amountOrQuantityExceeded
        );
      }
      if (validationResponse.value.quantityExceededFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.quantityExccededWarning
        );
      }
      if (validationResponse.value.invoiceNumberDuplicationFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.duplicateInvoice
        );
      }
      if (validationResponse.value.lineValidationWarningFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.lineValidationWarning
        );
      }
      if (validationResponse.value.grnValidationFailed) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.grnValidationFailureMessage
        );
      }
      if (validationResponse.value.unitPriceValidationFailed) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.unitPriceValidationFailureMessage
        );
      }
      if (validationResponse.value.profileValidatorFailureMessage) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.profileValidatorFailureMessage
        );
      }
      funcToSetResponseMessage('error', errorMessageBuilder);
    }
  };
  const getInvAccPayFormResponse = () => {
    dispatch(
      getINVDetailsForAccPay({
        key: AP_invIdConstant,
        id: invId,
        userId: globalutils.getDataFromStorage('userId'),
        reqType: 'subscribe',
      })
    );
  };

  useEffect(() => {
    if (accPaytraceEvents) {
      setTraceEvents(accPaytraceEvents.value);
      let data = null;
      dispatch({
        type: accPayConstants.ACCPAY_TRACE_EVENTS_SUCCESS,
        data,
      });
    }
  }, [accPaytraceEvents]);

  useEffect(() => {
    if (onSubmitvalidationSuccess) {
      notify();
      setOnSubmitvalidationSuccess(false);
    }
  }, [onSubmitvalidationSuccess]);

  const iconClickHandler = (event) => {
    event.preventDefault();
  };

  const [view, setView] = React.useState('view-1');

  const onAddCommentsChangeHandler = (event) => {
    setAddCommentsValue(event.target.value);
  };

  const getEmailTemplateFunc = (emailTempId) => {
    let currentUrl = '';

    dispatch(
      getNonPOApproverEmailTemplate({
        emailTemplateId: emailTempId,
        poId: null,
        invoiceId: invId,
        userId: userId,
        clientId: clientId,
        currentUrl: currentUrl,
        supplierId: suppId,
        profileId: profileId,
        invoiceCurrentStatus: tableName[1],
        invoiceGrossAmount: invGrossAmount,
        previousInvEventTime: invEventTime,
      })
    );
  };
  useLayoutEffect(() => {
    if (changedIPRes && changedIPRes?.value) {
      const defaultAnno = invoiceService.extractedValuesMapping(
        changedIPRes.value
      );
      invoiceService.annotationConfigs.defaultAnnotations = defaultAnno;
      invoiceService.annotationConfigs.initialScale =
        changedIPRes?.value?.scale;
      setReactDocViewerProp(invoiceService.annotationConfigs);
      let AISelectionCoords = [];
      changedIPRes?.value?.boundingBoxList?.map((coordsSelectedByAI) => {
        AISelectionCoords.push({
          ...coordsSelectedByAI,
          allCoords: changedIPRes?.value,
        });
      });
      setextractedCoordsVerification(AISelectionCoords);

      return () => {
        invoiceService.annotationConfigs.defaultAnnotations = [];
        setReactDocViewerProp(invoiceService.annotationConfigs);
      };
    }
  }, [changedIPRes]);

  useEffectOnce(() => {
    setReactDocViewerProp(invoiceService.annotationConfigs);
    return () => {
      invoiceService.annotationConfigs.defaultAnnotations = [];
      setReactDocViewerProp(invoiceService.annotationConfigs);
      dispatch(
        changedInputsAction({
          changedInputs: null,
          invID: invId,
          reqType: 'unsubscribe',
        })
      );
      setFocusedInput('');
      setReactDocViewerProp(null);
      invoiceService.annotationConfigs.entity = {};
    };
  }, []);

  //invoice edited details save response

  useEffect(() => {
    setisLoading(false);
    setAddCommentsValue('');
    let invEditedDetailsResponse = null;
    let error = null;
    if (invDetailsEditResponse && invDetailsEditResponse.value) {
      funcToSetResponseMessage('success', invDetailsEditResponse.value);
      setDisabledFlag(true);
      setOnSaveClicked(false);
      dispatch({
        type: accPayConstants.ACCPAY_PASS_INV_EDITED_DETAILS_SUCCESS,
        invEditedDetailsResponse,
      });
      dispatch(traceEvent({ tableName: 'INV', tableId: invId }));
      getInvAccPayFormResponse();
    } else if (invDetailsEditError) {
      if (invDetailsEditError.response && invDetailsEditError.response.data)
        funcToSetResponseMessage('error', invDetailsEditError.response.data);
      setDisabledFlag(true);
      setOnSaveClicked(false);
      dispatch({
        type: accPayConstants.ACCPAY_PASS_INV_EDITED_DETAILS_FAILURE,
        error,
      });
    }
  }, [invDetailsEditResponse, invDetailsEditError]);

  useEffect(() => {
    setisLoading(false);

    if (
      nonPoEmailTemplate &&
      nonPoEmailTemplate.value.emailPopUpHidden === false
    ) {
      if (
        nonPoEmailTemplate.value.content &&
        nonPoEmailTemplate.value.content.length > 0 &&
        nonPoEmailTemplate.value.subject &&
        nonPoEmailTemplate.value.subject.length > 0
      ) {
        setEmailTemplate({
          toAddress: nonPoEmailTemplate.value.toAddressList,

          subject: nonPoEmailTemplate.value.subject,

          content: nonPoEmailTemplate.value.content,
        });
      } else {
        funcToSetResponseMessage('error', 'Email content or subject is empty');
      }
    }
  }, [nonPoEmailTemplate]);

  useEffect(() => {
    if (!linkRtPoRes) return;
    setisLoading(false);
    if (linkRtPoRes?.value) {
      funcToSetResponseMessage('success', linkRtPoRes?.value);
      let Keyvalue = Object.keys(ApListingOnRowClickData)[1];
      let rtPoId = selectedPoData.po_id;
      dispatch(selectedPoDetails(null));

      navigate(
        `/${mainMenu}/${subMenu}/APListing/${parameters}/invoice/${Keyvalue}/${rtPoId}/${invId}`,
        { state: { suppId, invId } }
      );
    }
    return () => {
      dispatch({
        type: accPayConstants.LINK_RT_PO_WITH_PO_SUCCESS,
        payload: null,
      });
    };
  }, [linkRtPoRes]);

  // useEffect(() => {
  //   if (!creditNoteSubmitRes) return;
  //   setisLoading(false);
  //   if (creditNoteSubmitRes?.value?.postedSuccessfully) {
  //     funcToSetResponseMessage(
  //       "success",
  //       creditNoteSubmitRes?.value?.postedSuccessfully
  //     );
  //   }
  //   return () => {
  //     dispatch({
  //       type: accPayConstants.SUBMIT_CREDIT_NOTE_SUCCESS,
  //       payload: null,
  //     });
  //   };
  // }, [creditNoteSubmitRes]);

  useEffect(() => {
    setisLoading(false);
    setOnSubmittedClick(false);
    if (!creditNoteSubmitRes) return;

    if (creditNoteSubmitRes) {
      if (creditNoteSubmitRes?.value?.validationSuccess) {
        navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
      } else {
        amountAndInvDuplicateMessageFunc(creditNoteSubmitRes);
      }
    }
    return () => {
      dispatch({
        type: accPayConstants.SUBMIT_CREDIT_NOTE_RESET,
      });
    };
  }, [creditNoteSubmitRes]);

  const invNonPoConfirm = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (isConfirmed) {
      setOnSubmittedClick(true);
    }
  };

  const invNonPoSave = () => {
    disabledFlag === false
      ? setOnSaveClicked(true)
      : funcToSetResponseMessage('error', 'No changes detected');
  };

  const invNonPoEdit = () => {
    setDisabledFlag(false);
  };

  const invNonPoClose = () => {
    navigate(-1);
  };

  const statusChange = (status) => {
    poInvStatusChange({
      tablename: tableName[0],
      tableId: invId,
      status,
      userId,
      invEventTime,
      addCommentsValue,
    });
  };

  const invNonPoOnHold = () => {
    statusChange(AP_onHold);

    navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
  };

  // email submit click function

  const notify = () => {
    //call non po validation function

    if (tableName[1] === 'credit_note') {
      dispatch(submitCreditNote({ userId, obj: approvalObj }));
      return;
    }
    dispatch(
      nonPoInvoiceValidatorAndSubmitForApproval({ userId, obj: approvalObj })
    );
  };
  const handleConfirm = async (msg, anno, indexToReplace) => {
    const isConfirmed = await confirm(msg);
    if (isConfirmed) {
      anno.splice(indexToReplace, 1);
    } else {
      anno.splice(-1);
    }
    textMapping(anno);
  };

  const textMapping = (anno) => {
    var lastanno = anno.at(-1);
    const boundingBox = lastanno?.areaAnnotation?.boundingBox;
    const pdfInfo = lastanno?.areaAnnotation?.pdfInformation;
    const updatedCoords = invoiceService.mapDisplayNameToTextBox(anno);
    invoiceService.annotationConfigs.defaultAnnotations = updatedCoords;
    let updatedConfig = cloneDeep(invoiceService.annotationConfigs);

    if (Extraction && suppId) {
      Extraction?.invCordinates?.forEach((element) => {
        if (element.invFieldKey === focusedInput) {
          element.cordinateFound = true;
          element.cordinateMapped = true;
          element.rectangle.x = boundingBox.left;
          element.rectangle.y = boundingBox.top;
          element.rectangle.width = boundingBox.width;
          element.rectangle.height = boundingBox.height;
          element.rectangle.pageHeight = pdfInfo.height;
          element.rectangle.pageWidth = pdfInfo.width;
          element.rectangle.pageNumber = lastanno.page;
        }
      });
      let updatedValues = {
        ...Extraction,
        supplierId: suppId,
      };

      setextractionSaveObject(updatedValues);
      dispatch(
        extractData({
          data: {
            fileFullPath: Extraction?.filePath,
            rectangle: {
              x: boundingBox.left,
              y: boundingBox.top,
              width: boundingBox.width,
              height: boundingBox.height,
              pageWidth: pdfInfo.width,
              pageHeight: pdfInfo.height,
              pageNumber: lastanno.page,
            },
            key: focusedInput,
            clientId: globalutils.getDataFromStorage('clientId'),
          },
          reqType: 'subscribe',
        })
      );
    } else {
      var msg = 'Extraction failed!';
      if (!suppId) {
        msg = 'Extraction failed! Supplier not found.';
      }
      dispatch(
        showDialog({
          showPopup: true,
          type: 'error',
          responseMessage: msg,
          canClose: true,
          autoHide: false,
        })
      );
    }

    setReactDocViewerProp(updatedConfig);
  };

  const navigateRoute = (id, e) => {
    e.preventDefault();

    invoiceService.previousNext(
      id,
      dispatch,
      apCachedData,
      apListingFilteredData
    );
  };

  const updateFocusedInput = useCallback(
    (uniqueKey) => {
      if (uniqueKey && uniqueKey !== focusedInput) {
        const entity = invoiceService.entities.find(
          (item) => item.uniquekey === uniqueKey
        );
        invoiceService.annotationConfigs.entity = entity;
        invoiceService.annotationConfigs.config.disableOCR = false;
        invoiceService.annotationConfigs.config.hideAnnotateableBoxes = false;
        invoiceService.annotationConfigs.config.hideAnnotatingEntityVisualizations = false;
        invoiceService.annotationConfigs.config.readonly = false;
        invoiceService.annotationConfigs.config.hideAnnotatingTooltips = false;
        let updatedState = { ...invoiceService.annotationConfigs };
        setFocusedInput(uniqueKey);
        setReactDocViewerProp(updatedState);
      }
    },
    [focusedInput]
  );
  const getAnnotations = (anno, childRef) => {
    if (
      previousAnnoListLength &&
      anno.length &&
      previousAnnoListLength > anno.length
    )
      return;

    if (anno && anno.length) {
      setPreviousAnnoListLength(anno.length);
      var text = 'Text';
      var indexToReplace = null;
      var valueArr = anno.map(function (item) {
        return item.entity.uniquekey;
      });

      var isDuplicate = valueArr.some(function (item, idx) {
        if (valueArr.indexOf(item) !== idx) {
          indexToReplace = valueArr.indexOf(item);
          text = anno[valueArr.indexOf(item)]?.entity?.name;
          return true;
        }
      });

      if (isDuplicate) {
        handleConfirm(
          text + ' was already marked. Are you sure you want to replace it.',
          anno,
          indexToReplace
        );
      } else {
        textMapping(anno);
      }
      setModelClose(false);
    }
  };

  const zoomHandler = (scaleValue) => {
    invoiceService.annotationConfigs.initialScale = scaleValue;
  };

  const invmatchededit = () => {
    invNonPoEdit();
  };
  const invmatchsave = () => {
    invNonPoSave();
  };
  const linkRtPO = () => {
    setRtPoFlag(true);
  };
  const handleSubmitCreditNote = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (isConfirmed) {
      setOnSubmittedClick(true);
    }
  };

  const linkInv = () => {
    setInvoiceModal(true);
  };

  const invoiceLinkHandler = (parentInvId) => {
    dispatch(
      linkCreditNoteWithInvoice({
        parentInvId: parentInvId,
        creditNoteId: invId,
        userId: userId,
      })
    );
    setModalLoadng(true);
  };

  const functionsName = {
    invNonPoConfirm,
    invNonPoSave,
    invNonPoEdit,
    invNonPoClose,
    invNonPoOnHold,
    invmatchededit,
    invmatchsave,
    linkRtPO,
    handleSubmitCreditNote,
    linkInv,
  };

  const modalCloseHandler = () => {
    setInvoiceModal(false);
    clearList(accPayConstants.GET_INVOICE_LIST_SUCCESS);
    setInvoiceList([]);
  };

  const setValueOfDataTable = (dataTableValue) => {
    let formDetails = cloneDeep(poOrInvResponse);
    if (formDetails) {
      formDetails.invItemsDtoList = dataTableValue;
      formDetails?.formDetailsDtoList?.map((item) => {
        item.type === 'DataTable' ? (item.value = dataTableValue) : null;
      });
    }
    setPoOrInvResponse(formDetails);
  };

  return (
    <div>
      {filePath && filePath !== '' ? (
        <div>
          <CommonSpinner visible={isLoading} />
          <div className="inv-prev-next-icons">
            <div
              className="prev-wrap"
              onMouseEnter={() => setShowInvoiceToggle(true)}
              onMouseLeave={() => setShowInvoiceToggle(false)}
            >
              <div
                className="prev-container"
                style={{ display: showInvoiceToggle ? 'block' : 'none' }}
              >
                <div
                  title="Previous"
                  data-tooltip-id="invoice-prev"
                  onClick={(e) => navigateRoute('prev', e)}
                  className="icon-arrow-left2 fl prev"
                  style={{ display: showInvoiceToggle ? 'block' : 'none' }}
                ></div>
              </div>
            </div>

            <div
              className="next-wrap"
              onMouseEnter={() => setShowInvoiceToggle(true)}
              onMouseLeave={() => setShowInvoiceToggle(false)}
            >
              <div
                className="next-container"
                style={{ display: showInvoiceToggle ? 'block' : 'none' }}
              >
                <div
                  title="Next"
                  data-tooltip-id="invoice-next"
                  onClick={(e) => navigateRoute('next', e)}
                  className="icon-arrow-right2 fr next"
                  style={{ display: showInvoiceToggle ? 'block' : 'none' }}
                ></div>
              </div>
            </div>
          </div>
          <div className="invoice-compare mb20">
            <div className="pull-right view-switcher">
              {tableName[1] !== 'credit_note' ? (
                <div
                  onClick={() => setEmailModalOpen(true)}
                  style={{ cursor: 'pointer' }}
                  className="service-request-launcher  fr mr16"
                  hidden={alertMsgFlag}
                >
                  <i
                    className="icon-drawer mr8"
                    style={{ verticalAlign: 'baseline' }}
                  ></i>

                  <label className="label">Preview Email</label>
                </div>
              ) : (
                ''
              )}
            </div>{' '}
            <div className="mb20">
              <InvPageHeader invDetails={accpayINVDetails?.value} />
            </div>
            <div className="inv-table-view">
              <Tabs
                activeKey={activeTabKey}
                onChange={(key) => setActiveTabKey(key)}
                tabBarStyle={{ marginTop: '-24px', fontFamily: 'Roboto' }}
              >
                <TabPane className="th " tab={'Invoice'} key={0}>
                  <div className={'viewHolder ' + view}>
                    <InvMatchingComponent
                      accpayBreadCrumb={accpayBreadCrumb}
                      className="boxed unbox"
                      invId={invId}
                      tableName={tableName}
                      disabled={disabledFlag}
                      addCommentsValue={addCommentsValue}
                      onSaveClick={onSaveClicked}
                      onSaveDisable={(event) => setOnSaveClicked(event)}
                      onSubmittedClick={onSubmittedClick}
                      onSubmittedClickDisable={(event) =>
                        setOnSubmittedClick(event)
                      }
                      notificationBar={(type, message) =>
                        funcToSetResponseMessage(type, message)
                      }
                      onSubmitvalidationSuccess={(event) =>
                        setOnSubmitvalidationSuccess(event)
                      }
                      onFocusHandler={(formType) =>
                        updateFocusedInput(formType?.uniqueKey)
                      }
                      setisLoading={(event) => setisLoading(event)}
                      filePath={() => null}
                      passValueToParent={() => ''}
                      setPOINVCallback={(form) => {
                        setPoOrInvResponse(form);
                      }}
                    />
                    <div className="boxed" style={{ fontWeight: 700 }}>
                      {disabledFlag ? (
                        <div>
                          Invoice Image
                          <span
                            title="Click to open in new tab"
                            className="icon-attachment"
                            style={{
                              cursor: 'pointer',
                              float: 'right',
                              margin: '5px',
                            }}
                            onClick={() =>
                              utils.redirectToPDF(filePath, dispatch)
                            }
                          ></span>
                        </div>
                      ) : (
                        <div className="mb8">
                          Invoice Image
                          <span
                            title="Click to open in new tab"
                            className="icon-attachment"
                            style={{
                              cursor: 'pointer',
                              float: 'right',
                              margin: '5px',
                            }}
                            onClick={() =>
                              globalutils.goto(AP_file_url + filePath)
                            }
                          ></span>
                        </div>
                      )}
                      {disabledFlag ? (
                        <DocumentViewer
                          fileURL={filePath}
                          fileType={'iframe'}
                          zoom={'#zoom=100'}
                          iframeStyle={{ width: 100 + '%', height: 96 + '%' }}
                        />
                      ) : (
                        <Docviewer
                          reactDocprop={reactDocViewerProp}
                          actionHandlers={{ getAnnotations, zoomHandler }}
                          fileURL={filePath}
                          fileType={'reactDocViewer'}
                          zoom={'#zoom=100'}
                          disableClick={false}
                          fullScreen={false}
                        />
                      )}
                    </div>

                    <div className="mb20"></div>
                  </div>
                </TabPane>
                <TabPane className="th " tab={'Invoice Items'} key={1}>
                  <div className="inv-div">
                    <div className="force-full-width">
                      {tableName &&
                      (tableName[0] === 'PP' || tableName[0] === 'INV') ? (
                        <InvDatatable
                          fullWidth
                          disabledFlag={disabledFlag}
                          status={tableName[1]}
                          setValueOfDataTable={setValueOfDataTable}
                          invData={poOrInvResponse?.formDetailsDtoList}
                          onSaveClick={onSaveClicked}
                        />
                      ) : null}
                    </div>
                  </div>
                </TabPane>
                <TabPane className="th " tab={'Other Charges'} key={2}>
                  <div className="force-full-width">
                    <MultiRowInput
                      formType={otherChargesFormData}
                      disabled={disabledFlag}
                    />
                  </div>
                </TabPane>
              </Tabs>

              <div className="mb20"></div>
            </div>
            {rtPoFlag ? (
              <UnlinkAndMatchPO
                isSearchPoOpen={rtPoFlag}
                onCloseClick={() => setRtPoFlag(false)}
                invId={invId}
                clientId={clientId}
                suppId={suppId}
                suppName={suppName}
                menuData={mainMenu}
                submenu={subMenu}
                parameters={table_name}
                setisLoading={setisLoading}
                rtpo={rtPoFlag}
              />
            ) : (
              ''
            )}
            <LinkInvoiceModal
              modalOpen={invoiceModal}
              modalClose={modalCloseHandler}
              tableData={invoiceList}
              apiCallFunction={(obj) =>
                dispatch(getInvoiceList({ data: obj, clientId }))
              }
              linkButtonName={'Link'}
              linkButtonClick={invoiceLinkHandler}
              isLoading={modalLoadng}
            />
            <EmailModal
              isModalOpen={emailModalOpen}
              isModalClose={() => setEmailModalOpen(false)}
              invId={invId}
              invEventTime={invEventTime}
              tableName={tableName}
              emailDetails={emailTemplateDetails}
            />
          </div>

          <ButtonCommon
            tempButtonData={actionButtons}
            functionsName={functionsName}
          />

          {/* * INV Email Listing & TraceEvent Flow-chart Added @ Bottom
           * <AUTHOR> V
           */}

          <div className="data-table-wrap">
            {/* Jsut for testing ( Shahanas ) */}

            {invEmailListPresent ? (
              <INVEmailList invEmailTreeList={invEmailTreeList} />
            ) : null}

            <div className="mr20 " style={{ width: 100 + '%' }}>
              <div className=" page-sub-title">Events</div>
              <div
                className="boxed mb20 "
                style={{ width: 100 + '%', marginTop: 20 + 'px' }}
              >
                <TraceEvents
                  key="traceEventsEditor"
                  onAddCommentsChangeHandler={onAddCommentsChangeHandler}
                  addCommentsValue={addCommentsValue}
                  enableComments={true}
                  data={getTraceEvents}
                  sendButton={invNonPoSave}
                  onEnterKeyPress={onEnterKeyPress}
                  invId={invId}
                  userId={userId}
                />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <Empty />
      )}
    </div>
  );
};

export { InvoiceNonPO };
