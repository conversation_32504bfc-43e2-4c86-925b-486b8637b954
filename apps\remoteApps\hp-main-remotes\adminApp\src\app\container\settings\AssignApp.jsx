/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 01-03-2022 15:40:13
 * @modify date 2024-08-01 15:17:00
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';

import { AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { ButtonCommon, dataTableServiceProvider, Input } from '@hp/components';
import { CheckBoxInput } from '@hp/components';
import { showDialog } from '@hp/mainstore';
import {
  getClientApplicationFormDetails,
  saveClientApplicationFormDetails,
  clientAppFormDetailsResponseClear,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';

const AssignApp = (props) => {
  const dispatch = useDispatch();
  const { clientAppFormDetails, clientAppFormDetailsResponse } = useSelector(
    (store) => store.settings
  );

  let user = globalutils.getDataFromStorage('all');
  const clientIdVal = user?.clientId;
  const [formDetails, setFormDetails] = useState([]);
  const [clientId, setClientId] = useState(clientIdVal);
  const [appId, setAppId] = useState();
  const [showModule, setShowModules] = useState(false);
  // const [flag, setFlag] = useState();
  // const [ischecked, setIschecked] = useState();
  const [showButtons, setShowButtons] = useState(false);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (clientAppFormDetails && clientAppFormDetails.value) {
      setFormDetails(clientAppFormDetails.value);
      setTableData(
        clientAppFormDetails.value.find(
          (val) => val.uniqueKey === 'pageSubtitle'
        ).formSubDetailsInternalDTOList[0].value
      );
    }
  }, [clientAppFormDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch(clientAppFormDetailsResponseClear(removeNotification));
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  // Fetch Application and Modules details form
  const getClientApplicationFormDetailsFn = (clientId) => {
    dispatch(getClientApplicationFormDetails(clientId));
  };

  useEffect(() => {
    if (clientAppFormDetailsResponse && clientAppFormDetailsResponse.value) {
      funcToSetResponseMessage(
        clientAppFormDetailsResponse.value.type,
        clientAppFormDetailsResponse.value.text
      );
    }
    getClientApplicationFormDetailsFn(clientId);
  }, [clientAppFormDetailsResponse]);

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let appModuleList = '';
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle'
        ? (appModuleList = entry.formSubDetailsInternalDTOList[0].value)
        : '';
    });
    if (appModuleList.length > 0) {
      dispatch(
        saveClientApplicationFormDetails({
          formDetails: appModuleList,
          clientId: clientId,
        })
      );
      setShowButtons(false);
      setConditionalRowStyles([]);
    } else {
      funcToSetResponseMessage('error', 'Cannot be Saved');
    }
  };

  const functionsName = {
    handleSave,
  };

  // Select a value in the select combo-box
  const handleSelect = (event, uniqueKey) => {
    setConditionalRowStyles([]);
    setShowModules(false);
    setShowButtons(false);
    getClientApplicationFormDetailsFn(event.target.value);
    setClientId(event.target.value);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'clientSelect'
        ? (element.value = event.target.value)
        : '';
    });
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleSelect,
  };

  // Function to display corresponding modules of an application
  const handleRowClick = (obj) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.uniqueKey === 'selectedModuleDatatable'
            ? obj.moduleList
              ? (element.value = obj.moduleList)
              : (element.value = [])
            : '';
        });
    });
    setFormDetails(tempArray);
    setAppId(obj.appId);
    setShowModules(true);
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.appId,
      'appId'
    );
    setConditionalRowStyles(styleAttribute);
  };

  // Updated function to handle app checkbox selection
  function handleAppSelect(row) {
    let tempArray = cloneDeep(formDetails);
    tempArray.forEach((entry) => {
      if (entry.uniqueKey === 'pageSubtitle') {
        entry.formSubDetailsInternalDTOList[0].value.forEach((item) => {
          if (item.appId === row.appId) {
            item.flag = row.flag;
          }
        });
      }
    });

    setFormDetails(tempArray);
    setShowButtons(true);
  }

  function handleModuleSelect(row) {
    let tempArray = cloneDeep(formDetails);
    // let updated = false;
    tempArray.forEach((entry) => {
      if (entry.uniqueKey === 'pageSubtitle') {
        entry.formSubDetailsInternalDTOList[0].value.forEach((item) => {
          if (item.appId === appId) {
            item.moduleList.forEach((mod) => {
              if (mod.moduleId === row.moduleId) {
                mod.flag = row.flag;
                // updated = true;
              }
            });
          }
        });
      }
    });

    setFormDetails(tempArray);
    setShowButtons(true);
  }

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          let columns =
            element.formSubDetailsInternalDTOList &&
            element.formSubDetailsInternalDTOList.map((val, colIndex) => {
              return {
                selector: val.selector,
                width:
                  val.selector === 'appCheckBox' ||
                  val.selector === 'moduleCheckBox'
                    ? 80 + 'px'
                    : '',
                name: val.displayName,
                cell:
                  val.selector === 'appCheckBox'
                    ? function displayCell(row) {
                        const isChecked = row.flag === true;
                        return (
                          <CheckBoxInput
                            key={`checkbox-app-${row.appId}-${colIndex}`}
                            checked={isChecked}
                            onChange={() => {
                              const updatedRow = { ...row };
                              updatedRow.flag = !row.flag;
                              handleAppSelect(updatedRow);
                            }}
                          />
                        );
                      }
                    : val.selector === 'moduleCheckBox'
                    ? function displayCell(row) {
                        const isChecked = row.flag === true;
                        return (
                          <CheckBoxInput
                            key={`checkbox-module-${row.moduleId}-${colIndex}`}
                            checked={isChecked}
                            onChange={() => {
                              const updatedRow = { ...row };
                              updatedRow.flag = !row.flag;
                              handleModuleSelect(updatedRow);
                            }}
                          />
                        );
                      }
                    : '',
                id: `col-${val.selector}-${colIndex}`,
              };
            });

          if (element.uniqueKey === 'clientSelect') {
            return (
              <div className="three-col-layout" key={`client-select-${index}`}>
                <Input
                  isEditable={'notShowing'}
                  indexKey={index}
                  formType={element}
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey
                    );
                  }}
                />
              </div>
            );
          } else if (element.uniqueKey === 'search') {
            return null;
          } else if (element.uniqueKey === 'pageSubtitle') {
            return (
              <div className="two-col-layout" key={`page-subtitle-${index}`}>
                <div>
                  {formControlsBinding(
                    element.formSubDetailsInternalDTOList.slice(-2, -1)
                  )}
                </div>
                <div>
                  {showModule &&
                    formControlsBinding(
                      element.formSubDetailsInternalDTOList.slice(-1)
                    )}
                </div>
              </div>
            );
          } else {
            return (
              <Input
                key={`input-${element.uniqueKey}-${index}`}
                indexKey={index}
                formType={element}
                dataTableColumn={columns}
                dataTableEventHandler={(obj) => {
                  element.uniqueKey === 'selectedAppDatatable' &&
                    handleRowClick(obj);
                }}
                conditionalRowStyles={
                  element.uniqueKey === 'selectedAppDatatable'
                    ? conditionalRowStyles
                    : []
                }
              />
            );
          }
        })
      : null;
  };

  return (
    <>
      <div className="">
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
      {showButtons && <ButtonCommon functionsName={functionsName} />}
    </>
  );
};
export { AssignApp };
