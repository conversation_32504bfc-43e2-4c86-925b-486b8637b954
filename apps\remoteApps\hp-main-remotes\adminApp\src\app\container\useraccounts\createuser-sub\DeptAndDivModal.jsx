/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

import { AdvancedSelect, Button } from '@hp/components';
import React, { useState } from 'react';

const DeptAndDivModal = (props) => {
  const [notify, setnotify] = useState(null);
  const [deptErrorFlag, setDeptErrorFlag] = useState(false);
  const [deptName, setDeptName] = useState({ label: null, value: null });
  const [divName, setDivName] = useState({ label: null, value: null });
  let filterArray = props.filterArray;
  let flag = true;

  const saveRecordToTable = () => {
    if (deptName.value) {
      handleFilter(deptName.value, divName.value);
      flag && props.onSaveClickPassTableData(deptName, divName);
      setDeptErrorFlag(false);
    } else setDeptErrorFlag(true);
  };

  // Function to Show Alerts
  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  const handleFilter = (deptIdValue, divIdValue) => {
    filterArray &&
      filterArray.map((entry) => {
        if (entry.deptId === deptIdValue && entry.divId === divIdValue) {
          flag = false;
          funcToSetResMessageInModal('error', 'Duplicate Entry!');
        }
      });
  };

  return (
    <div>
      <div className="two-col-layout">
        <div className="col">
          <AdvancedSelect
            onModal
            getLabel
            required
            label="Department"
            className="mb20"
            value={deptName}
            onChange={(selected) => {
              let tempVal = { label: null, value: null };
              if (selected) {
                tempVal = { label: selected.label, value: selected.value };
                setDeptErrorFlag(false);
              }
              setDeptName(tempVal);
            }}
            options={props.deptList}
            error={{
              flag: deptErrorFlag,
              message: 'Please Select Department',
            }}
          />
        </div>
        <div className="col">
          <AdvancedSelect
            onModal
            getLabel
            label="Division"
            className="mb20"
            value={divName}
            onChange={(selected) => {
              let tempVal = { label: null, value: null };
              if (selected) {
                tempVal = { label: selected.label, value: selected.value };
              }
              setDivName(tempVal);
            }}
            options={
              props.deptList
                .find((entry) => entry.value === deptName.value)
                ?.divisionsList.map((item) => {
                  return { label: item.divName, value: item.divId };
                }) ?? []
            }
          />
        </div>
      </div>
      <Button className="mr16 default" onClick={saveRecordToTable}>
        Save
      </Button>
      {notify != null ? (
        <div
          className={['notification-bar', 'type-' + notify.type].join(' ')}
          style={{
            position: 'sticky !important',
            width: 90 + '%',
          }}
        >
          <i className="icon-close" onClick={() => setnotify(null)}></i>
          {notify.resMessage}
        </div>
      ) : (
        ''
      )}
    </div>
  );
};
export { DeptAndDivModal };
