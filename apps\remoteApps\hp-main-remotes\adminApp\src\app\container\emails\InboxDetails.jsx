/* eslint-disable react-hooks/exhaustive-deps */

import { DocumentViewer } from '@hp/components';
import React, { useEffect } from 'react';
import Modal from 'react-modal';
function InboxDetails(props) {
  const { state } = props;
  const [isModal, setIsModal] = React.useState(false);
  const [getIframe, setIframe] = React.useState(false);
  const [filePath, setfilePath] = React.useState('');

  const viewFrame = (path) => {
    setfilePath(path);
    setIsModal(true);
  };

  useEffect(() => {
    if (state) {
      setIframe(state);
    } else {
      setIframe('');
    }
  }, [props]);

  return (
    <>
      <h1 className="page-title">
        {'Subject : '}
        {state ? state.subject : ''}
      </h1>
      <div className="form-and-image ">
        <div className="force-full-width">
          <div className="col">
            <p>
              <b>From</b> : {state ? state.fromAddress : ''}
            </p>
            <div className="mb8"></div>

            {state && state.ccList !== null && state.ccList.length > 0 ? (
              <p>
                <b>Cc</b> : {state.ccList}
              </p>
            ) : null}
            <div className="mb8"></div>

            {/* <p>
          <b>Subject</b> : {state.DetailsData.subject}
        </p> */}
            <div className="mb8"></div>
          </div>

          <div className="col">
            <p>
              <b>To</b> :{' '}
              {state && state.toAddressList && state.toAddressList.length
                ? state.toAddressList.map((resData) => {
                    return resData;
                  })
                : ''}
            </p>
            <div className="mb8"></div>
          </div>
          {state &&
          state.attachmentsPathList &&
          state.attachmentsPathList.length
            ? state.attachmentsPathList.map((resdata, i) => {
                return (
                  <div className="col" key={i}>
                    <p
                      style={{ cursor: 'pointer' }}
                      onClick={() => viewFrame(resdata.fileViewPath)}
                    >
                      {i === 0 ? (
                        <b>Attachment :</b>
                      ) : (
                        <b style={{ paddingLeft: 90 + 'px' }}></b>
                      )}{' '}
                      <i className="icon-document"></i>{' '}
                      {resdata.originalFileName}
                    </p>
                    <div className="mb8"></div>
                  </div>
                );
              })
            : null}

          <div className="col">
            <DocumentViewer
              fileURL={`${state ? state.htmlPart : ''}`}
              fileType={'iframe'}
              iframeStyle={{
                width: 100 + '%',
                height: 300 + 'px',
                border: '1px solid grey',
              }}
            />
            <div className="mb20"> </div>
            {(getIframe &&
              getIframe.documentId === '5f5631314c29ba722696fcf4') ||
            getIframe.documentId === '5f63341add4f2f22e5633074' ? (
              <>
                <h1 className="page-title">{'Reply - Draft'}</h1>

                <DocumentViewer
                  fileURL={`<!DOCTYPE html> <html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns:m="http://schemas.microsoft.com/office/2004/12/omml" xmlns="http://www.w3.org/TR/REC-html40"><head><meta http-equiv=Content-Type content="text/html; charset=utf-8"><meta name=Generator content="Microsoft Word 15 (filtered medium)"><style><!--
                ↵/* Font Definitions */
                ↵@font-face
                {font-family:"Cambria Math";
                panose-1:2 4 5 3 5 4 6 3 2 4;}
                @font-face
                	{font-family:Calibri;
                	panose-1:2 15 5 2 2 2 4 3 2 4;}
                ↵/* Style Definitions */
                p.MsoNormal, li.MsoNormal, div.MsoNormal
                	{margin:0in;
                	margin-bottom:.0001pt;
                	font-size:11.0pt;
                	font-family:"Calibri",sans-serif;}
                a:link, span.MsoHyperlink
                	{mso-style-priority:99;
                color:#0563C1;
                	text-decoration:underline;}
                a:visited, span.MsoHyperlinkFollowed
                	{mso-style-priority:99;
                	color:#954F72;
                	text-decoration:underline;}
                p
                	{mso-style-priority:99;
                	mso-margin-top-alt:auto;
                	margin-right:0in;
                	mso-margin-bottom-alt:auto;
                	margin-left:0in;
                	font-size:12.0pt;
                	font-family:"Calibri",sans-serif;}
                p.msonormal0, li.msonormal0, div.msonormal0
                	{mso-style-name:msonormal;
                	mso-margin-top-alt:auto;
                	margin-right:0in;
                	mso-margin-bottom-alt:auto;
                	margin-left:0in;
                	font-size:12.0pt;
                	font-family:"Calibri",sans-serif;}
                span.EmailStyle18
                	{mso-style-type:personal;
                	font-family:"Calibri",sans-serif;
                	color:windowtext;}
                span.EmailStyle20
                	{mso-style-type:personal-reply;
                	font-family:"Arial",sans-serif;
                	color:#1F3864;
                	font-weight:normal;
                	font-style:normal;}
                .MsoChpDefault
                	{mso-style-type:export-only;
                	font-size:10.0pt;}
                @page WordSection1
                	{size:8.5in 11.0in;
                	margin:1.0in 1.0in 1.0in 1.0in;}
                div.WordSection1
                	{page:WordSection1;}
                </style><!--[if gte mso 9]><xml>
                <o:shapedefaults v:ext="edit" spidmax="1026" />
                </xml><![endif]--><!--[if gte mso 9]><xml>
                <o:shapelayout v:ext="edit">
                <o:idmap v:ext="edit" data="1" />
                </o:shapelayout></xml><![endif]--></head><body lang=EN-US link="#0563C1" vlink="#954F72">
                <div class=WordSection1>
                <p class=MsoNormal><b>To:</b><EMAIL><br>
                <b>&nbsp;</b>
                <textarea style="width:544px;height:187px">
Payment information for Invoice# 100931087
Type: ACH
Reference: 20098765
Date: 18-Jul-2020
Amount: 41,257.64
Currency: USD
                </textarea>
              
                 </div>
                </body></html>`}
                  fileType={'iframe'}
                  iframeStyle={{
                    width: 100 + '%',
                    height: 300 + 'px',
                    border: '1px solid grey',
                  }}
                />
              </>
            ) : null}
          </div>
        </div>
      </div>
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>
        <DocumentViewer
          fileURL={filePath}
          fileType={'iframe'}
          zoom={'#zoom=50'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
        />
      </Modal>
    </>
  );
}

export { InboxDetails };
