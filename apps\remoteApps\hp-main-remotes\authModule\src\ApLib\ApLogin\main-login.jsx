/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable jsx-a11y/anchor-has-content */
/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable jsx-a11y/alt-text */
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { CommonSpinner } from '@hp/components';
import { sharedAsset } from '@hp/assets';
import { useDispatch, useSelector } from 'react-redux';
import { AP_history } from '../ApBrowserHistory/history';
import { AP_USER } from '@hp/constants';
import { useNavigate } from 'react-router-dom';
import { userConstants } from '@hp/mainstore';
import { login } from '@hp/mainstore';
import { Button, Form, Input } from 'antd';
import './login.scss';
import { getAllMenus } from '@hp/mainstore';
import {
  getCredentials,
  getProfileImage,
  logout,
  tokenVerifications,
} from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';

const APLogin = () => {
  const { loggingIn, loggedIn, loggedOut } = useSelector(
    (store) => store.authentication
  );
  if (
    AP_history?.location?.state?.from?.search !== undefined &&
    AP_history?.location?.state?.from?.search !== null
  ) {
    let apParamObj = AP_history.location.state.from.search.slice(2);
    localStorage.setItem('apParamObj', apParamObj);
  }
  const userData = JSON.parse(localStorage.getItem(AP_USER));
  const userName = userData?.userName;
  const subscribe = 'subscribe';

  const selectedTheme = window.localStorage.getItem('hp-theme');
  if (selectedTheme) {
    document.documentElement.classList.add(`hp-main-${selectedTheme}-theme`);
    window.localStorage.setItem('hp-theme', selectedTheme);
  } else {
    document.documentElement.classList.add(`hp-main-default-theme`);
    window.localStorage.setItem('hp-theme', 'default');
  }
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { tokenVerification, tokenVerificationFailed } = useSelector(
    (store) => store.user
  );
  const [isSubmit, setisSubmit] = useState(false);
  const [OTPSection, setOTPSection] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [LoginState, setLoginState] = useState({
    username: {
      value: '',
      error: {
        flag: false,
      },
    },
    password: {
      value: '',
      error: {
        flag: false,
      },
    },

    OtpText: {
      value: '',
      error: {
        flag: false,
      },
    },
  });

  // useEffect(() => {
  //   if (loggingIn === false) {
  //     if (isSubmit === true) {
  //       const newState = { ...LoginState };
  //       newState.username.error = {
  //         flag: true,
  //         message: 'Invalid Username!',
  //       };
  //       newState.password.error = {
  //         flag: true,
  //         message: 'Invalid Password!',
  //       };
  //       setLoginState(newState);
  //     }
  //   }
  // }, [loggingIn]);

  function handleStorageChange(event) {
    if (event.key === 'logoutEvent' && event.newValue === 'true') {
      logout();
      dispatch({ type: 'LOGOUT' });
      navigate('/');
      window.removeEventListener('storage', handleStorageChange);
    } else if (event.key === 'user') {
      window.location.reload();
    }
  }

  useEffect(() => {
    window.addEventListener('storage', handleStorageChange);

    if (userData?.token) {
      dispatch(tokenVerifications());
    } else {
      dispatch(getCredentials());
      setIsLoading(false);
    }
  }, []);
  useEffect(() => {
    dispatch({ type: userConstants.LOGIN_REQUEST, userName });
    dispatch({ type: userConstants.LOGIN_SUCCESS, userData });
    dispatch(getAllMenus());
    dispatch(getProfileImage(subscribe));
  }, [tokenVerification, userData]);

  useEffect(() => {
    if (tokenVerificationFailed) {
      dispatch(getCredentials());
      setIsLoading(false);
    }
  }, [tokenVerificationFailed]);

  // useEffect(() => {

  //   if (loggedIn === true && user) {
  //     setOTPSection(true);
  //     let redirectURL =
  //       AP_history.location.state === undefined
  //         ? null
  //         : AP_history?.location?.state?.from?.pathname;
  //     if (passwordChangedFlag && passwordChangedFlag === 'Y') {
  //       AP_history.push(redirectURL ? redirectURL : '/home');
  //     } else {
  //       AP_history.push('/administration/admin-profile/profile/chg-pwd');
  //     }
  //   }
  // }, [loggedIn]);

  useEffect(() => {
    if (loggedIn === false) {
      if (isSubmit === true) {
        const newState = { ...LoginState };
        newState.username.error = {
          flag: true,
          message: 'Invalid Username!',
        };
        newState.password.error = {
          flag: true,
          message: 'Invalid Password!',
        };
        setLoginState(newState);
      }
    }
  }, [loggedIn]);

  useEffect(() => {
    setOTPSection(false);
  }, [loggedOut]);
  const onSubmitHandler = (event) => {
    const newState = { ...LoginState };
    let isValid = true;
    setisSubmit(true);
    if (newState.username.value === '' || newState.username.value == null) {
      newState.username.error = {
        flag: true,
        message: 'Invalid Username!',
      };
      isValid = false;
    } else {
      newState.username.error = {
        flag: false,
      };
      isValid = true;
    }
    if (newState.password.value === '' || newState.password.value === null) {
      newState.password.error = {
        flag: true,
        message: 'Invalid Password!',
      };
      isValid = isValid && false;
    } else {
      newState.password.error = {
        flag: false,
        message: 'Invalid Password!',
      };
      isValid = isValid && true;
    }
    // if (disclaimer === false) {
    //   funcToSetResponseMessage("error", "Please accept terms and conditions.");
    //   return;
    // }
    if (isValid) {
      let AP_historyCopy =
        AP_history?.location?.state === undefined
          ? null
          : AP_history?.location?.state?.from?.pathname;

      dispatch(
        login({
          username: newState.username.value,
          password: newState.password.value,
          AP_historyCopy: AP_historyCopy,
          loginType: '',
        })
      );
    }
  };

  const onFinish = (values) => {
    setLoginFailed(true);
    let AP_historyCopy =
      AP_history?.location?.state === undefined
        ? null
        : AP_history?.location?.state?.from?.pathname;

    dispatch(
      login({
        username: values.username,
        password: values.password,
        AP_historyCopy: AP_historyCopy,
        loginType: '',
      })
    );
  };

  const [getPopUp, setPopUp] = useState('');

  const dialogOpenClose = useSelector((state) => state.alert.dialogOpenClose);

  useEffect(() => {
    if (dialogOpenClose) {
      setPopUp(dialogOpenClose);
    }
  }, [dialogOpenClose]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const handleKeyPress = (e) => {
    if (e.keyCode === 13) onSubmitHandler();
  };

  //   return (
  //     <div className="login-page-wrap flex-row">
  //       {/* <CommonSpinner visible={isLoading} /> */}
  //       <div className="login-image-wrap">
  //         <img src={sharedAsset.Login.bannerImage} alt="Welcome to Client Name" />
  //       </div>
  //       <div className="login-form-wrap flex-column">
  //         <a
  //           rel="noopener noreferrer"
  //           target="_blank"
  //           href="http://pinnacle-solutionsllc.com/"
  //           className="client-logo-wrap"
  //         >
  //           {/* <img src={companyLogo} alt="Pinnacle Solutions LLC" /> */}
  //           {/* <img
  //             style={{ width: 50 + 'px', height: 50 + 'px' }}
  //             src={sharedAsset.Login.productLogo}
  //             alt="Client Logo"
  //           /> */}
  //         </a>
  //         {/* {OTPSection === false ? ( */}
  //         <div className="form-wrap">
  //           {/* <Modal
  //             className="login-disclaimer-modal"
  //             overlayClassName="login-modal-overlay"
  //             ariaHideApp={false}
  //             isOpen={disclaimerModal}
  //           >
  //             <div
  //               onClick={() => setDisclaimerModal(false)}
  //               className="modal-close icon-close "
  //             ></div>

  //             <div className="login-modal-content mb40">
  //               {" "}
  //               All intellectual property rights in or relating to the Software
  //               and all parts of the Software are and shall remain the exclusive
  //               property of Pinnacle Solutions LLC or its licensors.
  //             </div>
  //           </Modal> */}
  //           <h1>Sign In</h1>
  //           {/* <LoginInput
  //             label="User Name"
  //             type="text"
  //             onChange={(event) => onChangeHandler(event, 'username')}
  //             value={LoginState.username.value}
  //             error={LoginState.username.error}
  //             placeholder="<EMAIL>"
  //             onKeyDown={(e) => handleKeyPress(e)}
  //           />
  //           <LoginInput
  //             label="Password"
  //             type="passwordToggle"
  //             placeholder="password"
  //             onChange={(event) => onChangeHandler(event, 'password')}
  //             value={LoginState.password.value}
  //             error={LoginState.password.error}
  //             onKeyDown={(e) => handleKeyPress(e)}
  //             // showpassword={true}
  //           /> */}
  //           {/* check for ack */}
  //           {/* <div className="flex-row">
  //             <CheckBoxInput
  //               value={disclaimer}
  //               onChange={() => setDisclaimer(!disclaimer)}
  //               className="mb20"
  //             />
  //             <div className="disclaimer-text">
  //               {`I acknowledge the License agreement of this software. All intellectual property rights in or relating to the
  //               Software and all parts of the Software are and shall
  //               remain the exclusive property of
  //               Pinnacle Solutions LLC or its licensors.`}
  //             </div>
  //           </div> */}
  //           <Form
  //             layout="vertical"
  //             initialValues={{
  //               remember: true,
  //             }}
  //             onFinish={onSubmitHandler}
  //             onFinishFailed={onFinishFailed}
  //             autoComplete="on"
  //           >
  //             <Form.Item
  //               label="Username"
  //               name="username"
  //               rules={[
  //                 {
  //                   required: true,
  //                   message: 'Please input your username!',
  //                 },
  //               ]}
  //             >
  //               <Input />
  //             </Form.Item>

  //             <Form.Item
  //               label="Password"
  //               name="password"
  //               rules={[
  //                 {
  //                   required: true,
  //                   message: 'Please input your password!',
  //                 },
  //               ]}
  //             >
  //               <Input.Password />
  //             </Form.Item>

  //             <Form.Item>
  //               <Button loading={false} block type="primary" htmlType="submit">
  //                 Submit
  //               </Button>
  //             </Form.Item>
  //           </Form>
  //           {/* <input
  //             type="submit"
  //             onClick={onSubmitHandler}
  //             value="Sign In"
  //             // className={`largeSignIn ${
  //             //   !disclaimer && LoginState.password.error && LoginState.username.error ? "disabled" : ""
  //             // }`}
  //             className={`largeSignIn ${formLoading ? 'disabled' : ''}`}
  //           /> */}
  //           <Link className="forgotPassword" to="/forgot-password">
  //             Forgot Password?
  //           </Link>

  //           <div className="separator-line mt8">
  //             <div className="line"></div>
  //             <span className="separator-text">or</span>
  //             <div className="line"></div>
  //           </div>

  //           <button
  //             className={`largeSignInWhite mt16  `}
  //             type="submit"
  //             onClick={onOktaHandler}
  //           >
  //             {/* <img
  //               src={sharedAsset.Login.oktaLogo}
  //               style={{ width: '25px', height: '25px', marginRight: '8px' }}
  //             /> */}
  //             Sign In With OKTA
  //           </button>
  //           {/* {formLoading ? (
  //             <div className="align-spinner">
  //               <Ellipsis
  //                 color={globalutils.spinnerColor()}
  //                 size={100}
  //                 sizeUnit={'px'}
  //               />
  //             </div>
  //           ) : (
  //             ''
  //           )} */}
  //         </div>

  //         <div className="login-footer">
  //           <div className="footer-disclaimer mb20">
  //             {' '}
  //             All intellectual property rights in or relating to the Software and
  //             all parts of the Software are and shall remain the exclusive
  //             property of Pinnacle Solutions LLC or its licensors.
  //           </div>
  //           <a
  //             href="http://pinnacle-solutionsllc.com/"
  //             target="_blank"
  //             rel="noopener noreferrer"
  //           >
  //             <img
  //               src={sharedAsset.Login.companyLogo}
  //               alt="Pinnacle Solutions LLC"
  //               style={{ width: 210 + 'px', height: 50 + 'px' }}
  //             />
  //           </a>
  //           <p>Proudly powered by </p>
  //           <img
  //             src={sharedAsset.Login.companyproductLogo}
  //             alt="Pinnacle Solutions LLC"
  //             style={{ width: 82 + 'px', height: 25 + 'px' }}
  //           />
  //         </div>
  //       </div>
  //       {getPopUp.showPopup && getPopUp.responseMessage ? (
  //         <NotificationBar
  //           type={getPopUp.type}
  //           message={getPopUp.responseMessage}
  //           canClose={getPopUp.canClose}
  //           flagClose={getPopUp.flagClose}
  //         />
  //       ) : null}
  //     </div>
  //   );
  // };

  // const APLogin = () => {
  //   const form = Form.useFormInstance();

  //   return (
  //     <div className="login-page-wrap flex-row">
  //       {/* <CommonSpinner visible={isLoading} /> */}
  //       <div className="login-image-wrap">
  //         <img src={sharedAsset.Login.bannerImage} alt="Welcome to Client Name" />
  //       </div>
  //       <div className="login-form-wrap flex-column">
  //         <a
  //           rel="noopener noreferrer"
  //           target="_blank"
  //           href="http://pinnacle-solutionsllc.com/"
  //           className="client-logo-wrap"
  //         ></a>

  //         <div className="form-wrap">
  //           <h1>Sign In</h1>

  //           <Form
  //             layout="vertical"
  //             initialValues={{
  //               remember: true,
  //             }}
  //             autoComplete="on"
  //           >
  //             <Form.Item
  //               label="Username"
  //               name="username"
  //               hasFeedback
  //               validateStatus="warning"
  //               help="The information is being validated..."
  //               rules={[
  //                 {
  //                   required: true,
  //                   message: 'Please input your username!',
  //                 },
  //               ]}
  //             >
  //               <Input
  //                 allowClear={true}
  //                 placeholder="<EMAIL>"
  //                 className="input-text-for-login"
  //               />
  //             </Form.Item>

  //             <Form.Item
  //               className="mb40"
  //               label="Password"
  //               name="password"
  //               rules={[
  //                 {
  //                   required: true,
  //                   message: 'Please input your password!',
  //                 },
  //               ]}
  //             >
  //               <Input.Password
  //                 placeholder="password"
  //                 className="input-text-for-login"
  //               />
  //             </Form.Item>

  //             <Form.Item className="mb40">
  //               <Button
  //                 style={{
  //                   border: 0,
  //                   width: '100%',
  //                   height: '40px',
  //                   fontSize: '12px',
  //                   fontWeight: 'bold',
  //                   textAlign: 'center',
  //                   letterSpacing: '0.05m',
  //                   textTransform: 'uppercase',
  //                   fontFamily: 'Roboto',
  //                 }}
  //                 loading={false}
  //                 block
  //                 type="primary"
  //                 htmlType="submit"
  //               >
  //                 Sign In
  //               </Button>
  //             </Form.Item>
  //           </Form>

  //           <Link className="forgotPassword" to="/forgot-password">
  //             Forgot Password?
  //           </Link>

  //           <div className="separator-line mt8">
  //             <div className="line"></div>
  //             <span className="separator-text">or</span>
  //             <div className="line"></div>
  //           </div>

  //           <button className={`largeSignInWhite mt16  `} type="submit">
  //             Sign In With OKTA
  //           </button>
  //         </div>

  //         <div className="login-footer">
  //           <div className="footer-disclaimer mb20">
  //             {' '}
  //             All intellectual property rights in or relating to the Software and
  //             all parts of the Software are and shall remain the exclusive
  //             property of Pinnacle Solutions LLC or its licensors.
  //           </div>
  //           <a
  //             href="http://pinnacle-solutionsllc.com/"
  //             target="_blank"
  //             rel="noopener noreferrer"
  //           >
  //             <img
  //               src={sharedAsset.Login.companyLogo}
  //               alt="Pinnacle Solutions LLC"
  //               style={{ width: 210 + 'px', height: 50 + 'px' }}
  //             />
  //           </a>
  //           <p>Proudly powered by </p>
  //           <img
  //             src={sharedAsset.Login.companyproductLogo}
  //             alt="Pinnacle Solutions LLC"
  //             style={{ width: 82 + 'px', height: 25 + 'px' }}
  //           />
  //         </div>
  //       </div>
  //     </div>
  //   );
  // };

  // export { APLogin };
  // Adjust the import path as necessary

  // const APLogin = () => {
  const [form] = Form.useForm();

  const [loginFailed, setLoginFailed] = useState(false);

  const [invalid, setInvalid] = useState('');

  const { message } = useSelector((store) => store.alert);

  useEffect(() => {
    setTimeout(() => {
      setLoginFailed(false);
    }, 1000);
    if (message) {
      setInvalid(true);
    } else {
      setInvalid(false);
    }
  }, [loggingIn, loggedIn, loginFailed, message]);

  // const onFinish = (values) => {
  //   setLoginFailed(true);
  //   console.log('Success:', values);
  //   let AP_historyCopy =
  //     AP_history?.location?.state === undefined
  //       ? null
  //       : AP_history?.location?.state?.from?.pathname;

  //   dispatch(
  //     userActions.login(values.username, values.password, AP_historyCopy, '')
  //   );
  // };
  const formFields = [
    {
      label: 'Username',
      name: 'username',
      rules: [{ required: true, message: 'Please input your username!' }],
      hasFeedback: true,
      component: (
        <Input
          placeholder="<EMAIL>"
          className="input-text-for-login"
        />
      ),
      help: invalid ? 'Invalid username!' : null,
      validateStatus: invalid ? 'error' : undefined,
    },
    {
      label: 'Password',
      name: 'password',
      rules: [{ required: true, message: 'Please input your password!' }],
      hasFeedback: true,
      component: (
        <Input.Password
          placeholder="password"
          className="input-text-for-login"
        />
      ),
      help: invalid ? 'Invalid password!' : null,
      validateStatus: invalid ? 'error' : undefined,
    },
  ];

  return (
    <div className="login-page-wrap flex-row">
      <CommonSpinner visible={isLoading} />
      <div className="login-image-wrap">
        <img src={sharedAsset.Login.bannerImage} alt="Welcome to Client Name" />
      </div>
      <div className="login-form-wrap flex-column">
        <a
          rel="noopener noreferrer"
          target="_blank"
          href="http://pinnacle-solutionsllc.com/"
          className="client-logo-wrap"
        ></a>

        <div className="form-wrap">
          <h1>Sign In</h1>
          <Form
            form={form}
            layout="vertical"
            initialValues={{ remember: true }}
            autoComplete="on"
            onFinish={onFinish}
          >
            {formFields.map((field) => (
              <Form.Item
                key={field.name}
                label={field.label}
                name={field.name}
                hasFeedback={field.hasFeedback}
                validateStatus={field.validateStatus}
                help={field.help}
                rules={field.rules}
                className={field.className}
              >
                {field.component}
              </Form.Item>
            ))}

            <Form.Item className="mb40">
              <Button
                style={{
                  border: 0,
                  width: '100%',
                  height: '40px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  letterSpacing: '0.05m',
                  textTransform: 'uppercase',
                  fontFamily: 'Roboto',
                }}
                loading={loginFailed}
                block
                type="primary"
                htmlType="submit"
              >
                Sign In
              </Button>
            </Form.Item>
          </Form>

          <Link className="forgotPassword" to="/forgot-password">
            Forgot Password?
          </Link>

          <div className="separator-line mt8">
            <div className="line"></div>
            <span className="separator-text">or</span>
            <div className="line"></div>
          </div>

          <button className="largeSignInWhite mt16" type="button">
            Sign In With OKTA
          </button>
        </div>

        <div className="login-footer">
          <div className="footer-disclaimer mb20">
            {' '}
            All intellectual property rights in or relating to the Software and
            all parts of the Software are and shall remain the exclusive
            property of Pinnacle Solutions LLC or its licensors.
          </div>
          <a
            href="http://pinnacle-solutionsllc.com/"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src={sharedAsset.Login.companyLogo}
              alt="Pinnacle Solutions LLC"
              style={{ width: 210 + 'px', height: 50 + 'px' }}
            />
          </a>
          <p>Proudly powered by </p>
          <img
            src={sharedAsset.Login.companyproductLogo}
            alt="Pinnacle Solutions LLC"
            style={{ width: 82 + 'px', height: 25 + 'px' }}
          />
        </div>
      </div>
    </div>
  );
};

export { APLogin };
