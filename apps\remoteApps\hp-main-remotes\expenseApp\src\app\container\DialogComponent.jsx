/* eslint-disable react/jsx-no-useless-fragment */

import React, { memo } from 'react';
import Modal from 'react-modal';
import { Input } from '@hp/components';
const ExpenseDialogComponent = (props) => {
  const { formElements, isModal, isModalClose } = props;
  return (
    <>
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={isModalClose}
          className="modal-close icon-close"
          style={{ fontSize: 20 + 'px' }}
        ></div>
        <div className="page-title">
          {(formElements && formElements.uniqueKey) || ''}
        </div>
        <div className="flex-row-wrap three-col-layout">
          {formElements &&
          formElements.formSubDetailsInternalDTOList &&
          formElements.formSubDetailsInternalDTOList.length
            ? formElements.formSubDetailsInternalDTOList.map(
                (subLevelElements, subIndex) => {
                  return (
                    <Input
                      key={subIndex}
                      formType={subLevelElements}
                      dataTablePagination={false}
                      // onChangeHandler={(onChangeFunction, e, uniqueKey) => onChangeHandlingFunctions[onChangeFunction](e, uniqueKey, index, element)}
                    />
                  );
                }
              )
            : null}
        </div>
      </Modal>
    </>
  );
};

export default memo(ExpenseDialogComponent);
