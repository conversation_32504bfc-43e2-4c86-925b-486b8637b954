/* eslint-disable react/no-unescaped-entities */
/* eslint-disable react/display-name */
import { Button } from '@hp/components';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import Modal from 'react-modal';

const ECRFromECRPopup = forwardRef(
  (
    {
      handleCreateEcoFromEcr,
      handleFormDetails,
      showModal,
      setShowModal,
      ...props
    },
    ref
  ) => {
    const [notify, setnotify] = useState(null);
    useEffect(() => {
      if (showModal) {
        setnotify(null);
      }
    }, [showModal]);

    useImperativeHandle(ref, () => ({
      funcToSetResMessageInModal: (type, resMessage) => {
        setnotify({ type, resMessage });
      },
    }));

    return (
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={showModal}
      >
        <div
          style={{
            position: 'sticky',
            top: '0px',
            marginLeft: '-40px',
            zIndex: '999',
            width: 'calc(100% + 40px)',
            height: '40px',
            background: '#fff',
            transform: 'translate(40px, -40px)',
          }}
        >
          <div
            style={{
              fontSize: '16px',
              lineHeight: '16px',
              fontWeight: '500',
              color: 'rgb(32, 32, 42)',
              letterSpacing: '-0.02em',
              borderBottom: '1px solid rgb(232, 232, 233)',
              paddingTop: '20px',
              width: 'calc(100% - 40px)',
            }}
          >
            Approved ECR's
          </div>
          <span
            className="modal-close icon-close"
            onClick={() => {
              setShowModal(false);
            }}
            style={{ fontSize: 20 + 'px' }}
          ></span>
        </div>
        <div className="boxed mb20" style={{ marginTop: '-30px' }}>
          <div>{handleFormDetails()}</div>
        </div>

        <div
          style={{
            position: 'sticky',
            bottom: '0px',
            zIndex: '999',
            height: '60px',
            background: '#fff',
            transform: 'translateY(40px)',
          }}
        >
          {notify != null ? (
            <div
              className={['notification-bar', 'type-' + notify.type].join(' ')}
              style={{
                marginBottom: '10px',
              }}
            >
              <i className="icon-close" onClick={() => setnotify(null)}></i>
              {notify.resMessage}
            </div>
          ) : (
            ''
          )}
          <Button
            className="default mr20"
            onClick={() => handleCreateEcoFromEcr()}
          >
            Create ECO From ECR
          </Button>
        </div>
      </Modal>
    );
  }
);

export default ECRFromECRPopup;
