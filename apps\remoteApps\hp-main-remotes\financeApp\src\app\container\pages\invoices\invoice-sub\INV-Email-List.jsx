/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable eqeqeq */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Collapse, Pagination } from 'antd';
import { INVEmailDetailsComponent } from './INVEmailDetailsComponent';
import { CaretRightOutlined } from '@ant-design/icons';
import { AP_USER } from '@hp/constants';
import { getInvEmailDetailsByInOutTypeAction } from '@hp/mainstore';
import { globalutils } from '@hp/components';/**
 * <AUTHOR> V
 * @see This Component display the complete INV-Email-List
 * */
function INVEmailList(props) {
  const { invEmailTreeList } = props;
  let user = globalutils.getDataFromStorage('all');
  const dispatch = useDispatch();

  //* Pagination
  const [page, setPage] = useState(1);
  const [numEachPage, setNumEachPage] = useState(5);
  const [minMaxValue, setMinMaxValue] = useState({
    minValue: 0,
    maxValue: numEachPage,
  });
  const [activeKey, setActiveKey] = useState(null);

  //& *************************** Single Email Details Call ***********
  const [isSingleEmailDtlLoading, setIsSingleEmailDtlLoading] = useState(true);
  const [key, setKey] = useState([]);

  const { singleInvEmailDetails } = useSelector((store) => store.email);

  useEffect(() => {
    if (key.length > 0) {
      let data = invEmailTreeList.slice(
        minMaxValue.minValue,
        minMaxValue.maxValue
      )[+key[0]];

      setIsSingleEmailDtlLoading(true);
      dispatch(
        getInvEmailDetailsByInOutTypeAction({
          docId: data?.documentId,
          emailId: data?.emailId,
          in_out_type:
            data?.icon && data?.icon == 'in-coming'
              ? 'in'
              : data?.icon && data?.icon == 'out-going'
              ? 'out'
              : '',
          type: 'subscribe',
        })
      );
    }

    return () => {
      dispatch(
        getInvEmailDetailsByInOutTypeAction({
          docId: null,
          emailId: null,
          in_out_type: null,
          type: 'unsubscribe',
        })
      );
    };
  }, [key]);
  //& *****************************************************************

  const showTotal = (total) => `Total ${total} Email(s) : `;

  useEffect(() => {
    setPage(1);
  }, [numEachPage]);

  useEffect(() => {
    setMinMaxValue({
      minValue: (page - 1) * numEachPage,
      maxValue: page * numEachPage,
    });
  }, [page, numEachPage]);

  const handleMinMaxValueChange = (value, pageSize) => {
    setNumEachPage(pageSize);
    setPage(value);
  };

  const [isExpanded, setIsExpanded] = useState(false);

  const handalChange = (key) => {
    setKey(key);
    let activeIndex = +key[0];
    setActiveKey(activeIndex);
    if (activeIndex >= 0) {
      setIsExpanded(true);
    } else {
      setIsExpanded(false);
    }
  };

  return (
    <div className="mr20 " style={{ width: 100 + '%' }}>
      {' '}
      <div className=" page-sub-title">Email</div>
      <div
        className="inv-email-list-auto-height"
        style={{ borderRadius: '0px', marginTop: '5px' }}
      >
        <div
          className="inv-email-height-fix "
          style={{ borderRadius: '0px', marginTop: '5px' }}
        >
          {invEmailTreeList && invEmailTreeList?.length > 0 ? (
            <div>
              <div className="email-dropdown-div"></div>

              <div className="force-full-width">
                <Collapse
                  // key={'1'}
                  bordered={true}
                  ghost={true}
                  size="small"
                  accordion
                  onChange={handalChange}
                  style={{
                    fontFamily: 'Roboto',
                  }}
                  expandIcon={({ isActive }) => (
                    <CaretRightOutlined
                      style={{
                        marginTop: '40px',
                        color: isActive ? 'white' : '',
                      }}
                      rotate={isActive ? 90 : 0}
                    />
                  )}
                  items={INVEmailDetailsComponent(
                    invEmailTreeList.slice(
                      minMaxValue.minValue,
                      minMaxValue.maxValue
                    ),
                    activeKey,
                    isExpanded,
                    singleInvEmailDetails,
                    isSingleEmailDtlLoading,
                    setIsSingleEmailDtlLoading
                  )}
                />

                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    paddingBottom: '20px',
                  }}
                >
                  <Pagination
                    size="small"
                    className="mt24"
                    defaultPageSize={numEachPage}
                    pageSizeOptions={[5, 10]}
                    total={invEmailTreeList?.length}
                    showTotal={showTotal}
                    current={page}
                    showSizeChanger
                    onChange={handleMinMaxValueChange}
                  />
                </div>
              </div>
            </div>
          ) : (
            <div
              style={{
                textAlign: 'center',
                // padding: '10px',
                fontSize: '13px',
              }}
              className="card-10"
            >
              There are no records to display.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default INVEmailList;
