const { nxE2EPreset } = require('@nx/cypress/plugins/cypress-preset');

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    ...nxE2EPreset(__filename, {
      cypressDir: 'cypress',
      webServerCommands: {
        default: 'nx run mainAuthApp:serve',
        production: 'nx run mainAuthApp:serve:production',
      },
      ciWebServerCommand: 'nx run mainAuthApp:serve-static',
    }),
  },
});
