module.exports = {
  displayName: 'auth-module',
  preset: '../../../../jest.preset.js',
  transform: {
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nx/react/babel'] }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  coverageDirectory:
    '../../../../coverage/apps/remoteApps/hp-main-remotes/authModule',
  moduleNameMapper: {
    /////////////*  module mapping syntax - remove the regex slashes */////////////
    '^@hp/assets$': '<rootDir>/../../../../__mocks__/hp-assets.js',
    '^@hp/auth$': '<rootDir>/../../../../libs/hp-auth/src/index.js',
    '^@hp/components/(.*)$':
      '<rootDir>/../../../../libs/hp-components/src/lib/$1',
    '^@hp/constants$': '<rootDir>/../../../../libs/hp-constants/src/index.js',
    '^@hp/mainstore$':
      '<rootDir>/../../../../libs/store/hp-main-store/src/redux',
    '^@hp/services$':
      '<rootDir>/../../../../libs/store/hp-main-store/src/service/index.js',
    '^@hp/utils$': '<rootDir>/../../../../libs/hp-util/src/index.js',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/../../../../__mocks__/fileMock.js',
  },
  setupFilesAfterEnv: ['<rootDir>/setupTests.js'],
  testEnvironment: 'jsdom',
};
