/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/**
 * <AUTHOR> Samuel
 * @email <EMAIL>
 * @create date 27-05-2021 15:40:00
 * @modify date 27-05-2021 15:40:01
 * @desc [description]
 */
import React, { useLayoutEffect, useEffect, useState } from 'react';
import { useRef } from 'react';
import {
  ButtonCommon,
  Input,
  HPDigitalClock,
  globalutils,
} from '@hp/components';
import { AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import { showDialog } from '@hp/mainstore';
import {
  addDateAndTimeFormDetails,
  fetchUtcTime,
  getDateAndTimeFormDetails,
  timeZoneDate,
  resetaddDateAndTimeFormDetails,
  resettimeZoneDate,
} from '@hp/mainstore';
import cloneDeep from 'lodash.clonedeep';

const DateAndTime = (props) => {
  const dispatch = useDispatch();
  var { dateAndTimeFormDetails, utcTimeStore, tzTimeStore } = useSelector(
    (store) => store.settings
  );

  const { dateAndTimeFormatSaved } = useSelector((store) => store.admin);

  let user = globalutils.getDataFromStorage('all');
  const clientId = user.clientId;

  const [formDetails, setFormDetails] = useState([]);
  const [tzTime, settzTime] = useState(null);
  const [dateString, setDateString] = useState(null);
  const [flag, setFlag] = useState(true);
  const [utcFormatteddate, setutcFormatteddate] = useState(new Date());

  const [clockAttributes, setClockAttributes] = useState({
    clientID: null,
    cCode: null,
    tZone: null,
  });
  let countryCode = '';
  useLayoutEffect(() => {
    if (dateAndTimeFormDetails && dateAndTimeFormDetails.value) {
      setFormDetails(dateAndTimeFormDetails.value);
      setFlag(true);
    }
  }, [dateAndTimeFormDetails]);

  useLayoutEffect(() => {
    if (tzTimeStore && tzTimeStore.value) {
      settzTime(tzTimeStore.value);
      setFlag(false);
    }
  }, [tzTimeStore]);

  useLayoutEffect(() => {
    if (utcTimeStore && utcTimeStore.value && utcTimeStore.value !== '') {
      var isoDateString = utcTimeStore.value;
      var startTime = new Date(isoDateString);
      startTime = new Date(
        startTime.getTime() + startTime.getTimezoneOffset() * 60000
      );

      const fDate = globalutils.formatDate(startTime, 'dd/mm/yyyy');
      setutcFormatteddate(fDate);
      setDateString(startTime);
    }
  }, [utcTimeStore]);

  // Clear Notification
  useLayoutEffect(() => {
    dispatch(fetchUtcTime());
    return () => {
      dispatch(resetaddDateAndTimeFormDetails());
      dispatch(resettimeZoneDate());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type: type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (dateAndTimeFormatSaved && dateAndTimeFormatSaved.value) {
      funcToSetResponseMessage(
        dateAndTimeFormatSaved.value.type,
        dateAndTimeFormatSaved.value.text
      );
    }
    dispatch(getDateAndTimeFormDetails(clientId));
  }, [dateAndTimeFormatSaved]);

  // Function to select Country Code
  const handleCountryCode = (event, uniqueKey) => {
    let tempArray1 = cloneDeep(formDetails);
    tempArray1.map((entry) => {
      entry.uniqueKey === 'fieldSet' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'countryCode') {
            countryCode = entry.comboDtoListMap[event.target.value];
            element.uniqueKey === uniqueKey
              ? (element.value = event.target.value)
              : '';
          } else if (element.uniqueKey === 'timeZone') {
            element.comboBoxOptions = countryCode;
            element.value = '';
          }
        });
    });
    // setClockAttributes({ ...clockAttributes, cCode: event.target.value });
    setFlag(false);
    settzTime(null);
    setFormDetails(tempArray1);
  };

  // Function to select Time Zone
  const handleTimeZone = (event, uniqueKey) => {
    let tempArray1 = cloneDeep(formDetails);
    tempArray1.map((entry) => {
      entry.uniqueKey === 'fieldSet' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.uniqueKey === uniqueKey
            ? (element.value = event.target.value)
            : '';
        });
    });

    setClockAttributes({ ...clockAttributes, tZone: event.target.value });
    setFormDetails(tempArray1);
    settzTime(null);
    dispatch(
      timeZoneDate({ timeZone: event.target.value, ClientID: clientId })
    );
  };

  // Function to select other Select Inputs
  const handleCombo = (event, uniqueKey) => {
    if (uniqueKey === 'clientName') {
      dispatch(getDateAndTimeFormDetails(event.target.value));
    } else {
      let tempArray1 = cloneDeep(formDetails);
      tempArray1.map((entry) => {
        entry.uniqueKey === uniqueKey ? (entry.value = event.target.value) : '';
      });

      let clientState = { ...clockAttributes };
      clientState.clientID = event.target.value;
      setClockAttributes(clientState);
      setFormDetails(tempArray1);
    }
  };

  const onChangeHandlingFunctions = {
    handleCountryCode,
    handleTimeZone,
    handleCombo,
  };

  // Save Button Function
  const handleSave = () => {
    let clientName = '';
    let dateFormat = '';
    let timeFormat = '';
    let dateTimeFormat = '';
    var timeZone = '';
    formDetails.map((entry) => {
      entry.uniqueKey === 'clientName' ? (clientName = entry.value) : '';
      entry.uniqueKey === 'dateFormat' ? (dateFormat = entry.value) : '';
      entry.uniqueKey === 'timeFormat' ? (timeFormat = entry.value) : '';
      entry.uniqueKey === 'dateTimeFormat'
        ? (dateTimeFormat = entry.value)
        : '';
      entry.uniqueKey === 'fieldSet' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.uniqueKey === 'timeZone' ? (timeZone = element.value) : '';
        });
    });
    dispatch(
      addDateAndTimeFormDetails({
        clientName: clientName,
        dateFormat: dateFormat,
        timeFormat: timeFormat,
        dateTimeFormat: dateTimeFormat,
        timeZone: timeZone,
      })
    );
  };

  // Clear Button Function
  const handleClear = () => {
    let tempArray1 = cloneDeep(formDetails);
    tempArray1.map((entry) => {
      entry.value = '';
      entry.uniqueKey === 'fieldSet' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.value = '';
        });
    });
    setFormDetails(tempArray1);
  };

  const functionsName = {
    handleSave,
    handleClear,
  };

  // Function to Render Form Elements

  const previousTimeZone = useRef(null); // Store previously dispatched timeZone

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'fieldSet') {
            let countryCodeVal = null;
            let timeZoneEntry = null;

            const updatedFormSubDetails =
              element.formSubDetailsInternalDTOList.map((entry) => {
                if (entry.uniqueKey === 'countryCode') {
                  countryCodeVal = entry.value;
                } else if (entry.uniqueKey === 'timeZone' && countryCodeVal) {
                  timeZoneEntry = { ...entry };
                  return {
                    ...entry,
                    comboBoxOptions:
                      element.comboDtoListMap[countryCodeVal] || [],
                  };
                }

                return entry;
              });

            // ✅ Dispatch only if timeZone has changed
            if (
              flag &&
              timeZoneEntry &&
              timeZoneEntry.value &&
              previousTimeZone.current !== timeZoneEntry.value
            ) {
              dispatch(
                timeZoneDate({
                  timeZone: timeZoneEntry.value,
                  ClientID: clientId,
                })
              );
              previousTimeZone.current = timeZoneEntry.value; // Update ref after dispatch
            }

            return (
              <div key={index}>
                <fieldset style={{ marginBottom: '20px' }}>
                  {formControlsBinding(updatedFormSubDetails)}
                </fieldset>
              </div>
            );
          } else {
            return (
              <Input
                isEditable={'notShowing'}
                key={index.toString()}
                formType={element}
                disabledFlag={
                  clientId !== 1 && element.uniqueKey === 'clientName'
                }
                onChangeHandler={(val, event) =>
                  onChangeHandlingFunctions[val.onChangeFunction](
                    event,
                    val.uniqueKey
                  )
                }
              />
            );
          }
        })
      : null;
  };

  return (
    <>
      <div className="page-title">Date & Time</div>
      <div className="two-col-layout mb20">
        <div className="col">
          {formDetails && formDetails.length
            ? formControlsBinding(formDetails)
            : ''}
        </div>

        <div className="col  mb20" style={{ fontSize: 0.8 + 'rem' }}>
          {formDetails && formDetails.length && dateString !== null ? (
            <HPDigitalClock
              utcFormatted={utcFormatteddate}
              dateString={dateString}
              title={'Date & Time'}
              detailSection={false}
            />
          ) : (
            ''
          )}
          <div className="mb20"></div>
          &nbsp;
          {formDetails && formDetails.length && !flag && tzTime !== null ? (
            <HPDigitalClock
              dateString={tzTime}
              detailSection={true}
              title={'Date & Time'}
            />
          ) : (
            ''
          )}
        </div>
      </div>

      <ButtonCommon functionsName={functionsName} />
    </>
  );
};

export { DateAndTime };
