import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
// import { dataTableServiceProvider } from '@hp/components';

function SubDataTable(props) {
  const [subRow] = useState(props.subData);
  const [subList, setsubList] = useState([]);
  // const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const columns = [
    { name: '#', selector: 'serialNo', width: '10%' },
    { name: 'Description', selector: 'itemName' },
    { name: 'Part #', selector: 'partNumber' },
    { name: 'User', selector: 'userName' },
    { name: 'Time Stamp', selector: 'eventTime' },
  ];

  const toggle = (row) => {
    if (
      row.pdmItemDeletedEventLogSubDtoList &&
      row.pdmItemDeletedEventLogSubDtoList.length
    ) {
      setsubList(row.pdmItemDeletedEventLogSubDtoList);
    }
  };
  // const DataTableEventHandler = (e) => {
  //   const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
  //     e.serialNo,
  //     'serialNo'
  //   );
  // setConditionalStyles(styleAttribute);
  // };

  return (
    <div style={{ margin: '8px', padding: '8px' }}>
      <DataTable
        columns={columns}
        highlightOnHover={true}
        noHeader={true}
        responsive={false}
        dense={true}
        className="mb16 main-table force-to-fill-width"
        data={subRow}
        expandableRows={true}
        pagination={subList && subList.length >= 10 ? true : false}
        defaultSortAsc={true}
        paginationDefaultPage={1}
        paginationResetDefaultPage={true}
        paginationPerPage={10}
        // onRowClicked={(e, index) => DataTableEventHandler(e, index)}
        // conditionalRowStyles={conditionalRowStyles}
        expandableRowDisabled={(row) =>
          !row.pdmItemDeletedEventLogSubDtoList.length
        }
        onRowExpandToggled={(toggledState, row) => toggle(row)}
        expandableRowsComponent={<SubDataTable subData={subList} />}
      />
    </div>
  );
}

export default SubDataTable;
