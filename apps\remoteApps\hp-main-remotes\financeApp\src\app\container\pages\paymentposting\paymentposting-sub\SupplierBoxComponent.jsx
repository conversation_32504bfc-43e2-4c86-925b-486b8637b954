/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { getSuppliersById } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { TextInput } from '@hp/components';
const SupplierBoxComponent = (props) => {
  const dispatch = useDispatch();
  const { supplierdetails, supplierNameCombo } = useSelector(
    (store) => store.suppliers
  );
  const suppId = props.suppId ? props.suppId : '';

  // const [isDisabled, setisDisabled] = React.useState(true);
  // const [supplierCombo, setSupplierCombo] = useState();
  // const [filterString, setFilterString] = useState({
  //   filterStr: "",
  // });
  const [state, setState] = useState({
    number: '',
    telephone: '',
    name: '',
    mobile: '',
    code: '',
    fax: '',
    email: '',
    website: '',
    primaryAddressId: '',
    primaryCountry: '',
    primaryAddress: '',
    primaryState: '',
    primaryCity: '',
    primaryPostcode: '',
    deliveryAddressId: '',
    deliveryAddress: '',
    deliveryCountry: '',
    deliveryState: '',
    deliveryCity: '',
    deliveryPostcode: '',
  });
  const [supplierAcc, setSupplierAcc] = useState({
    suppAccounts: {
      currency_id: '',
      currency: '',
      bankGlId: '',
      bankgl: '',
      accstatus: '',
      glacc: '',
      gl_acc_id: '',
      ctrl_gl_id: '',
      ctrl_gl_name: '',
      area: '',
      accgroup: '',
      accAreaId: '',
      department: '',
    },
  });
  useEffect(() => {
    dispatch(getSuppliersById(suppId));
  }, []);

  useEffect(() => {
    if (supplierNameCombo) {
      // const Options = supplierNameCombo.map((value) => {
      //   return {
      //     value: value.supplierId,
      //     display: value.supplierName,
      //   };
      // });
      // setSupplierCombo(Options);
    }
  }, [supplierNameCombo]);

  useEffect(() => {
    if (supplierdetails.value) {
      setState({
        number: supplierdetails.value.number,
        telephone: supplierdetails.value.telephone,
        name: supplierdetails.value.name,
        mobile: supplierdetails.value.mobile,
        code: supplierdetails.value.code,
        fax: supplierdetails.value.fax,
        email: supplierdetails.value.email,
        website: supplierdetails.value.website,
        primaryAddressId: supplierdetails.value.primaryAddressBean.address_id,
        primaryAddress: supplierdetails.value.primaryAddressBean.address,
        primaryCity: supplierdetails.value.primaryAddressBean.city,
        primaryState: supplierdetails.value.primaryAddressBean.state,
        primaryPostcode: supplierdetails.value.primaryAddressBean.post_code,
        primaryCountry: supplierdetails.value.primaryAddressBean.country,
        deliveryAddressId: supplierdetails.value.deliveryAddressBean.address_id,
        deliveryAddress: supplierdetails.value.deliveryAddressBean.address,
        deliveryCity: supplierdetails.value.deliveryAddressBean.city,
        deliveryState: supplierdetails.value.deliveryAddressBean.state,
        deliveryPostcode: supplierdetails.value.deliveryAddressBean.post_code,
        deliveryCountry: supplierdetails.value.deliveryAddressBean.country,
      });
      setSupplierAcc({
        bankgl: supplierdetails.value.bankGlBean.bank_gl_code,
        bankGlId: supplierdetails.value.bankGlBean.bank_gl_id,
        currency_id: supplierdetails.value.supplierAccountsBean.currency_id,
        currency: supplierdetails.value.supplierAccountsBean.currency,
        accstatus: supplierdetails.value.supplierAccountsBean.account_status,
        glacc: supplierdetails.value.glAccountsBean.gl_acc_name,
        gl_acc_id: supplierdetails.value.glAccountsBean.gl_acc_id,
        area: supplierdetails.value.accountAreaBean.acc_area_name,
        accAreaId: supplierdetails.value.accountAreaBean.acc_area_id,
        accgroup: supplierdetails.value.bankGlBean.bank_gl_name,
        department: supplierdetails.value.accountAreaBean.acc_area_desc,
      });
      if (supplierdetails.value.controlGLBean !== null) {
        setSupplierAcc({
          ctrl_gl_id: supplierdetails.value.controlGLBean.ctrl_gl_id
            ? supplierdetails.value.controlGLBean.ctrl_gl_id
            : '',
          ctrl_gl_name: supplierdetails.value.controlGLBean.ctrl_gl_name,
        });
      }
    }
  }, [supplierdetails]);
  // const SupplierListing = [
  //   { name: "Number", selector: "number", sortable: true },
  //   { name: "Name", selector: "supplier", sortable: true },
  //   { name: "Code", selector: "code", sortable: true },
  //   { name: "State", selector: "state", sortable: true },
  //   { name: "Country", selector: "country", sortable: true },
  // ];
  // const handleChange = (event) => {
  //   setFilterString({ filterStr: event.target.value });
  // };

  return (
    <div className="boxed low-contrast">
      <h1 className="page-title">Supplier</h1>
      <div className="mb20"></div>
      <Tabs className="mb24">
        <TabList>
          <Tab>Details</Tab>
          <Tab>Accounts</Tab>
        </TabList>
        <TabPanel>
          <div className="flex-row two-col-layout">
            <div className="col">
              <TextInput
                label="Number"
                name="number"
                className="mb20"
                value={state.number}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                label="Name"
                name="name"
                className="mb20"
                value={state.name}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                label="Code"
                name="code"
                className="mb20"
                value={state.code}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                label="Email"
                name="email"
                className="mb20"
                value={state.email}
                // onChange={(e) => handleAll(e)}
              />
            </div>
            <div className="col">
              <TextInput
                label="Telephone"
                name="telephone"
                className="mb20"
                value={state.telephone}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                label="Mobile"
                name="mobile"
                className="mb20"
                value={state.mobile}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="fax"
                label="Fax"
                className="mb20"
                value={state.fax}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="website"
                label="Website"
                className="mb20"
                value={state.website}
                // onChange={(e) => handleAll(e)}
              />
            </div>
          </div>
          <div className="flex-row two-col-layout">
            <fieldset className="col">
              <legend>Primary Address</legend>
              <TextInput
                name="primaryAddress"
                label="Address"
                className="mb20"
                value={state.primaryAddress}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="primaryPostcode"
                label="Post-Code"
                className="mb20"
                value={state.primaryPostcode}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="primaryCountry"
                label="Country"
                className="mb20"
                value={state.primaryCountry}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="primaryState"
                label="State"
                className="mb20"
                value={state.primaryState}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="primaryCity"
                label="City"
                className="mb20"
                value={state.primaryCity}
                // onChange={(e) => handleAll(e)}
              />
            </fieldset>
            <fieldset className="col">
              <legend>Delivery Address</legend>
              <TextInput
                name="deliveryAddress"
                label="Address"
                className="mb20"
                value={state.deliveryAddress}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="deliveryPostcode"
                label="Post-Code"
                className="mb20"
                value={state.deliveryPostcode}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="deliveryCountry"
                label="Country"
                className="mb20"
                value={state.deliveryCountry}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="deliveryState"
                label="State"
                className="mb20"
                value={state.deliveryState}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="deliveryCity"
                label="City"
                className="mb20"
                value={state.deliveryCity}
                // onChange={(e) => handleAll(e)}
              />
            </fieldset>
          </div>
        </TabPanel>
        <TabPanel>
          <div className="flex-row two-col-layout">
            <fieldset className="col">
              <legend>Supplier Account</legend>
              {/* <Select
                style={{ width: 250 + "px" }}
                label="Currency"
                name="currency"
                className="mb20"
                onChange={(e) => getCurrency(e.target.value)}
                options={currencyCombo}
              /> */}
              <TextInput
                name="bankgl"
                label="BankGl"
                className="mb20"
                value={supplierAcc.bankgl}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="accstatus"
                label="Account Status"
                className="mb20"
                value={supplierAcc.accstatus}
                // onChange={(e) => handleAll(e)}
              />
            </fieldset>
            <fieldset className="col">
              <legend>GL Settings</legend>
              <TextInput
                name="glacc"
                label="Gl Account"
                className="mb20"
                value={supplierAcc.glacc}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="ctrl_gl_name"
                label="Control GL"
                className="mb20"
                value={supplierAcc.ctrl_gl_name}
                // onChange={(e) => handleAll(e)}
              />
            </fieldset>
          </div>
          <div className="mb20"></div>
          <div className="flex-row two-col-layout">
            <fieldset className="col">
              <legend>Account Dimensions</legend>
              <TextInput
                name="area"
                label="Area"
                className="mb20"
                value={supplierAcc.area}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="accgroup"
                label="Account Group"
                className="mb20"
                value={supplierAcc.accgroup}
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="department"
                label="Department"
                className="mb20"
                value={supplierAcc.department}
                // onChange={(e) => handleAll(e)}
              />
            </fieldset>
            <fieldset className="col">
              <legend>Purchase Order/Approval Settings</legend>
              <TextInput
                name="Require Order"
                label="Require Order"
                className="mb20"
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="Require Approval"
                label="Require Approval"
                className="mb20"
                // onChange={(e) => handleAll(e)}
              />
              <TextInput
                name="Approver"
                label="Approver"
                className="mb20"
                // onChange={(e) => handleAll(e)}
              />
            </fieldset>
          </div>
        </TabPanel>
      </Tabs>
    </div>
  );
};
export { SupplierBoxComponent };
