/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> sherlin
 * @email <EMAIL>
 */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @description: migration
 */
import React, { useState, useLayoutEffect, useEffect, useRef } from 'react';
import { Input } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { CommonSpinner } from '@hp/components';
import { AP_file_url } from '@hp/constants';
import { dataTableServiceProvider } from '@hp/components';
import { cloneDeep } from 'lodash';
import {
  bbMPNviewSearch,
  bbMPNSearchSubList,
  bbForms,
  bbMPNPreviewLists,
  bbMPNEmailPreview,
} from '@hp/mainstore';
const Search = () => {
  const dispatch = useDispatch();

  const {
    bbFormDetails,
    loading,
    mpnFiles,
    mpnSearchSubFiles,
    emailPreviewGenerated,
    bbMPNPreviewList,
  } = useSelector((store) => store.bb);

  //=========================================Local States Declaration Section Begins ===========================================//
  const [formInputs, setFormInputs] = useState([]);
  const [form, setForm] = useState();
  const [emailPreviewForm, setEmailPreviewForm] = useState();
  const [excelPreviewPath, setExcelPreviewPath] = useState();
  const [isSearch, setIsSearch] = useState({
    isSearchDisabled: true,
    isClientValue: '',
    isDateValue: '',
    addOnchange: false,
  });
  const [productFileMasterId, setProductFileMasterId] = useState(null);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [conditionalRowStylesTwo, setConditionalStylesTwo] = useState([]);

  const [emailPreview, setEmailPreview] = useState();

  //=========================================Local States Declaration Section Ends===========================================//
  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Component did mount First call for this component.
     */

    dispatch(bbForms('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
    };
  }, []);

  useLayoutEffect(() => {
    if (emailPreviewGenerated && emailPreviewGenerated.value) {
      setEmailPreview(emailPreviewGenerated.value);
    } else {
      setEmailPreview('');
    }
  }, [emailPreviewGenerated]);
  useLayoutEffect(() => {
    if (bbMPNPreviewList && bbMPNPreviewList.value?.path) {
      setExcelPreviewPath(bbMPNPreviewList.value.path);

      setFormInputs((prevInputs) => {
        const updatedInputs = cloneDeep(prevInputs);
        const previewIndex = updatedInputs.findIndex(
          (item) => item.uniqueKey === 'tablePreview'
        );

        if (previewIndex !== -1) {
          updatedInputs[previewIndex].value = [
            { fileName: bbMPNPreviewList.value.fileName },
          ];
        }

        return updatedInputs;
      });
    }
  }, [bbMPNPreviewList]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: form response.
     */
    if (
      bbFormDetails &&
      bbFormDetails !== undefined &&
      bbFormDetails.value &&
      bbFormDetails.value.length
    ) {
      bbFormDetails.value.map((items) => {
        if (items.uniqueKey === 'page-title') {
          if (items.parameters === 'mpnSearch') {
            let emailPreviewDetails = bbFormDetails.value.filter((e) => {
              return e.type === 'EmailPreview';
            });

            let formControlDetails = bbFormDetails.value.filter((e) => {
              return e.type !== 'EmailPreview';
            });

            //const withOutemaiklPreview = filter(item=>item.typ!==emailPreview"")
            //check whether emial previwe present or not,1 ,-1
            //bbformdeatils[2];

            var clientID = null;
            var selectedDate = null;
            const form = bbFormDetails?.value;
            if (form && form.length) {
              const headerControls = form.filter(
                (e) => e.uniqueKey === 'page-title'
              );
              if (headerControls && headerControls.length) {
                headerControls[0].formSubDetailsInternalDTOList &&
                  headerControls[0].formSubDetailsInternalDTOList.length &&
                  headerControls[0].formSubDetailsInternalDTOList.map(
                    (element) => {
                      if (element.uniqueKey === 'client-control') {
                        clientID = element.value;
                      } else if (element.uniqueKey === 'date-control') {
                        selectedDate = element.value;
                      }
                    }
                  );
                setIsSearch({
                  ...isSearch,
                  isDateValue: selectedDate,
                  isClientValue: clientID,
                });
              }
            }

            setEmailPreviewForm(emailPreviewDetails);
            setForm(formControlDetails);
            setFormInputs(formControlDetails);
          }
        }
      });
    }
  }, [bbFormDetails]);

  useLayoutEffect(() => {
    if (mpnFiles?.value) {
      const stateValues = cloneDeep(formInputs); // deep clone to avoid mutating frozen objects
      const foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'fileTable'
      );

      if (foundIndex !== -1) {
        if (mpnFiles?.value.length) {
          stateValues[foundIndex].value = mpnFiles.value;
        } else {
          const subIndex = stateValues.findIndex(
            (e) => e.uniqueKey === 'tablePreview'
          );
          stateValues[foundIndex].value = [];
          if (subIndex !== -1) {
            stateValues[subIndex].value = [];
          }
        }

        setFormInputs(stateValues);
        setProductFileMasterId(null);
      }
    }
  }, [mpnFiles]);
  useLayoutEffect(() => {
    if (mpnSearchSubFiles?.value) {
      const stateValues = cloneDeep(formInputs); // deep clone is safer
      const foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'tablePreview'
      );

      if (foundIndex !== -1) {
        stateValues[foundIndex].value = mpnSearchSubFiles.value;
        setFormInputs(stateValues); // always update
      }

      if (mpnSearchSubFiles.value.length === 0) {
        setEmailPreview('');
      }
    }
  }, [mpnSearchSubFiles]);

  const searchTableCell = (terms) => {
    if (terms.selector === 'status') {
      return function displayCell(row) {
        const statusColor =
          row.status === 'Sent'
            ? 'green'
            : row.status === 'Queued'
            ? 'yellow'
            : row.status === 'Failed'
            ? 'red'
            : row.status === 'Draft'
            ? 'blue'
            : '';
        return (
          <div className={`react-tbl-custom-status-cell ${statusColor}`}>
            {row?.status ? row.status : ''}
          </div>
        );
      };
    }
  };
  const forms = (formControl) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function to Render Form Element.
     */
    return formControl
      ? formControl.map((element, index) => {
          if (element.uniqueKey === 'page-title') {
            return (
              <div key={index}>
                <Input formType={element} />
                <div
                  className="two-col-layout-right-30"
                  style={{ marginBottom: '-40px' }}
                >
                  <div
                    className="three-col-layout"
                    // style={{
                    //   position: 'absolute',
                    //   top: '-5px',
                    //   left: '250px',
                    // }}
                  >
                    {forms(element.formSubDetailsInternalDTOList)}
                  </div>
                  <div></div>
                </div>
              </div>
            );
          } else if (
            element.uniqueKey !== 'tablePreview' &&
            element.uniqueKey !== 'emailPreview'
          ) {
            return (
              <Input
                onChangeHandler={(type, event) =>
                  handlers[element.onChangeFunction](type, event, 'search')
                }
                dataTableColumn={
                  element.formSubDetailsInternalDTOList &&
                  element.formSubDetailsInternalDTOList.length
                    ? element.formSubDetailsInternalDTOList.map((rowData) => {
                        return {
                          width: rowData.displayWidth
                            ? rowData.displayWidth
                            : '',
                          name: rowData.displayName ? rowData.displayName : '',
                          selector: rowData.selector ? rowData.selector : '',
                          cell: dataTableServiceProvider.bbModuleCustomTableCell(
                            rowData
                          ),
                        };
                      })
                    : ''
                }
                dataTableEventHandler={handlers.handleRowClick}
                conditionalRowStyles={conditionalRowStyles}
                isEditable="notShowing"
                formType={element}
                key={index}
              />
            );
          } else {
            return (
              <div
                style={{ display: 'inline-flex', width: 50 + '%' }}
                key={index}
              >
                {element.uniqueKey === 'tablePreview' ? (
                  <div style={{ width: 98 + '%', marginRight: 20 + 'px' }}>
                    <div style={{ float: 'right' }}>
                      {element.value !== null && element.value.length > 0 ? (
                        <span
                          title="Download broker list"
                          className="icon-download"
                          onClick={() => {
                            const url = AP_file_url + excelPreviewPath;
                            const link = document.createElement('a');
                            link.href = url;
                            return link.click();
                          }}
                        ></span>
                      ) : (
                        ''
                      )}
                    </div>
                    <Input
                      formType={element}
                      onChangeHandler={(type, event) =>
                        handlers[element.onChangeFunction](
                          type,
                          event,
                          'search'
                        )
                      }
                      dataTableColumn={
                        element.formSubDetailsInternalDTOList &&
                        element.formSubDetailsInternalDTOList.length
                          ? element.formSubDetailsInternalDTOList.map(
                              (rowData) => {
                                return {
                                  width: rowData.displayWidth
                                    ? rowData.displayWidth
                                    : '',
                                  name: rowData.displayName
                                    ? rowData.displayName
                                    : '',
                                  selector: rowData.selector
                                    ? rowData.selector
                                    : '',
                                  cell: searchTableCell(rowData),
                                };
                              }
                            )
                          : []
                      }
                      dataTableEventHandler={
                        handlers.handlePreviewTableRowClick
                      }
                      conditionalRowStyles={conditionalRowStylesTwo}
                    />
                  </div>
                ) : (
                  ''
                )}
                {element.uniqueKey === 'emailPreview' ? (
                  <div style={{ width: 100 + '%' }}>
                    <Input formType={element} EmailPreviewData={emailPreview} />{' '}
                  </div>
                ) : (
                  ''
                )}
              </div>
            );
          }
        })
      : null;
  };

  const onChangeHandler = (type, event, action) => {
    const deepClone = (obj) => JSON.parse(JSON.stringify(obj));
    const stateValues = deepClone(formInputs);

    if (action === 'add') {
      const foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'subTitle'
      );
      if (foundIndex !== -1) {
        const subIndex = stateValues[
          foundIndex
        ].formSubDetailsInternalDTOList.findIndex(
          (e) => e.uniqueKey === type.uniqueKey
        );
        stateValues[foundIndex].formSubDetailsInternalDTOList[subIndex].value =
          event.target.value;
      }
    } else {
      if (type.uniqueKey === 'client-control') {
        stateValues[0].formSubDetailsInternalDTOList[0].value =
          event.target.value;
        setIsSearch((prev) => ({
          ...prev,
          isClientValue: event.target.value,
        }));
      } else if (type.uniqueKey === 'date-control') {
        stateValues[0].formSubDetailsInternalDTOList[1].value = event;
        setIsSearch((prev) => ({
          ...prev,
          isDateValue: event,
        }));
      }
    }

    setFormInputs(stateValues);
  };
  const initialSearchDone = useRef(false);

  useEffect(() => {
    if (
      !initialSearchDone.current &&
      isSearch?.isDateValue &&
      isSearch?.isClientValue &&
      isSearch?.isSearchDisabled
    ) {
      searchMPN();
      initialSearchDone.current = true;
    }
  }, [isSearch.isDateValue, isSearch.isClientValue]);

  const handleRowClick = (type, event) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    var styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      type.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
    setProductFileMasterId(type.productFileMasterId);
    dispatch(
      bbMPNSearchSubList({
        masterID: type.productFileMasterId,
        reqType: 'subscribe',
      })
    );
    dispatch(bbMPNPreviewLists(type.productFileMasterId));
  };

  const handlePreviewTableRowClick = (type, event) => {
    const styleAttributeTwo = dataTableServiceProvider.conditionalDataTableRow(
      type.serialNo,
      'serialNo'
    );

    dispatch(
      bbMPNEmailPreview({
        documentId: type.documentId,
        emailId: type.bbEmailOutMasterId,
      })
    );

    setConditionalStylesTwo(styleAttributeTwo);
    const updatedFormInputs = cloneDeep(formInputs).filter(
      (e) => e.uniqueKey !== 'emailPreview'
    );

    if (emailPreviewForm && emailPreviewForm.length > 0) {
      updatedFormInputs.push(emailPreviewForm[0]);
    }

    setFormInputs(updatedFormInputs);
  };

  function searchMPN() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for Search.
     */
    let tempArray = cloneDeep(formInputs);
    if (isSearch.isSearchDisabled) {
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
    tempArray.map((formDetails) => {
      if (formDetails.uniqueKey === 'tablePreview') {
        formDetails.value = null;
      } else if (formDetails.uniqueKey === 'emailPreview') {
        tempArray.splice(4, 1);
      }
    });

    setFormInputs(tempArray);
  }
  function onClearFunction() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: to clear all selected controls.
     */
    if (!isSearch.isSearchDisabled) {
      var updatedState = [...formInputs];
      updatedState.map((input) => {
        input.value = null;

        input.formSubDetailsInternalDTOList &&
          input.formSubDetailsInternalDTOList !== null &&
          input.formSubDetailsInternalDTOList.map((subInput) => {
            subInput.value = null;
            if (subInput.uniqueKey === 'button-search') {
              subInput.disableFlag = 'T';
            }
            if (subInput.uniqueKey === 'clear-button') {
              subInput.disableFlag = 'T';
            }
          });
      });
    }

    let FormupdatedState = updatedState.filter((e) => {
      return e.type !== 'EmailPreview';
    });

    setFormInputs(FormupdatedState);
    setProductFileMasterId(null);
  }
  const handlers = {
    onChangeHandler,
    handleRowClick,
    searchMPN,
    onClearFunction,
    handlePreviewTableRowClick,
  };
  return (
    <>
      {/* Loader for entire Component */}
      <CommonSpinner visible={loading} />

      {/* rendering Form Controls */}
      {formInputs && formInputs.length ? forms(formInputs) : ''}
    </>
  );
};
export { Search };
