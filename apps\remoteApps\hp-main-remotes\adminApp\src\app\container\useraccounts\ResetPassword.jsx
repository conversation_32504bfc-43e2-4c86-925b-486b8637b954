/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:40:44
 * @modify date 22-02-2022 11:16:13
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';

import DataTable from 'react-data-table-component';
import {
  logout,
  showDialog,
  getClientComboBox,
  getUserListing,
  resetPassword,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import { resetResetPasswordUser } from '@hp/mainstore';
import {
  ButtonCommon,
  dataTableServiceProvider,
  globalutils,
} from '@hp/components';
import { Select, TextInput, useConfirm } from '@hp/components';

const ResetPassword = (props) => {
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const { viewuserlist, resetPasswordResponse } = useSelector(
    (store) => store.admin
  );
  const { clientCombolist } = useSelector((store) => store.email);
  let user = globalutils.getDataFromStorage('all');
  const client_Id = user?.clientId;
  const [searchBox, setSearchBox] = useState();
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [TableListing, setTableList] = useState();
  const [userID, setUserID] = useState(null);
  const [clientComboState, setClientComboState] = useState([]);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [clientIdSelect, setClientIdSelect] = useState(client_Id);

  useEffect(() => {
    return () => {
      dispatch(resetResetPasswordUser());
    };
  }, []);

  useEffect(() => {
    if (clientIdSelect && clientIdSelect !== undefined) {
      getParameter();
    }
  }, [clientIdSelect]);

  useEffect(() => {
    if (
      clientCombolist &&
      clientCombolist.value &&
      clientCombolist.value.length > 0
    ) {
      const clientOptions = clientCombolist.value.map((value) => {
        return {
          value: value.clientId,
          display: value.clientName,
        };
      });
      setClientComboState(clientOptions);
    }
  }, [clientCombolist]);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    dispatch(
      showDialog({
        showPopup: true,
        type: type,
        responseMessage: resMessage,
        canClose: true,
        autoHide: true,
      })
    );
  };

  useEffect(() => {
    if (resetPasswordResponse && resetPasswordResponse.value) {
      funcToSetResponseMessage(
        resetPasswordResponse.value.type,
        resetPasswordResponse.value.text
      );
      setUserID(null);
      setConditionalStyles([]);
      if (resetPasswordResponse.value.type === 'success') {
        if (globalutils.getDataFromStorage('userId') === userID) {
          dispatch(logout());
        }
      }
    }
  }, [resetPasswordResponse]);

  useEffect(() => {
    getParameter();
    dispatch(getClientComboBox());
  }, [props]);

  useEffect(() => {
    if (viewuserlist && viewuserlist.value) {
      setTableList(viewuserlist.value);
      setSearchBox(viewuserlist.value);
    }
  }, [viewuserlist]);

  const getParameter = () => {
    let clientId =
      clientIdSelect && clientIdSelect !== undefined
        ? clientIdSelect
        : client_Id;
    dispatch(getUserListing(clientId));
  };

  const DataTableEventHandler = (data) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      data.userId,
      'userId'
    );
    setConditionalStyles(styleAttribute);
    setUserID(data.userId);
  };

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const handleOnChange = (event) => {
    setClientIdSelect(event.target.value);
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const resetpassword = async () => {
    const isConfirmed = await confirm(
      'Are you sure you want to reset the password?'
    );
    if (isConfirmed && userID) {
      dispatch(resetPassword(userID));
    }
  };

  const clearSelection = () => {
    setUserID(null);
    setConditionalStyles([]);
  };

  const functionsName = {
    resetpassword,
    clearSelection,
  };

  const columns = [
    {
      name: 'First Name',
      selector: 'firstName',
      sortable: true,
      width: '17%',
    },
    {
      name: 'Middle Name',
      selector: 'middleName',
      sortable: true,
      width: '12%',
    },
    {
      name: 'Last Name',
      selector: 'lastName',
      sortable: true,
      width: '17%',
    },
    {
      name: 'User Name',
      selector: 'username',
      sortable: true,
      width: '17%',
    },
    {
      name: 'Email',
      selector: 'email',
      sortable: true,
      maxWidth: '47%',
    },
  ];

  return (
    <>
      <div className="page-title">Reset Password</div>

      <div className="two-col-layout mb20">
        {client_Id === 1 && (
          <Select
            label="Client"
            style={{ width: 200 + 'px' }}
            value={clientIdSelect}
            name="clientId"
            options={clientComboState}
            onChange={handleOnChange}
          />
        )}
        <TextInput
          label="Search"
          style={{ width: 200 + 'px' }}
          value={filterString.filterStr}
          onChange={(e) => handleChange(e)}
        />
      </div>
      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          theme={'solarized'}
          onRowClicked={(data) => DataTableEventHandler(data)}
          conditionalRowStyles={conditionalRowStyles}
        />
      </div>
      <div className="mb20"></div>
      {userID ? <ButtonCommon functionsName={functionsName} /> : ''}
    </>
  );
};

export { ResetPassword };
