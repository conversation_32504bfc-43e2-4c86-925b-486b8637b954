import React from 'react';
import './PF_ReportStyle.scss'; // CSS file for styling
import { SnippetsFilled } from '@ant-design/icons';
import Scrollbars from 'react-custom-scrollbars';

const PfReportDataList = ({ data, summary }) => {
  return (
    <div className="invoice-container">
      <Scrollbars autoHeight autoHeightMin={100} autoHeightMax={500}>
        {data &&
          data?.map((item, index) => (
            <>
              <div className="header">
                <SnippetsFilled style={{ marginRight: '8px' }} />
                {item.headerDisplay}
              </div>
              <div className="invoice-list">
                {item.value &&
                  item.value.map((subItem, subIndex) => (
                    <div className="invoice-item" key={subIndex}>
                      <div
                        className="item-name"
                        title={subItem.amount ? `${subItem.amount}` : ''}
                      >
                        {subItem.displayStatus || subItem.displayName} :{' '}
                        {subItem.currencyCovAmount}
                      </div>
                      {subItem.subHeaderDto && (
                        <>
                          <div className="subheader">
                            <ul>
                              <li>
                                {subItem.subHeaderDto.headerDisplay ||
                                  subItem.subHeaderDto.displayName}
                              </li>{' '}
                            </ul>
                          </div>
                          <div className="subheader-list">
                            {subItem.subHeaderDto.value.map(
                              (subSubItem, subSubIndex) => (
                                <div
                                  className="subheader-item"
                                  key={subSubIndex}
                                >
                                  <div
                                    className="subitem-name"
                                    title={
                                      subSubItem.amount
                                        ? `${subSubItem.amount}`
                                        : ''
                                    }
                                  >
                                    {subSubItem.displayStatus} :{' '}
                                    {subSubItem.currencyCovAmount}
                                  </div>
                                  {subSubItem.subHeaderDto && (
                                    <>
                                      <div className="subheader">
                                        {subSubItem.subHeaderDto
                                          .headerDisplay ||
                                          subSubItem.subHeaderDto.displayName}
                                      </div>
                                      <div className="subheader-list">
                                        {subSubItem.subHeaderDto.value.map(
                                          (dataItem, dataIndex) => (
                                            <div
                                              className="subheader-item"
                                              key={dataIndex}
                                            >
                                              <div
                                                className="subitem-name"
                                                title={
                                                  dataItem.amount
                                                    ? `${dataItem.amount}`
                                                    : ''
                                                }
                                              >
                                                {dataItem.displayStatus} :{' '}
                                                {dataItem.currencyCovAmount}
                                              </div>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </>
                                  )}
                                </div>
                              )
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  ))}
              </div>
            </>
          ))}
        {summary ? (
          <>
            <div className="header">Summary:</div>
            <div className="subitem-name">{summary}</div>
          </>
        ) : (
          ''
        )}
      </Scrollbars>
    </div>
  );
};
export default PfReportDataList;
