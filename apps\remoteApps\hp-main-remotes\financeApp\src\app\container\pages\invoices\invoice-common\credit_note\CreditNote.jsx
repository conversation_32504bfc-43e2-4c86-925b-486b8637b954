/* eslint-disable react-hooks/exhaustive-deps */
import { Button, Input } from '@hp/components';
import { InputNumber } from 'antd';

import { useEffect, useState } from 'react';
import Scrollbars from 'react-custom-scrollbars';

function CreditNote(props) {
  const { tittle, invData, addCreditHandler, back, active } = props;

  const [invoiceData, setInvoiceData] = useState([]);
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (!invData?.length) return;
    let keys = ['invNumber', 'subTotalConverted', 'amountConverted'];
    let columnsNotNeeded = [
      'rcvdQty',
      'unitType',
      'taxCode',
      'tax_amount',
      'glCode',
      'dept',
    ];
    let requiredData = [];
    let unqkeys = ['itemList', 'creditNoteItemList'];
    for (let field of invData) {
      if (keys.includes(field.uniqueKey)) {
        requiredData.push(field);
      } else if (
        unqkeys.includes(field.uniqueKey) &&
        field?.formSubDetailsInternalDTOList.length
      ) {
        let columns = field.formSubDetailsInternalDTOList.filter(
          (dto) => !columnsNotNeeded.includes(dto.uniqueKey)
        );

        setTableData({
          ...field,
          formSubDetailsInternalDTOList: columns,
        });
      }
    }
    setInvoiceData(requiredData);
  }, [invData]);

  const quantityHandler = (value, row, index) => {
    let matches = value.match(/(^$)|(\d+)/);
    if (matches) {
      let tableValues = [...tableData.value];
      tableValues.splice(index, 1, {
        ...row,
        quantity: parseInt(matches[0]),
      });
      setTableData({ ...tableData, value: tableValues });
    }
  };

  const addCreditNote = () => {
    addCreditHandler(tableData.value);
  };

  const tableColumns = () => {
    return (
      tableData.formSubDetailsInternalDTOList &&
      tableData.formSubDetailsInternalDTOList.map((val, inde) => {
        return {
          selector: val.selector,
          width: val.selector === 'quantity' ? '125px' : val.displayWidth,
          name: val.displayName,
          cell:
            val.selector === 'quantity' && active
              ? function displayCell(row, index) {
                  return (
                    <InputNumber
                      min={0}
                      defaultValue={row.quantity}
                      onBlur={(e) =>
                        quantityHandler(e.target.value, row, index)
                      }
                      style={{
                        fontSize: '12px',
                        border: '1px solid rgb(205, 202, 202)',
                      }}
                    />
                  );
                }
              : '',
        };
      })
    );
  };

  return (
    <>
      {tittle && <div className="page-title mb20">{tittle}</div>}
      <div
        className="four-col-layout mb20"
        style={{
          fontSize: '12px',
        }}
      >
        {invoiceData && invoiceData.length
          ? invoiceData.map((data, index) => {
              return (
                <div key={index}>
                  <span
                    style={{
                      fontWeight: '600',
                    }}
                  >
                    {data?.displayName || ''}
                  </span>
                  <span> {data?.value || ''}</span>
                </div>
              );
            })
          : ''}
      </div>

      <Scrollbars
        // This will activate auto-height
        autoHeight
        autoHeightMin={400}
        autoHeightMax={410}
        className="mb20"
      >
        <Input formType={tableData} dataTableColumn={tableColumns()} />
      </Scrollbars>
      {active ? (
        <div className=" content-button flex-right">
          <Button className="small  secondary mr20 " onClick={() => back()}>
            Back
          </Button>
          <Button
            className="small default primary mr20 "
            onClick={() => addCreditNote()}
          >
            Add Credit Note
          </Button>
        </div>
      ) : (
        ''
      )}
    </>
  );
}

export default CreditNote;
