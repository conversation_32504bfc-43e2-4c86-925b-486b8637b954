/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useEffect } from 'react';
import { Input } from '@hp/components';
import cloneDeep from 'lodash.clonedeep';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { showDialog } from '@hp/mainstore';
function VendorSearch(props) {
  const { close } = props;
  const [searchForm, setSearchForm] = useState([]);
  const [searchKeys, setSearchKeys] = useState({});

  const dispatch = useDispatch();
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  const { pdmVendorSearchList } = useSelector(
    (store) => store.pdmReducer,
    shallowEqual
  );

  useEffect(() => {
    if (pdmVendorSearchList?.value) {
      if (!searchForm.length) return setSearchForm(pdmVendorSearchList.value);
      let updatedForm = searchForm.map((form, index) => {
        if (form.type === 'DataTable') return pdmVendorSearchList.value[index];
        else return form;
      });
      setSearchForm(updatedForm);
    }
  }, [pdmVendorSearchList]);

  const inputHandler = (form, type, event, index, index2) => {
    let newSearchForm = cloneDeep(searchForm);
    if (type.uniqueKey === 'vendor')
      setSearchKeys({ ...searchKeys, supplierName: event.target.value });
    if (type.uniqueKey === 'product')
      setSearchKeys({ ...searchKeys, partNumber: event.target.value });
    const inputField = { ...type, value: event.target.value };
    form.formSubDetailsInternalDTOList.splice(index2, 1, inputField);
    newSearchForm.splice(index, 1, form);
    setSearchForm(newSearchForm);
  };

  const searchHandler = () => {
    if (!searchKeys.partNumber && !searchKeys.supplierName)
      return funcToSetResponseMessage('info', "Both fields can't be empty");
    // dispatch(pdmAction.getVendorSearchList(searchKeys));
  };

  const onChangeHandlingFunctions = {
    inputHandler,
    searchHandler,
  };

  return (
    <div>
      <span className="icon-close fr" onClick={close}></span>
      {searchForm && searchForm.length
        ? searchForm.map((form, index) => {
            return (
              <Input
                onChangeHandler={(type, event, index2) =>
                  onChangeHandlingFunctions[type.onChangeFunction](
                    form,
                    type,
                    event,
                    index,
                    index2
                  )
                }
                formType={form}
                key={index}
              />
            );
          })
        : ''}
    </div>
  );
}

export default VendorSearch;
