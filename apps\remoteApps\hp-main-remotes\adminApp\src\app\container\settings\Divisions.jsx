/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 18-08-2022 15:40:13
 * @modify date 24-08-2022 12:05:20
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';

import { AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { settingsConstants } from '@hp/mainstore';
import {
  AlertModalPopup,
  dataTableServiceProvider,
  Input,
  useConfirm,
} from '@hp/components';
import { Button } from '@hp/components';
import {
  addDivisionFormDetails,
  deleteDivisionConfirm,
  deleteDivisionFormDetails,
  editDivisionFormDetails,
  getDivisionFormDetail,
  divFormDetailsResponseClear,
} from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import { globalutils } from '@hp/components';
const Divisions = () => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { divFormDetails, divFormDetailsResponse } = useSelector(
    (store) => store.settings
  );

  let user = globalutils.getDataFromStorage('all');
  let userId = user.userId;
  const [clientId, setClientId] = useState(user.clientId);
  const [deptId, setDeptId] = useState(3);
  const [formDetails, setFormDetails] = useState([]);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [selected, setSelected] = useState({});
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [notify, setnotify] = useState(null);
  const [buttonAlertMessage, setButtonAlertMessage] = useState();
  const [buttonAlertModal, setButtonAlertModal] = useState(false);
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (divFormDetails && divFormDetails.value) {
      setFormDetails(divFormDetails.value);
      setTableData(
        divFormDetails.value.find((val) => val.uniqueKey === 'dataTable').value
      );
    }
  }, [divFormDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch(divFormDetailsResponseClear(removeNotification));
      dispatch({
        type: settingsConstants.EDIT_DIVISION_FORM_SUCCESS,
        removeNotification,
      });
      dispatch({
        type: settingsConstants.DELETE_DIVISION_FORM_SUCCESS,
        removeNotification,
      });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  useEffect(() => {
    if (
      divFormDetailsResponse &&
      divFormDetailsResponse.value &&
      divFormDetailsResponse.value !== undefined
    ) {
      funcToSetResponseMessage(
        divFormDetailsResponse.value.type,
        divFormDetailsResponse.value.text
      );
    }
    getDivisionFormDetailsFn(clientId, deptId);
  }, [divFormDetailsResponse]);

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  // Fetch Division details form
  const getDivisionFormDetailsFn = (clientId, deptId) => {
    dispatch(
      getDivisionFormDetail({
        deptId: deptId,
        userId: userId,
        clientId: clientId,
      })
    );
  };

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let divNotExist = true;
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'name' || element.uniqueKey === 'code') {
            if (element.value === null || element.value.trim() === '') {
              divNotExist = false;
              element.uniqueKey === 'name'
                ? funcToSetResMessageInModal(
                    'error',
                    'Please Enter Division Name'
                  )
                : funcToSetResMessageInModal(
                    'error',
                    'Please Enter Division Code'
                  );
            } else {
              tempArray.map((item) => {
                if (item.uniqueKey === 'dataTable') {
                  item.value.map((val) => {
                    if (val.divName === element.value) {
                      if (selected.divName === element.value) {
                        return '';
                      } else {
                        divNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Division Name already exists'
                        );
                      }
                    }
                    if (val.divCode === element.value) {
                      if (selected.divCode === element.value) {
                        return '';
                      } else {
                        divNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Division Code already exists'
                        );
                      }
                    }
                  });
                }
              });
            }
          }
        });
    });
    if (divNotExist) {
      edit
        ? dispatch(
            editDivisionFormDetails({
              formDetails: formDetails,
              userId: userId,
              divId: selected.divId,
            })
          )
        : dispatch(
            addDivisionFormDetails({
              formDetails: formDetails,
              userId: userId,
              divId: 0,
            })
          );
      setShowModal(false);
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.disableFlag = 'F';
          element.value = '';
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setEdit(true);
    setSelected(obj);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          entry.uniqueKey === 'name' ? (entry.value = obj.divName) : '';
          entry.uniqueKey === 'code' ? (entry.value = obj.divCode) : '';
          entry.uniqueKey === 'description' ? (entry.value = obj.divDesc) : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.divId,
      'divId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = async (obj) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteDivisionFormDetails({ divId: obj.divId, userId: userId }));
      setDisable(false);
      setShowModal(false);
    }
  };
  const handleProceed = () => {
    dispatch(deleteDivisionConfirm({ divId: selected.divId, userId: userId }));
    setButtonAlertModal(false);
    setSelected({});
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event.target.value;
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Select a value in the select combo-box
  const handleSelect = (event, uniqueKey) => {
    if (uniqueKey === 'clientName') {
      getDivisionFormDetailsFn(event.target.value, 0);
      setClientId(event.target.value);
    } else {
      getDivisionFormDetailsFn(clientId, event.target.value);
      setDeptId(event.target.value);
    }
    setDisable(false);
    setConditionalRowStyles([]);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleSelect,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelected(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'T';
          entry.uniqueKey === 'name' ? (entry.value = obj.divName) : '';
          entry.uniqueKey === 'code' ? (entry.value = obj.divCode) : '';
          entry.uniqueKey === 'description' ? (entry.value = obj.divDesc) : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.divId,
      'divId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  // const handleFilterData = (filterData) => {
  //   let tempArray = cloneDeep(formDetails);
  //   tempArray.map((element) =>
  //     element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
  //   );
  //   setFormDetails(tempArray);
  // };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'pageSubtitle') {
            while (showModal) {
              return (
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                >
                  <div
                    onClick={() => {
                      handleCancel();
                      setEdit(false);
                      setSelected({});
                      setConditionalRowStyles([]);
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div className="boxed" style={{ margin: 0 }}>
                    <Input key={index} formType={element} />
                    <div>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList
                      )}
                    </div>
                    {showButtons && (
                      <div>
                        <Button
                          onClick={() => handleSave()}
                          className="default mr20"
                        >
                          Save
                        </Button>
                        <Button
                          onClick={() => handleCancel()}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-bar',
                        'type-' + notify.type,
                      ].join(' ')}
                      style={{ position: 'sticky !important', width: 90 + '%' }}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'add_button') {
            return (
              <div style={{ float: 'right', marginBottom: 10 + 'px' }}>
                <Button
                  onClick={() => {
                    handleCancel();
                    setSelected({});
                    setConditionalRowStyles([]);
                    setEdit(false);
                    setShowModal(true);
                    setShowButtons(true);
                  }}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-add-button "> </i>Add
                </Button>
              </div>
            );
          } else if (element.type === 'Select') {
            if (element.uniqueKey === 'deptSelect') {
              return (
                <div className="three-col-layout">
                  {/* <div style={{ width: '200px', marginTop: '25px' }}>
                    <Input
                      formType={data.find(
                        (item) => item.uniqueKey === 'search'
                      )}
                      isEditable="notShowing"
                      tableListData={tableData}
                      getFilteredData={(filterData) =>
                        handleFilterData(filterData)
                      }
                    />
                  </div> */}
                  {user.clientId === 1 && (
                    <div className="col">
                      {data.map((entry) => {
                        return (
                          entry.uniqueKey === 'clientName' && (
                            <Input
                              isEditable={'notShowing'}
                              formType={entry}
                              onChangeHandler={(element, event) => {
                                onChangeHandlingFunctions[
                                  element.onChangeFunction
                                ](event, element.uniqueKey);
                              }}
                            />
                          )
                        );
                      })}
                    </div>
                  )}
                  <div className="col">
                    <Input
                      key={index}
                      isEditable={'notShowing'}
                      formType={element}
                      onChangeHandler={(element, event) => {
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey
                        );
                      }}
                    />
                  </div>
                </div>
              );
            }
          } else if (element.uniqueKey === 'search') {
            return;
          } else if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else {
            return (
              <Input
                key={index}
                disabledFlag={disable}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey
                  );
                }}
              />
            );
          }
        })
      : null;
  };

  return (
    <>
      <div className="">
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
      {buttonAlertMessage && (
        <AlertModalPopup
          isAlertOpen={buttonAlertModal}
          setAlertClose={() => setButtonAlertModal(false)}
          message={buttonAlertMessage}
          handleProceed={handleProceed}
          handleCancel={() => setButtonAlertModal(false)}
        />
      )}
    </>
  );
};
export { Divisions };
