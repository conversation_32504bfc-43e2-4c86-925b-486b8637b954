/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-useless-fragment */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:12:13
 * @modify date 2024-07-23 10:55:39
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { getPoMatrixs } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Input, TextInput } from '@hp/components';

const AddFundStatic = (props) => {
  const modalToOpen = props.isModalOpen ? props.isModalOpen : false;
  const dispatch = useDispatch();
  const [getpoMatrix, setPoMatrix] = useState();
  const [balanceAmt, setBalanceAmt] = useState();
  const [currency, setCurrency] = useState();
  const [subTotalBalanceAmt, setSubTotalBalanceAmt] = useState();
  const [taxAmtBalance, setTaxAmtBalance] = useState();

  useEffect(() => {
    if (modalToOpen) {
      if (props.poID && props.invID !== undefined) {
        let poId = props.poID;
        let invId = props.invID;
        getPoMatrix(poId, invId);
      } else if (props.invID === null) {
        let poId = props.poID;
        getPoMatrix(poId, null);
      }
    }
  }, [modalToOpen]);

  const getPoMatrix = async (poId, invId) => {
    dispatch(getPoMatrixs({ poId: poId, invId: invId }));
  };

  const { poMatrix } = useSelector((store) => store.accpay);

  useEffect(() => {
    if (poMatrix && poMatrix.value) {
      setPoMatrix(poMatrix.value.formDetailsDtoList);
      setBalanceAmt(poMatrix.value.balanceAmount);
      setSubTotalBalanceAmt(poMatrix.value.subTotalAmount);
      setTaxAmtBalance(poMatrix.value.taxAmount);
      setCurrency(poMatrix.value.currency);
    }
  }, [poMatrix]);

  const formSingleControlsBinding = (data) => {
    return (
      <div>
        <div>
          <Input formType={data} />
        </div>
      </div>
    );
  };

  return (
    <>
      <Modal
        className="ModalPoMatrix"
        overlayClassName="ModalOverlay"
        style={{ maxWidth: 850 }}
        ariaHideApp={false}
        isOpen={props.isModalOpen}
      >
        <div onClick={props.onClick} className="modal-close icon-close"></div>
        <div className="ai-based-status-match">
          <>
            <div>
              <h2 className="page-title mb20">Service Request: </h2>
              <div style={{ paddingBottom: '40px' }}>
                <div className="new-service-request">
                  <div className="two-col-layout mb20">
                    <TextInput
                      className="mb20"
                      label="Request Type"
                      value={'Add Fund'}
                      disabled={true}
                    />
                    <TextInput
                      className="mb20"
                      label="Request Id"
                      value={'1053'}
                      disabled={true}
                    />
                  </div>
                  <div>
                    <div
                      className="four-col-layout"
                      style={{
                        fontSize: '12px',
                      }}
                    >
                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          PO #:
                        </span>
                        <span> {'700100'}</span>
                      </div>

                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          Invoice #:
                        </span>
                        <span> {'01827201'}</span>
                      </div>
                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          Invoice Amount :
                        </span>
                        <span> {'709.34'}</span>
                      </div>
                    </div>
                    <div className="mb20" />

                    <h2 className="page-title mb20"> PO Balance </h2>

                    <div
                      className="four-col-layout"
                      style={{
                        fontSize: '12px',
                      }}
                    >
                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          Currency:
                        </span>
                        <span> {currency}</span>
                      </div>

                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          Sub Total:
                        </span>
                        <span> {subTotalBalanceAmt}</span>
                      </div>
                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          Tax Amount :
                        </span>
                        <span> {taxAmtBalance}</span>
                      </div>
                      <div>
                        <span
                          style={{
                            fontWeight: '600',
                          }}
                        >
                          Total Amount :
                        </span>
                        <span> {balanceAmt}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="mb20" />
                    <h2 className="page-title">Item Balance Details: </h2>
                    {getpoMatrix && getpoMatrix.length
                      ? formSingleControlsBinding(getpoMatrix[1])
                      : null}
                  </div>
                  <Button className="small outline mb20">Send</Button>
                </div>
              </div>
            </div>
          </>
        </div>
      </Modal>
    </>
  );
};
export { AddFundStatic };
