/* eslint-disable react/no-unknown-property */
/* eslint-disable no-unused-vars */
/* eslint-disable no-dupe-else-if */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */

import {
  Button,
  Input,
  RichTextEditor,
  MultiEmailInput,
  useConfirm,
  CommonSpinner,
} from '@hp/components';
import { dataTableServiceProvider } from '@hp/components';
import React, { useState, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { bbMPNSentEmail, bbMPNEmailPreview, bbSaveDraft } from '@hp/mainstore';
import Modal from 'react-modal';

const SentRFQ = (props) => {
  const { form, setSelectedTab } = props;
  const { confirm } = useConfirm();
  const { emailPreviewGenerated, savedDraft } = useSelector(
    (store) => store.bb
  );
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [emailPreview, setEmailPreview] = useState();
  const [emailPreviewVisible, setEmailPreviewVisible] = useState(false);
  const [SentMailId, setSentMailId] = useState([]);
  const [sendEmailEnable, setSendEmailEnable] = useState(false);
  const [ccOnClick, setCcOnClick] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [draftAndSent, setDraftAndSent] = useState(false);
  const [saveDraftCick, setSaveDraftCick] = useState(false);
  const [emailTemplate, setEmailTemplate] = useState('');
  const [toggleState, setToggleState] = useState(true);
  const [type, setType] = useState();
  const [documentID, setdocumentID] = useState('');
  const [inputChanged, setinputChanged] = useState(false);
  const [isFileOpen, setIsFileOpen] = useState({
    isOpen: false,
    filePath: '',
  });
  const [bccOnClick, setBccOnClick] = useState(false);
  const dispatch = useDispatch();
  const [previewItem, setPreviewItem] = useState({
    buttonEnable: true,
    saveDraftBtn: false,
    isEmailPreviewEnable: true,
    status: '',
  });

  const rowDisabledCriteria = (row) => row.status === 'Sent';

  const onChangeHandler = (event, key) => {
    if (emailPreview !== undefined) {
      let updatedState = { ...emailPreview };
      switch (event.target.name) {
        case 'subject':
          updatedState.subject = event.target.value;
          break;
        case 'toAddress':
          updatedState.toAddress = event.target.value;
          break;

        default:
          break;
      }
      setinputChanged(true);
      setEmailPreview(updatedState);
    }
  };

  const onRichTextChangeHandler = (event, key, emailTemplate) => {
    let tempMail;
    if (toggleState) {
      tempMail = event;
      setToggleState(false);
    }
    setEmailTemplate(event);
    if (event === tempMail) {
      setinputChanged(false);
    } else {
      setinputChanged(true);
    }
  };

  useLayoutEffect(() => {
    if (emailPreviewGenerated && emailPreviewGenerated.value) {
      let emailContent = emailPreviewGenerated.value?.content;
      setEmailTemplate(emailContent);
      setEmailPreview(emailPreviewGenerated.value);
    } else {
      setEmailPreview('');
      setEmailTemplate('');
    }
  }, [emailPreviewGenerated]);

  useLayoutEffect(() => {
    setToggleState(true);
  }, [documentID]);

  useLayoutEffect(() => {
    if (savedDraft) {
      setinputChanged(false);
      setPreviewItem({ ...previewItem, isEmailPreviewEnable: true });
    }
    if (draftAndSent && savedDraft) {
      dispatch(bbMPNSentEmail(SentMailId));
      setDraftAndSent(false);
      setSaveDraftCick(false);
    } else if (!savedDraft) {
      setDraftAndSent(false);
    }
  }, [savedDraft]);

  const handleRowClick = (type, event, el, changeDetect) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    setType(type);
    if (inputChanged && isEdit && changeDetect !== 'processed') {
      handleConfirm(type, event, el);
    } else {
      switch (el.uniqueKey) {
        case 'tablePreview':
          var styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
            type.serialNo,
            'serialNo'
          );

          dispatch(
            bbMPNEmailPreview({
              documentId: type.documentId,
              emailId: type.bbEmailOutMasterId,
            })
          );
          setConditionalStyles(styleAttribute);
          setEmailPreviewVisible(true);
          setPreviewItem({ ...previewItem, isEmailPreviewEnable: true });
          setdocumentID(type.documentId);
          if (event === 'editClick') {
            setIsEdit(true);
            setToggleState(true);
            setPreviewItem({
              ...previewItem,
              saveDraftBtn: true,
              isEmailPreviewEnable: false,
            });
          } else {
            if (type.status === 'Draft' || type.status === 'Failed') {
              setPreviewItem({
                ...previewItem,
                saveDraftBtn: true,
                isEmailPreviewEnable: true,
              });
            } else {
              setPreviewItem({
                ...previewItem,
                saveDraftBtn: false,
                isEmailPreviewEnable: true,
              });
            }
          }

          break;
        default:
          break;
      }
    }
  };

  const checkSelectedRow = (items) => {
    let listArr = [];
    let statusCheck = false;
    if (items.selectedRows.length > 0) {
      items.selectedRows.map((item) => {
        listArr.push(item.bbEmailOutMasterId);
        items?.selectedRows.map((element) => {
          if (element.status === 'Draft' || element.status === 'Failed') {
            if (items.selectedRows.length === 1) {
              statusCheck = true;
            }
          } else {
            statusCheck = false;
          }
        });
      });
      setSendEmailEnable(true);
      setSentMailId(listArr);
      if (statusCheck) {
        setPreviewItem({ ...previewItem, saveDraftBtn: true });
      } else {
        setPreviewItem({ ...previewItem, saveDraftBtn: false });
      }
    } else {
      setSendEmailEnable(false);
      setPreviewItem({ ...previewItem, saveDraftBtn: false });
    }
  };

  const sendMail = async () => {
    if (!isEdit) {
      dispatch(bbMPNSentEmail(SentMailId));
    } else if (saveDraftCick && isEdit) {
      dispatch(bbMPNSentEmail(SentMailId));
    } else {
      let editObject = {
        ...emailPreview,
        content: emailTemplate,
      };

      dispatch(
        bbSaveDraft({
          documentId: documentID,
          editobj: editObject,
          reqType: 'subscribe',
        })
      );
      setDraftAndSent(true);
    }
  };

  const saveDraft = () => {
    let editObject = {
      ...emailPreview,
      content: emailTemplate,
    };

    dispatch(
      bbSaveDraft({
        documentId: documentID,
        editobj: editObject,
        reqType: 'subscribe',
      })
    );
    setSaveDraftCick(true);
  };

  function previewFile(attach) {
    setIsFileOpen({
      ...isFileOpen,
      isOpen: true,
      filePath: attach.fileViewPath,
    });
  }

  //Function to confirm delete
  const handleConfirm = async (type, event, el) => {
    const isConfirmed = await confirm(
      'Changes you have made may not be saved ?'
    );
    if (isConfirmed) {
      handleRowClick(type, event, el, 'processed');
      setinputChanged((prev) => !prev);
      setIsEdit(false);
    }
  };

  const emailOnChange = (_emails, removeDuplicates) => {
    const changedArray = removeDuplicates(_emails);
    setEmailPreview({ ...emailPreview, toAddressList: changedArray });
    setinputChanged(true);
  };

  const emailOnChangeCc = (_emails, removeDuplicates) => {
    const changedArray = removeDuplicates(_emails);
    setEmailPreview({ ...emailPreview, ccList: changedArray });
    setinputChanged(true);
  };

  const emailOnChangeBcc = (_emails, removeDuplicates) => {
    const changedArray = removeDuplicates(_emails);
    setEmailPreview({ ...emailPreview, bccList: changedArray });
    setinputChanged(true);
  };

  const handlers = {
    handleRowClick,
  };

  const rfqTableCell = (terms) => {
    if (terms.selector === 'editIcon') {
      return function displayCell(row) {
        if (row.status === 'Draft') {
          // return (
          //   <div
          //     className="icon-edit-button"
          //     onClick={() =>
          //       handleRowClick(row, 'editClick', {
          //         uniqueKey: 'tablePreview',
          //       })
          //     }
          //   ></div>
          // );
        }
      };
    }
    if (terms.selector === 'status') {
      return function displayCell(row) {
        const statusColor =
          row.status === 'Sent'
            ? 'green'
            : row.status === 'Queued'
            ? 'yellow'
            : row.status === 'Failed'
            ? 'red'
            : row.status === 'Draft'
            ? 'blue'
            : row.status === 'Discarded'
            ? 'grey'
            : '';
        return (
          <div className={`react-tbl-custom-status-cell ${statusColor}`}>
            {row?.status ? row.status : ''}
          </div>
        );
      };
    }
  };

  return (
    <>
      <div className="two-col-layout">
        {form && form
          ? form.formSubDetailsInternalDTOList.map((items, index) => {
              if (items.uniqueKey === 'tablePreview') {
                return (
                  <Input
                    formType={items}
                    isEditable="notShowing"
                    onChangeHandler={(type, event) =>
                      handlers[type.onChangeFunction](type, event, 'search')
                    }
                    dataTableEventHandler={(type, event) =>
                      handlers[items.onChangeFunction](type, event, items)
                    }
                    selectedRow={(type) => checkSelectedRow(type)}
                    dataTableColumn={
                      items.formSubDetailsInternalDTOList &&
                      items.formSubDetailsInternalDTOList.length
                        ? items.formSubDetailsInternalDTOList.map((terms) => {
                            return {
                              width: terms.displayWidth
                                ? terms.displayWidth
                                : '',
                              name: terms.displayName ? terms.displayName : '',
                              selector: terms.selector ? terms.selector : '',
                              cell: rfqTableCell(terms),
                            };
                          })
                        : []
                    }
                    conditionalRowStyles={conditionalRowStyles}
                    selectableRows={true}
                    rowDisabledCriteria={rowDisabledCriteria}
                    selectableRowsNoSelectAll={true}
                    selectableRowsHighlight={true}
                    key={index}
                  />
                );
              } else if (
                items.uniqueKey !== 'tablePreview' &&
                emailPreviewVisible
              ) {
                return (
                  <div className="email-new-compose" key={index}>
                    {/* <h2>Email Preview</h2> */}
                    <div className="new-compose-body">
                      <div className="to-compose-input-custom-for-multiEmail">
                        <div className="compose-input-custom-for-multiEmail">
                          <p className="prefix">To :</p>
                          <MultiEmailInput
                            emailList={
                              emailPreview?.toAddressList &&
                              emailPreview?.toAddressList !== null
                                ? emailPreview.toAddressList
                                : []
                            }
                            emailOnchange={emailOnChange}
                            disabled={previewItem.isEmailPreviewEnable}
                          />
                        </div>
                        {/* <input
                          type="email"
                          autoComplete="false"
                          multiple={true}
                          value={
                            emailPreview?.toAddressList &&
                            emailPreview?.toAddressList !== null
                              ? emailPreview.toAddressList.join(",")
                              : ""
                          }
                          name="toAddress"
                          // disabled={!emailPreviewProp.emailAction}
                          onChange={(event) => onChangeHandler(event)}
                        /> */}
                        <div>
                          <div className="button-cc-bcc-div">
                            {!ccOnClick ? (
                              <button onClick={() => setCcOnClick(!ccOnClick)}>
                                Cc
                              </button>
                            ) : (
                              ''
                            )}
                            {!bccOnClick ? (
                              <button
                                onClick={() => setBccOnClick(!bccOnClick)}
                              >
                                Bcc
                              </button>
                            ) : (
                              ''
                            )}
                          </div>
                        </div>
                      </div>
                      {ccOnClick ? (
                        <div className="compose-input-custom-for-multiEmail">
                          <p className="prefix">Cc :</p>
                          <MultiEmailInput
                            emailList={
                              emailPreview?.ccList &&
                              emailPreview?.ccList !== null
                                ? emailPreview.ccList
                                : []
                            }
                            emailOnchange={emailOnChangeCc}
                            disabled={previewItem.isEmailPreviewEnable}
                          />
                        </div>
                      ) : null}

                      {bccOnClick ? (
                        <div className="compose-input-custom-for-multiEmail">
                          <p className="prefix">Bcc :</p>
                          <MultiEmailInput
                            emailList={
                              emailPreview?.bccList &&
                              emailPreview?.bccList !== null
                                ? emailPreview.bccList
                                : []
                            }
                            emailOnchange={emailOnChangeBcc}
                            disabled={previewItem.isEmailPreviewEnable}
                          />
                        </div>
                      ) : null}

                      {/* {bccOnClick ? (
                        <div className="compose-input">
                          <p className="prefix">Bcc :</p>
                          <input
                            type="text"
                            autoComplete="false"
                            value={
                              emailPreview?.bccList &&
                              emailPreview?.bccList !== null
                                ? emailPreview.bccList.join(',')
                                : ''
                            }
                            // disabled={previewItem?.bCC?.disable}
                            // disabled={!emailPreviewProp.emailAction}
                            //  onChange={(event) => onChangeHandler(event)}
                            name="bCC"
                            disabled={previewItem.isEmailPreviewEnable}
                          />
                        </div>
                      ) : null} */}

                      <div className="compose-input">
                        <p className="prefix">Subject :</p>
                        <input
                          type="text"
                          name="subject"
                          autoComplete="false"
                          value={
                            emailPreview?.subject &&
                            emailPreview?.subject !== null
                              ? emailPreview.subject
                              : ''
                          }
                          disabled={previewItem.isEmailPreviewEnable}
                          onChange={(event) => onChangeHandler(event)}
                        />
                      </div>
                      <div className="compose-content mb20">
                        <RichTextEditor
                          name="content"
                          onChange={(event) =>
                            onRichTextChangeHandler(
                              event,
                              'richText',
                              emailTemplate
                            )
                          }
                          disabled={previewItem.isEmailPreviewEnable}
                          value={emailTemplate}
                        />
                      </div>

                      {emailPreview?.attachmentsPathList &&
                      emailPreview.attachmentsPathList.length
                        ? emailPreview.attachmentsPathList.map((attach, i) => {
                            return (
                              <div
                                className="preview-attach mb20"
                                onClick={() => previewFile(attach)}
                                key={i}
                              >
                                {attach?.originalFileName || ''}
                                {attach.disable ? (
                                  <span
                                    aria-disabled={
                                      previewItem.isEmailPreviewEnable
                                    }
                                    class="icon-ico-close"
                                  ></span>
                                ) : (
                                  ''
                                )}
                              </div>
                            );
                          })
                        : ''}
                      {previewItem?.buttonEnable ? (
                        <div className="compose-button">
                          <div className="compose-send-save">
                            {/* {previewItem.sendBtn ? (
                              <button
                                className="compose-send-button"
                                // onClick={() => actionHandler("send")}
                              >
                                Send
                              </button>
                            ) : (
                              ""
                            )} */}

                            {previewItem.saveDraftBtn ? (
                              <>
                                <Button
                                  // disabled={!previewItem.saveDraftBtnn}
                                  className="small outline mr20"
                                  onClick={() => saveDraft()}
                                >
                                  Save
                                </Button>
                                <Button
                                  // disabled={!previewItem.saveDraftBtnn}
                                  className="small secondary "
                                  onClick={() =>
                                    handleRowClick(type, 'editClick', {
                                      uniqueKey: 'tablePreview',
                                    })
                                  }
                                >
                                  Edit
                                </Button>
                              </>
                            ) : (
                              ''
                            )}
                          </div>
                          {/* <button className="compose-delete-button">
                             <i className="icon-delete"></i>
                           </button> */}
                        </div>
                      ) : (
                        ''
                      )}
                    </div>
                    <Modal
                      className="Modal"
                      overlayClassName="ModalOverlay"
                      ariaHideApp={false}
                      isOpen={isFileOpen.isOpen}
                    >
                      <div
                        onClick={() =>
                          setIsFileOpen({
                            ...isFileOpen,
                            filePath: '',
                            isOpen: false,
                          })
                        }
                        className="modal-close icon-close"
                      ></div>
                      <h2 className="page-title mb20">Preview</h2>
                      <Input
                        formType={{ type: 'Excel Viewer' }}
                        excelPath={isFileOpen.filePath}
                      />
                    </Modal>
                  </div>

                  // <EmailPreview
                  //   emailPreviewProp={{
                  //     EmailPreviewData: emailPreview,
                  //     getEmailTemplateData: onChangeHandler,
                  //     emailAction: sendEmailEnable,
                  //   }}
                  ///>
                );
              }
            })
          : ''}
      </div>

      <Button
        className=" secondary small mr20"
        onClick={() => setSelectedTab((prev) => prev - 1)}
      >
        Previous
      </Button>
      <Button
        className=" default small mr20 "
        onClick={() => sendMail()}
        disabled={!sendEmailEnable}
      >
        Send Email
      </Button>
      <CommonSpinner visible={draftAndSent} />
    </>
  );
};

export default SentRFQ;
