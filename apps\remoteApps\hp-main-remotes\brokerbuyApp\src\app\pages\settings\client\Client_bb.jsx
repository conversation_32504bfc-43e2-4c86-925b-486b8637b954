/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */

/**
 * <AUTHOR> A
 * @email <EMAIL>
 * @create date 25-07-2022 15:40:13
 * @modify date 09-08-2022 12:40:20
 * @desc [description]
 */

import React, { useState, useLayoutEffect } from 'react';
import {
  useConfirm,
  Button,
  Input,
  CommonSpinner,
  formValidationUtil,
  dataTableServiceProvider,
} from '@hp/components';
// import { bbAction } from 'apps/hp-main/src/redux/_actions';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { bbForms, ClientList, bbAdd, bbUpdate, bbDelete } from '@hp/mainstore';
const ClientBb = (props) => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const {
    bbFormDetails,
    refreshForm,
    clientTableList,
    loading,
    bbClientFormResponse,
    bbClientEditFormResponse,
  } = useSelector((store) => store.bb);

  //=========================================Local States Declaration Section Begins ===========================================//
  const [formDetails, setFormDetails] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [edit, setEdit] = useState(false);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [notify, setnotify] = useState(null);
  const [formState, setFormState] = useState({
    bbClientId: null,
    email: '',
    clientName: '',
    clientCode: '',
    contactNumber: '',
    description: '',
    contactName: '',
  });
  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };
  //=========================================Local States Declaration Section Ends===========================================//

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Component did mount First call of the component.
     */

    dispatch(bbForms('subscribe'));
    dispatch(ClientList('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
      dispatch(ClientList('unSubscribe'));
    };
  }, []);
  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Component Refresh.
     */
    if (refreshForm !== undefined) {
      var timerId = setTimeout(() => {
        setShowModal(false);
      }, 3000);
      dispatch(bbForms('subscribe'));
      dispatch(ClientList('subscribe'));
      return () => {
        clearTimeout(timerId);
      };
    }
  }, [refreshForm]);

  useLayoutEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  useLayoutEffect(() => {
    if (bbClientFormResponse) {
      if (bbClientFormResponse.httpStatus === 'OK')
        funcToSetResMessageInModal('success', bbClientFormResponse.value);
    } else {
      funcToSetResMessageInModal('error', 'Something went wrong');
    }
  }, [bbClientFormResponse]);

  useLayoutEffect(() => {
    if (bbClientEditFormResponse) {
      if (bbClientEditFormResponse.httpStatus === 'OK')
        funcToSetResMessageInModal('success', bbClientEditFormResponse.value);
    } else {
      funcToSetResMessageInModal('error', 'Something went wrong');
    }
  }, [bbClientEditFormResponse]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Form Response.
     */
    if (bbFormDetails && bbFormDetails?.value) {
      // const tableIndex = bbFormDetails?.value.findIndex(
      //   (e) => e.uniqueKey === "clientDatatable"
      // );
      // if (tableIndex !== -1) {
      //   bbFormDetails.value[tableIndex].value = clientTableList.value;
      // }

      setFormDetails(bbFormDetails.value);
    }
  }, [bbFormDetails, clientTableList]);

  // Save Button Function
  const clientFormAction = () => {
    let validatedElement = formValidationUtil.validateForm(formDetails);
    // let validate = formValidationUtil.checkMandatoryField(formDetails);
    let validatedFc = cloneDeep(validatedElement.formList);
    var mappingObject = { ...formState };
    validatedFc.map((element, i) => {
      <div key={i}></div>;
      element.uniqueKey === 'clientSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          switch (entry.uniqueKey) {
            case 'clientName':
              mappingObject.clientName = entry.value;
              break;
            case 'clientCode':
              mappingObject.clientCode = entry.value;
              break;
            case 'email':
              mappingObject.email = entry.value;
              break;
            case 'contName':
              mappingObject.contactName = entry.value;
              break;
            case 'contactNumber':
              mappingObject.contactNumber = entry.value;
              break;
            case 'description':
              mappingObject.description = entry.value;
              break;

            default:
              break;
          }
        });
    });

    setFormDetails(validatedFc);

    if (validatedElement.validSuccess) {
      if (!edit) {
        dispatch(bbAdd(mappingObject));
      } else {
        dispatch(bbUpdate(mappingObject));
      }
    }
  };

  const handleAdd = () => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Add button Action.
     */
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'clientSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          entry.value = '';
        });
    });
    setFormDetails(tempArray);
    setShowModal(true);
    setEdit(false);
  };
  const clientCancel = () => {
    setShowModal(false);
    setEdit(false);
  };

  // Edit Button Function
  const handleEdit = (row) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'clientSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          switch (entry.uniqueKey) {
            case 'clientName':
              entry.value = row.bbClientName;
              break;
            case 'clientCode':
              entry.value = row.bbClientCode;
              break;
            case 'email':
              entry.value = row.email;
              break;
            case 'contName':
              entry.value = row.contactName;
              break;
            case 'contactNumber':
              entry.value = row.contactNumber;
              break;
            case 'description':
              entry.value = row.address;
              break;

            default:
              break;
          }
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
    setFormState({ ...formState, bbClientId: row.bbClientId });
    setShowModal(true);
    setEdit(true);
  };

  //Function to confirm delete
  const handleDelete = async (client) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(bbDelete(client.bbClientId));
    }
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey, element) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'clientSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event.target.value;
          }
        });
    });
    setFormDetails(tempArray);

    // let validatedElement = formValidationUtil.isFormcontrolValidDynamic(
    //   event,
    //   element
    // );
    // let updateFormElement = cloneDeep(formDetails);
    // updateFormElement.map((entry) => {
    //   entry.uniqueKey === 'clientSubtitle' &&
    //     entry.formSubDetailsInternalDTOList.map((element, elementIndex) => {
    //       if (element.uniqueKey === uniqueKey) {
    //         entry.formSubDetailsInternalDTOList.splice(
    //           elementIndex,
    //           1,
    //           validatedElement
    //         );
    //       }
    //     });
    // });
    // setFormDetails(updateFormElement);
    // seterrorFlag(validatedElement.errorFlag);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.serialNo,
      'serialNo'
    );
    setConditionalRowStyles(styleAttribute);
  };
  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'clientSubtitle') {
            while (showModal) {
              return (
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                  key={index}
                >
                  <div
                    onClick={() => {
                      clientCancel();
                      formValidationUtil.clear(formDetails);
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div>
                    <Input formType={element} />
                    <div>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList
                      )}
                    </div>

                    <div className="mb20">
                      <Button
                        onClick={() => clientFormAction()}
                        className="default mr20"
                      >
                        Save
                      </Button>
                      <Button
                        onClick={() => {
                          clientCancel();
                          formValidationUtil.clear(formDetails);
                        }}
                        className="info mr20"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-inside-dialog',
                        'type-' + notify.type,
                      ].join(' ')}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'client_add') {
            return (
              <div style={{ float: 'right', marginTop: '10px' }} key={index}>
                <Button
                  onClick={() => handleAdd()}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-user-plus "> </i>Add Client
                </Button>
              </div>
            );
          } else if (element.uniqueKey === 'clientDatatable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val, i) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : val.displayWidth,
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                isCopy={true}
                key={index}
                formType={element}
                isEditable="notShowing"
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else {
            return (
              <Input
                key={index}
                disabledFlag={false}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            );
          }
        })
      : null;
  };

  return (
    <>
      <CommonSpinner visible={loading} />
      <div className="">
        <div className="">
          {formDetails && formDetails.length
            ? formControlsBinding(formDetails)
            : ''}
        </div>
      </div>
    </>
  );
};
export { ClientBb };
