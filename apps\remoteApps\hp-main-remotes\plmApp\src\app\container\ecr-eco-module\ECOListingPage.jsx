/* eslint-disable react-hooks/exhaustive-deps */
import {
  Button<PERSON>ommon,
  CheckBoxInput,
  Input,
  useConfirm,
  CommonSpinner,
  dataTableServiceProvider,
} from '@hp/components';
import './ecr_eco_common.scss';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAppRouterDom } from '@hp/utils';
import {
  deleteECOFormDetails,
  getECOListingDetails,
  showDialog,
  updateECOStatusListDetails,
  getECOListingFromOtherApps,
  setRowStyle,
  setPageName,
  resetgetECOListingDetails,
  resetdeleteECOFormDetails,
  resetupdateECOStatusListDetails,
} from '@hp/mainstore';
import { Progress, Tooltip } from 'antd';
import { globalutils } from '@hp/components';
const ECOListingPage = () => {
  const { navigate, location } = useAppRouterDom();

  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const { ecoListingForm, ecoChangeStatusRes, ecoFormDeleteRes } = useSelector(
    (store) => store.pdm
  );
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);

  const buttonData = useSelector((state) => state.buttons.buttons || null);
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;
  const segments = location.pathname.split('/');
  // const menuData = segments[1] || '';
  // const submenu = segments[2] || '';
  const parameter = segments[segments.length - 1] || '';
  const isApproval =
    parameter === 'pending_review' || parameter === 'for-approval';
  const currentTableKey =
    parameter === 'other_sources' ? 'from_other_apps' : parameter ?? null;

  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState(null);
  const [selectedEcoList, setSelectedEcoList] = useState([]);
  const [buttonCommonData, setButtonCommonData] = useState(null);

  useEffect(() => {
    dispatch(setPageName(innerMenuName));
    if (parameter === 'other_sources')
      dispatch(getECOListingFromOtherApps(userId));
    // else if (parameter === 'imp_progress')
    //   dispatch(pdmAction.getECOListingDetails(userId, clientId, 'approved'));
    else
      dispatch(
        getECOListingDetails({
          userId: userId,
          clientId: clientId,
          status: parameter,
        })
      );
    setIsLoading(true);
    return () => {
      setFormData(null);
      setSelectedEcoList([]);
      dispatch(resetgetECOListingDetails());
      dispatch(resetdeleteECOFormDetails());
      dispatch(resetupdateECOStatusListDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, [parameter]);

  useEffect(() => {
    if (buttonData && buttonData !== undefined) {
      let data = buttonData.filter((btn) => btn.funcPath !== 'handleReject');
      setButtonCommonData(data);
    }
  }, [buttonData]);

  useEffect(() => {
    if (ecoListingForm?.value) {
      const tempFormData = ecoListingForm.value.find(
        (item) => item.uniqueKey === currentTableKey
      );
      setFormData(tempFormData);
      setIsLoading(false);
    }
  }, [ecoListingForm]);

  useEffect(() => {
    if (ecoChangeStatusRes?.value) {
      funcToSetResponseMessage(
        ecoChangeStatusRes.value?.type,
        ecoChangeStatusRes.value?.text
      );
      dispatch(
        getECOListingDetails({
          userId: userId,
          clientId: clientId,
          status: parameter,
        })
      );
    }
  }, [ecoChangeStatusRes]);

  useEffect(() => {
    if (ecoFormDeleteRes?.value) {
      funcToSetResponseMessage(
        ecoFormDeleteRes.value?.type,
        ecoFormDeleteRes.value?.text
      );
      dispatch(
        getECOListingDetails({
          userId: userId,
          clientId: clientId,
          status: parameter,
        })
      );
    }
  }, [ecoFormDeleteRes]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  async function handleSubmit() {
    if (selectedEcoList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECOStatusListDetails({
            status: 'pending_review',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: selectedEcoList,
          })
        );
        setSelectedEcoList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECO');
  }

  async function handleApproval() {
    if (selectedEcoList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECOStatusListDetails({
            status: 'approval1',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: selectedEcoList,
          })
        );
        setSelectedEcoList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECO');
  }

  async function handleReject() {
    if (selectedEcoList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECOStatusListDetails({
            status: 'rejected',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: selectedEcoList,
          })
        );
        setSelectedEcoList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECO');
  }

  // async function handleApprove() {
  //   const isConfirmed = await confirm('Are you sure you want to continue?');
  //   if (isConfirmed) {
  //     if (selectedEcoList?.length)
  //       dispatch(
  //         pdmAction.updateECOStatusListDetails(
  //           'approved',
  //           userId,
  //           clientId,'N',
  //           selectedEcoList
  //         )
  //       );
  //     else funcToSetResponseMessage('error', 'Please Select at least one ECO');
  //   }
  // }

  async function moveToDrafts() {
    if (selectedEcoList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECOStatusListDetails({
            status: 'draft',
            userId: userId,
            clientId: clientId,
            flag: 'Y',
            payload: selectedEcoList,
          })
        );
        setSelectedEcoList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECO');
  }

  const functionsName = {
    handleSubmit,
    handleApproval,
    handleReject,
    // handleApprove,
    moveToDrafts,
  };

  function handleEditClicked(row, operation) {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    dispatch(setRowStyle(styleAttribute));
    let ecoId = row.ecoId;
    let ecrId = row?.ecrId;

    navigate(`${operation}/${ecoId}`, {
      state: { operation, ecoId, ecrId },
    });
  }

  function handleRowClicked(row, operation) {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    dispatch(setRowStyle(styleAttribute));
    let auth = false;
    if (row.curApproverId === userId) {
      auth = true;
    }
    let ecoId = row.ecoId;

    navigate(`${operation}/${ecoId}`, {
      state: {
        operation: operation,
        ecoId: ecoId,
        ecrId: row?.ecrId ?? null,
        selectedEcoData: row,
        isApproval: isApproval,
        authorised: auth,
        isPurchase:
          parameter === 'for-approval' &&
          row?.showPurchaseNotificationForm === 'Y',
        showPurchaseNotificationSaveButton:
          row?.showPurchaseNotificationSaveButton === 'Y',
      },
    });
  }

  function handleCheckbox(row) {
    const isRowSelected = selectedEcoList.some(
      (item) => item.ecoId === row.ecoId
    );
    let updatedList;
    if (isRowSelected) {
      updatedList = selectedEcoList.filter((item) => item.ecoId !== row.ecoId);
    } else {
      updatedList = [...selectedEcoList, { ...row, checked: true }];
    }
    setSelectedEcoList(updatedList);
  }

  const handleDeleteECO = async (ecoId) => {
    const isConfirmed = await confirm(
      'Are you sure you want to delete this record?'
    );
    if (isConfirmed)
      dispatch(deleteECOFormDetails({ ecoId: ecoId, userId: userId }));
  };

  const handleFormDetails = (element) => {
    let columns =
      element.formSubDetailsInternalDTOList &&
      element.formSubDetailsInternalDTOList.map((val, index) => {
        return {
          selector: val.selector,
          width:
            val.selector === 'editIcon' ||
            val.selector === 'deleteIcon' ||
            val.selector === 'completed'
              ? '4%'
              : val.displayWidth ?? '',
          name: val.displayName,
          cell:
            val.selector === 'editIcon'
              ? function displayCell(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title="Edit"
                    >
                      <span
                        className="icon-edit-button"
                        onClick={() => handleEditClicked(row, 'edit')}
                        key={index}
                      />
                    </Tooltip>
                  );
                }
              : val.selector === 'deleteIcon'
              ? function displayCell(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title="Delete"
                    >
                      <span
                        className="icon-2-trash"
                        onClick={() => {
                          if (row?.ecoId) handleDeleteECO(row.ecoId);
                        }}
                        key={index}
                      />
                    </Tooltip>
                  );
                }
              : val.selector === 'completed'
              ? function displayCell(row) {
                  const isChecked = selectedEcoList.some(
                    (item) => item.ecoId === row.ecoId
                  );
                  return row.isComplete ? (
                    <CheckBoxInput
                      checked={isChecked}
                      onChange={() => handleCheckbox(row)}
                    />
                  ) : (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title="Incomplete Draft"
                    >
                      <span
                        className="icon icon-notification"
                        style={{
                          color: '#FF6400',
                          marginLeft: '3px',
                        }}
                      />
                    </Tooltip>
                  );
                }
              : val.selector === 'checkBox'
              ? function displayCell(row) {
                  const isChecked = selectedEcoList.some(
                    (item) => item.ecoId === row.ecoId
                  );
                  return (
                    <CheckBoxInput
                      checked={isChecked}
                      onChange={() => handleCheckbox(row)}
                    />
                  );
                }
              : val.selector === 'progressPercentage'
              ? function displayCell(row) {
                  return (
                    <Progress
                      percent={Math.round(Number(row?.progressPercentage))}
                    />
                  );
                }
              : function displayTitle(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title={row[val.selector]}
                    >
                      <div className="display-title custom-overflow">
                        {row[val.selector]}
                      </div>
                    </Tooltip>
                  );
                },
        };
      });
    return (
      <div className="inv-div">
        <Input
          dataTablePersistHead
          formType={element}
          dataTableEventHandler={(row) => {
            handleRowClicked(row, 'view');
          }}
          dataTableColumn={columns}
          customTableStyles={{
            table: {
              style: {
                contain: 'inline-size',
              },
            },
          }}
        />
      </div>
    );
  };

  return (
    <>
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      <CommonSpinner visible={isLoading} />
      {formData ? handleFormDetails(formData) : ''}
      {parameter === 'draft' ? (
        <ButtonCommon
          tempButtonData={
            buttonData?.filter((btn) => btn.funcPath === 'handleSubmit') ?? []
          }
          functionsName={functionsName}
        />
      ) : parameter === 'rejected' ? (
        <ButtonCommon
          tempButtonData={buttonCommonData}
          functionsName={functionsName}
        />
      ) : (
        ''
      )}
    </>
  );
};
export { ECOListingPage };
