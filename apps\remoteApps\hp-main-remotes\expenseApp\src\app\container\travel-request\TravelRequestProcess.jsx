/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable no-restricted-globals */

import React, { useEffect, useState, useMemo } from 'react';
import {
  Input,
  CommonSpinner,
  dataTableServiceProvider,
} from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { getExpenseListingAction } from '@hp/mainstore';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import ExpenseDialogComponent from '../DialogComponent';
import { ExpenseListingComponent } from '../ListingComponent';

const TravelRequestProcess = (props) => {
  let locations = location.pathname.split('/');
  let selectedListStatus = locations[4];
  const {
    expenseListingResponse,
    expenseFormControls,
    isExpReportFormloading,
  } = useSelector((store) => store.expense);
  const dispatch = useDispatch();
  const [formElements, setFormElements] = useState([]);
  const [selectedRowFormControls, setselectedRowFormControls] = useState([]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [isModal, setIsModal] = useState(false);
  const [dialogControls, setdialogControls] = useState('');
  const [selectedRowDataTable, setselectedRowDataTable] = useState(null);
  const [conditionalRowStyles, setConditionalStyles] = React.useState([]);

  useEffect(() => {
    dispatch(
      getExpenseListingAction({
        listName: 'TRList',
        listStatus: selectedListStatus,
      })
    );
    return () => {
      setFormElements([]);
    };
  }, [selectedListStatus]);

  useEffect(() => {
    if (selectedRowDataTable) {
      const clickedRow = selectedRowFormControls.filter(
        (item) => item.uniqueKey === selectedRowDataTable.expense
      );
      setdialogControls(clickedRow);
      // expenseFormControls[selectedTab + 1].formSubDetailsInternalDTOList.pop();
      // expenseFormControls[selectedTab + 1].formSubDetailsInternalDTOList.push(...clickedRow);
      selectedTab === 1 ? setIsModal(true) : setIsModal(false);
      setFormElements(expenseFormControls);
    }
    return () => {
      setFormElements([]);
    };
  }, [selectedRowDataTable]);

  useEffect(() => {
    if (expenseFormControls && selectedListStatus.includes('NewRequest')) {
      let expenseRows = [];
      const selectedTabIndx = expenseFormControls.findIndex(
        (item) => item.displayName === 'Expenses'
      );
      if (selectedTabIndx !== -1) {
        let duplicateCopy =
          expenseFormControls[selectedTabIndx].formSubDetailsInternalDTOList;
        setselectedRowFormControls(duplicateCopy);
        const filterFormData = expenseFormControls[
          selectedTabIndx
        ].formSubDetailsInternalDTOList.filter(
          (item) => item.uniqueKey === 'AirFare' || item.type === 'DataTable'
        );
        expenseFormControls[selectedTabIndx].formSubDetailsInternalDTOList = [];
        expenseFormControls[selectedTabIndx].formSubDetailsInternalDTOList =
          filterFormData;
        duplicateCopy.forEach((element, i) => {
          if (element.displayName.includes('RowClick')) {
            expenseRows.push({
              expense: element.uniqueKey,
              cost: '',
              serialNo: i,
            });
          }
        });
        expenseFormControls[
          selectedTabIndx
        ].formSubDetailsInternalDTOList[0].value = expenseRows;
        setFormElements(expenseFormControls);
      }
    } else if (expenseListingResponse) {
      setFormElements(expenseListingResponse);
    }

    return () => {
      setFormElements([]);
    };
  }, [expenseFormControls, selectedListStatus, expenseListingResponse]);
  const onRowClick = (e) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      e.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
    setselectedRowDataTable(e);
  };

  const formControlsBinding = (data) => {
    return data && data.length
      ? data.map((element, index) => {
          if (
            element.type === 'PageTitle' &&
            element.formSubDetailsInternalDTOList &&
            element.formSubDetailsInternalDTOList.length
          ) {
            return (
              <div key={index}>
                <div key={index + 1} className="page-title">
                  {element.displayName}
                </div>
                <div className="card mb20">
                  <div key={index + 2} className="three-col-layout">
                    {' '}
                    {formControlsBinding(element.formSubDetailsInternalDTOList)}
                  </div>
                </div>
              </div>
            );
          }
          return (
            <Input
              key={index}
              formType={element}
              dataTableEventHandler={(e) => onRowClick(e)}
              conditionalRowStyles={conditionalRowStyles}
              dataTablePagination={false}
              // onChangeHandler={(onChangeFunction, e, uniqueKey) => onChangeHandlingFunctions[onChangeFunction](e, uniqueKey, index, element)}
            />
          );
        })
      : null;
  };

  const tabChange = (index) => {
    setSelectedTab(index);
  };

  const memoChildDialog = useMemo(() => {
    return (
      <ExpenseDialogComponent
        formElements={dialogControls[0]}
        isModal={isModal}
        isModalClose={() => setIsModal(false)}
      />
    );
  }, [isModal]);

  return (
    <>
      <
      >
        <CommonSpinner visible={isExpReportFormloading} />
        {formElements &&
        formElements.length &&
        selectedListStatus.includes('NewRequest') ? (
          <section>
            {formControlsBinding(formElements)}
            <div className="mb20"></div>
            <Tabs
              onSelect={(tabIndex) => tabChange(tabIndex)}
              className="mb24"
              selectedIndex={selectedTab}
            >
              <TabList>
                {formElements && formElements.length
                  ? formElements.map((tabListElement, index) => {
                      if (tabListElement.type.includes('TabPanel')) {
                        return (
                          <Tab onClick={() => tabChange(index)} key={index}>
                            {tabListElement.displayName}
                          </Tab>
                        );
                      }
                    })
                  : null}
              </TabList>
              {formElements && formElements.length
                ? formElements.map((tabPanelElement, index) => {
                    if (tabPanelElement.type.includes('TabPanel')) {
                      return (
                        <TabPanel key={index}>
                          <div className="card mb40">
                            <div
                              className={
                                selectedTab !== 1
                                  ? 'flex-row-wrap two-col-layout'
                                  : ''
                              }
                            >
                              {formControlsBinding(
                                tabPanelElement.formSubDetailsInternalDTOList
                              )}
                            </div>
                          </div>
                        </TabPanel>
                      );
                    }
                  })
                : null}
            </Tabs>
          </section>
        ) : (
          <section>
            <ExpenseListingComponent formElements={formElements} />
          </section>
        )}

        {isModal ? memoChildDialog : ''}
      </>
    </>
  );
};
export { TravelRequestProcess };
