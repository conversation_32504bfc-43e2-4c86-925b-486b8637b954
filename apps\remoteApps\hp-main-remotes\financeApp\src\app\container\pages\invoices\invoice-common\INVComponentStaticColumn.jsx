import React from 'react';

const DisplayTitle = (row, key) => {
  return (
    <div className="display-title" title={row[key]}>
      {row[key]}
    </div>
  );
};

const invoiceColumns = [
  {
    name: '',
    selector: 'fileicon',
    sortable: true,
    width: '80px',
  },
  {
    name: 'Line',
    selector: 'serial_no',
    sortable: true,
    width: '80px',
    cell: (row) => DisplayTitle(row, 'serial_no'),
  },
  {
    name: 'Description',
    selector: 'description',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'description'),
    width: '200px',
  },
  {
    name: 'Quantity',
    selector: 'quantity',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'quantity'),
  },
  {
    name: 'Unit',
    selector: 'unit',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'unit'),
  },
  {
    name: 'Unit Price',
    selector: 'unit_price',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'unit_price'),
  },
  {
    name: 'Amount',
    selector: 'amount',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'amount'),
  },
  {
    name: 'Tax',
    selector: 'tax',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'tax'),
  },
  {
    name: 'Tax Amount',
    selector: 'tax_amount',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'tax_amount'),
  },
];

// Do not make any changes in order and value to the onHoldSelectList
const onHoldSelectList = [
  {
    value: 1,
    display: 'Move to Non-PO',
  },
  {
    value: 2,
    display: 'Move to unmatch',
  },
  {
    value: 3,
    display: 'Move to Match ',
  },
  // {
  //   value: 4,
  //   display: 'Match with PO',
  // },
  // {
  //   value: 5,
  //   display: "Move to Partially Matched",
  // },

  {
    value: 6,
    display: 'Send return email to supplier',
  },
  {
    value: 7,
    display: 'Send email to buyer',
  },
  {
    value: 8,
    display: 'Send email to supplier',
  },
  {
    value: 9,
    display: 'Request for adding funds',
  },
];

const poMatrixColumns = [
  {
    name: 'Inv #',
    selector: 'invNumber',
    sortable: true,
    width: '80px',
    cell: (row) => DisplayTitle(row, 'invNumber'),
  },
  {
    name: 'Inv Date',
    selector: 'invDate',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'invDate'),
    width: '200px',
  },
  {
    name: 'Supplier',
    selector: 'supplierName',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'supplierName'),
  },
  {
    name: 'Received On',
    selector: 'rcvdAt',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'rcvdAt'),
  },
  {
    name: 'Amount',
    selector: 'amount',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'amount'),
  },
];

const addFundStaticColumns = [
  {
    name: '#',
    selector: 'serialNo',
    sortable: true,
    width: '80px',
  },
  {
    name: 'Description',
    selector: 'description',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'description'),
    width: '200px',
  },
  {
    name: 'Bal Qnty',
    selector: 'quantity',
    sortable: true,
    // right: true,
    cell: (row) => DisplayTitle(row, 'quantity'),
  },
  {
    name: 'Unit Price',
    selector: 'unit',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'unit'),
  },
  {
    name: 'Currency',
    selector: 'currency',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'currency'),
  },
  {
    name: 'Balance Amount',
    selector: 'amount',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'amount'),
  },
  {
    name: 'Tax',
    selector: 'tax',
    sortable: true,
    cell: (row) => DisplayTitle(row, 'tax'),
  },
  {
    name: 'Tax Amount',
    selector: 'tax_amount',
    sortable: true,
    right: true,
    cell: (row) => DisplayTitle(row, 'tax_amount'),
  },
];

const addFundStaticColumnsData = [
  {
    serialNo: 1,
    description: 'N95 RESPIRATOR MASK',
    quantity: '490',
    unit: '8',
    currency: 'USD',
    amount: '3,920',
    tax: '',
    tax_amount: '',
  },
];

export {
  invoiceColumns,
  onHoldSelectList,
  poMatrixColumns,
  addFundStaticColumns,
  addFundStaticColumnsData,
};
