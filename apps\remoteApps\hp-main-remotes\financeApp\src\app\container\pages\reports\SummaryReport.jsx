/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useEffect } from 'react';
import DataTable from 'react-data-table-component';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER, AP_clearReduxValue } from '@hp/constants';
import { accPayConstants } from '@hp/mainstore';
import { DatePickerInput } from '@hp/components';
import { getSummaryDetailsByDate } from '@hp/mainstore';
import { globalutils } from '@hp/components'
const SummaryReport = () => {
  const dispatch = useDispatch();
  let user = globalutils.getDataFromStorage('all');
  const clientId = user.clientId;
  const [searchDate, setSearchDate] = useState();
  const [reportDetails, setReportDetails] = useState([]);

  const { reportSummaryDetails } = useSelector((store) => store.accpay);

  useEffect(() => {
    reportSummaryDetails &&
    reportSummaryDetails !== undefined &&
    reportSummaryDetails !== AP_clearReduxValue
      ? setReportDetails(reportSummaryDetails.value)
      : [];
  }, [reportSummaryDetails]);

  useEffect(() => {
    return () => {
      let data = AP_clearReduxValue;
      setReportDetails([]);
      dispatch({
        type: accPayConstants.REPORTS_SUMMARY_DETAILS,
        data,
      });
    };
  }, []);

  const handleDateChange = (date) => {
    setSearchDate(date);
    dispatch(getSummaryDetailsByDate(date, clientId));
  };

  const columns = [
    { name: 'Serial #', selector: 'serialNo', sortable: true, width: '10%' },
    {
      name: 'Invoice Date',
      selector: 'rcvdAtDate',
      sortable: true,
      width: '20%',
    },
    { name: 'Supplier', selector: 'supplierName', sortable: true },
    {
      name: 'Invoice Count',
      selector: 'invCount',
      sortable: true,
      width: '12%',
    },
  ];

  return (
    <>
      <div className="two-col-layout mb20">
        <div className="three-col-layout">
          <DatePickerInput
            label="Search Date"
            className="flex-shrink"
            value={searchDate}
            onChange={(date) => handleDateChange(date)}
          />
        </div>
      </div>
      <div className="mb24 force-max-width styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          pagination={true}
          columns={columns}
          data={reportDetails}
        />
      </div>
    </>
  );
};
export { SummaryReport };
