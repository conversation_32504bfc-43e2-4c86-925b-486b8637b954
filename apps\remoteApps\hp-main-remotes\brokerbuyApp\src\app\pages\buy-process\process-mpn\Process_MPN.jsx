/* eslint-disable no-console */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */

/**
 * <AUTHOR> sherlin
 * @email <EMAIL>
 */
import React, {
  useState,
  useLayoutEffect,
  useCallback,
  useEffect,
} from 'react';
import { CommonSpinner, Input } from '@hp/components';
// import { bbAction } from 'apps/hp-main/src/redux/_actions';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { dataTableServiceProvider, utils } from '@hp/components';
import DistributorBroker from './Distributor_broker';
import PreviewList from './Preview_List';
import SentRFQ from './Sent_RFQ';
import { processMPNCache } from './processMPNCache';
import {
  bbF<PERSON>,
  bbMPNSearchSubList,
  bbMPNPreviewLists,
  bbMPNviewSearchSubList,
  bbMPNviewSearch,
} from '@hp/mainstore';

const ProcessMpn = (props) => {
  const [clientId, setclientId] = useState();
  const [bbClientID, setbbClientID] = useState(null);
  const dispatch = useDispatch();
  const {
    bbFormDetails,
    loading,
    mpnFiles,
    mpnSubFiles,
    mpnSearchSubFiles,
    emailGenerated,
    bbMPNPreviewList,
    processAddEditResponse,
    bbFormResponse,
    processAddEditForm,
  } = useSelector((store) => store.bb);
  //=========================================Local States Declaration Section Begins ===========================================//
  const [formInputs, setFormInputs] = useState([]);
  const [addForm, setAddForm] = useState(false);
  const [action, setAction] = useState('');
  const [productDetailsId, setProductDetailsId] = useState();
  const [sendRFQEnable, setSendRFQEnable] = useState(false);

  const [brokerDistributorId, setBrokerDistributorId] = useState();
  const [initialDataLoaded, setInitialDataLoaded] = useState(false); // Add this state

  const [rowData, setRowData] = useState();
  const [showTab, setShowTab] = useState(false);

  const [isSearch, setIsSearch] = useState({
    isSearchDisabled: true,
    isClientValue: '',
    isDateValue: '',
    addOnchange: false,
  });
  const [selectedTab, setSelectedTab] = useState(0);
  const [productFileMasterId, setProductFileMasterId] = useState(null);
  const [excelPreview, setExcelPreview] = useState();

  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const [distributorForm, setDistributorForm] = useState([]);
  const [previewForm, setPreviewForm] = useState([]);
  const [sentRFQForm, setSentRFQForm] = useState([]);

  const [addBrokerEnable, setAddBrokerEnable] = useState(false);

  //=========================================Local States Declaration Section Ends===========================================//

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Component did mount First call for this component.
     */
    // document.addEventListener('contextmenu', function (e) {
    //   e.preventDefault();
    // });
    dispatch(bbForms('subscribe'));
    // dispatch(bbAction.clientList("subscribe"));
    // dispatch(bbAction.processMpnGetForm("subscribe"));
    return () => {
      dispatch(bbForms('unSubscribe'));
      // dispatch(bbAction.clientList("unSubscribe"));
      // dispatch(bbAction.processMpnGetForm("unSubscribe"));
    };
  }, []);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: form response.
     */
    if (bbFormDetails && bbFormDetails?.value) {
      var distributorForm = null;
      var previewForm = null;
      var sendRFQForm = null;
      var headerSection = null;

      if (
        bbFormDetails &&
        bbFormDetails !== undefined &&
        bbFormDetails.value &&
        bbFormDetails.value.length
      ) {
        var clientID = null;
        var selectedDate = null;
        const form = bbFormDetails?.value;
        if (form && form.length) {
          form.map((items) => {
            if (items.uniqueKey === 'page-title') {
              if (items.parameters === 'processFile') {
                const headerControls = form.filter(
                  (e) => e.uniqueKey === 'page-title'
                );
                if (headerControls && headerControls.length) {
                  headerControls[0].formSubDetailsInternalDTOList &&
                    headerControls[0].formSubDetailsInternalDTOList.length &&
                    headerControls[0].formSubDetailsInternalDTOList.map(
                      (element) => {
                        if (element.uniqueKey === 'client-control') {
                          clientID = element.value;
                        } else if (element.uniqueKey === 'date-control') {
                          selectedDate = element.value;
                        }
                      }
                    );
                  setFormInputs(bbFormDetails.value);
                  setIsSearch({
                    ...isSearch,
                    isDateValue: selectedDate,
                    isClientValue: clientID,
                  });
                  setInitialDataLoaded(true);
                }
              }
            }
          });
        }
      }

      bbFormDetails.value.map((items) => {
        if (items.uniqueKey === 'page-title') {
          headerSection = items?.formSubDetailsInternalDTOList;
        } else if (items.uniqueKey === 'addBroker') {
          distributorForm = items;
        } else if (items.uniqueKey === 'previewPanel') {
          previewForm = items;
        } else if (items.uniqueKey === 'rfqEmailPanel') {
          sendRFQForm = items;
        }
      });

      let clientObject = headerSection.find(
        (o) => o.uniqueKey === 'client-control'
      );
      let clientId = clientObject?.value;

      setclientId(clientId);
      setDistributorForm(distributorForm);
      setPreviewForm(previewForm);
      setSentRFQForm(sendRFQForm);
      setFormInputs(bbFormDetails.value);
    }
  }, [bbFormDetails]);

  // useLayoutEffect(() => {
  //   if (formInputs) {
  //     formInputs.map((items) => {
  //       if (items.uniqueKey === "addBroker") {
  //         setDistributorForm(items);
  //       } else if (items.uniqueKey === "previewPanel") {
  //         setPreviewForm(items);
  //       } else if (items.uniqueKey === "rfqEmailPanel") {
  //         setSentRFQForm(items);
  //       }
  //     });
  //   }
  // }, [bbFormDetails]);

  useLayoutEffect(() => {
    if (emailGenerated && selectedTab === 1) {
      dispatch(
        bbMPNSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
      setSelectedTab(2);
      setSendRFQEnable(true);
    }
  }, [emailGenerated]);

  useLayoutEffect(() => {
    dispatch(
      bbMPNSearchSubList({
        masterID: productFileMasterId,
        reqType: 'subscribe',
      })
    );
  }, [bbFormResponse]);

  useLayoutEffect(() => {
    if (processAddEditResponse) {
      setAddForm(false);
      dispatch(bbMPNPreviewLists(productFileMasterId));
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
  }, [processAddEditResponse]);

  useLayoutEffect(() => {
    if (selectedTab === 0 || selectedTab === 1) {
      setSendRFQEnable(false);
    } else if (selectedTab === 2) {
      dispatch(
        bbMPNSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
  }, [selectedTab]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: Excel Preview
     */
    if (bbMPNPreviewList) {
      setExcelPreview(bbMPNPreviewList.value.path);
    }
  }, [bbMPNPreviewList]);
  useEffect(() => {
    /**
     * @description: Auto-search when default date and client values are present
     */
    if (initialDataLoaded && isSearch.isClientValue && isSearch.isDateValue) {
      // Trigger search automatically if both client and date values are present
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
  }, [initialDataLoaded, isSearch.isClientValue, isSearch.isDateValue]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @description: getting Main list based on client Search.
     */
    if (mpnFiles?.value) {
      const newFormInputs = [...formInputs];
      const foundIndex = newFormInputs.findIndex(
        (e) => e.uniqueKey === 'fileTable'
      );

      if (foundIndex !== -1) {
        const updatedItem = {
          ...newFormInputs[foundIndex],
          value: mpnFiles.value,
        };

        newFormInputs[foundIndex] = updatedItem;
        setFormInputs(newFormInputs);
      }
    } else if (mpnFiles === null || mpnFiles?.value?.length <= 0) {
      setShowTab(false);
      setSelectedTab(0);
      setProductFileMasterId(null);
      setConditionalStyles([]);
    }
  }, [mpnFiles]);

  useLayoutEffect(() => {
    if (productDetailsId !== null && productDetailsId !== undefined) {
      setAddBrokerEnable(true);
    }
  }, [productDetailsId]);

  useLayoutEffect(() => {
    if (mpnSubFiles?.value) {
      const stateValues = { ...distributorForm }; // Shallow copy of distributorForm

      if (
        stateValues?.formSubDetailsInternalDTOList &&
        stateValues?.formSubDetailsInternalDTOList.length
      ) {
        const brokerTableIndex =
          stateValues.formSubDetailsInternalDTOList.findIndex(
            (e) => e.uniqueKey === 'MPN-subDetails'
          );

        if (brokerTableIndex !== -1) {
          // Create a shallow copy of the object before modifying
          stateValues.formSubDetailsInternalDTOList = [
            ...stateValues.formSubDetailsInternalDTOList,
          ];
          stateValues.formSubDetailsInternalDTOList[brokerTableIndex] = {
            ...stateValues.formSubDetailsInternalDTOList[brokerTableIndex],
            value: mpnSubFiles?.value,
          };
        }
      }

      setDistributorForm(stateValues);
    }
  }, [mpnSubFiles]);
  useLayoutEffect(() => {
    /**
     * <AUTHOR> Sherlin
     * @email <EMAIL>
     * @description: getting Sublist based on client Search RFQ response.
     */
    if (mpnSearchSubFiles?.value) {
      const stateValues = { ...sentRFQForm };

      if (stateValues?.formSubDetailsInternalDTOList?.length) {
        let rfqbrokerTableIndex =
          stateValues.formSubDetailsInternalDTOList.findIndex(
            (e) => e.uniqueKey === 'tablePreview'
          );

        if (rfqbrokerTableIndex !== -1) {
          // Create a new copy of the item you're modifying
          const updatedItem = {
            ...stateValues.formSubDetailsInternalDTOList[rfqbrokerTableIndex],
            value: mpnSearchSubFiles.value, // Update value
          };

          // Replace the old item with the updated one in a new array
          const updatedFormSubDetailsInternalDTOList = [
            ...stateValues.formSubDetailsInternalDTOList.slice(
              0,
              rfqbrokerTableIndex
            ),
            updatedItem,
            ...stateValues.formSubDetailsInternalDTOList.slice(
              rfqbrokerTableIndex + 1
            ),
          ];

          // Update the state with the new array
          setSentRFQForm({
            ...stateValues,
            formSubDetailsInternalDTOList: updatedFormSubDetailsInternalDTOList,
          });
        }
      }
    }
  }, [mpnSearchSubFiles]);

  const selectTab = (tIndex) => {
    if (tIndex === 2) {
      dispatch(
        bbMPNSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
    setSelectedTab(tIndex);
  };

  const forms = (formControls) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function to Render Form Element.
     */
    return formControls && formControls.length
      ? formControls?.map((element, i) => {
          if (element.uniqueKey === 'page-title') {
            return (
              <div key={i}>
                <Input formType={element} excelPath={excelPreview} />
                <div
                  className="two-col-layout-right-30"
                  style={{ marginBottom: '-40px' }}
                >
                  <div className="three-col-layout ">
                    {forms(element.formSubDetailsInternalDTOList)}
                  </div>
                  <div></div>
                </div>
              </div>
            );
          } else {
            return (
              <Input
                excelPath={excelPreview}
                onChangeHandler={(type, event) =>
                  handlers[element.onChangeFunction](type, event, 'search')
                }
                dataTableEventHandler={(type, event) =>
                  handlers[element.onChangeFunction](type, event, element)
                }
                selectedRow={(selectedProps) =>
                  handlers[element.onChangeFunction]?.(
                    selectedProps,
                    null,
                    'rowSelect'
                  )
                }
                dataTableColumn={
                  element.formSubDetailsInternalDTOList &&
                  element.formSubDetailsInternalDTOList.length
                    ? element.formSubDetailsInternalDTOList.map((rowData) => {
                        return {
                          width: rowData.displayWidth
                            ? rowData.displayWidth
                            : '',
                          name: rowData.displayName ? rowData.displayName : '',
                          selector: rowData.selector ? rowData.selector : '',
                          cell: dataTableServiceProvider.bbModuleCustomTableCell(
                            rowData
                          ),
                        };
                      })
                    : []
                }
                conditionalRowStyles={conditionalRowStyles}
                isEditable="notShowing"
                formType={element}
                key={i}
              />
            );
          }
        })
      : null;
  };
  const FormTabs = useCallback(
    ({ tabControls }) => {
      /**
       * <AUTHOR> sherlin
       * @email <EMAIL>
       * @description: callBack to Render Form Tab to avoid reRendering.
       */

      if (showTab) {
        return (
          <Tabs
            onSelect={(tabIndex) => selectTab(tabIndex)}
            className="mb24"
            selectedIndex={selectedTab}
          >
            <TabList>
              {tabControls && tabControls.length
                ? tabControls.map((tabListElement, key) => {
                    if (tabListElement.type.includes('TabPanel')) {
                      return (
                        <Tab key={key.toString()}>
                          {tabListElement.displayName}
                        </Tab>
                      );
                    }
                  })
                : null}
            </TabList>

            <TabPanel key={0}>
              <div className="card mb40">
                <DistributorBroker
                  form={distributorForm}
                  selectedTab={selectedTab}
                  productDetailsId={productDetailsId}
                  conditionalRowStyles={conditionalRowStyles}
                  setSelectedTab={selectTab}
                />
              </div>
            </TabPanel>
            <TabPanel key={1}>
              <div className="card mb40">
                <PreviewList
                  form={previewForm}
                  excelPath={excelPreview}
                  productFileMasterId={productFileMasterId}
                  clientId={bbClientID}
                  setSelectedTab={selectTab}
                />
              </div>
            </TabPanel>
            <TabPanel key={2}>
              <div className="card mb40">
                <SentRFQ
                  form={sentRFQForm}
                  sentEnable={sendRFQEnable}
                  setSelectedTab={selectTab}
                />
              </div>
            </TabPanel>
          </Tabs>
        );
      } else {
        <div></div>;
      }
    },
    [
      selectedTab,
      bbClientID,
      distributorForm,
      previewForm,
      sentRFQForm,
      showTab,
    ]
  );

  const onChangeHandler = (type, event, action) => {
    const newFormInputs = [...formInputs];
    const subDetails = [...newFormInputs[0].formSubDetailsInternalDTOList];

    if (type.uniqueKey === 'client-control') {
      subDetails[0] = {
        ...subDetails[0],
        value: event.target.value,
      };
      setIsSearch({
        ...isSearch,
        isClientValue: event.target.value,
      });
    } else if (type.uniqueKey === 'date-control') {
      subDetails[1] = {
        ...subDetails[1],
        value: event,
      };
      setIsSearch({
        ...isSearch,
        isDateValue: event,
      });
    }

    const isClient = subDetails[0].value;

    if (isClient) {
      const searchButtonIndex = subDetails.findIndex(
        (el) => el.uniqueKey === 'button-search'
      );
      if (searchButtonIndex !== -1) {
        subDetails[searchButtonIndex] = {
          ...subDetails[searchButtonIndex],
          disableFlag: 'F',
        };
      }
    }

    newFormInputs[0] = {
      ...newFormInputs[0],
      formSubDetailsInternalDTOList: subDetails,
    };

    setFormInputs(newFormInputs);
    setProductFileMasterId(null);
  };

  function searchMPN() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for Search.
     */
    if (isSearch.isSearchDisabled) {
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
  }
  function onClearHandler() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: to clear all selected controls.
     */
    if (!isSearch.isSearchDisabled) {
      var updatedState = [...formInputs];
      updatedState.map((input) => {
        input.value = null;
        input.formSubDetailsInternalDTOList.map((subInput) => {
          subInput.value = null;
          if (subInput.uniqueKey === 'button-search') {
            subInput.disableFlag = 'T';
          }
          if (subInput.uniqueKey === 'clear-button') {
            subInput.disableFlag = 'T';
          }
        });
      });
      setFormInputs(updatedState);
      setShowTab(false);
    }
    if (!isSearch.isSearchDisabled) {
      dispatch(bbForms('subscribe'));
    }
  }
  const handleRowClick = (type, event, el) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    switch (el.uniqueKey) {
      case 'fileTable':
        var styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
          type.serialNo,
          'serialNo'
        );
        processMPNCache.addDistributor.mpnSelectedStyle = [];
        processMPNCache.addDistributor.distributorStyle = [];
        setConditionalStyles(styleAttribute);
        setProductFileMasterId(type.productFileMasterId);
        setbbClientID(type.bbClientId);

        dispatch(
          bbMPNviewSearchSubList({
            masterID: type.productFileMasterId,
            reqType: 'subscribe',
          })
        );
        dispatch(bbMPNPreviewLists(type.productFileMasterId));
        setProductDetailsId(type.productFileDetailsId);

        setShowTab(true);
        setSelectedTab(0);

        break;

      default:
        break;
    }
  };

  const handlers = {
    onChangeHandler,
    handleRowClick,
    searchMPN,
    onClearHandler,
  };
  return (
    <>
      {/* Loader for entire Component */}
      <CommonSpinner visible={loading} />

      {/* rendering Form Controls */}
      {forms(formInputs)}
      <FormTabs tabControls={formInputs} />
    </>
  );
};

export { ProcessMpn };
