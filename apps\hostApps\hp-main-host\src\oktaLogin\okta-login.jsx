import { OktaA<PERSON> } from '@okta/okta-auth-js';
import { useDispatch, useSelector } from 'react-redux';
import { login } from '@hp/mainstore';
import { useNavigate } from 'react-router-dom';
import { environment } from './environments/environment';

const ApOktaLogin = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { userCredential } = useSelector((store) => store.user);

  let authClient;
  if (userCredential) {
    authClient = new OktaAuth({
      issuer: userCredential.issuer,
      clientId: userCredential.clientId,
      //redirecting
      // redirectUri:
      // `${process.env.NX_API_BASE}` + userCredential.redirectLoginUrl,
      // onAuthRequired: onAuthRequired,
      // pkce: true
      // clientId: environment.OktaClientID,
      // issuer: environment.OktaIssuer,
      // redirectUri: environment.oktaRedirectUri,
    });
  } else {
    authClient = new OktaAuth({
      // onAuthRequired: onAuthRequired,
      // pkce: true
      clientId: environment.OktaClientID,
      issuer: environment.OktaIssuer,
      redirectUri: environment.oktaRedirectUri,
    });
  }

  if (authClient.isLoginRedirect()) {
    // Parse token from redirect url
    authClient.token.parseFromUrl().then((data) => {
      const { idToken } = data.tokens;
      // Store parsed token in Token Manager
      authClient.tokenManager.add('idToken', idToken);
      dispatch(
        login({
          username: '',
          password: '',
          AP_historyCopy: '/home',
          loginType: 'okta',
        })
      );
      navigate('/');
    });
  } else {
    // Attempt to retrieve ID Token from Token Manager
    authClient.tokenManager.get('idToken').then((idToken) => {
      if (idToken) {
        dispatch(
          login({
            username: '',
            password: '',
            AP_historyCopy: '/home',
            loginType: 'okta',
          })
        );
        navigate('/');
      } else {
        // You're not logged in, you need a sessionToken
        authClient.token.getWithRedirect({
          responseType: 'id_token',
        });
      }
    });
  }
};
export default ApOktaLogin;
