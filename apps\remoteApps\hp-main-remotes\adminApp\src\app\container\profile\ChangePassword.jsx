/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> Samuel
 * @email <EMAIL>
 * @create date 27-05-2021 15:39:10
 * @modify date 2024-07-26 16:42:09
 * @desc [description]
 */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { resetsaveChangePasswordDetailsAndSendMail } from '@hp/mainstore';
import cloneDeep from 'lodash.clonedeep';
import { Input } from '@hp/components';
import { Button } from '@hp/components';
import { logout, showDialog } from '@hp/mainstore';
import {
  getChangePasswordFormDetails,
  saveChangePasswordDetailsAndSendMail,
} from '@hp/mainstore';
import './change_password.scss';
import { globalutils } from '@hp/components';
const ChangePassword = () => {
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  const dispatch = useDispatch();

  const { changePasswordFormDetails, changePasswordStatus } = useSelector(
    (store) => store.settings
  );
  const [formDetails, setFormDetails] = useState([]);
  const [btnDisabled, setbtnDisabled] = useState(false);
  const [viewPassword, setViewPasswd] = useState({
    oldPwd: false,
    newPwd: false,
    confirmPwd: false,
  });

  useEffect(() => {
    getChangePasswordFormDetailsfn();
    return () => {
      dispatch(resetsaveChangePasswordDetailsAndSendMail());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  //function for setting notification
  const funcToSetResponseMessage = async (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    await dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  async function autoLogout() {
    var clearTimer = null;
    clearTimer = setTimeout(function () {
      var count = 10;
      var countdownTimer = setInterval(async function () {
        // Display the count on the screen
        await dispatch(
          showDialog({
            showPopup: true,
            type: 'success',
            responseMessage: `Your password has been changed successfully, You will be logged out in ${count} seconds`,
            canClose: false,
            autoHide: false,
          })
        );
        count--;
        if (count === 0) {
          // Redirect to the next page

          clearInterval(countdownTimer);
          clearTimeout(clearTimer);
          dispatch(logout());
        }
      }, 1000); // Update the count every second
    }, 1000); // Wait for 10 seconds
  }

  useEffect(() => {
    if (changePasswordStatus && changePasswordStatus.value) {
      // let showPopup = true;
      // let canClose = false;
      // let autoHide = false;

      if (changePasswordStatus?.value?.type === 'success') {
        autoLogout();
        // dispatch(
        //   alertActions.showDialog(
        //     showPopup,
        //     'success',
        //     'Your password has been changed successfully, You will be logged out in 10 seconds',
        //     canClose,
        //     autoHide
        //   )
        // );
      } else if (changePasswordStatus?.value?.type === 'error') {
        setbtnDisabled(false);
        funcToSetResponseMessage('error', changePasswordStatus.value.text);
      }
    }
  }, [changePasswordStatus]);

  useEffect(() => {
    changePasswordFormDetails && changePasswordFormDetails.value
      ? setFormDetails(changePasswordFormDetails.value)
      : '';
  }, [changePasswordFormDetails]);

  const getChangePasswordFormDetailsfn = () => {
    dispatch(getChangePasswordFormDetails());
  };

  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);

    tempArray = tempArray.map((item) =>
      item.uniqueKey === uniqueKey
        ? { ...item, value: event.target.value }
        : item
    );

    setFormDetails(tempArray);
  };

  const handleSave = () => {
    if (
      formDetails[0].value === '' ||
      formDetails[1].value === '' ||
      formDetails[0].value === null ||
      formDetails[1].value === null
    ) {
      funcToSetResponseMessage('error', 'Please enter valid details');
    } else {
      dispatch(
        saveChangePasswordDetailsAndSendMail({
          formDetails: formDetails,
          userId: userId,
        })
      );

      setbtnDisabled(true);
    }
  };

  // const functionsName = {
  //   handleSave,
  // };

  const onChangeHandlingFunctions = {
    handleOnChange,
  };

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          return (
            <>
              <div style={{ display: 'inline-block', width: '300px' }}>
                <Input
                  indexKey={index}
                  formType={element}
                  showPassword={viewPassword[element.uniqueKey]}
                  onChangeHandler={(element, event) =>
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey,
                      element
                    )
                  }
                />
              </div>
              <div className="chg-passwd-show-pass-container">
                <span
                  className="chg-passwd-show-pass-main"
                  onClick={() =>
                    setViewPasswd((prev) => {
                      return {
                        ...prev,
                        [element.uniqueKey]: !prev[element.uniqueKey],
                      };
                    })
                  }
                >
                  <span
                    className={
                      viewPassword[element.uniqueKey]
                        ? 'icon icon-eye'
                        : 'icon icon-eye-blocked'
                    }
                  ></span>
                </span>
              </div>
            </>
          );
        })
      : null;
  };

  return (
    <>
      <div className="page-title">Change Password</div>
      <div className="three-col-layout mb20">
        <div className="col">
          {formDetails && formDetails.length
            ? formControlsBinding(formDetails)
            : ''}
        </div>
      </div>
      <div id="btn-bottom-white-bar" className="group fixed-button-bar">
        <Button
          disabled={btnDisabled}
          className="default mr20 fl button"
          onClick={handleSave}
        >
          Save
        </Button>
      </div>
      {/* <ButtonCommon functionsName={functionsName} /> */}
    </>
  );
};
export { ChangePassword };
