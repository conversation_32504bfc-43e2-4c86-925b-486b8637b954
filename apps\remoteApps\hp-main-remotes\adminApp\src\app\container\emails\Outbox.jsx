/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-prototype-builtins */
/* eslint-disable @typescript-eslint/no-unused-expressions */

import React, { useState, useEffect } from 'react';

import {
  emailOutboxDetailsById,
  ouboxTableListing,
  getOutgoingEmailId,
  getClientComboBox,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { Scrollbars } from 'react-custom-scrollbars';
import { EmailListing } from '@hp/components';
import { EmailSearch } from '@hp/components';
import { EmailBody } from '@hp/components';
import { EmailEmpty } from '@hp/components';
import { EmailNewCompose } from '@hp/components';
import { EmailReply } from '@hp/components';
import { EmailPagination } from '@hp/components';
import '@hp/styles/EmailUI.scss';
import Modal from 'react-modal';
import cloneDeep from 'lodash.clonedeep';
import { Tabs } from 'react-tabs';
import {
  CommonSpinner,
  DocumentViewer,
  getClientFormattedCurrentDate,
  Select,
} from '@hp/components';

const Outbox = (props) => {
  const dispatch = useDispatch();
  const {
    clientCombolist,
    listingEmailOutbox,
    outboxTableListing,
    outboxDetails,
  } = useSelector((store) => store.email);
  const [filteredData, setFilteredData] = useState();
  const [limit, setLimit] = useState(0);
  const [offset, setOffset] = useState(50);
  //const [activeFirst, setActiveFirst] = useState(true);
  const [activeLast, setActiveLast] = useState(true);
  const [state, setState] = useState();
  const [newCompose, setNewCompose] = useState(false);
  const [emailReply, setEmailReply] = useState(false);
  const [currentDate, setCurrentDate] = useState();
  const [isLoading, setLoading] = useState(false);
  const [filePath, setfilePath] = React.useState('');
  const [isModal, setIsModal] = React.useState(false);
  //const [tableListLength, setTableListLength] = useState(0);

  let tableName = [];
  // if (table_name.indexOf(",") > -1) {
  //   tableName = table_name.split(",");
  // }

  useEffect(() => {
    setCurrentDate(getClientFormattedCurrentDate());
  }, []);

  const [TableListing, setTableList] = useState([]);
  const [clientCombo, setClientCombo] = useState();
  const [emailCombo, setEmailCombo] = useState();

  useEffect(() => {
    getClientCombo();
  }, [props]);

  useEffect(() => {
    if (clientCombolist && clientCombolist.value) {
      const clientOptions = clientCombolist.value.map((value) => {
        return {
          value: value.clientId,
          display: value.clientName,
        };
      });
      setClientCombo(clientOptions);
    }
  }, [clientCombolist]);

  useEffect(() => {
    if (listingEmailOutbox && listingEmailOutbox.value) {
      const emailOptions = listingEmailOutbox.value.map((value) => {
        return {
          value: value.connKeyId,
          display: value.email,
        };
      });
      setEmailCombo(emailOptions);
    }
  }, [listingEmailOutbox]);

  useEffect(() => {
    if (outboxTableListing && outboxTableListing.value) {
      setActiveLast(true);
      setTableList(outboxTableListing.value);
      setFilteredData(outboxTableListing.value);
      //setTableListLength(outboxTableListing.value.length);
      if (outboxTableListing.value.length < offset) {
        setActiveLast(false);
      }
      setState('');
    }
    return () => {
      setTableList('');
    };
  }, [outboxTableListing]);

  useEffect(() => {
    setLoading(false);
    setState('');
    if (outboxDetails && outboxDetails.value) {
      setState(outboxDetails.value);
    }
    return () => {
      setState('');
    };
  }, [outboxDetails]);

  const getClientCombo = () => {
    dispatch(getClientComboBox());
  };

  const getEmailId = (e) => {
    let clientId = e;
    dispatch(getOutgoingEmailId(clientId));
  };

  const getEmailTableListing = (e) => {
    let emailConnKeyId = e;
    dispatch(ouboxTableListing(emailConnKeyId));
  };

  const onclickFunc = (data, id) => {
    let docId = data.documentId ? data.documentId : '';
    let emailId = data.emailId ? data.emailId : '';
    dispatch(emailOutboxDetailsById({ docId: docId, emailId: emailId }));

    let newTableListing = cloneDeep(filteredData);
    newTableListing.map(function (data, idx) {
      newTableListing[idx].isSelected = false;
      if (idx === id) {
        newTableListing[idx].isSelected = true;
        newTableListing[idx].readFlag = 'Y';
      }
    });
    setFilteredData(newTableListing);
    setNewCompose(false);
    setState('');
    setLoading(true);
    setEmailReply(false);
  };

  const pageableFunc = (iconName) => {
    if (limit >= 0) {
      iconName === 'first'
        ? setLimit(limit - limit)
        : iconName === 'prev'
        ? setLimit(limit - 1)
        : iconName === 'next'
        ? setLimit(limit + 1)
        : iconName === 'last'
        ? setLimit(limit + 1)
        : null;
    }
  };

  const viewFrame = (path) => {
    setfilePath(path);
    setIsModal(true);
  };

  return (
    <>
      <div>
        <div className="compare-layout-mask mb32">
          <Select
            label="Client"
            className="mr56"
            style={{ width: 130 + 'px' }}
            onChange={(e) => getEmailId(e.target.value)}
            options={clientCombo}
          />
          <Select
            label="Email"
            style={{ width: 250 + 'px' }}
            onChange={(e) => getEmailTableListing(e.target.value)}
            options={emailCombo}
          />
        </div>
      </div>
      <CommonSpinner visible={isLoading} />

      <div className="email-ui flex-row">
        <div className="left-controls">
          {/* New Message Button */}
          <div className="new-message-wrap">
            {/* <button
              className="new-message"
              onClick={() => setNewCompose(!newCompose)}
            >
              New Message
            </button> */}
          </div>

          {/* Message Count */}
          <div className="message-count">Outbox</div>

          {/* Search Wrap */}
          <EmailSearch
            tableList={TableListing}
            // processFlag={processTabClick}
            menuParameters={tableName}
            filteredData={(data) => setFilteredData(data)}
          />

          <Tabs className="email-tabs">
            <div className="email-list">
              <Scrollbars class="scroll-wrap">
                {filteredData
                  ? filteredData.map(function (data, idx) {
                      return (
                        <EmailListing
                          key={idx}
                          isEmailRead={data.readFlag === 'Y' ? false : true}
                          subject={data.subject}
                          email={data.fromAddress}
                          name={data?.fromName || ''}
                          isSelected={
                            data.hasOwnProperty('isSelected')
                              ? data.isSelected
                              : false
                          }
                          date={data.sentDate}
                          time={data.sentTime}
                          attachmentCount={data?.attachmentCount}
                          invCount={data?.invCount}
                          onClick={() => onclickFunc(data, idx)}
                          // org="Pinnacle India"
                          // label="2 Invoices"
                        />
                      );
                    })
                  : null}
              </Scrollbars>
              <EmailPagination
                // activeFirst={activeFirst}
                activeLast={activeLast}
                currentPage={limit + 1}
                totalPage={''}
                pageableIconClick={(iconName) => pageableFunc(iconName)}
                pagePerRowClick={(value) => setOffset(Number(value))}
              />
            </div>
          </Tabs>
        </div>
        <div className="right-controls">
          {newCompose ? (
            <EmailNewCompose />
          ) : state ? (
            <>
              <EmailBody
                subject={state ? state.subject : ''}
                // isProcessed={isProcessedFlag}
                // name={assignedUser ? assignedUser : ""}
                fromName=""
                fromEmail={state ? state.fromAddress : ''}
                toName=""
                toEmail={
                  state && state.toAddressList && state.toAddressList.length
                    ? state.toAddressList.map((resData) => {
                        return resData;
                      })
                    : ''
                }
                attachments={
                  state &&
                  state.emailAttachmentDetailsDtoList &&
                  state.emailAttachmentDetailsDtoList.length
                    ? state.emailAttachmentDetailsDtoList.map((resdata) => {
                        return {
                          fileName: resdata.fileName,
                          filePath: resdata.attachementPath,
                          isVerified: resdata.verified,
                          isZip: resdata.zip,
                          subData:
                            resdata.emailAttachmentDetailsDtoSub &&
                            resdata.emailAttachmentDetailsDtoSub.length
                              ? resdata.emailAttachmentDetailsDtoSub
                              : '',
                        };
                      })
                    : []
                }
                emailBody={state && state.content ? state.content : ''}
                onFileClick={(filePath) => viewFrame(filePath)}
                replyEmail={() => setEmailReply(true)}
              />
              {emailReply ? (
                <EmailReply
                  subject={state ? 'RE: ' + state.subject : ''}
                  // fromName="Sudhir Raj"
                  fromEmail={
                    state && state.toAddressList && state.toAddressList.length
                      ? state.toAddressList.map((resData) => {
                          return resData;
                        })
                      : ''
                  }
                  toName=""
                  toEmail={state ? state.fromAddress : ''}
                  date={currentDate ? currentDate : ''}
                  onDeleteClick={() => setEmailReply(false)}
                />
              ) : null}
            </>
          ) : (
            <EmailEmpty />
          )}
        </div>
      </div>
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>
        <DocumentViewer
          fileURL={filePath}
          fileType={'iframe'}
          zoom={'#zoom=50'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
        />
      </Modal>
    </>
  );
};

export { Outbox };
