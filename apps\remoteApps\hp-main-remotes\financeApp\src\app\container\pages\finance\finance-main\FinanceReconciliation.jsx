/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import cloneDeep from 'lodash.clonedeep';
import './finance_list.scss';
import {
  getReconciliationDetailsFormCount,
  getReconciliationDetailsForm,
  getReconciliationMasterForm,
  getTotalCountForMasterForm,
  getAllRestOfPayments,
  financeResetStateField,
  updatePaymentOfStatment,
  showDialog,
} from '@hp/mainstore';
import { dynamicListlimitAndOffset } from '@hp/mainstore';
import DataTable from 'react-data-table-component';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import { CommonSpinner, dataTableServiceProvider, Input } from '@hp/components';
import { Tooltip } from 'antd';
import RestPaymentsTable from './RestPaymentsTable';

function FinanceReconciliation(props) {
  const dispatch = useDispatch();
  let user = JSON.parse(localStorage.getItem(AP_USER));
  const userId = parseInt(user?.userId);
  const clientId = user?.clientId;
  const {
    masterFormRes,
    detailsFormRes,
    detailsFormCountRes,
    loading,
    getAllRestPaymentsRes,
    updatePaymentRes,
  } = useSelector((store) => store.finance);

  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);
  const { dynamicListLimitAndOffset } = useSelector((store) => store.util);

  const [masterTableData, setMasterTableData] = useState([]);
  const [detailsTableData, setDetailsTableData] = useState([]);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [detailsConditionalRowStyles, setDetailsConditionalStyles] = useState(
    []
  );
  const [selectedDetailsRowId, setSelectedDetailsRowId] = useState(null);
  const [persistentHighlightRowId, setPersistentHighlightRowId] =
    useState(null);
  const [bankInTxnId, setBankInTxnId] = useState(null);
  const [offset, setOffset] = useState(1);
  const [limit, setLimit] = useState(10);
  const [modalVisible, setModalVisible] = useState(false);
  const [restPaymentsTable, setRestPayemntTable] = useState([]);
  const [currStatementId, setCurrStatementId] = useState(null);
  const [waitingForUpdate, setWaitingForUpdate] = useState(false);
  const [msgShow, setMsgShow] = useState(false);
  const [paymentTableData, setPaymentTableData] = useState([]);

  useEffect(() => {
    let obj = {
      userId,
      clientId,
      limit: 10,
      offset: 1,
    };

    dispatch(getReconciliationMasterForm(obj));
    dispatch(getTotalCountForMasterForm({ userId, clientId }));
    return () => {
      resetDataTable();
      setBankInTxnId(null);
      setModalVisible(false);
      // Reset details table highlighting
      setDetailsConditionalStyles([]);
      setSelectedDetailsRowId(null);
      setPersistentHighlightRowId(null);
      setMsgShow(false);
      dispatch(
        financeResetStateField({
          fieldNames: [
            'masterFormRes',
            'detailsFormRes',
            'getAllRestPaymentsRes',
            'updatePaymentRes',
          ],
        })
      );
    };
  }, []);

  useEffect(() => {
    if (dynamicListLimitAndOffset) {
      let obj = {
        userId,
        clientId,
        limit: dynamicListLimitAndOffset.limit,
        offset: dynamicListLimitAndOffset.offset,
      };
      dispatch(getReconciliationMasterForm(obj));
    }
  }, [dynamicListLimitAndOffset]);

  useEffect(() => {
    if (masterFormRes?.value?.length) {
      let tempMstrTbl = masterFormRes.value.filter(
        (item) => item.uniqueKey === 'pf_reconcile_master'
      );
      setMasterTableData(tempMstrTbl);
    }
  }, [masterFormRes]);

  useEffect(() => {
    if (getAllRestPaymentsRes?.value?.type === 'DataTable') {
      setRestPayemntTable(getAllRestPaymentsRes.value);
      setModalVisible(true);
    }
  }, [getAllRestPaymentsRes]);

  useEffect(() => {
    if (updatePaymentRes?.value) {
      dispatch(
        showDialog({
          showPopup: true,
          type: 'success',
          responseMessage: updatePaymentRes.value,
          canClose: true,
          autoHide: true,
        })
      );

      setWaitingForUpdate(true);
    }
  }, [updatePaymentRes]);

  useEffect(() => {
    if (waitingForUpdate && !loading) {
      setModalVisible(false);
      setWaitingForUpdate(false);

      setTimeout(() => {
        handleOnSaveModal();
      }, 50);
    }
  }, [loading, waitingForUpdate]);

  useEffect(() => {
    if (detailsFormRes?.value?.length) {
      setDetailsTableData(detailsFormRes.value);
      let tempPymtTbl = detailsFormRes?.value.find(
        (item) => item.uniqueKey === 'pf_reconcile_pymt'
      );
      setPaymentTableData(tempPymtTbl?.value || []);
      if (
        persistentHighlightRowId
        // && bankInTxnId
      ) {
        const statementTable = detailsFormRes.value.find(
          (item) => item.uniqueKey === 'pf_reconcile_stmt'
        );

        if (statementTable?.value) {
          const targetRow = statementTable.value.find(
            (row) => row.bankInDetailsId === persistentHighlightRowId
          );

          if (targetRow) {
            const styleAttribute =
              dataTableServiceProvider.conditionalDataTableRow(
                targetRow.serialNo,
                'serialNo'
              );
            setDetailsConditionalStyles(styleAttribute);
            setSelectedDetailsRowId(persistentHighlightRowId);
          } else {
            // If the persistent highlight row is not found in new data, clear it
            setPersistentHighlightRowId(null);
            setDetailsConditionalStyles([]);
            setSelectedDetailsRowId(null);
          }
        }
      } else {
        // Clear highlighting when there's no persistent highlight or no master row selected
        setDetailsConditionalStyles([]);
        setSelectedDetailsRowId(null);
      }
    }
  }, [
    detailsFormRes,
    persistentHighlightRowId,
    // bankInTxnId
  ]);

  useEffect(() => {
    getReconciliationDetailsFormHandler(bankInTxnId);
  }, [limit, offset]);

  const resetDataTable = () => {
    let obj = {
      limit: 10,
      offset: 1,
    };
    dispatch(dynamicListlimitAndOffset(obj, 'subscribe'));
  };

  const onRowClick = (row) => {
    if (row?.bankInTxnId === bankInTxnId) return;
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
    setBankInTxnId(row?.bankInTxnId);

    // Clear details table highlighting when master table selection changes
    setDetailsConditionalStyles([]);
    setSelectedDetailsRowId(null);
    setPersistentHighlightRowId(null);

    getReconciliationDetailsFormHandler(row?.bankInTxnId);
  };

  const getReconciliationDetailsFormHandler = (id) => {
    if (!id) return;
    let obj = masterFormRes.value.filter(
      (item) => item.uniqueKey !== 'pf_reconcile_master'
    );

    dispatch(
      getReconciliationDetailsForm({
        intOutTxnId: id,
        limit: limit,
        offset: offset,
        obj: obj,
      })
    );
    if (id === bankInTxnId) return;
    dispatch(getReconciliationDetailsFormCount(id));
  };

  const DisplayTitle = (row, key) => {
    return (
      <Tooltip classNames={{ root: 'ant-tooltip-container' }} title={row[key]}>
        <div
          className="display-title custom-overflow"
          style={{
            width: key === 'amount' ? '100%' : '',
            textAlign: key === 'amount' ? 'end' : '',
          }}
        >
          {row[key]}
        </div>
      </Tooltip>
    );
  };

  const handlePageChange = (currentPageParam) => {
    setOffset(currentPageParam);
  };

  const handlePerRowsChange = (newPerPageParam) => {
    setLimit(newPerPageParam);
  };

  // Check if all fields except serialNo are null in paymentTableData
  let checkPymtStmt = (stmtRow, paymentTableData) => {
    let isRowFound = paymentTableData?.find(
      (row) => row?.serialNo === stmtRow?.serialNo
    );

    if (!isRowFound) return false;

    return Object.entries(isRowFound).every(([key, value]) => {
      return key === 'serialNo' || value === null;
    });
  };

  const callBackFunc = async (data) => {
    setMsgShow(checkPymtStmt(data, paymentTableData));
    setCurrStatementId(data?.bankInDetailsId);
    setPersistentHighlightRowId(data?.bankInDetailsId);

    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      data.serialNo,
      'serialNo'
    );
    setDetailsConditionalStyles(styleAttribute);
    setSelectedDetailsRowId(data?.bankInDetailsId);

    await dispatch(
      getAllRestOfPayments({
        statementId: data?.bankInDetailsId,
      })
    );
  };

  const handleOnSaveModal = () => {
    dispatch(
      getReconciliationMasterForm({
        userId,
        clientId,
        limit,
        offset,
      })
    );
    dispatch(getTotalCountForMasterForm({ userId, clientId }));
    if (bankInTxnId) {
      getReconciliationDetailsFormHandler(bankInTxnId);
    }
  };

  const renderMasterTable = (data) => {
    if (!data.length) return;
    return data.map((item) => {
      const dataClone = cloneDeep(item);

      let columns = [];
      if (dataClone.formSubDetailsInternalDTOList?.length) {
        columns = dataClone.formSubDetailsInternalDTOList.map((col) => {
          return {
            selector: col.selector,
            name: col.displayName,
            width: col.selector,
            cell: function renderCell(row) {
              return (
                <Tooltip
                  classNames={{ root: 'ant-tooltip-container' }}
                  title={row[col.selector]}
                >
                  <div
                    className="display-title custom-overflow"
                    style={{
                      width: col.selector === 'totalAmount' ? '100%' : '',
                      textAlign: col.selector === 'totalAmount' ? 'right' : '',
                    }}
                  >
                    {row[col.selector]}
                  </div>
                </Tooltip>
              );
            },
          };
        });
      }

      return (
        <Input
          formType={item}
          dataTableEventHandler={onRowClick}
          conditionalRowStyles={conditionalRowStyles}
          dataTableColumn={columns}
        />
      );
    });
  };
  const renderReconciliationDetailsTable = (data) => {
    if (!data.length) return;
    const statementObj = data.find(
      (item) => item.uniqueKey === 'pf_reconcile_stmt'
    );
    const statementDataValues =
      statementObj?.value?.length !== 0 ? true : false;
    return data.map((item) => {
      const isStatementTable = item.uniqueKey === 'pf_reconcile_stmt';
      return (
        <div key={item.uniqueKey}>
          <div className="mb24 styledDatatable">
            <h1 className="page-sub-title">{item.displayName}</h1>

            <DataTable
              persistTableHead
              highlightOnHover={true}
              noHeader={true}
              striped={true}
              {...(isStatementTable &&
                statementDataValues && {
                  conditionalRowStyles: detailsConditionalRowStyles,
                  customStyles: {
                    rows: {
                      style: {
                        '&:hover': {
                          cursor: 'pointer',
                          '& > div:last-child': {
                            backgroundColor: '#F0F0F0 !important',
                            zIndex: 15,
                          },
                        },
                        '&.rdt_TableRow.selected > div:last-child': {
                          backgroundColor: '#F0F0F0 !important',
                          zIndex: 20,
                          opacity: 1,
                        },
                      },
                    },
                    headCells: {
                      style: {
                        backgroundColor: '#fff',
                        '&:last-child': {
                          position: 'sticky',
                          right: 0,
                          zIndex: 35,
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundColor: '#C0C0C0',
                            zIndex: -1,
                          },
                          borderLeft: '1px solid #ddd',
                          boxShadow: '-2px 0 4px rgba(0,0,0,0.1)',
                          opacity: 1,
                          backgroundColor: '#C0C0C0',
                        },
                      },
                    },
                    cells: {
                      style: {
                        '&:last-child': {
                          position: 'sticky',
                          right: 0,
                          zIndex: 15,
                          borderLeft: '1px solid #ddd',
                          backgroundColor: '#fff !important',
                          color: 'black',
                          opacity: 1,
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundColor: '#fff',
                            opacity: 1,
                            zIndex: -1,
                          },
                        },
                      },
                    },
                    tableWrapper: {
                      style: {
                        overflowX: 'auto',
                        position: 'relative',
                      },
                    },
                  },
                })}
              columns={
                item.formSubDetailsInternalDTOList &&
                item.formSubDetailsInternalDTOList.length
                  ? [
                      ...item.formSubDetailsInternalDTOList.map((value) => {
                        return {
                          name: value.displayName ? value.displayName : '',
                          selector: value.selector ? value.selector : '',
                          width: value.displayWidth ? value.displayWidth : '',
                          cell: (row) => DisplayTitle(row, value.selector),
                          sortable: true,
                        };
                      }),
                      ...(isStatementTable && statementDataValues
                        ? [
                            {
                              name: '',
                              width: '40px',
                              cell: (row) => (
                                <Tooltip
                                  classNames={{ root: 'ant-tooltip-container' }}
                                  title="Manually Reconcile"
                                >
                                  <div
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      callBackFunc(row);
                                    }}
                                  >
                                    <div className="icon-edit-button"></div>
                                  </div>
                                </Tooltip>
                              ),
                              ignoreRowClick: true,
                              allowOverflow: true,
                              button: true,
                            },
                          ]
                        : []),
                    ]
                  : []
              }
              data={item?.value}
              pagination={true}
              paginationDefaultPage={offset}
              paginationResetDefaultPage={true}
              paginationPerPage={limit}
              paginationTotalRows={
                detailsFormCountRes && detailsFormCountRes.value
                  ? detailsFormCountRes.value
                  : ''
              }
              onChangeRowsPerPage={handlePerRowsChange}
              onChangePage={handlePageChange}
            />
          </div>
        </div>
      );
    });
  };

  return (
    <>
      <div className="page-title Finance-listing-container">
        {' '}
        {subMenuName} / {innerMenuName}
      </div>
      <CommonSpinner visible={loading} />
      <div className="mb20">{renderMasterTable(masterTableData)}</div>
      <div className="two-col-layout-20">
        {renderReconciliationDetailsTable(detailsTableData)}
      </div>
      {modalVisible && (
        <Modal
          className="ModalPoMatrix"
          overlayClassName="ModalOverlay"
          ariaHideApp={false}
          isOpen={modalVisible}
        >
          <div
            onClick={() => {
              setModalVisible(false);
              setMsgShow(false);
            }}
            className="modal-close icon-close"
          ></div>
          {restPaymentsTable && (
            <RestPaymentsTable
              formData={restPaymentsTable}
              MsgShow={msgShow}
              onCloseModal={() => setModalVisible(false)}
              onTriggerUpdate={(paymentId) => {
                dispatch(
                  updatePaymentOfStatment({
                    paymentId,
                    statementId: currStatementId,
                  })
                );
                setMsgShow(false);
              }}
            />
          )}
        </Modal>
      )}
    </>
  );
}

export { FinanceReconciliation };
