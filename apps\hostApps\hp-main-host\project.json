{"name": "hp-main-host", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/hostApps/hp-main-host/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "dist/apps/hostApps/hp-main-host", "main": "apps/hostApps/hp-main-host/src/main.jsx", "tsConfig": "apps/hostApps/hp-main-host/tsconfig.app.json", "rspackConfig": "apps/hostApps/hp-main-host/rspack.config.js", "assets": ["apps/hostApps/hp-main-host/src/favicon.ico", "apps/hostApps/hp-main-host/src/assets"], "commands": ["npx workbox-cli generateSW ./apps/hostApps/hp-main-host/workbox-config.js"], "parallel": false}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "optimization": true, "sourceMap": false}}, "dependsOn": []}, "serve": {"executor": "@nx/rspack:module-federation-dev-server", "options": {"buildTarget": "hp-main-host:build:development", "port": 4200, "host": "0.0.0.0", "publicPath": "auto", "allowedHosts": "all", "hmr": true}, "configurations": {"development": {}, "production": {"buildTarget": "hp-main-host:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/rspack:module-federation-static-server", "defaultConfiguration": "production", "options": {"serveTarget": "hp-main-host:serve"}, "configurations": {"development": {"serveTarget": "hp-main-host:serve:development"}, "production": {"serveTarget": "hp-main-host:serve:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hostApps/hp-main-host/jest.config.js"}}}, "implicitDependencies": []}