import React from 'react';
import Modal from 'react-modal';
import { LinkInvoiceBlock } from './LinkInvoiceBlock';
import { CommonSpinner } from '@hp/components';

function LinkInvoiceModal({
  linkButtonClick,
  tableData,
  modalOpen,
  modalClose,
  apiCallFunction,
  linkButtonName,
  isLoading,
}) {
  return (
    <Modal
      className="Modal-content"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={modalOpen}
    >
      <div onClick={modalClose} className="modal-close icon-close mb20"></div>

      <CommonSpinner visible={isLoading} />
      <LinkInvoiceBlock
        tableData={tableData}
        serachApiCall={apiCallFunction}
        rowLink={linkButtonClick}
        linkButtonName={linkButtonName}
        active={modalOpen}
      />
    </Modal>
  );
}

export { LinkInvoiceModal };
