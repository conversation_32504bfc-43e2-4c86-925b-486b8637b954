import React from 'react';

function SessionElement({ details }) {
  return (
    <div className="session-element mb20 card">
      <div className="element-details">
        <div className="field mb16">
          <span className="label">Session Time </span>
          <span> : {details?.sessionTime}</span>
        </div>
        {/* <div className="field">
          <span className="label">Event Time</span>
          <span>: {details?.eventTime}</span>
        </div> */}
        {details?.amount ? (
          <div className="field">
            <span className="label">Amount</span>
            <span>: {details?.amount}</span>
          </div>
        ) : (
          ''
        )}
        {details?.fileName ? (
          <div className="field">
            <span className="label">File Name</span>
            <span>: {details?.fileName}</span>
          </div>
        ) : (
          ''
        )}

        {details?.message ? <p className="msg mt16">{details?.message}</p> : ''}
      </div>
      <div className="status-overview">
        <p className={details?.statusType}>{details?.status}</p>
      </div>
    </div>
  );
}

export { SessionElement };
