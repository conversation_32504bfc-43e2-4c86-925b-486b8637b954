/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> AUGUSTINE
 * @email <EMAIL>
 * @create date 2022-09-01 14:06:08
 * @modify date 2023-05-22 16:52:01
 * @desc [description]
 */

import {
  useConfirm,
  ButtonCommon,
  Input,
  Button,
  CommonSpinner,
  formValidationUtil,
} from '@hp/components';
import React, { useEffect, useState } from 'react';
import {
  showDialog,
  getManageSequenceForm,
  deleteCategorySequenceDetail,
  addCategorySequenceDetail,
  editCategorySequenceDetail,
  resetSeqActionRes,
} from '@hp/mainstore';
import { useSelector, useDispatch } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { dataTableServiceProvider } from '@hp/components';

function ManageSequenceNumber() {
  const [sequneceForm, setSequneceForm] = useState([]);
  const [initialFormState, setInitialFormState] = useState([]);
  const [rowData, setRowData] = useState({});
  const [action, setAction] = useState('');
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [formIsOpen, setFormIsOpen] = useState(false);
  const [formDisable, setFormDisable] = useState(true);
  const [toggle, setToggle] = useState('N');
  // const [filterData, setFilterData] = useState([]);

  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  const { manageSequenceForm, seqActionRes, loading } = useSelector(
    (store) => store.pdm
  );
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);

  //function for showing notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };
  // Fetch Manage Sequnce Form
  useEffect(() => {
    dispatch(getManageSequenceForm());
    return () => {
      dispatch(resetSeqActionRes());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (seqActionRes && seqActionRes.value) {
      funcToSetResponseMessage('success', seqActionRes.value);
      dispatch(getManageSequenceForm());
      setFormIsOpen(false);
      setAction('');
    }
    return () => {
      dispatch(resetSeqActionRes());
    };
  }, [seqActionRes]);

  useEffect(() => {
    if (
      manageSequenceForm &&
      manageSequenceForm.value &&
      manageSequenceForm.value.length
    ) {
      // Create a deep copy of the form
      let seqForm = manageSequenceForm.value.map((formList) => {
        // Copy the formList object
        let updatedFormList = { ...formList };

        if (
          updatedFormList.type === 'PageTitle' &&
          updatedFormList.formSubDetailsInternalDTOList
        ) {
          // Copy the sub-details array to avoid modifying the original
          updatedFormList.formSubDetailsInternalDTOList =
            updatedFormList.formSubDetailsInternalDTOList.map((list) => {
              // Copy each sub-detail object
              let updatedList = { ...list };

              if (updatedList.uniqueKey === 'existing_category') {
                updatedList.type = ''; // Update the copied object
              }

              return updatedList; // Return the modified object
            });

          return updatedFormList; // Return the modified formList object
        } else if (updatedFormList.type === 'DataTable') {
          // setFilterData(updatedFormList.value);

          return updatedFormList;
        }

        return updatedFormList; // Return the copied formList object
      });

      setSequneceForm(seqForm); // Update state with the new sequence form
      setInitialFormState(seqForm); // Set the initial state
    }
  }, [manageSequenceForm]);

  const elementMap = (subForm, subIndex, index, isToggle) => {
    let sequneceFormClone = cloneDeep(sequneceForm);
    let subDiv = sequneceFormClone[index];
    subDiv.formSubDetailsInternalDTOList.splice(subIndex, 1, subForm);
    if (isToggle === 'Y' || isToggle === 'N') {
      setToggle(isToggle);
      subDiv.formSubDetailsInternalDTOList.map((list) => {
        if (list.uniqueKey === 'manual_entry')
          isToggle === 'Y' ? (list.type = '') : (list.type = 'div');
        else if (list.uniqueKey === 'existing_category')
          isToggle === 'Y' ? (list.type = 'div') : (list.type = '');
      });
    }
    sequneceFormClone.slice(index, 1, subDiv);
    setSequneceForm(sequneceFormClone);
  };

  // handle toggle button
  const toggleHandler = (event, element, subList, subIndex, index) => {
    let obj = { ...element, value: element.value === 'Y' ? 'N' : 'Y' };
    let subFormList = [...subList.formSubDetailsInternalDTOList];

    subFormList = subFormList.map((list) => {
      if (list.type === 'Toggle') return obj;
      else return list;
    });
    let subForm = { ...subList, formSubDetailsInternalDTOList: subFormList };

    elementMap(subForm, subIndex, index, obj.value);
  };

  // select Handler
  const selectHandler = (event, element, subList, subIndex, index) => {
    let obj = { ...element, value: parseInt(event.target.value) };
    let subFormList = [...subList.formSubDetailsInternalDTOList];
    subFormList = subFormList.map((list) => {
      if (list.uniqueKey === element.uniqueKey) return obj;
      else return list;
    });
    let subForm = { ...subList, formSubDetailsInternalDTOList: subFormList };

    elementMap(subForm, subIndex, index, null);
  };

  // input field Handler
  const inputHandler = (event, element, subList, subIndex, index) => {
    let selectedElement = formValidationUtil.isFormcontrolValidDynamic(
      event,
      element
    );
    if (!selectedElement.valid) return;
    if (element.uniqueKey === 'seq_no')
      selectedElement.value = Number(selectedElement.value);
    else selectedElement.value = selectedElement.value.toUpperCase();
    let subFormList = [...subList.formSubDetailsInternalDTOList];
    subFormList = subFormList.map((list) => {
      if (list.uniqueKey === element.uniqueKey) return selectedElement;
      else return list;
    });
    let subForm = { ...subList, formSubDetailsInternalDTOList: subFormList };

    elementMap(subForm, subIndex, index, null);
  };

  const addSeqHandler = () => {
    setAction('Add');
    setFormDisable(false);
    setFormIsOpen(true);
    setSequneceForm(initialFormState);
  };

  // Edit table row data handler function

  const editCategory = (row, edit) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalRowStyles(styleAttribute);
    setRowData(row);
    setFormIsOpen(true);
    let sequneceFormClone = cloneDeep(sequneceForm);
    if (edit) setFormDisable(false);
    else setFormDisable(true);

    const valueMaper = (form) => {
      if (form.type === 'PageTitle') {
        form.formSubDetailsInternalDTOList.map((list) => {
          if (list.uniqueKey === 'manual_entry')
            row.useExistingSeqCategoryId
              ? (list.type = '')
              : (list.type = 'div');
          if (list.uniqueKey === 'existing_category')
            row.useExistingSeqCategoryId
              ? (list.type = 'div')
              : (list.type = '');
        });
        form.formSubDetailsInternalDTOList.some(valueMaper);
      } else if (form.type === 'div' || form.type === '') {
        form.formSubDetailsInternalDTOList.some(valueMaper);
      }
      form.uniqueKey === 'category_name'
        ? (form.value = row.categoryId ? row.categoryId : '')
        : form.uniqueKey === 'toggle'
        ? ((form.value = row.useExistingSeqCategoryId ? 'Y' : 'N'),
          setToggle(row.useExistingSeqCategoryId ? 'Y' : 'N'))
        : form.uniqueKey === 'seq_no'
        ? (form.value = row.seqNum ? row.seqNum : '')
        : form.uniqueKey === 'prefix'
        ? (form.value = row.prefix ? row.prefix : '')
        : form.uniqueKey === 'suffix'
        ? (form.value = row.suffix ? row.suffix : '')
        : form.uniqueKey === 'category'
        ? (form.value = row.useExistingSeqCategoryId
            ? row.useExistingSeqCategoryId
            : '')
        : '';
    };

    sequneceFormClone.some(valueMaper);
    setSequneceForm(sequneceFormClone);
  };

  //delete function
  const deleteCategory = async (row) => {
    let isConfirm = await confirm('Confirm Delete?');
    if (isConfirm && row?.itemCategorySeqNumId) {
      dispatch(deleteCategorySequenceDetail(row.itemCategorySeqNumId));
    }
  };

  // save function
  const saveHandler = () => {
    if (!formIsOpen) return;
    var requiredCheckedObj =
      formValidationUtil.checkMandatoryField(sequneceForm);
    setSequneceForm([...requiredCheckedObj.formList]);
    if (requiredCheckedObj.validSuccess) {
      let values = [];
      const formExtractor = (form) => {
        if (form.type === 'PageTitle') {
          form.formSubDetailsInternalDTOList.some(formExtractor);
        } else if (form.type === 'div') {
          values = [...values, ...form.formSubDetailsInternalDTOList];
        }
      };
      sequneceForm.some(formExtractor);
      if (action === 'Add')
        dispatch(addCategorySequenceDetail({ value: values, toggle }));
      else
        dispatch(
          editCategorySequenceDetail({
            itemCategorySeqNumId: rowData?.itemCategorySeqNumId ?? null,
            obj: values,
            toggleState: toggle,
          })
        );
    } else funcToSetResponseMessage('error', 'Please fill mandatory fields');
  };

  // cancel function

  const cancelHandler = () => {
    setFormIsOpen(false);
  };

  // table Column Handler

  const tableColumnHandler = (data) => {
    let column;
    data.formSubDetailsInternalDTOList &&
    data.formSubDetailsInternalDTOList.length
      ? (column = data.formSubDetailsInternalDTOList.map((value, index) => {
          return {
            width: value.displayWidth ? value.displayWidth : '',
            name: value.displayName ? value.displayName : '',
            selector: value.selector ? value.selector : '',
            cell:
              value.selector === 'editIcon'
                ? function displayCell(row) {
                    return (
                      <div
                        className="icon-edit-button"
                        onClick={() => {
                          editCategory(row, true);
                          setAction('Edit');
                        }}
                        key={index}
                      ></div>
                    );
                  }
                : value.selector === 'deleteIcon'
                ? function displayCell(row) {
                    return (
                      <div
                        className="icon-2-trash"
                        onClick={() => deleteCategory(row)}
                      ></div>
                    );
                  }
                : '',
          };
        }))
      : [];
    return column;
  };

  // form loader

  const seqFormHandler = (list, index) => {
    if (list.type === 'Button') {
      return (
        <div
          className="fr"
          key={index}
          style={{
            marginTop: '10px',
          }}
        >
          {!formIsOpen && (
            <Button
              onClick={() => addSeqHandler()}
              className="small mb8 outline add-button-custom flex-row vam "
            >
              <i className="icon-add-button "> </i>
              {list.displayName || 'add'}
            </Button>
          )}
        </div>
      );
    } else if (list.type === 'DataTable') {
      return (
        <Input
          key={index}
          formType={list}
          dataTableColumn={tableColumnHandler(list)}
          dataTableEventHandler={(row) => {
            editCategory(row, false);
          }}
          conditionalRowStyles={conditionalRowStyles}
        />
      );
    } else if (list.type === 'PageTitle' && formIsOpen) {
      return (
        <div className="card mb20" style={{ minHeight: '215px' }} key={index}>
          <span
            className="icon-close fr "
            onClick={cancelHandler}
            style={{
              cursor: 'pointer',
            }}
          ></span>
          {list.formSubDetailsInternalDTOList &&
          list.formSubDetailsInternalDTOList.length
            ? list.formSubDetailsInternalDTOList.map((sublist, subIndex) => {
                return (
                  <Input
                    key={subIndex}
                    disabledFlag={formDisable}
                    isEditable={'notShowing'}
                    formType={sublist}
                    onChangeHandler={(element, event) =>
                      onChangeHandlingFunctions[element.onChangeFunction](
                        event,
                        element,
                        sublist,
                        subIndex,
                        index
                      )
                    }
                  />
                );
              })
            : ''}
        </div>
      );
    } else if (list.type === 'GlobalSearch') {
      return;
      // return (
      //   <Input
      //     key={index}
      //     tableListData={filterData}
      //     getFilteredData={dataTableFilter}
      //     formType={list}
      //   />
      // );
    }
  };

  // Data table filter function
  // const dataTableFilter = (filteredValue) => {
  //   let tempArray = structuredClone(sequneceForm);
  //   tempArray.map((element) => {
  //     if (element.type === 'DataTable') {
  //       element.value = filteredValue;
  //     }
  //   });
  //   setSequneceForm(tempArray);
  // };

  const onChangeHandlingFunctions = {
    addSeqHandler,
    toggleHandler,
    selectHandler,
    inputHandler,
  };

  const functionsName = {
    saveHandler,
    cancelHandler,
  };

  return (
    <>
      <h1 className="page-title">
        {subMenuName}/{innerMenuName}
      </h1>
      <CommonSpinner visible={loading} />
      {sequneceForm.map((form, index) => {
        return seqFormHandler(form, index);
      })}
      <ButtonCommon functionsName={functionsName} />
    </>
  );
}

export { ManageSequenceNumber };
