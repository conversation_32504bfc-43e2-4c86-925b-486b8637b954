/* eslint-disable eqeqeq */
/* eslint-disable react/no-unknown-property */
import { Card, Skeleton } from 'antd';

import React, { useState } from 'react';
import { Tooltip } from 'react-tooltip';
import { SingleEmailDetails } from '@hp/components';

/**
 * <AUTHOR> V
 * @see This function returns list of object
 * */
function INVEmailDetailsComponent(
  emails,
  activeKey,
  isExpanded,
  singleInvEmailDetails,
  isSingleEmailDtlLoading,
  setIsSingleEmailDtlLoading
) {
  const panelStyle = (panelKey) => ({
    marginBottom: 15,
    background: 'rgb(246, 246, 246)',
    borderRadius: '0px',

    ...(activeKey === panelKey && {
      background: 'rgb(137, 171, 213)',
      border: '1px solid black',
      color: '#FFFFFF',
    }),
  });

  return emails.map((data, index) => {
    return {
      key: index,
      label: (
        <>
          <div className="email-list-outer">
            <div style={{ width: 42 + '%' }}>
              <p
                data-tooltip-id={`email-eventTime-toolTip-${index}`}
                data-tooltip-delay-show={500}
                data-tooltip-content={data?.eventTime}
                data-tooltip-place={'top-start'}
                data-tooltip-offset={5}
                style={{
                  color: isExpanded && index == activeKey ? 'white' : '',
                }}
              >
                {data?.eventTime}
              </p>
              <div>
                <EmailUserPopUp
                  fromName={data.fromName}
                  fromAddress={data.fromAddress}
                  toAddress={data?.toAddress}
                  toAddressList={data?.toAddressList}
                  index={index}
                  activeKey={activeKey}
                  isExpanded={isExpanded}
                  icon={data?.icon}
                />
              </div>
            </div>

            <div
              data-tooltip-id={`email-subject-tooltip-${index}`}
              data-tooltip-content={data?.subject}
              data-tooltip-place={'bottom-start'}
              data-tooltip-delay-show={1000}
              data-tooltip-offset={5}
              className="subject-ellipsis"
              style={{
                width: '53%',
                wordBreak: 'break-word',
                // background:'yellow'
              }}
            >
              <span
                style={{
                  color: isExpanded && index == activeKey ? 'white' : '',
                }}
              >
                <span style={{ fontWeight: '500' }}>Sub : </span>
              </span>
              <span
                style={{
                  color: isExpanded && index == activeKey ? 'white' : '',
                }}
              >
                {data?.subject}
                {/* {(data?.subject).length >= 50
                  ? data?.subject.substring(0, 50) + '...'
                  : data?.subject} */}
              </span>
            </div>
            <div
              style={{
                width: '5%',
                justifyContent: 'center',
                textAlign: 'center',
              }}
            >
              <div
                data-tooltip-id={`email-recieve-sent-icon-${index}`}
                data-tooltip-place={'top'}
                style={{
                  color:
                    data?.icon == 'in-coming'
                      ? '#00157F'
                      : data?.icon == 'out-going'
                      ? '#194d03'
                      : '',
                }}
                className={
                  data?.icon == 'in-coming'
                    ? 'icon-down-arrow email-icon-size'
                    : data?.icon == 'out-going'
                    ? 'icon-up-arrow email-icon-size'
                    : ''
                }
                // rcvd : rgb(0, 205, 0)', send:'rgb(0, 178, 238)'
              ></div>
            </div>
          </div>

          {/* //* Subject ToolTip */}

          <Tooltip
            id={`email-subject-tooltip-${index}`}
            className="example-no-radius"
            arrowColor="transparent"
            opacity={1}
            style={{
              backgroundColor: '#f3f3f3',
              WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
              color: '#000',
              borderRadius: '0',
              padding: '2px 5px 2px 5px',
              zIndex: '1',
              fontSize: '11px',
            }}
          ></Tooltip>
          {/* //* Email IN-OUT Icon ToolTip*/}

          <Tooltip
            id={`email-recieve-sent-icon-${index}`}
            className="example-no-radius"
            arrowColor="transparent"
            opacity={1}
            style={{
              backgroundColor: '#f3f3f3',
              WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
              color: '#000',
              borderRadius: '0',
              padding: '2px 5px 2px 5px',
              zIndex: '1',
              fontSize: '11px',
            }}
          >
            {data?.icon == 'in-coming'
              ? 'Recieved'
              : data?.icon == 'out-going'
              ? 'Sent'
              : ''}
          </Tooltip>

          <Tooltip
            id={`email-eventTime-toolTip-${index}`}
            className="example-no-radius"
            arrowColor="transparent"
            opacity={1}
            style={{
              backgroundColor: '#f3f3f3',
              WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
              color: '#000',
              borderRadius: '0',
              padding: '2px 5px 2px 5px',
              zIndex: '1',
              fontSize: '11px',
            }}
          ></Tooltip>
        </>
      ),

      children:
        activeKey != null && emails[activeKey] != undefined ? (
          <SingleEmailDetails
            singleInvEmailDetails={singleInvEmailDetails}
            isSingleEmailDtlLoading={isSingleEmailDtlLoading}
            setIsSingleEmailDtlLoading={setIsSingleEmailDtlLoading}
          />
        ) : (
          ''
        ),
      style: panelStyle(index),
    };
  });
}

/**
 * <AUTHOR> V
 * @see This Component display the User Details of In/Out emails.
 * */
const EmailUserPopUp = ({
  fromName,
  fromAddress,
  toAddress,
  toAddressList,
  index,
  activeKey,
  isExpanded,
  icon,
}) => {
  const [copiedEmail, setCopiedEmail] = useState(null);

  const copyUserEmailAddress = async (event, email) => {
    event.stopPropagation(); // Prevent event propagation
    event.preventDefault(); // Prevent default action (e.g., link navigation)

    navigator.clipboard.writeText(email);
    const text = await navigator.clipboard.readText();
    setCopiedEmail(text);
  };

  const toAddressListToString = (toAddressListArray) => {
    if (toAddressListArray?.length > 1) {
      return `${toAddressListArray[0]} +${toAddressListArray?.length - 1}`;
    } else {
      return `${toAddressListArray[0]}`;
    }
  };

  return (
    <>
      <div
        // className="compose-content"
        style={{
          display: 'flex',
          overflow: 'hidden',
          color: isExpanded && index == activeKey ? 'white' : '',
        }}
        data-tooltip-id={`email-fromName-tooltip-${index}`}
        data-tooltip-place={'bottom-start'}
        data-tooltip-delay-show={1000}
      >
        <div class="from-to-ellipsis">
          <span>
            {' '}
            {icon == 'in-coming' ? (
              <span style={{ fontWeight: '500' }}>From</span>
            ) : (
              <span style={{ fontWeight: '500' }}>To</span>
            )}{' '}
            :{' '}
            {icon == 'in-coming'
              ? fromName
                ? fromName
                : fromAddress
              : toAddressList && toAddressList?.length
              ? toAddressListToString(toAddressList)
              : 'NA'}{' '}
          </span>
        </div>
        {/* <div>
               10
        </div> */}
      </div>

      <Tooltip
        id={`email-fromName-tooltip-${index}`}
        className="example-no-radius"
        arrowColor="transparent"
        opacity={1}
        style={{
          backgroundColor: '#f3f3f3',
          WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
          color: '#000',
          borderRadius: '0',
          padding: '2px 5px 2px 5px',
          zIndex: '1',
          fontSize: '11px',
        }}
        clickable
      >
        <Card
          style={{
            marginTop: 5,
          }}
          size="small"
        >
          {icon == 'in-coming' ? (
            <Skeleton loading={false} avatar active>
              <div className="inv-email-dflex">
                <div
                  className="icon-user-check secondary mr8"
                  style={{ color: 'gray' }}
                ></div>
                <div style={{ fontSize: '14px', color: 'gray' }}>
                  {fromName ? <b>{fromName}</b> : <b>NA</b>}
                </div>
              </div>
              <div>
                {fromAddress && fromAddress != null ? (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'center',
                    }}
                  >
                    {/* <div className="icon-mail4 mr4"></div> */}
                    <a
                      className="email-on-hover "
                      style={{ fontSize: '12px' }}
                      href={fromAddress}
                    >
                      {fromAddress}
                    </a>
                    <div
                      style={{
                        borderRadius: '50%',
                        background: 'rgb(230,230,230)',
                        // paddingLeft: '5px',
                        marginLeft: '5px',
                      }}
                    >
                      <div
                        className={
                          copiedEmail == fromAddress
                            ? 'icon-checked'
                            : 'icon-copy'
                        }
                        data-tooltip-id={`email-copy-tooltip-${index}`}
                        data-tooltip-offset={0}
                        data-tooltip-place={'top'}
                        onClick={(event) =>
                          copyUserEmailAddress(event, fromAddress)
                        }
                        style={{
                          color: 'rgb(0, 178, 238)',
                          textAlign: 'center',
                          padding: '5px',
                        }}
                      ></div>
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </div>
            </Skeleton>
          ) : (
            <Skeleton loading={false} avatar active>
              <div>
                {toAddressList && toAddressList?.length ? (
                  <div
                  // style={{
                  //   display: 'flex',
                  //   justifyContent: 'start',
                  //   alignItems: 'center',
                  // }}
                  >
                    {toAddressList?.map((data, index) => {
                      return (
                        <div key={index}>
                          <div style={{ display: 'flex' }}>
                            <a
                              className="email-on-hover "
                              style={{ fontSize: '12px' }}
                              href={data}
                            >
                              {data}
                            </a>
                            <div
                              style={{
                                borderRadius: '50%',
                                background: 'rgb(230,230,230)',
                                // paddingLeft: '5px',
                                marginLeft: '5px',
                              }}
                            >
                              <div
                                className={
                                  copiedEmail == data
                                    ? 'icon-checked'
                                    : 'icon-copy'
                                }
                                data-tooltip-id={`email-copy-tooltip-emailList-${index}`}
                                data-tooltip-offset={0}
                                data-tooltip-place={'top'}
                                data-tooltip-content={
                                  copiedEmail == data ? 'Copied' : 'Copy'
                                }
                                onClick={(event) =>
                                  copyUserEmailAddress(event, data)
                                }
                                style={{
                                  color: 'rgb(0, 178, 238)',
                                  textAlign: 'center',
                                  padding: '5px',
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  ''
                )}
              </div>
            </Skeleton>
          )}
        </Card>
      </Tooltip>

      <Tooltip
        id={`email-copy-tooltip-${index}`}
        className="example-no-radius"
        arrowColor="transparent"
        delayHide={1000}
        opacity={1}
        style={{
          backgroundColor: '#f3f3f3',
          WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
          color: '#000',
          borderRadius: '0',
          padding: '2px 5px 2px 5px',
          fontSize: '11px',
          zIndex: 2,
        }}
      >
        {copiedEmail == fromAddress ? 'Copied' : 'Copy'}
      </Tooltip>

      <Tooltip
        id={`email-copy-tooltip-emailList-${index}`}
        className="example-no-radius"
        arrowColor="transparent"
        delayHide={1000}
        opacity={1}
        style={{
          backgroundColor: '#f3f3f3',
          WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
          color: '#000',
          borderRadius: '0',
          padding: '2px 5px 2px 5px',
          fontSize: '11px',
          zIndex: 2,
        }}
      ></Tooltip>
    </>
  );
};

export { INVEmailDetailsComponent };
