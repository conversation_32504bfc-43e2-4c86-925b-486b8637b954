
import React, { useEffect } from 'react';
import DataTable from 'react-data-table-component';
import { DatePickerInput } from '@hp/components';

function LaunchProduct(props) {
  const sourceHead1 = [
    { name: 'Item #', selector: 'P/N' },
    { name: 'Item Name', selector: 'Title' },
    { name: 'Use Net', selector: 'UseNet' },
    { name: 'Buy Net', selector: 'BuyNet' },
    { name: 'Buy As', selector: 'BuyAs' },
    { name: 'Vendor', selector: 'Vendor' },
    { name: 'Lead Time', selector: 'Lead time' },
    { name: 'Promise Date', selector: 'Promise Date' },
  ];

  // const [nodeId, setNodeId] = useState(null);
  const formHandeler = (action, node) => {
    // setNodeId(node.id);
    // dispatch(pdmAction.getPDMItemDetails(id));
  };
  useEffect(() => {
    formHandeler(props.Action, props.Node);
  }, [props.Action, props.Node]);

  return (
    <>
      <div className="two-col-layout">
        <DatePickerInput label={'Launch Date'} className={'mb20'} />
      </div>
      <div className="mb20 styledDatatable ">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={true}
          //   onRowDoubleClicked={(event) => rowDblClickHandler(event)}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationTotalRows={10}
          paginationPerPage={10}
          //   conditionalRowStyles={conditionalRowStyles}
          columns={sourceHead1}
          data={[]}
        />
      </div>
    </>
  );
}

export { LaunchProduct };
