/* eslint-disable eqeqeq */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable array-callback-return */
/* eslint-disable no-control-regex */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @modify date 2024-07-29 16:46:18
 * @desc [Approval details screen]
 */

import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useState,
} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getINVDetailsForAccPay } from '@hp/mainstore';
import {
  traceEvent,
  getInvEmailTree,
  emailResetStateField,
} from '@hp/mainstore';
import {
  AP_file_url,
  AP_USER,
  PP_APPROVE_REJECT_ERROR_ALERT,
} from '@hp/constants';
import { showDialog } from '@hp/mainstore';

import { PP_APPROVE_ERROR_ALERT, PP_REJECT_ERROR_ALERT } from '@hp/constants';
import { accPayConstants } from '@hp/mainstore';
import { PoMatrix } from '../../invoices/invoice-common/PoMatrix';
import { InvMatchingComponent } from '../../invoices/invoice-sub/InvMatchingComponent';
import { Tabs } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import cloneDeep from 'lodash.clonedeep';
import { InvDatatable } from '../../invoices/invoice-sub/inv-table/INV-DataTable';
import { invoiceService } from '../../invoices/invoice-common/invoice.service';
import INVEmailList from '../../invoices/invoice-sub/INV-Email-List';
import { useAppRouterDom, utils } from '@hp/utils';
import {
  approveInvoiceOnNthApprovals,
  approvalTraceEvents,
  updateppStatusOnReject,
} from '@hp/mainstore';
import {
  ButtonCommon,
  CommonSpinner,
  DocumentViewer,
  MultiRowInput,
  NewCheckBoxInput,
  TraceEvents,
  useConfirm,
  RejectModal,
} from '@hp/components';
import { InvPageHeader } from '../../invoices/invoice-main/InvPageHeader';
import { globalutils } from '@hp/components';
const CombinedInvSupplier = (props) => {
  const { confirm } = useConfirm();

  const { domParameters, navigate, location } = useAppRouterDom();
  let user = globalutils.getDataFromStorage('all');
  const buttonData = useSelector((state) => state.buttons.buttons);
  const userId = parseInt(user?.userId);

  const emailRedirection = location.pathname.split('/')[6];
  const { emailTree } = useSelector((store) => store.email);
  const clientId = user?.clientId;
  const mainMenu = domParameters?.menuData || '';
  const subMenu = domParameters?.submenu || '';
  const table_name = domParameters?.parameters || '';
  let tableName = [];
  if (table_name.indexOf(',') > -1) {
    tableName = table_name.split(',');
  }
  const [getTraceEvents, setTraceEvents] = React.useState([]);
  const [getApprovalTraceEvents, setApprovalTraceEvents] = React.useState([]);

  const [addCommentsValue, setAddCommentsValue] = useState('');
  const [modalForReject, setModalForReject] = useState(false);
  const [rejectParameter, setRejectParameter] = useState();
  const [rejectErrorFlag, setRejectErrorFlag] = useState(false);
  const [buttonDetails, setButtons] = useState([]);
  const [filePath, setFilePath] = useState('');
  const [activeTabKey, setActiveTabKey] = useState('0');
  const [otherChargesFormData, setOtherChargesFormData] = useState();
  const [poOrInvResponse, setPoOrInvResponse] = useState([]);
  const [showInvoiceToggle, setShowInvoiceToggle] = useState(true);
  const [invEmailListPresent, setInvEmailListPresent] = useState(false);
  const [invEmailTreeList, setInvEmailTreeList] = useState([]);

  const onAddCommentsChangeHandler = (event) => {
    setAddCommentsValue(event.target.value);
    if (event) setRejectErrorFlag(false);
  };
  const modalForRejectCloseClick = () => {
    setModalForReject(false);
    setAddCommentsValue('');
  };
  const dispatch = useDispatch();
  const {
    accPaytraceEvents,
    accpayBreadCrumb,
    invEventTime,
    pprejectResponse,
    pprejectResponseError,
    ApListingOnRowClickData,
    approveInvoiceOnNthApproval,
    approveInvoiceOnNthApprovalError,
    accPayApprovalTraceEvents,
    accpayINVDetails,
    apCachedData,
    apListingFilteredData,
  } = useSelector((store) => store.accpay);

  let PO_ID =
    ApListingOnRowClickData && ApListingOnRowClickData.po_id
      ? parseInt(ApListingOnRowClickData.po_id)
      : null;
  let PP_ID =
    ApListingOnRowClickData && ApListingOnRowClickData.pp_id
      ? parseInt(ApListingOnRowClickData.pp_id)
      : null;

  let INV_ID =
    ApListingOnRowClickData && ApListingOnRowClickData.inv_id
      ? parseInt(ApListingOnRowClickData.inv_id)
      : null;

  let ppEventTime =
    ApListingOnRowClickData && ApListingOnRowClickData.eventTime
      ? ApListingOnRowClickData.eventTime
      : null;
  let currApproverId =
    ApListingOnRowClickData && ApListingOnRowClickData.curApproverId
      ? parseInt(ApListingOnRowClickData.curApproverId)
      : null;
  let profileId =
    ApListingOnRowClickData && ApListingOnRowClickData.profileId
      ? parseInt(ApListingOnRowClickData.profileId)
      : null;
  let ppStatus =
    ApListingOnRowClickData && ApListingOnRowClickData.status
      ? ApListingOnRowClickData.status
      : null;
  let Keyvalue =
    props?.location?.state && props?.location?.state?.Keyvalue
      ? props?.location?.state?.Keyvalue
      : emailRedirection
      ? emailRedirection
      : null;
  let invoiceGrossAmount =
    ApListingOnRowClickData && ApListingOnRowClickData.amountConverted
      ? ApListingOnRowClickData.amountConverted
      : null;

  if (window.location.pathname.split('/').includes('mail-access')) {
    // const encodedString = window.location.search.slice(2);
    const encodedString =
      window.location.search !== null &&
      window.location.search !== undefined &&
      window.location.search !== ''
        ? window.location.search.slice(2)
        : localStorage.getItem('apParamObj');
    const decodedString = decodeURIComponent(
      // encodedString.replace(/%(?!\[\da-f]{2})/gi, "")
      encodedString.replace(/%(\[0-9A-F]{2})/g, '')
    );
    let convertedString = atob(decodedString);
    // eslint-disable-next-line no-control-regex
    convertedString = convertedString.replace(/[^\x00-\x7F]/g, '');
    const selectedRecord = JSON.parse(convertedString);

    PO_ID = selectedRecord?.po_id;
    PP_ID = selectedRecord?.pp_id;
    INV_ID = selectedRecord?.inv_id;
    ppEventTime = selectedRecord?.eventTime;
    currApproverId = selectedRecord?.curApproverId;
    profileId = selectedRecord?.profileId;
    ppStatus = selectedRecord?.status;
    invoiceGrossAmount = selectedRecord?.amountConverted;
  }
  const [invoiceImageChecked, setInvoiceImageChecked] = useState(
    PO_ID ? false : true
  );

  const isThreeColLayout = !filePath && !PO_ID ? true : false;
  const [isLoading, setisLoading] = useState(false);
  const approvalObj = {
    clientId: clientId,
    poId: PO_ID,
    invId: INV_ID,
    ppId: PP_ID,
    ppStatus: ppStatus,
    ppEventTime: ppEventTime,
    userId: userId,
    userComments: addCommentsValue,
    profileId: profileId,
    invoiceGrossAmount: invoiceGrossAmount,
  };

  const funcToSetResponseMessage = useCallback(
    (type, resMessage, autoHide) => {
      let showPopup = true;
      let canClose = true;
      dispatch(
        showDialog({
          showPopup,
          type,
          responseMessage: resMessage,
          canClose,
          autoHide,
        })
      );
    },
    [dispatch]
  );

  useEffect(() => {
    //To show next and previous button for 3 Seconds
    setShowInvoiceToggle(true);
    const timeout = setTimeout(() => {
      setShowInvoiceToggle(false);
    }, 3000);
    return () => {
      clearTimeout(timeout);
    };
  }, []);
  useEffect(() => {
    return () => {
      dispatch({
        type: accPayConstants.ACCPAY_APPROVAL_TRACE_EVENTS_SUCCESS,
        payload: null,
      });
      dispatch({
        type: accPayConstants.ACCPAY_APPROVAL_TRACE_EVENTS_FAILURE,
        payload: null,
      });
    };
  }, []);

  useLayoutEffect(() => {
    if (INV_ID) {
      INV_ID && user?.clientId && user?.clientId != undefined
        ? dispatch(
            getInvEmailTree({
              clientId: user?.clientId,
              invId: INV_ID,
            })
          )
        : '';
    }

    return () => {
      dispatch(dispatch(emailResetStateField({ fieldNames: ['emailTree'] })));
    };
  }, []);

  useEffect(() => {
    if (emailTree?.value && emailTree?.value?.length > 0) {
      setInvEmailTreeList(emailTree?.value);
      setInvEmailListPresent(true);
    }
  }, [emailTree]);

  useEffect(() => {
    setisLoading(false);
    let error = null;
    if (pprejectResponse) {
      navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
    } else if (pprejectResponseError && pprejectResponseError.value) {
      funcToSetResponseMessage('error', pprejectResponseError.value);
      dispatch({
        type: accPayConstants.ACCPAY_REJECT_RESPONSE_Failure,
        error,
      });
    }
  }, [
    dispatch,
    funcToSetResponseMessage,
    navigate,
    mainMenu,
    pprejectResponse,
    pprejectResponseError,
    subMenu,
    table_name,
  ]);

  useEffect(() => {
    setisLoading(false);
    let error = null;
    if (approveInvoiceOnNthApproval) {
      navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
      navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
    } else if (approveInvoiceOnNthApprovalError) {
      funcToSetResponseMessage('error', approveInvoiceOnNthApprovalError);
      dispatch({
        type: accPayConstants.APPROVE_INVOICE_ON_Nth_APPROVAL_FAIL,
        error,
      });
    }
  }, [
    approveInvoiceOnNthApproval,
    approveInvoiceOnNthApprovalError,
    dispatch,
    funcToSetResponseMessage,
    navigate,
    mainMenu,
    subMenu,
    table_name,
  ]);

  useEffect(() => {
    let Active = true;
    let autoHide = true;
    if (tableName && tableName[1] !== 'for-xmsn' && userId !== currApproverId)
      funcToSetResponseMessage(
        'error',
        PP_APPROVE_REJECT_ERROR_ALERT,
        autoHide
      );

    if (Active) {
      dispatch(
        getINVDetailsForAccPay({
          key: Keyvalue,
          id: INV_ID,
          userId: userId,
          reqType: 'subscribe',
        })
      );
      dispatch(traceEvent({ tableName: 'INV', tableId: INV_ID }));
      dispatch(approvalTraceEvents({ tableId: INV_ID, clientId }));
    }
    return () => {
      Active = false;
      dispatch(
        getINVDetailsForAccPay({
          key: Keyvalue,
          id: INV_ID,
          userId: userId,
          reqType: 'unsubscribe',
        })
      );
    };
  }, [
    INV_ID,
    Keyvalue,
    currApproverId,
    dispatch,
    funcToSetResponseMessage,
    userId,
  ]);

  useEffect(() => {
    if (accpayINVDetails && accpayINVDetails.value) {
      if (accpayINVDetails?.value?.formDetailsDtoList) {
        accpayINVDetails?.value?.formDetailsDtoList.map((items) => {
          if (items.uniqueKey === 'totalcharges') {
            setOtherChargesFormData(items?.formSubDetailsInternalDTOList[0]);
          }
        });
      }
      setPoOrInvResponse(accpayINVDetails.value);
    }
  }, [accpayINVDetails]);

  useEffect(() => {
    if (accPaytraceEvents) {
      setTraceEvents(accPaytraceEvents.value);
    }
  }, [accPaytraceEvents]);

  useEffect(() => {
    if (accPayApprovalTraceEvents?.value) {
      setApprovalTraceEvents(accPayApprovalTraceEvents.value);
    }
  }, [accPayApprovalTraceEvents]);

  useEffect(() => {
    if (Array.isArray(buttonData) && buttonData.length > 0) {
      if (userId !== currApproverId) {
        const updatedButtons = buttonData.map((data) => {
          if (data.labelEn === 'Approve' || data.labelEn === 'Reject') {
            return { ...data, disable: true };
          }
          return data;
        });
        setButtons(updatedButtons);
      }
    }
  }, [buttonData, currApproverId, userId]);

  const rejectedClick = () => {
    var isRegexValid = /([^\s])/.test(addCommentsValue) ? true : false;

    if (isRegexValid) {
      setModalForReject(false);
      setisLoading(true);
      dispatch(
        updateppStatusOnReject({
          tablename: 'PP',
          INV_ID: INV_ID,
          PP_ID: PP_ID,
          parameter: rejectParameter,
          user_Id: userId,
          ppEventTime: ppEventTime,
          eventTime: invEventTime,
          addCommentsValue: addCommentsValue,
          pymtReqId: '',
        })
      );
      navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
    } else setRejectErrorFlag(true);
  };

  const ppforapprovalreject = async (parameter) => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (isConfirmed) {
      if (userId === currApproverId) {
        setModalForReject(true);
        setRejectParameter(parameter);
      } else {
        let autoHide = true;
        funcToSetResponseMessage('error', PP_REJECT_ERROR_ALERT, autoHide);
      }
    }
  };

  const ppforapprovalapprove = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (isConfirmed) {
      if (userId === currApproverId) {
        dispatch(approveInvoiceOnNthApprovals({ userId, obj: approvalObj }));
      } else {
        let autoHide = true;
        funcToSetResponseMessage('error', PP_APPROVE_ERROR_ALERT, autoHide);
      }
    }
  };

  const ppforapprovalclose = () => {
    navigate(-1);
  };

  const ppfortxmnclose = () => {
    navigate(-1);
  };

  const functionsName = {
    ppforapprovalreject,
    ppforapprovalclose,
    ppforapprovalapprove,
    ppfortxmnclose,
  };
  const navigateRoute = (id, e) => {
    e.preventDefault();
    invoiceService.previousNext(
      id,
      dispatch,
      apCachedData,
      apListingFilteredData
    );
  };

  const setValueOfDataTable = (dataTableValue) => {
    let formDetails = cloneDeep(poOrInvResponse);
    if (formDetails) {
      formDetails.invItemsDtoList = dataTableValue;
      formDetails?.formDetailsDtoList?.map((item) => {
        item.type === 'DataTable' ? (item.value = dataTableValue) : null;
      });
    }
    setPoOrInvResponse(formDetails);
  };

  return (
    <>
      <CommonSpinner visible={isLoading} />
      <div className="inv-prev-next-icons">
        <div
          className="prev-wrap"
          onMouseEnter={() => setShowInvoiceToggle(true)}
          onMouseLeave={() => setShowInvoiceToggle(false)}
        >
          <div
            className="prev-container"
            style={{ display: showInvoiceToggle ? 'block' : 'none' }}
          >
            <div
              title="Previous"
              data-tooltip-id="invoice-prev"
              onClick={(e) => navigateRoute('prev', e)}
              className="icon-arrow-left2 fl prev"
              style={{ display: showInvoiceToggle ? 'block' : 'none' }}
            ></div>
          </div>
        </div>

        <div
          className="next-wrap"
          onMouseEnter={() => setShowInvoiceToggle(true)}
          onMouseLeave={() => setShowInvoiceToggle(false)}
        >
          <div
            className="next-container"
            style={{ display: showInvoiceToggle ? 'block' : 'none' }}
          >
            <div
              title="Next"
              data-tooltip-id="invoice-next"
              onClick={(e) => navigateRoute('next', e)}
              className="icon-arrow-right2 fr next"
              style={{ display: showInvoiceToggle ? 'block' : 'none' }}
            ></div>
          </div>
        </div>
      </div>
      <div className="invoice-compare mb20">
        <div className="pull-right view-switcher">
          <NewCheckBoxInput
            name="headerInvImage"
            disabled={isThreeColLayout}
            checked={isThreeColLayout ? !isThreeColLayout : invoiceImageChecked}
            onChange={() =>
              setInvoiceImageChecked(PO_ID ? !invoiceImageChecked : true)
            }
            label="Invoice Image"
            className="mr16"
            style={{ cursor: 'pointer' }}
          />
        </div>
        <div className="mb20">
          <InvPageHeader invDetails={accpayINVDetails?.value} />
        </div>
        <div className="inv-table-view">
          <Tabs
            activeKey={activeTabKey}
            onChange={(key) => setActiveTabKey(key)}
            tabBarStyle={{ marginTop: '-24px', fontFamily: 'Roboto' }}
          >
            <TabPane className="th " tab={'Invoice'} key={0}>
              <div
                className={`viewHolder ${!isThreeColLayout ? 'view-1' : ''}`}
              >
                <InvMatchingComponent
                  accpayBreadCrumb={accpayBreadCrumb}
                  className="boxed unbox"
                  poId={PO_ID}
                  invId={INV_ID}
                  tableName={tableName}
                  disabled={true}
                  filePath={(filePath) => setFilePath(filePath)}
                  threeColLayout={isThreeColLayout}
                  setPOINVCallback={(form) => {
                    setPoOrInvResponse(form);
                  }}
                />
                {!isThreeColLayout ? (
                  invoiceImageChecked ? (
                    <div className="boxed unbox">
                      <div className="mb16 th" style={{ fontWeight: 700 }}>
                        Invoice Image
                        <span
                          title="Click to open in new tab"
                          className="icon-attachment"
                          style={{ cursor: 'pointer', float: 'right' }}
                          onClick={() =>
                            utils.redirectToPDF(filePath, dispatch)
                          }
                        ></span>
                      </div>
                      <DocumentViewer
                        fileURL={filePath}
                        fileType={'iframe'}
                        zoom={'#zoom=100'}
                        title="Invoice Image"
                        iframeStyle={{ width: 100 + '%', height: 95 + '%' }}
                      />
                    </div>
                  ) : (
                    <PoMatrix
                      isModalopen={true}
                      poID={PO_ID}
                      invID={INV_ID}
                      combinedInvSupplier={true}
                      poMatrixClassName={'unbox'}
                    />
                  )
                ) : (
                  ''
                )}
              </div>
            </TabPane>
            <TabPane className="th " tab={'Invoice Items'} key={1}>
              <div className="inv-div">
                <div className="force-full-width">
                  {tableName &&
                  (tableName[0] === 'PP' || tableName[0] === 'INV') ? (
                    <InvDatatable
                      fullWidth
                      disabledFlag
                      status={tableName[1]}
                      setValueOfDataTable={setValueOfDataTable}
                      invData={poOrInvResponse?.formDetailsDtoList}
                    />
                  ) : null}
                </div>
              </div>
            </TabPane>
            <TabPane className="th " tab={'Other Charges'} key={2}>
              <div className="force-full-width">
                <MultiRowInput
                  formType={otherChargesFormData}
                  disabled={true}
                />
              </div>
            </TabPane>
          </Tabs>
        </div>
      </div>
      <ButtonCommon
        functionsName={functionsName}
        tempButtonData={buttonDetails}
      />
      <RejectModal
        onAddCommentsChangeHandler={onAddCommentsChangeHandler}
        addCommentsValue={addCommentsValue}
        rejectClick={() => rejectedClick()}
        isModalOpen={modalForReject}
        isModalClose={() => modalForRejectCloseClick()}
        errorFlag={rejectErrorFlag}
      />

      {/* * INV Email Listing & TraceEvent Flow-chart Added @Bottom
       * <AUTHOR> V
       */}
      <div className="data-table-wrap">
        {/* Jsut for testing ( Shahanas ) */}

        {invEmailListPresent ? (
          <INVEmailList invEmailTreeList={invEmailTreeList} />
        ) : null}

        <div className="mr20 " style={{ width: 100 + '%' }}>
          <div className=" page-sub-title">Events</div>
          <div
            className="boxed mb20 "
            style={{ width: 100 + '%', marginTop: 20 + 'px' }}
          >
            <Tabs
              className="mb20"
              style={{ marginTop: -18 + 'px', fontFamily: 'Roboto' }}
            >
              <TabPane tab={'WorkFlow Events'} key={0}>
                <TraceEvents
                  disabled={true}
                  enableComments={true}
                  data={getTraceEvents}
                  onAddCommentsChangeHandler={onAddCommentsChangeHandler}
                  invId={INV_ID}
                  userId={userId}
                />
              </TabPane>
              <TabPane tab={'Integration Events'} key={1}>
                <TraceEvents
                  disabled={true}
                  enableComments={true}
                  data={getApprovalTraceEvents}
                  onAddCommentsChangeHandler={onAddCommentsChangeHandler}
                  invId={INV_ID}
                  userId={userId}
                />
              </TabPane>
            </Tabs>
          </div>
        </div>
      </div>
    </>
  );
};

export { CombinedInvSupplier };
