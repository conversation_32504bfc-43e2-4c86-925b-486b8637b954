/* eslint-disable array-callback-return */

import { Button, Input } from '@hp/components';
import { useState, useEffect } from 'react';
import {
  getECRECORouterProfileCombo,
  addECRECORouterDetails,
  editECRECORouterDetails,
} from '@hp/mainstore';
import { useDispatch } from 'react-redux';
import { cloneDeep } from 'lodash';

function ECRECOProfileRouterPopup(props) {
  const { action, profileRouterId, routerData, clientId, userId } = props;
  const [routerForm, setRouterForm] = useState([]);

  useEffect(() => {
    setRouterForm(routerData);
  }, [routerData]);

  const dispatch = useDispatch();

  const SelectHandler = (type, event, index) => {
    let selectedFeild = { ...type, value: event.target.value };

    if (type.uniqueKey === 'cateCode') {
      dispatch(
        getECRECORouterProfileCombo({
          clientId: clientId,
          categoryCode: event.target.value,
          form: routerForm.map((form, ind) => {
            if (ind === index) return selectedFeild;
            else return form;
          }),
        })
      );
    } else {
      selectedFeild = { ...type, value: parseInt(event.target.value) };
      setRouterForm(
        routerForm.map((form, ind) => {
          if (ind === index) return selectedFeild;
          else return form;
        })
      );
    }
  };

  const searchInputHandler = (type, event, index) => {
    if (event?.id) {
      let tempForm = cloneDeep(routerForm);
      tempForm.map((item) => {
        if (item.uniqueKey === type.uniqueKey) {
          item.value = event?.value;
          item.defaultValue = event.id;
        }
      });
      setRouterForm(tempForm);
    }
  };

  const onChangeHandlingFunctions = {
    SelectHandler,
    searchInputHandler,
  };

  const saveHandler = () => {
    if (action === 'addDefault') {
      dispatch(
        addECRECORouterDetails({
          userId: userId,
          clientId: clientId,
          defaultFlag: 'Y',
          form: routerForm,
        })
      );
    } else if (action === 'addRoute') {
      dispatch(
        addECRECORouterDetails({
          userId: userId,
          clientId: clientId,
          defaultFlag: 'N',
          form: routerForm,
        })
      );
    } else {
      let activeFlag = action.split(' ');
      dispatch(
        editECRECORouterDetails({
          profileRouterId: profileRouterId,
          userId: userId,
          clientId: clientId,
          defaultFlag: activeFlag[1],
          form: routerForm,
        })
      );
    }
  };

  return (
    <div>
      <div className="two-col-layout mb20">
        {routerForm && routerForm.length
          ? routerForm.map((form, index) => {
              return (
                <Input
                  onChangeHandler={(type, event) =>
                    onChangeHandlingFunctions[form.onChangeFunction](
                      type,
                      event,
                      index
                    )
                  }
                  disabledFlag={form.isEditable === 'Y' ? false : true}
                  formType={form}
                  key={index}
                  isEditable="notShowing"
                />
              );
            })
          : ''}
      </div>
      <div className="flex-right">
        {routerForm && routerForm.length ? (
          <Button className={'small default mr20'} onClick={saveHandler}>
            Save
          </Button>
        ) : (
          ''
        )}
      </div>
    </div>
  );
}

export default ECRECOProfileRouterPopup;
