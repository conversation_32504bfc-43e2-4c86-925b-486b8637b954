#!/bin/sh

# Prevent infinite loop
if [ "$HUSKY_SKIP_HOOK" = "1" ]; then
  exit 0
fi

export HUSKY_SKIP_HOOK=1
echo "Running post-checkout hook..."

FILES=(
  "scripts/hpmain.modules.json"
  "apps/hostApps/hp-main-host/src/app/app.jsx"
  "apps/hostApps/hp-main-host/module-federation.config.js"
)

for file in "${FILES[@]}"; do
  if git ls-files "$file" --error-unmatch > /dev/null 2>&1; then
    echo "Restoring $file"
    git update-index --no-skip-worktree "$file"
    git restore --source=HEAD -- "$file"
  else
    echo "Skipping (file not tracked by Git): $file"
  fi
done
