/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CommonSpinner,
  formValidationUtil,
  Input,
  ProcessFlow,
  TraceEvents,
  useConfirm,
  NavBlocker,
} from '@hp/components';
import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  deleteECRAttachment,
  getECRDetailsFormDetails,
  getECRECODocumentCategories,
  getECRECOTraceEventsDetails,
  getECRECOTraceEventsHistory,
  getECRProcessFlowDetails,
  getECRSavedDocumentsDetails,
  saveECRAttachments,
  saveECRDetailsFormDetails,
  showDialog,
  updateECRDetailsFormDetails,
  viewOrEditECRDetailsFormDetails,
  resetECRECOTraceEventsDetails,
  resetsaveECRDetailsFormDetails,
  resetsaveECRAttachments,
  resetupdateECRDetailsFormDetails,
  resetdeleteECRAttachment,
  resetgetECRProcessFlowDetails,
} from '@hp/mainstore';
import { ecrFlowCreateData } from './ecr_eco_process_flow_data';
import './ecr_eco_common.scss';
import { Tabs } from 'antd';
import { ECRECOTree } from './ECRECOTree';
import ECRECOHeader from './ECRECOHeader';
import { getClientFormattedCurrentDate } from '@hp/components';
import { useAppRouterDom } from '@hp/utils';
import AffectedItems from './AffectedItems';
import { cloneDeep } from 'lodash';
import ECRECOPartNoSection from './ECRECOPartNoSection';
import { useLocation } from 'react-router-dom';
import ECRECOApprovers from './ECRECOApprovers';
import { globalutils } from '@hp/components';

const ECRDetailsPage = (props) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isDataChanged, setIsDataChanged] = useState(false);
  // const [isNavBlocked, setIsNavBlocked] = useState(false);
  const [currentTab, setCurrentTab] = useState('1');
  const [ecrForm, setEcrForm] = useState([]);
  const [listValues, setListValues] = useState([
    { label: 'ECR #', value: null },
    { label: 'Title', value: null },
    { label: 'Requestor', value: null },
    { label: 'Originator', value: null },
  ]);
  const [fileDataList, setFileDataList] = useState(null);
  const [treeDataList, setTreeDataList] = useState(null);
  const [traceEvents, setTraceEvents] = useState([]);
  const [showButtons, setShowButtons] = useState(true);
  const [redirectTo, setRedirectTo] = useState(null);
  const [processFlowData, setProcessFlowData] = useState(ecrFlowCreateData);
  const [selectedPartNumbers, setSelectedPartNumbers] = useState([]);
  const [filteredPartNumber, setFilteredPartNumber] = useState(null);
  const [showApproverModal, setShowApproverModal] = useState(false);
  const { domParameters, navigate, location } = useAppRouterDom();
  let path = useLocation().pathname;
  const pathSegments = path.split('/').filter(Boolean);
  const menuData = pathSegments[0] || domParameters?.menuData || '';
  const submenu = pathSegments[1] || domParameters?.submenu || '';
  const state = location?.state || null;
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const {
    ecrEcoDocCategories,
    ecrDetailsForm,
    ecrDetailsFormRes,
    ecrDetailsSaveRes,
    ecrAttachmentDeleteRes,
    ecrSavedDocuments,
    ecrProcessFlowRes,
    ecrEcoTraceEvents,
    // ecrEcoPartNoFiltered,
    // ecrAffectedItemsRes,
  } = useSelector((store) => store.pdm);
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;
  const ecrId = domParameters?.ecrId ?? null;
  const isDraft = state?.operation === 'edit';
  const dateToday = getClientFormattedCurrentDate();
  const partNoRef = useRef(null);
  const documentsRef = useRef(null);

  useEffect(() => {
    if (isDraft) {
      if (ecrId) {
        dispatch(
          viewOrEditECRDetailsFormDetails({
            ecrId: ecrId,
            userId: userId,
            clientId: clientId,
            method: 'Edit',
          })
        );
        dispatch(getECRSavedDocumentsDetails(ecrId));
        dispatch(
          getECRProcessFlowDetails({ ecrId: ecrId, clientId: clientId })
        );
        dispatch(
          getECRECOTraceEventsDetails({ tableName: 'ECR', tableId: ecrId })
        );
      }
    } else {
      dispatch(getECRDetailsFormDetails(userId));
      dispatch(getECRECODocumentCategories(userId));
    }
    return () => {
      // setSelectedPartNo(null);
      setEcrForm([]);
      dispatch(resetsaveECRDetailsFormDetails());
      dispatch(resetsaveECRAttachments());
      dispatch(resetupdateECRDetailsFormDetails());
      dispatch(resetdeleteECRAttachment());
      dispatch(resetgetECRProcessFlowDetails());
      dispatch(resetECRECOTraceEventsDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (ecrDetailsForm?.value) {
      let tempForm = cloneDeep(ecrDetailsForm.value);
      tempForm.map((item) => {
        if (item.uniqueKey === 'createEcr') {
          handleHeaderValues(item?.formSubDetailsInternalDTOList ?? []);
          if (!isDraft) {
            item.formSubDetailsInternalDTOList.map((entry) =>
              entry.uniqueKey === 'dateRequested'
                ? (entry.value = dateToday)
                : ''
            );
          }
        } else if (item.uniqueKey === 'partNoTable') {
          if (item?.value?.length) {
            let tempPartNoList = item.value.map((entry) => entry.value);
            setSelectedPartNumbers(tempPartNoList);
            tempForm.map((element) =>
              element.uniqueKey === 'affectedItems'
                ? element.formSubDetailsInternalDTOList.map((entry) => {
                    if (entry.uniqueKey === 'partNumberSelect')
                      entry.comboBoxOptions = item.value.map((entry) => ({
                        commonId: entry.value,
                        commonName: entry.partNo,
                      }));
                    return entry;
                  })
                : ''
            );
            item.value = tempPartNoList;
          } else setSelectedPartNumbers([]);
        }
      });
      setIsDataChanged(false);
      setEcrForm(tempForm);
      setIsLoading(false);
    }
  }, [ecrDetailsForm]);

  // useEffect(() => {
  //   if (ecrEcoPartNoFiltered?.value) {
  //     let tempForm = [...ecrForm];
  //     tempForm.map((item) => {
  //       if (item.uniqueKey === 'createEcr') {
  //         item.formSubDetailsInternalDTOList.map((entry) =>
  //           entry.uniqueKey === 'partNumber'
  //             ? (entry.comboBoxOptions = ecrEcoPartNoFiltered?.value)
  //             : ''
  //         );
  //       }
  //     });
  //     setEcrForm(tempForm);
  //   }
  // }, [ecrEcoPartNoFiltered]);

  // useEffect(() => {
  //   if (ecrAffectedItemsRes?.value) {
  //     setAffectedItems(ecrAffectedItemsRes?.value);
  //   }
  // }, [ecrAffectedItemsRes]);

  useEffect(() => {
    if (ecrProcessFlowRes?.value) {
      setProcessFlowData({
        nodes: ecrProcessFlowRes.value?.pdmProfileWorkflowRenderingDtos,
        edges: ecrProcessFlowRes.value?.pdmProfileWorkflowEdgeDtos,
      });
    }
  }, [ecrProcessFlowRes]);

  useEffect(() => {
    if (ecrEcoTraceEvents?.value) {
      setTraceEvents(ecrEcoTraceEvents.value);
    }
  }, [ecrEcoTraceEvents]);

  useEffect(() => {
    if (ecrEcoDocCategories?.value) {
      const transformedData =
        ecrEcoDocCategories.value.map((doc) => ({
          id: doc.documentsId,
          text: doc.documentName,
          parent: 0,
          hasChild: true,
          className: 'x',
          creationDate: null,
          curRevNum: null,
          detail: null,
          droppable: true,
          extension: null,
          fileName: null,
          filePath: null,
          fileTypeId: null,
          firstChild: true,
          itemCode: null,
          orderId: null,
          orginatedBy: null,
          partNumber: null,
          qty: 5,
          sibilingNext: true,
          uomId: 1,
          uploadedAt: null,
          uploadedBy: null,
          documentNumber: null,
          expriryDate: null,
        })) ?? [];
      setTreeDataList(transformedData);
    }
  }, [ecrEcoDocCategories]);

  useEffect(() => {
    if (ecrSavedDocuments?.value) {
      setTreeDataList(ecrSavedDocuments.value);
    }
  }, [ecrSavedDocuments]);

  useEffect(() => {
    if (ecrDetailsFormRes?.value) {
      setIsDataChanged(false);
      funcToSetResponseMessage(
        ecrDetailsFormRes.value?.type,
        ecrDetailsFormRes.value?.text
      );
      if (redirectTo) {
        const timeout = setTimeout(() => {
          navigate({
            pathname: `/${menuData}/${submenu}/ecr/${redirectTo}`,
          });
        }, 5000);
        return () => {
          clearTimeout(timeout);
        };
      } else if (ecrId) {
        dispatch(
          viewOrEditECRDetailsFormDetails({
            ecrId: ecrId,
            userId: userId,
            clientId: clientId,
            method: 'Edit',
          })
        );
        dispatch(getECRSavedDocumentsDetails(ecrId));
        dispatch(
          getECRProcessFlowDetails({ ecrId: ecrId, clientId: clientId })
        );
        dispatch(
          getECRECOTraceEventsDetails({ tableName: 'ECR', tableId: ecrId })
        );
      }
    }
  }, [ecrDetailsFormRes]);

  useEffect(() => {
    if (ecrDetailsSaveRes?.value?.ecrIdOrecoId) {
      let tempFileDataList = new FormData();
      tempFileDataList.append('file', null);

      if (fileDataList) tempFileDataList = fileDataList;
      dispatch(
        saveECRAttachments({
          ecrId: ecrDetailsSaveRes.value.ecrIdOrecoId,
          userId: userId,
          clientId: clientId,
          payload: tempFileDataList,
        })
      );
    }
  }, [ecrDetailsSaveRes]);

  useEffect(() => {
    if (ecrAttachmentDeleteRes?.value) {
      funcToSetResponseMessage(
        ecrAttachmentDeleteRes.value?.type,
        ecrAttachmentDeleteRes.value?.text
      );
    }
  }, [ecrAttachmentDeleteRes]);

  // useEffect(() => {
  //   if (searchString?.length > 0) {
  //     const timeout = setTimeout(() => {
  //       dispatch(pdmAction.getECRECOPartNoDetails(searchString));
  //     }, 500);
  //     return () => {
  //       clearTimeout(timeout);
  //     };
  //   }
  // }, [searchString]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  async function handleSubmit() {
    let validateForm = formValidationUtil.validateForm(ecrForm);
    if (validateForm.validSuccess) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        if (isDraft) {
          if (ecrId)
            dispatch(
              updateECRDetailsFormDetails({
                ecrId: ecrId,
                userId: userId,
                clientId: clientId,
                status: 'pending-review',
                payload: ecrForm,
              })
            );
        } else
          dispatch(
            saveECRDetailsFormDetails({
              userId: userId,
              payload: ecrForm,
              status: 'pending-review',
              clientId: clientId,
            })
          );
        setRedirectTo('pending-review');
        setShowButtons(false);
      }
    } else {
      setEcrForm([...validateForm.formList]);
      if (currentTab !== '1')
        funcToSetResponseMessage('error', 'Please Enter Mandatory Fields');
    }
  }

  function isBlank(str) {
    return !str || /^\s*$/.test(str);
  }

  async function handleSaveDraft() {
    if (isDraft && !isDataChanged) {
      funcToSetResponseMessage('info', 'No Changes Detected');
      return;
    }
    let validateForm = { validSuccess: true, formList: null };
    validateForm.formList = cloneDeep(ecrForm);
    validateForm.formList.map((item) =>
      item.uniqueKey === 'createEcr'
        ? item.formSubDetailsInternalDTOList.map((entry) =>
            entry.uniqueKey === 'changeAnalyst'
              ? isBlank(entry.value)
                ? ((entry.errorFlag = true),
                  (entry.valid = false),
                  (validateForm.validSuccess = false),
                  (entry.errorMessage = 'Enter Mandatory Fields'))
                : ''
              : ''
          )
        : ''
    );
    if (!validateForm.validSuccess) {
      funcToSetResponseMessage(
        'info',
        'Please Select Change Analyst to Save as Draft'
      );
      return;
    }
    if (isDraft) {
      if (ecrId)
        dispatch(
          updateECRDetailsFormDetails({
            ecrId: ecrId,
            userId: userId,
            clientId: clientId,
            status: 'draft',
            payload: ecrForm,
          })
        );
    } else {
      dispatch(
        saveECRDetailsFormDetails({
          userId: userId,
          payload: ecrForm,
          status: 'draft',
          clientId: clientId,
        })
      );
      setRedirectTo('draft');
      setShowButtons(false);
    }
  }

  async function handleClear() {
    if (isDataChanged) {
      const isConfirmed = await confirm(
        'Are you sure you want to clear all the fields?'
      );
      if (isConfirmed) {
        handleClearConfirm();
        setIsDataChanged(false);
      }
    } else handleClearConfirm();

    function handleClearConfirm() {
      let tempForm = [...ecrForm];
      tempForm.map((item) => {
        if (item.uniqueKey === 'createEcr') {
          item.formSubDetailsInternalDTOList.map((entry) => {
            if (entry.uniqueKey !== 'dateRequested') {
              entry.value = null;
            }
          });
        } else if (item.uniqueKey === 'partNoTable') item.value = [];
        else if (item.uniqueKey === 'affectedItems') {
          item.formSubDetailsInternalDTOList.map((entry) => {
            if (entry.uniqueKey === 'partNumberSelect')
              entry.comboBoxOptions = [];
          });
        }
      });
      setSelectedPartNumbers([]);
      setEcrForm(tempForm);
      handlePartNoChange([], []);
      if (partNoRef.current) {
        partNoRef.current.handleClearPartNo();
      }
      if (documentsRef.current) {
        documentsRef.current.handleClearDocuments();
      }
      setFileDataList(null);
    }
  }

  async function handleCancel() {
    if (isDataChanged) {
      const isConfirmed = await confirm(
        'Changes made are not saved. Do you want to continue?'
      );
      if (isConfirmed) {
        navigate({
          pathname: `/${menuData}/${submenu}/ecr/draft`,
        });
      }
    } else {
      navigate({
        pathname: `/${menuData}/${submenu}/ecr/draft`,
      });
    }
  }

  const functionsName = {
    handleSubmit,
    handleSaveDraft,
    handleClear,
    handleCancel,
  };

  const handleOnChange = (event, uniqueKey, element) => {
    setIsDataChanged(true);
    let tempForm = [...ecrForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'createEcr') {
        item.formSubDetailsInternalDTOList.map((entry, elementIndex) => {
          if (entry.uniqueKey === uniqueKey) {
            let data = formValidationUtil.validateInputMaxLengthDynamic(
              event,
              element
            );
            item.formSubDetailsInternalDTOList.splice(elementIndex, 1, data);
          }
        });
      }
    });
    setEcrForm(tempForm);
  };

  const handleAdvancedSelect = (event, uniqueKey, element) => {
    setIsDataChanged(true);
    let tempForm = [...ecrForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'createEcr') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
          }
        });
      } else if (item.uniqueKey === 'affectedItems') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
            setFilteredPartNumber(event);
          }
        });
      }
    });
    setEcrForm(tempForm);
  };

  const handleOnChangeMain = (event, uniqueKey, element) => {
    setIsDataChanged(true);
    let tempArray = [...ecrForm];
    tempArray.map((item) => {
      if (item.uniqueKey === uniqueKey) {
        item.value = event?.target?.value ?? event;
      }
    });
    setEcrForm(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleAdvancedSelect,
    handleOnChangeMain,
  };

  function handleUpdatedFileList(list) {
    const formData = new FormData();
    list.forEach((entry) => {
      if (entry.file)
        formData.append(
          `files[${entry.parentName}][${entry.index}]`,
          entry.file,
          entry.fileName
        );
    });
    setFileDataList(formData);
  }

  // const handlePartNoSearch = (filter, element) => {
  //   setSearchString(filter.trim());
  // };

  const handleFormDetails = (data) => {
    return data
      ? data.map((element, index) => {
          // if (element.uniqueKey === 'partNumber')
          //   return (
          //     <Input
          //       key={index}
          //       formType={element}
          //       isEditable="notShowing"
          //       onChangeHandler={(element, event) => {
          //         onChangeHandlingFunctions[element.onChangeFunction](
          //           event,
          //           element.uniqueKey,
          //           element
          //         );
          //       }}
          //       onSearchHandler={(element, event) =>
          //         handlePartNoSearch(event, element)
          //       }
          //       getLabel
          //     />
          //   );
          // else
          if (element.uniqueKey !== 'partNumber')
            return (
              <Input
                key={index}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            );
        })
      : '';
  };

  const handlePartNoChange = (selectedPartNumbers, partNoOptions) => {
    setSelectedPartNumbers(selectedPartNumbers);
    setEcrForm((prev) =>
      prev.map((item) => {
        if (item.uniqueKey === 'partNoTable') item.value = selectedPartNumbers;
        else if (item.uniqueKey === 'affectedItems') {
          item.formSubDetailsInternalDTOList.map((entry) => {
            if (entry.uniqueKey === 'partNumberSelect')
              entry.comboBoxOptions = partNoOptions;
            return entry;
          });
        }
        return item;
      })
    );
    setIsDataChanged(true);
  };

  const handleTabs = (item) => {
    if (item.type === 'div') {
      if (item.uniqueKey === 'processFlow') {
        return (
          <div
            className="boxed mb20"
            style={{ maxWidth: '600px', maxHeight: '600px' }}
          >
            <h3 className="page-sub-title">Process Flow</h3>
            <div className="ecr-process-flow">
              <ProcessFlow dataFlowResponse={processFlowData} />
            </div>
          </div>
        );
      } else
        return item.uniqueKey === 'createEcr' ? (
          <>
            <div className="card mb20">
              {/* <div
                style={{
                  maxWidth: '370px',
                }}
              >
                <Input
                  formType={ecrForm?.find(
                    (entry) => entry.uniqueKey === 'changeRequest'
                  )}
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey,
                      element
                    );
                  }}
                />
              </div>
              <div className="page-sub-title"></div> */}
              <div className="three-col-layout">
                {handleFormDetails(item.formSubDetailsInternalDTOList)}
              </div>
            </div>
            <div className="card mb20">
              <ECRECOPartNoSection
                ref={partNoRef}
                parameter={isDraft ? 'draftEcr' : 'createEcr'}
                formData={ecrDetailsForm?.value ?? []}
                partNoChanged={(selectedPartNumbers, partNoOptions) =>
                  handlePartNoChange(selectedPartNumbers, partNoOptions)
                }
              />
            </div>
          </>
        ) : item.uniqueKey === 'attachments' ? (
          <div className="mb20">
            <ECRECOTree
              ref={documentsRef}
              treeDataList={treeDataList}
              parameter={isDraft ? 'draft' : 'newPrePr'}
              updatedFileList={(list) => {
                handleUpdatedFileList(list);
                setIsDataChanged(true);
              }}
              deleteAttachmentFile={(fileId) => {
                dispatch(
                  deleteECRAttachment({
                    fileId: fileId,
                    userId: userId,
                    ecrId: ecrId,
                  })
                );
              }}
            />
          </div>
        ) : item.uniqueKey === 'affectedItems' ? (
          <div className="card mb20">
            {handleFormDetails(item.formSubDetailsInternalDTOList)}
            <AffectedItems
              selectedPartNumbers={selectedPartNumbers}
              filteredPartNumber={filteredPartNumber}
            />
          </div>
        ) : (
          ''
        );
    }
  };

  function handleHeaderValues(formData) {
    let tempListValues = [...listValues];
    formData.forEach((item) => {
      switch (item.uniqueKey) {
        case 'ecrNo':
          tempListValues[0].value = item.value;
          break;
        case 'title':
          tempListValues[1].value = item.value;
          break;
        case 'requestedBy':
          tempListValues[2].value = item.comboBoxOptions.find(
            (entry) => entry.commonId === item.value
          )?.commonName;
          break;
        case 'originator':
          tempListValues[3].value = item.comboBoxOptions.find(
            (entry) => entry.commonId === item.value
          )?.commonName;
          break;
      }
    });
    setListValues(tempListValues);
  }

  return (
    <>
      <NavBlocker
        isDataChanged={isDataChanged}
        isNavBlocked={(flag) => {
          // You can use this callback if needed
        }}
      />
      {isDraft && ecrForm?.length ? (
        <>
          <div className="pull-right view-switcher">
            <div
              className="service-request-launcher fr"
              onClick={() => {
                setShowApproverModal(true);
              }}
            >
              <i className="icon-user-check mr8"></i>
              <label className="label">Approvers</label>
            </div>
          </div>
          <ECRECOApprovers
            userId={userId}
            ecrId={ecrId}
            modalOpen={showApproverModal}
            handleModalClose={(flag) => setShowApproverModal(flag)}
            approvalFormData={ecrForm.find(
              (entry) => entry.uniqueKey === 'approvalForm'
            )}
            handleApprovalChangeSave={() =>
              dispatch(
                viewOrEditECRDetailsFormDetails({
                  ecrId: ecrId,
                  userId: userId,
                  clientId: clientId,
                  method: 'Edit',
                })
              )
            }
          />
        </>
      ) : (
        ''
      )}
      <ECRECOHeader
        listValues={isDraft ? listValues : listValues.slice(1)}
        noOfFields={isDraft ? 4 : 3}
      />
      {/* <NavBlocker
      isDataChanged={isDataChanged}
      isNavBlocked={(flag) => {
        setIsNavBlocked(flag);
      }}
    /> */}

      <div className="ecr-eco-main-container">
        {ecrForm?.length ? (
          <Tabs
            activeKey={currentTab}
            items={ecrForm.map((item, index) => {
              if (item.type === 'div' && item.uniqueKey !== 'approvalForm') {
                return {
                  key: String(index + 1),
                  label: item?.displayName,
                  children: handleTabs(item),
                };
              }
            })}
            onChange={(tabIndex) => {
              if (currentTab === '1') {
                handleHeaderValues(
                  ecrForm.find((item) => item.uniqueKey === 'createEcr')
                    ?.formSubDetailsInternalDTOList ?? []
                );
              }
              setCurrentTab(tabIndex);
            }}
            tabBarStyle={{
              fontFamily: 'Roboto',
            }}
          />
        ) : null}
      </div>

      {isDraft && traceEvents?.length ? (
        <div>
          <div className="page-sub-title">Events</div>
          <div className="boxed mb20">
            <Tabs
              activeKey="1"
              items={[
                {
                  key: '1',
                  label: 'WorkFlow Events',
                  children: (
                    <TraceEvents
                      externalHistoryFunction
                      externalHistoryValue={'pdm'}
                      key="traceEventsEditor"
                      enableComments={false}
                      data={traceEvents}
                      userId={userId}
                      onEnterKeyPress={() => null}
                      sendButton={() => null}
                      handleHistoryClick={(eventId, editFunction) =>
                        dispatch(
                          getECRECOTraceEventsHistory({
                            editFunction: editFunction,
                            eventId: eventId,
                            userId: userId,
                          })
                        )
                      }
                    />
                  ),
                },
              ]}
            />
          </div>
        </div>
      ) : null}

      <CommonSpinner visible={isLoading} />
      {showButtons ? (
        <ButtonCommon
          functionsName={functionsName}
          // isNavBlocked={isNavBlocked}
        />
      ) : null}
    </>
  );
};
export { ECRDetailsPage };
