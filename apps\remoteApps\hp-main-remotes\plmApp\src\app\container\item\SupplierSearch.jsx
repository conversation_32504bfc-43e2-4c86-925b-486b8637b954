/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import { getItemSource, addVendor } from '@hp/mainstore';
import { useSelector, useDispatch } from 'react-redux';
import {
  Input,
  CheckBoxInput,
  CommonSpinner,
  SearchComponent,
} from '@hp/components';
import cloneDeep from 'lodash.clonedeep';

function SupplierSearch({ Searchform, itemId }) {
  const [supplierSearchForm, setSupplierSearchForm] = useState([]);
  const [selectedVendors, setSelectedVendors] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [searchKeys, setSearchKeys] = useState({});
  const [spinner, setSpinner] = useState(false);

  const dispatch = useDispatch();

  const { addVendorRes } = useSelector((store) => store.pdm);

  useEffect(() => {
    let formList = [];

    let isData = Searchform.filter((form) => form.type === 'DataTable')[0]
      .value;

    if (!supplierSearchForm.length) {
      formList = Searchform.filter((form) => form.type === 'div');
    } else {
      formList = Searchform.map((form) => {
        if (form.type === 'div') {
          return supplierSearchForm[0];
        } else if (isData?.length) {
          setTableData(isData);
          return form;
        } else return {};
      });
    }
    setSupplierSearchForm(formList);
    setSpinner(false);
  }, [Searchform]);

  useEffect(() => {
    setSpinner(false);
  }, [addVendorRes]);

  const inputHandler = (form, type, event, index) => {
    let searchForm = cloneDeep(form);
    searchForm.formSubDetailsInternalDTOList[0].value = event.target.value;
    const newForm = supplierSearchForm.map((list) => {
      if (list.type === 'div') {
        return searchForm;
      } else return list;
    });

    setSupplierSearchForm(newForm);
    setSearchKeys({ supplierName: event.target.value });
  };

  const searchHandler = () => {
    setSpinner(true);
    dispatch(
      getItemSource({
        itemId: itemId,
        obj: searchKeys,
      })
    );
  };

  const addSupplierHandler = () => {
    if (!selectedVendors.length) return;
    setSpinner(true);

    dispatch(addVendor({ itemId: itemId, vendorList: selectedVendors }));
  };

  const checkBoxHandler = (id) => {
    const isIncludes = selectedVendors.includes(id);
    if (isIncludes)
      return setSelectedVendors(
        selectedVendors.filter((vendorId) => vendorId !== id)
      );
    setSelectedVendors([...selectedVendors, id]);
  };

  const handleFilterData = (filterData) => {
    let tempArray = cloneDeep(supplierSearchForm);
    tempArray.map((element) =>
      element.uniqueKey === 'itemSourceSearchList'
        ? (element.value = filterData)
        : ''
    );

    setSupplierSearchForm(tempArray);
  };

  const onChangeHandlingFunctions = {
    inputHandler,
    searchHandler,
    addSupplierHandler,
  };

  return (
    <>
      <CommonSpinner visible={spinner} />

      {supplierSearchForm.map((form, index) => {
        if (form.type === 'DataTable') {
          return (
            <Input
              formType={form}
              key={index}
              // dataTableEventHandler={(obj) => {
              //   handleRowClick(obj);
              // }}
              dataTableColumn={
                form.formSubDetailsInternalDTOList &&
                form.formSubDetailsInternalDTOList.length
                  ? form.formSubDetailsInternalDTOList.map((value) => {
                      return {
                        width: value.displayWidth ? value.displayWidth : '',
                        name: value.displayName ? value.displayName : '',
                        selector: value.selector ? value.selector : '',
                        cell:
                          value.selector === 'add_icon'
                            ? (row) => {
                                return (
                                  <CheckBoxInput
                                    checked={
                                      selectedVendors.includes(
                                        row.supplierId
                                      ) || row.vendorAddedFlag
                                        ? true
                                        : false
                                    }
                                    disabled={
                                      row.vendorAddedFlag ? true : false
                                    }
                                    onChange={() =>
                                      checkBoxHandler(row.supplierId)
                                    }
                                  />
                                );
                              }
                            : '',
                      };
                    })
                  : []
              }
            />
          );
        } else if (form.type === 'GlobalSearch') {
          return (
            <SearchComponent
              tableListData={tableData}
              getFilteredData={(filterData) => handleFilterData(filterData)}
              placeholder={'Search'}
              style={{
                marginBottom: '20px',
                width: '150px',
              }}
            />
          );
        } else
          return (
            <Input
              onChangeHandler={(type, event) =>
                onChangeHandlingFunctions[type.onChangeFunction](
                  form,
                  type,
                  event,
                  index
                )
              }
              formType={form}
              key={index}
              styleSheet={
                form.type === 'div'
                  ? {
                      Button: { marginTop: '20px' },
                    }
                  : {}
              }
            />
          );
      })}
    </>
  );
}

export default SupplierSearch;
