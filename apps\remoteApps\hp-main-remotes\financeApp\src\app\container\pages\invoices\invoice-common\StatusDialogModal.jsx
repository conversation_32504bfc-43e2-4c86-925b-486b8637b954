/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import DataTable from 'react-data-table-component';
import { useDispatch, useSelector } from 'react-redux';
import { getMatchStatusForPoInvCompare } from '@hp/mainstore';

const StatusDialogModal = (props) => {
  const innerMenuName = props.innerMenuName ? props.innerMenuName : '';
  const modalToOpen = props.isModalopen ? props.isModalopen : false;

  const dispatch = useDispatch();
  const [Isstatus, setIsStatus] = React.useState({
    statusArray: {
      amountFLag: '',
      currencyFlag: '',
      invAmountValue: '',
      invCurrencyValue: '',
      invSupplierName: '',
      invoiceItemMatchStatusReturnDtoList: [],
      poAmountValue: '',
      poCurrencyValue: '',
      poNumberFLag: '',
      poNumberFromInv: '',
      poNumberFromPo: '',
      poSupplierName: '',
      supplierNameFlag: '',
    },
  });

  useEffect(() => {
    if (modalToOpen) {
      if (props.poID && props.invID !== undefined) {
        let poId = props.poID;
        let invId = props.invID;
        getMatchStatus(poId, invId);
      }
    }
  }, [modalToOpen]);

  const getMatchStatus = async (poId, invId) => {
    dispatch(getMatchStatusForPoInvCompare({ poId, invId }));
  };

  const { poInvCompareMatchStatus } = useSelector((store) => store.accpay);
  useEffect(() => {
    poInvCompareMatchStatus && poInvCompareMatchStatus.value
      ? setIsStatus({ statusArray: poInvCompareMatchStatus.value })
      : '';
  }, [poInvCompareMatchStatus]);

  const conditionalStatusRowStyles = [
    {
      when: function (row) {
        return row.match_status.props.className.includes('icon-not-checked');
      },
      style: {
        backgroundColor: '#fff0f0 !important',
      },
    },
  ];

  const conditionalItemRowStyles = [
    {
      when: function (row) {
        return row.Field_col_status.props.className.includes(
          'icon-not-checked'
        );
      },
      style: {
        backgroundColor: '#fff0f0 !important',
      },
    },
  ];

  const StatusColumn = [
    {
      name: 'Status',
      selector: 'match_status',
      maxWidth: '100px',
    },
    { name: 'Fields', selector: 'Field_id' },
    { name: 'Purchase Order', selector: 'Po_no' },
    { name: 'Invoice', selector: 'Invoice_id' },
  ];

  const StatusDynamicData = [
    {
      Field_id: 'Po#',
      Po_no: Isstatus.statusArray.poNumberFromPo,
      Invoice_id:
        Isstatus.statusArray.poNumberFLag === 'Y'
          ? Isstatus.statusArray.poNumberFromInv
          : '',
      match_status:
        Isstatus.statusArray.poNumberFLag === 'Y' ? (
          <span className="matched-checked icon-checked"></span>
        ) : (
          <span className="matched-checked icon-not-checked"></span>
        ),
    },
    {
      Field_id: 'Currency',
      Po_no: Isstatus.statusArray.poCurrencyValue,
      Invoice_id:
        Isstatus.statusArray.currencyFlag === 'Y'
          ? Isstatus.statusArray.invCurrencyValue
          : '',
      match_status:
        Isstatus.statusArray.currencyFlag === 'Y' ? (
          <span className="matched-checked icon-checked"></span>
        ) : (
          <span className="matched-checked icon-not-checked"></span>
        ),
    },
    {
      Field_id: 'Amount',
      Po_no: Isstatus.statusArray.poAmountValue,
      Invoice_id:
        Isstatus.statusArray.amountFLag === 'Y'
          ? Isstatus.statusArray.invAmountValue
          : '',
      match_status:
        Isstatus.statusArray.amountFLag === 'Y' ? (
          <span className="matched-checked icon-checked"></span>
        ) : (
          <span className="matched-checked icon-not-checked"></span>
        ),
    },
    {
      Field_id: 'Supplier',
      Po_no: Isstatus.statusArray.poSupplierName,
      Invoice_id:
        Isstatus.statusArray.supplierNameFlag === 'Y'
          ? Isstatus.statusArray.invSupplierName
          : '',
      match_status:
        Isstatus.statusArray.supplierNameFlag === 'Y' ? (
          <span className="matched-checked icon-checked"></span>
        ) : (
          <span className="matched-checked icon-not-checked"></span>
        ),
    },
  ];

  const ItemColumn = [
    {
      name: 'Status',
      selector: 'Field_col_status',
      maxWidth: '100px',
    },
    { name: 'Fields', selector: 'Field_col_name' },
    { name: 'Purchase Order', selector: 'Field_col_po' },
    { name: 'Invoice', selector: 'Field_col_inv' },
  ];

  // const initAccordionState = [];
  const [accordionState, setAccordionStatus] = useState([]);

  const toggleAccordion = (event, id) => {
    let newAccordionState = [...accordionState];
    if (newAccordionState.includes(id)) {
      newAccordionState = newAccordionState.filter((item) => item !== id);
    } else {
      newAccordionState.push(id);
    }
    setAccordionStatus(newAccordionState);
    if (event !== null) event.stopPropagation();
  };

  const ItemsTable = (datas, index) => {
    let ItemsArray = [
      {
        Field_col_name: 'Description',
        Field_col_po: datas.poItemDescription,
        Field_col_inv: datas.invItemDescription,
        Field_col_status:
          datas.descriptionFlag === 'Y' ? (
            <span className="matched-checked icon-checked"></span>
          ) : (
            <span className="matched-checked icon-not-checked"></span>
          ),
      },
      {
        Field_col_name: 'Unit Price ',
        Field_col_po: datas.poItemUnitPrice,
        Field_col_inv: datas.invItemUnitPrice,
        Field_col_status:
          datas.unit_priceFlag === 'Y' ? (
            <span className="matched-checked icon-checked"></span>
          ) : (
            <span className="matched-checked icon-not-checked"></span>
          ),
      },
      {
        Field_col_name: 'Quantity',
        Field_col_po: datas.poItemQuantity,
        Field_col_inv: datas.invItemQuantity,
        Field_col_status:
          datas.quantityFlag === 'Y' ? (
            <span className="matched-checked icon-checked"></span>
          ) : (
            <span className="matched-checked icon-not-checked"></span>
          ),
      },
      {
        Field_col_name: 'Amount',
        Field_col_po: datas.poItemAmount,
        Field_col_inv: datas.invitemAmount,
        Field_col_status:
          datas.amountFlag === 'Y' ? (
            <span className="matched-checked icon-checked"></span>
          ) : (
            <span className="matched-checked icon-not-checked"></span>
          ),
      },
    ];

    let classes = ['accordion-single'];

    if (accordionState.includes(index)) {
      classes.push('active');
    }

    if (datas.itemCompleteMatchFlag !== 'Y') {
      classes.push('has-error');
    }

    return (
      <div key={index} className={classes.join(' ')}>
        <div
          onClick={(event) => toggleAccordion(event, index)}
          className="accordion-header group"
        >
          {datas.itemCompleteMatchFlag === 'Y' ? (
            <i className="matched-checked icon-checked"></i>
          ) : (
            <i className="matched-checked icon-not-checked"></i>
          )}
          Item # {index + 1} <i className="fr icon-caret"></i>
        </div>
        <div className="accordion-content">
          <DataTable
            persistTableHead
            dense={true}
            noHeader={true}
            data={ItemsArray}
            responsive={false}
            columns={ItemColumn}
            highlightOnHover={true}
            conditionalRowStyles={conditionalItemRowStyles}
            className="force-to-fill-width match-sub-table"
          />
        </div>
      </div>
    );
  };

  return (
    <Modal
      className="Modal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={props.isModalopen}
      style={{
        content: {
          padding: '0px',
        },
      }}
    >
      {/* <div onClick={props.onClick} className="icon-close modal-close-btn"></div> */}
      <div className="modal-close-sticky">
        <span onClick={props.onClick} className="modal-close icon-close"></span>
      </div>

      <div
        className="ai-based-status-match mb24 "
        style={{ padding: '0px 40px' }}
      >
        <h2 className="page-title">{innerMenuName} Status</h2>
        {Isstatus.statusArray.invoiceItemMatchStatusReturnDtoList &&
        Isstatus.statusArray.invoiceItemMatchStatusReturnDtoList.length ? (
          <DataTable
            persistTableHead
            highlightOnHover={true}
            noHeader={true}
            responsive={false}
            dense={true}
            className="mb24 main-table force-to-fill-width"
            columns={StatusColumn}
            data={StatusDynamicData}
            conditionalRowStyles={conditionalStatusRowStyles}
          />
        ) : (
          <div style={{ textAlign: 'center' }}>No data available!</div>
        )}
        {Isstatus.statusArray.invoiceItemMatchStatusReturnDtoList &&
        Isstatus.statusArray.invoiceItemMatchStatusReturnDtoList.length ? (
          <div className="accordion-component-wrap mb40">
            {Isstatus.statusArray.invoiceItemMatchStatusReturnDtoList.map(
              (element, index) => {
                return ItemsTable(element, index);
              }
            )}
          </div>
        ) : null}
      </div>
    </Modal>
  );
};
export { StatusDialogModal };
