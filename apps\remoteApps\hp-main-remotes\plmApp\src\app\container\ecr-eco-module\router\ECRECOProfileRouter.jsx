/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 27-09-2024 09:40:13
 * @modify date 01-10-2024 12:20:40
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import {
  ButtonCommon,
  CommonSpinner,
  Input,
  dataTableServiceProvider,
} from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import {
  getECRECORouterDetailsForm,
  showDialog,
  getECRECORouterAddEdit,
  deleteECRECORouterDetails,
  setECRECORouterSelectedClient,
  pdmResetStateField,
} from '@hp/mainstore';
import { AP_USER } from '@hp/constants';
import { useConfirm } from '@hp/components';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import ECRECOProfileRouterPopup from './ECRECOProfileRouterPopup';
import { globalutils } from '@hp/components';

const ECRECOProfileRouter = (props) => {
  let user = globalutils.getDataFromStorage('all');
  const userId = user.userId;
  const clientIdVal = user.clientId;
  // const menuData = props.match.params ? props.match.params.menuData : '';
  // const submenu = props.match.params ? props.match.params.submenu : '';
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const { confirm } = useConfirm();

  const { ecrEcoRouterDetails, ecrEcoRouterSelectedClient, ecrEcoRouterRes } =
    useSelector((store) => store.pdm);
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);
  const buttonData = useSelector((state) => state.buttons.buttons || null);

  const [clientId, setClientId] = useState(clientIdVal);
  const [formDetails, setFormDetails] = useState([]);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [isModal, setIsModal] = useState(false);
  const [action, setAction] = useState('');
  const [profileId, setProfileId] = useState(null);
  const [profileRouterId, setProfileRouterId] = useState(null);
  const [profileRouterData, setprofileRouterData] = useState([]);
  const [primaryFlag, setPrimaryFlag] = useState(false);

  useEffect(() => {
    let tempClientId = ecrEcoRouterSelectedClient ?? clientIdVal;
    dispatch(
      getECRECORouterDetailsForm({ userId: userId, clientId: tempClientId })
    );
  }, []);
  useEffect(() => {
    return () => {
      dispatch(showDialog({ showPopup: false }));
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoRouterSelectedClient' }));
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoRouterDetails' }));
    };
  }, []);

  useEffect(() => {
    if (ecrEcoRouterDetails?.value) {
      setFormDetails(ecrEcoRouterDetails.value?.formDetailsDtoList ?? []);
      funcToSetResponseMessage(
        `${
          ecrEcoRouterDetails.value.defaultRouterStatusFlag ? 'success' : 'info'
        }
        `,
        ecrEcoRouterDetails.value.defaultRouterStatusMessage,
        ecrEcoRouterDetails.value.defaultRouterStatusFlag ? false : true
      );
      setPrimaryFlag(
        ecrEcoRouterDetails.value.defaultRouterStatusFlag ?? false
      );
      setIsModal(false);
    }
  }, [ecrEcoRouterDetails]);

  useEffect(() => {
    if (ecrEcoRouterRes?.value) {
      setprofileRouterData(ecrEcoRouterRes.value);
    }
  }, [ecrEcoRouterRes]);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage, flag) => {
    let showPopup = true;
    let canClose = flag ? true : false;
    let autoHide = flag ? false : true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  function setDefaultRouterButtonFlag() {
    let flag = ecrEcoRouterDetails?.value?.defaultRouterStatusFlag ?? null;

    if (buttonData && buttonData !== undefined) {
      // Create a new array with updated button objects
      const updatedButtonData = buttonData.map((btn) => {
        if (btn.label === 'Create Default Router') {
          return { ...btn, disable: flag ? true : false }; // Create a new object with updated 'disable'
        } else if (btn.label === 'Create Router') {
          return { ...btn, disable: flag ? false : true }; // Create a new object with updated 'disable'
        }
        return btn; // Return unchanged objects
      });

      return updatedButtonData;
    }
  }

  const handleRouter = (parameter) => {
    setIsModal(true);
    setAction(parameter === 'Y' ? 'addDefault' : 'addRoute');
    let isDefaultRouterFlag = parameter === 'Y' ? true : false;
    // To get Add Router Form, passing profile router id as 0
    dispatch(
      getECRECORouterAddEdit({
        profileRouterId: 0,
        userId: userId,
        clientId: clientId,
        isDefaultRouterFlag: isDefaultRouterFlag,
        primaryFlag: primaryFlag,
        action: 'add',
      })
    );
  };

  const functionsName = {
    handleRouter,
  };

  const handleRowClick = (row) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalRowStyles(styleAttribute);
    setProfileId(row.profileId);
    setProfileRouterId(row.profileRouterId);
  };

  const handleEdit = (row) => {
    setprofileRouterData([]);
    setAction(`edit ${row.defaultRouterFlag ?? ''}`);
    setProfileId(row.profileId);
    setProfileRouterId(row.profileRouterId);
    setIsModal(true);
    // To get Edit Router Form
    dispatch(
      getECRECORouterAddEdit({
        profileRouterId: row.profileRouterId,
        userId: userId,
        clientId: clientId,
        isDefaultRouterFlag: false,
        primaryFlag: primaryFlag,
        action: 'edit',
      })
    );
  };

  const handleDelete = async (row) => {
    const isConfirm = await confirm('Are you sure you want to delete');
    if (isConfirm) {
      dispatch(
        deleteECRECORouterDetails({
          profileRouterId: row.profileRouterId,
          userId: userId,
          clientId: clientId,
        })
      );
    }
  };

  function handleSelect(event, uniqueKey, element) {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      if (entry.uniqueKey === uniqueKey) {
        if (uniqueKey === 'clientSelect') {
          entry.value = event?.target?.value ?? event;
          setClientId(entry.value);
          dispatch(
            getECRECORouterDetailsForm({
              userId: userId,
              clientId: entry.value,
            })
          );
          dispatch(setECRECORouterSelectedClient(entry.value));
        }
      }
    });
    setFormDetails(tempArray);
  }

  const onChangeHandlingFunctions = {
    handleSelect,
  };

  function handleFormDetails(data) {
    return data
      ? data.map((element, index) => {
          if (element.type === 'DataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val, idx) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : val.displayWidth ?? '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                              key={idx}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                              key={idx}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                dataTablePersistHead={true}
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else if (element.uniqueKey === 'clientSelect') {
            return (
              <div key={index} style={{ width: '300px' }}>
                <Input
                  key={index}
                  formType={element}
                  isEditable="notShowing"
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey,
                      element
                    );
                  }}
                />
              </div>
            );
          }
        })
      : '';
  }
  return (
    <>
      <CommonSpinner visible={loading} />
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      {formDetails?.length ? handleFormDetails(formDetails) : ''}
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={() => {
            setIsModal(false);
            setProfileId(null);
          }}
          className="modal-close icon-close"
        ></div>
        <ECRECOProfileRouterPopup
          routerData={profileRouterData}
          profileId={profileId}
          profileRouterId={profileRouterId}
          popupClose={() => setIsModal(false)}
          action={action}
          clientId={clientId}
          userId={userId}
        />
      </Modal>

      <ButtonCommon
        tempButtonData={setDefaultRouterButtonFlag()}
        functionsName={functionsName}
      />
    </>
  );
};

export { ECRECOProfileRouter };
