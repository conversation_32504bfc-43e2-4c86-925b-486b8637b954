/* eslint-disable react/no-children-prop */
/* eslint-disable react-hooks/exhaustive-deps */

/**
 * <AUTHOR> AUGUSTINE
 * @email <EMAIL>
 * @create date 2022-10-12 10:11:23
 * @modify date 2022-11-24 14:09:11
 * @desc [description]
 */

import { Button, TextInput, ToggleInput } from '@hp/components';
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { itemNodeCreateAs, showDialog } from '@hp/mainstore';

function CreateAs({ form, data, deleteHandler, close }) {
  const [createAsValues, setcreateAsValues] = useState([]);
  const [toggle, settoggle] = useState(true);
  const dispatch = useDispatch();

  const { createAsData, currentNodeId, createAsRes } = useSelector(
    (store) => store.pdm
  );

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  useEffect(() => {
    if (!createAsData || !createAsData.length) return close();
    let newData = createAsData.map((data) => ({ ...data, newName: '' }));
    setcreateAsValues(newData);
  }, [createAsData]);

  useEffect(() => {
    if (createAsRes?.value) {
      funcToSetResponseMessage('success', createAsRes.value);
      close();
    }
    //  return ()=>{
    //   dispatch()
    //  }
  }, [createAsData]);

  const inputHandler = (data, value, index) => {
    let formData = [...createAsValues];
    formData.splice(index, 1, { ...data, newName: value });
    setcreateAsValues(formData);
  };

  const createAsHandler = () => {
    if (!createAsValues.length) return;
    let isValue = true;
    let form = createAsValues.map((value) => {
      if (!value.newName) isValue = false;
      return { itemId: value.itemId, itemName: value.newName };
    });
    if (isValue)
      return dispatch(
        itemNodeCreateAs({
          action: toggle,
          parentItemId: currentNodeId.id,
          form: form,
        })
      );
    funcToSetResponseMessage('info', "Fields can't be empty");
  };

  return (
    <>
      <div
        className="icon-down-arrow"
        title={'Back'}
        style={{
          cursor: 'pointer',
          transform: 'rotate(90deg)',
          width: 'fit-content',
          fontSize: '17px',
        }}
        onClick={() => close()}
      >
        {' '}
      </div>

      <div className=" boxed unbox edit-only-half is-editing ">
        <div className=" inv-table-view ">
          <ToggleInput
            label={'Include Sub Nodes'}
            checked={toggle}
            className={'mb16 flex-right'}
            onChange={() => settoggle(!toggle)}
          />
          <div
            className="two-col-layout mb8"
            style={{
              fontSize: '14px',
            }}
          >
            <p
              style={{
                marginRight: '20px',
              }}
            >
              Item Name
            </p>
            <p>New Item Name</p>
          </div>
          <div className=" inv-table-body-format ">
            <div className=" inv-table-body-format">
              {createAsValues && createAsValues.length
                ? createAsValues.map((data, index) => {
                    return (
                      <div className="single" key={index}>
                        <div className="td po">
                          <p
                            style={{
                              fontSize: '12px',
                            }}
                          >
                            {data.itemName.replaceAll('-', '').trim()}
                          </p>
                        </div>
                        <div style={{ display: 'flex' }}>
                          <TextInput
                            value={data.newName}
                            onChange={(e) =>
                              inputHandler(data, e.target.value, index)
                            }
                          />
                          <span
                            className="icon-close"
                            onClick={() =>
                              deleteHandler(data.position, data.itemId, data)
                            }
                            style={{
                              marginTop: '15px',
                              marginLeft: '8px',
                              fontSize: '13px',
                              cursor: 'pointer',
                            }}
                          />
                        </div>
                      </div>
                    );
                  })
                : ''}
            </div>
          </div>
        </div>
      </div>

      <Button
        children={'Save'}
        className={'default small fr'}
        onClick={createAsHandler}
      />
    </>
  );
}

export default CreateAs;
