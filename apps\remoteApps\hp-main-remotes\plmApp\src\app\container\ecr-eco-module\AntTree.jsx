import { useState } from 'react';
import { Tree } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import './ecr_eco_common.scss';

const AntTree = (props) => {
  const { treeData } = props;
  const [expandedKeys, setExpandedKeys] = useState([]);

  const onExpand = (newExpandedKeys) => {
    setExpandedKeys(newExpandedKeys);
  };

  const onSelect = (selectedKeys, { node }) => {
    const { key } = node;
    let newExpandedKeys = [...expandedKeys];

    if (expandedKeys.includes(key)) {
      newExpandedKeys = newExpandedKeys.filter((k) => k !== key);
    } else {
      newExpandedKeys.push(key);
    }

    setExpandedKeys(newExpandedKeys);
  };

  const filterTreeNode = (node) => {
    return node?.key?.split('-')?.length === 2;

    // return !node?.affectedSubItemDtoList?.length;
  };

  return (
    <div>
      <Tree
        // className="antd-tree"
        showLine
        blockNode
        defaultExpandAll
        switcherIcon={<DownOutlined />}
        // rootStyle={{ backgroundColor: 'white' }}
        treeData={treeData}
        expandedKeys={expandedKeys}
        onExpand={onExpand}
        onSelect={onSelect}
        filterTreeNode={filterTreeNode}
        fieldNames={{
          title: 'parentItemName',
          // key: 'componentItemId',
          children: 'affectedSubItemDtoList',
        }}
        style={{
          fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
        }}
      />
    </div>
  );
};

export default AntTree;
