/* eslint-disable react-hooks/exhaustive-deps */
import Dropdown from 'antd/es/dropdown/dropdown';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import { Spin } from 'antd';
import EmailToRequestor from './EmailToRequestor';
import Modal from 'react-modal';
import { accPayConstants } from '@hp/mainstore';
import { getEmailTemplateCombo, getaccpayEmailTemplate } from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';

function EmailTemplateOptions(props) {
  const { invId } = props;

  const { emailTemplateOptions, emailSendReqRes, emailError } = useSelector(
    (state) => state.accpay
  );

  const [emailComboOptions, setEmailComboOptions] = useState([
    {
      key: 1,
      label: (
        <Spin
          tip="Loading..."
          indicator={<span className="icon-spinner2 rotate-this-icon"></span>}
        />
      ),
    },
  ]);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [templateId, setTemplateId] = useState(null);

  let dispatch = useDispatch();

  useEffect(() => {
    dispatch(getEmailTemplateCombo());
  }, []);

  useEffect(() => {
    if (emailTemplateOptions?.value && emailTemplateOptions.value.length) {
      let tempArr = emailTemplateOptions.value.map((option, index) => {
        return {
          key: index + 1,
          label: (
            <div
              className="service-request-launcher"
              onClick={() => emailTemplateFn(option.templateCodeId)}
            >
              <i className=" icon-menu-envelop mr8"></i>
              <label className="label ">{option.templateName}</label>
            </div>
          ),
        };
      });
      setEmailComboOptions(tempArr);
    }
  }, [emailTemplateOptions]);

  useEffect(() => {
    if (emailSendReqRes) {
      funcToSetResponseMessage('success', emailSendReqRes.value, true);
    }

    setEmailModalOpen(false);

    return () => {
      dispatch({
        type: accPayConstants.SEND_MAIL_REQUEST_SUCCESS,
        payload: null,
      });
      dispatch({
        type: accPayConstants.SEND_MAIL_REQUEST_FAILURE,
        payload: null,
      });
    };
  }, [emailSendReqRes, emailError]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const emailTemplateFn = (emailTemplateId) => {
    if (invId) {
      dispatch(
        getaccpayEmailTemplate({ invId: invId, templateId: emailTemplateId })
      );
      setEmailModalOpen(true);
      setTemplateId(emailTemplateId);
    }
  };

  return (
    <>
      <Dropdown
        menu={{ items: emailComboOptions }}
        placement="bottomRight"
        arrow
      >
        <div>
          <i
            className=" icon-menu-envelop mr8"
            style={{ fontSize: '16px', paddingBottom: ' 2px' }}
          ></i>
          <label>Compose</label>
        </div>
      </Dropdown>

      <Modal
        className={'ModalPoMatrix'}
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={emailModalOpen}
      >
        <div
          onClick={() => setEmailModalOpen(false)}
          className="modal-close icon-close"
        ></div>
        <EmailToRequestor
          templateId={templateId}
          invId={invId}
          modalClose={() => setEmailModalOpen(false)}
        />
      </Modal>
    </>
  );
}

export { EmailTemplateOptions };
