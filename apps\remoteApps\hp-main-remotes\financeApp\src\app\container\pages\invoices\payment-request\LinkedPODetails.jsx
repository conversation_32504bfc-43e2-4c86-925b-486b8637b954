import PoMasterCard from './PoMasterCard';
import { Input } from '@hp/components';

function LinkedPODetails({ poDetails, pymtReqForm }) {
  return (
    <div>
      {poDetails && (
        <>
          <div className="page-sub-title mb20">PO details </div>
          <PoMasterCard formResponse={poDetails} linkbtn={false} />
          <Input
            formType={
              poDetails?.length
                ? poDetails?.filter((val) => val.type === 'DataTable')[0]
                : ''
            }
          />
        </>
      )}
    </div>
  );
}

export { LinkedPODetails };
