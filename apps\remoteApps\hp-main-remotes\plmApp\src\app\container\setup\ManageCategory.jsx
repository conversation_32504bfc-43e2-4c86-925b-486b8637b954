/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> <PERSON>tian
 * @email <EMAIL>
 * @create date 12-09-2022 9:39:10
 * @modify date 2023-05-22 16:40:00
 * @desc [description]
 */
import React, { useState, useEffect } from 'react';
import {
  Input,
  Button,
  ButtonCommon,
  useConfirm,
  CommonSpinner,
} from '@hp/components';
import {
  showDialog,
  getManageCategoryForm,
  addManageCategoryForm,
  editManageCategoryForm,
  deleteManageCategoryForm,
  resetManageCategoryResponse,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { dataTableServiceProvider } from '@hp/components';

const ManageCategory = (props) => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { manageCategoryFormDetails, manageCategoryResponse, loading } =
    useSelector((store) => store.pdm);
  const { innerMenuName, subMenuName } = useSelector((store) => store.menu);
  const [formDetails, setFormDetails] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [edit, setEdit] = useState(false);
  const [disable, setDisable] = useState(false);
  const [selected, setSelected] = useState({});
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filterData, setFilterData] = useState([]);

  // Clear Notification on exit
  useEffect(() => {
    return () => {
      dispatch(resetManageCategoryResponse());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    setIsLoading(loading);
  }, [loading]);

  useEffect(() => {
    if (
      manageCategoryFormDetails?.value &&
      manageCategoryFormDetails.value.length
    ) {
      setFormDetails(manageCategoryFormDetails.value);
      manageCategoryFormDetails.value.map((form) => {
        if (form.type === 'DataTable') {
          setFilterData(form.value);
        }
      });
    }
  }, [manageCategoryFormDetails]);

  useEffect(() => {
    setIsLoading(false);

    if (manageCategoryResponse) {
      funcToSetResponseMessage('success', manageCategoryResponse.value);
    }
    getManageCategoryFormMain();
  }, [manageCategoryResponse]);

  //function for showing notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  // Fetch Manage Catergory Form
  const getManageCategoryFormMain = () => {
    dispatch(getManageCategoryForm());
  };

  // Save Button Function
  const handleSave = () => {
    if (showModal) {
      let iscategoryPresent = true;
      let tempArray = cloneDeep(formDetails);
      tempArray.map((entry) => {
        if (entry.uniqueKey === 'pageSubtitle') {
          entry.formSubDetailsInternalDTOList.map((element) => {
            if (element.value === null || element.value.trim() === '') {
              iscategoryPresent = false;
              element.uniqueKey === 'category'
                ? funcToSetResponseMessage('error', 'Please Enter Category')
                : funcToSetResponseMessage(
                    'error',
                    'Please Enter Category Code'
                  );
            }
          });
        }
      });
      if (iscategoryPresent) {
        setIsLoading(true);
        let inputDetails = tempArray.find((element) => {
          return element.uniqueKey === 'pageSubtitle';
        }).formSubDetailsInternalDTOList;
        edit
          ? dispatch(
              editManageCategoryForm({
                formDetails: inputDetails,
                itemCategoryId: selected.itemCategoryId,
              })
            )
          : dispatch(addManageCategoryForm(inputDetails));
        setShowModal(false);
      }
    }
  };

  // Cancel Button Function
  const cancelHandler = () => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      if (element.uniqueKey === 'pageSubtitle') {
        element.formSubDetailsInternalDTOList.map((item) => (item.value = ''));
      }
    });
    setFormDetails(tempArray);
    setEdit(false);
    setDisable(false);
    setConditionalRowStyles([]);
    setSelected({});
  };

  // close button function
  const handleCancel = () => {
    setShowModal(false);
    setIsLoading(false);
    cancelHandler();
  };

  const functionsName = {
    handleSave,
    handleCancel,
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setDisable(false);
    setSelected(obj);
    setEdit(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.uniqueKey === 'category' ? (entry.value = obj.category) : '';
          entry.uniqueKey === 'categoryCode'
            ? (entry.value = obj.categoryCode)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.itemCategoryId,
      'itemCategoryId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.itemCategoryId
      ? handleConfirm(obj.itemCategoryId)
      : funcToSetResponseMessage('error', 'Please Select a Category to Delete');
  };

  //Function to confirm delete
  const handleConfirm = async (itemCategoryId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteManageCategoryForm(itemCategoryId));
      setShowModal(false);
      setDisable(false);
      setSelected({});
    }
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setSelected(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          entry.uniqueKey === 'category' ? (entry.value = obj.category) : '';
          entry.uniqueKey === 'categoryCode'
            ? (entry.value = obj.categoryCode)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.itemCategoryId,
      'itemCategoryId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  // Function which lets to type in the form fields
  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      if (element.uniqueKey === 'pageSubtitle') {
        element.formSubDetailsInternalDTOList.map((entry) =>
          entry.uniqueKey === uniqueKey
            ? (entry.value = event.target.value)
            : ''
        );
      }
    });
    setFormDetails(tempArray);
  };

  // Data table filter function

  const dataTableFilter = (filteredValue) => {
    let tempArray = structuredClone(formDetails);
    tempArray.map((element) => {
      if (element.type === 'DataTable') {
        element.value = filteredValue;
      }
    });
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
  };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey) {
            if (element.uniqueKey === 'pageSubtitle') {
              while (showModal) {
                let len = Math.ceil(
                  element.formSubDetailsInternalDTOList.length / 2
                );
                return (
                  <div
                    className="card three-col-layout mb20"
                    style={{
                      position: 'relative',
                    }}
                    key={index}
                  >
                    <div className="col" key={index + '_col1'}>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList.slice(0, len)
                      )}
                    </div>
                    <div className="col" key={index + '_col2'}>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList.slice(len)
                      )}
                    </div>
                    <span
                      className="icon-close"
                      onClick={handleCancel}
                      style={{
                        position: 'absolute',
                        top: '5px',
                        right: '5px',
                        width: 'fit-content',
                        cursor: 'pointer',
                      }}
                      key={index + '_close'}
                    >
                      {' '}
                    </span>
                  </div>
                );
              }
            } else if (element.uniqueKey === 'Item_Category_datatable') {
              let columns =
                element.formSubDetailsInternalDTOList &&
                element.formSubDetailsInternalDTOList.map((val, i) => {
                  return {
                    selector: val.selector,
                    name: val.displayName,
                    width: val.displayWidth,
                    cell:
                      val.selector === 'editIcon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-edit-button"
                                onClick={() => handleEdit(row)}
                                key={i}
                              ></div>
                            );
                          }
                        : val.selector === 'deleteIcon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-2-trash"
                                onClick={() => handleDelete(row)}
                                key={index}
                              ></div>
                            );
                          }
                        : '',
                  };
                });
              return (
                <div key={index}>
                  <Input
                    indexkey={index}
                    formType={element}
                    dataTableEventHandler={(obj) => {
                      handleRowClick(obj);
                    }}
                    dataTableColumn={columns}
                    conditionalRowStyles={conditionalRowStyles}
                  />
                </div>
              );
            } else if (element.uniqueKey === 'add_button') {
              return (
                <div
                  className="fr mb8 "
                  key={index}
                  style={{
                    marginTop: '10px',
                  }}
                >
                  {!showModal && (
                    <Button
                      onClick={() => {
                        cancelHandler();
                        setShowModal(true);
                      }}
                      className="small outline add-button-custom flex-row vam"
                    >
                      <i className="icon-add-button "> </i>Add
                    </Button>
                  )}
                </div>
              );
            } else if (element.uniqueKey === 'search') {
              return;
            } else
              return (
                <Input
                  indexkey={index}
                  disabledFlag={disable}
                  getFilteredData={dataTableFilter}
                  tableListData={filterData}
                  formType={element}
                  onChangeHandler={(element, event) =>
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey
                    )
                  }
                  key={index}
                />
              );
          }
        })
      : null;
  };

  return (
    <>
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      <CommonSpinner visible={isLoading} />
      <div>
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
      <ButtonCommon functionsName={functionsName} />
    </>
  );
};

export { ManageCategory };
