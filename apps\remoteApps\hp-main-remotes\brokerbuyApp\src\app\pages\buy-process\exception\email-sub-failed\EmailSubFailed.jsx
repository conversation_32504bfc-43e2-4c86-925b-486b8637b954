/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> sherlin
 * @email <EMAIL>
 */
import React, { useState, useLayoutEffect } from 'react';
import { Input, ButtonCommon } from '@hp/components';
import {
  bbForms,
  bbMPNviewSearch,
  bbMPNSentEmail,
  bbMPNDiscardEmail,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { CommonSpinner } from '@hp/components';
import { dataTableServiceProvider } from '@hp/components';

const EmailSubFailed = (props) => {
  const dispatch = useDispatch();
  const {
    bbFormDetails,
    loading,
    emailFiles,
    emailPreviewGenerated,
    bbFormResponse,
  } = useSelector((store) => store.bb);
  //=========================================Local States Declaration Section Begins ===========================================//
  const [formInputs, setFormInputs] = useState([]);
  const [form, setForm] = useState();
  const [emailPreviewForm, setEmailPreviewForm] = useState();
  const [buttonDisable, setButtonDisable] = useState(false);
  const [emailPreview, setEmailPreview] = useState();
  const [mailID, setMailID] = useState([]);
  const [isSearch, setIsSearch] = useState({
    isSearchDisabled: true,
    isClientValue: '',
    isDateValue: '',
    addOnchange: false,
  });
  const [productFileMasterId, setProductFileMasterId] = useState(null);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  //=========================================Local States Declaration Section Ends===========================================//
  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Component did mount First call for this component.
     */

    dispatch(bbForms('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
    };
  }, []);

  useLayoutEffect(() => {
    if (emailPreviewGenerated && emailPreviewGenerated.value) {
      setEmailPreview(emailPreviewGenerated.value);
    }
  }, [emailPreviewGenerated]);

  useLayoutEffect(() => {
    if (bbFormResponse && bbFormResponse.value) {
      dispatch(bbForms('subscribe'));
    }
  }, [bbFormResponse]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: form response.
     */

    if (bbFormDetails && bbFormDetails !== undefined && bbFormDetails?.value) {
      var clientID = null;
      var selectedDate = null;
      const form = bbFormDetails?.value;
      if (form && form.length) {
        form?.map((items) => {
          if (items.uniqueKey === 'page-title') {
            if (items.parameters === 'emailFail') {
              const headerControls = form.filter(
                (e) => e.uniqueKey === 'page-title'
              );
              if (headerControls && headerControls.length) {
                headerControls[0].formSubDetailsInternalDTOList &&
                  headerControls[0].formSubDetailsInternalDTOList.length &&
                  headerControls[0].formSubDetailsInternalDTOList.map(
                    (element) => {
                      if (element.uniqueKey === 'client-control') {
                        clientID = element.value;
                      } else if (element.uniqueKey === 'date-control') {
                        selectedDate = element.value;
                      }
                    }
                  );
                setIsSearch({
                  ...isSearch,
                  isDateValue: selectedDate,
                  isClientValue: clientID,
                });
                let emailPreviewDetails = bbFormDetails.value.filter((e) => {
                  return e.type === 'EmailPreview';
                });

                let formControlDetails = bbFormDetails.value.filter((e) => {
                  return e.type !== 'EmailPreview';
                });
                setEmailPreviewForm(emailPreviewDetails);
                setForm(formControlDetails);
                setFormInputs(formControlDetails);
              }
            }
          }
        });
      }
    }
  }, [bbFormDetails]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: getting Main list based on client Search.
     */
    if (emailFiles?.value) {
      const stateValues = [...formInputs];
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'previewTable'
      );
      if (foundIndex !== -1) {
        stateValues[foundIndex].value = emailFiles.value;
        setFormInputs(stateValues);
      }
    }
  }, [emailFiles]);

  const forms = (formControl) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function to Render Form Element.
     */
    return formControl
      ? formControl?.map((element, index) => {
          if (element.uniqueKey === 'page-title') {
            return (
              <div key={index}>
                <Input
                  formType={element}
                  onChangeHandler={(type, event) =>
                    handlers[element.onChangeFunction](type, event, 'search')
                  }
                />
                <div
                  className="two-col-layout-right-30"
                  style={{ marginBottom: '100px', position: 'relative' }}
                >
                  <div
                    className="three-col-layout "
                    style={{
                      position: 'absolute',
                      top: '0px',
                      left: '250px',
                    }}
                  >
                    {forms(element.formSubDetailsInternalDTOList)}
                  </div>
                  <div></div>
                </div>
              </div>
            );
          } else if (element.uniqueKey === 'bb-details') {
            return;
          } else if (element.uniqueKey === 'previewTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val) => {
                return {
                  selector: val.selector,
                  width: val.displayWidth,
                  name: val.displayName,
                };
              });
            return (
              <Input
                dataTableColumn={columns}
                dataTableEventHandler={handlers.handleRowClick}
                selectedRow={(type) => checkSelectedRow(type)}
                conditionalRowStyles={conditionalRowStyles}
                isEditable="notShowing"
                formType={element}
                key={index}
              />
            );
          } else {
            return (
              <Input
                onChangeHandler={(type, event) =>
                  handlers[element.onChangeFunction](type, event, 'search')
                }
                conditionalRowStyles={conditionalRowStyles}
                isEditable="notShowing"
                formType={element}
                key={index}
              />
            );
          }
        })
      : null;
  };
  const onChangeHandler = (type, event, action) => {
    const stateValues = JSON.parse(JSON.stringify(formInputs)); // Deep clone

    if (action === 'add') {
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'bb-details'
      );
      if (foundIndex !== -1) {
        const subIndex = stateValues[
          foundIndex
        ].formSubDetailsInternalDTOList.findIndex(
          (e) => e.uniqueKey === type.uniqueKey
        );
        if (subIndex !== -1) {
          stateValues[foundIndex].formSubDetailsInternalDTOList[
            subIndex
          ].value = event.target.value;
        }
      }
    } else {
      if (type.uniqueKey === 'client-control') {
        stateValues[0].formSubDetailsInternalDTOList[0].value =
          event.target.value;
        setIsSearch((prev) => ({
          ...prev,
          isClientValue: event.target.value,
        }));
      } else if (type.uniqueKey === 'date-control') {
        stateValues[0].formSubDetailsInternalDTOList[1].value = event;
        setIsSearch((prev) => ({
          ...prev,
          isDateValue: event,
        }));
      }

      const searchButtonIndex =
        stateValues[0].formSubDetailsInternalDTOList.findIndex(
          (el) => el.uniqueKey === 'button-search'
        );

      if (searchButtonIndex !== -1) {
        stateValues[0].formSubDetailsInternalDTOList[
          searchButtonIndex
        ].disableFlag = 'F';
      }
    }

    setFormInputs(stateValues);
  };

  const handleRowClick = (type, event) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    var styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      type.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  };

  function searchEmailSub() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for Search.
     */
    if (isSearch.isSearchDisabled) {
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
  }

  function onClearFunction() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: to clear all selected controls.
     */
    if (!isSearch.isSearchDisabled) {
      var updatedState = [...formInputs];
      updatedState.map((input) => {
        input.value = null;

        input.formSubDetailsInternalDTOList &&
          input.formSubDetailsInternalDTOList !== null &&
          input.formSubDetailsInternalDTOList.map((subInput) => {
            subInput.value = null;
            if (subInput.uniqueKey === 'button-search') {
              subInput.disableFlag = 'T';
            }
            if (subInput.uniqueKey === 'clear-button') {
              subInput.disableFlag = 'T';
            }
          });
      });
    }

    let FormupdatedState = updatedState.filter((e) => {
      return e.type !== 'EmailPreview';
    });

    setFormInputs(FormupdatedState);
    setProductFileMasterId(null);
    setButtonDisable(false);
  }
  const checkSelectedRow = (items) => {
    let listArr = [];
    if (items.selectedRows.length > 0) {
      items.selectedRows.map((item) => {
        listArr.push(item.bbEmailOutMasterId);
      });
      setMailID(listArr);
      setButtonDisable(true);
    } else {
      setButtonDisable(false);
    }
  };

  const email_Resend = () => {
    dispatch(bbMPNSentEmail(mailID));
  };
  const email_discard = () => {
    dispatch(bbMPNDiscardEmail(mailID));
  };
  const functionsName = { email_Resend, email_discard };

  const handlers = {
    onChangeHandler,
    handleRowClick,
    onClearFunction,
    searchEmailSub,
  };
  return (
    <>
      {/* Loader for entire Component */}
      <CommonSpinner visible={loading} />

      {/* rendering Form Controls */}
      {formInputs && formInputs.length ? forms(formInputs) : ''}
      {buttonDisable && <ButtonCommon functionsName={functionsName} />}
    </>
  );
};
export { EmailSubFailed };
