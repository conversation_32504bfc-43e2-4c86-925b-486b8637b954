/* eslint-disable no-case-declarations */
/* eslint-disable array-callback-return */
/* eslint-disable no-sequences */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @nx/enforce-module-boundaries */

import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { Ellipsis } from 'react-awesome-spinners';
import {
  getApListing,
  getOnHoldFilterFormDetails,
  getSerReqLists,
  getOnHoldSearchData,
  getapListingFilteredData,
  passApListingData,
  getbreadCrumb,
  setPage,
  setPageName,
  showDialog,
  resetPoInvStatus,
  poLinkedResponseClear,
  NonPoINVSubmitForApprovalReset,
  addCreditResReset,
  invDetailsEditResponseReset,
  invUnmatchedOnMatchResponseReset,
  ApprovalsOnMatchAndSubmitForApprovalReset,
  SubmitCostInvoiceReset,
  approveInvoiceOnNthApprovalReset,
  emailTemplateResponseReset,
  pprejectResponseReset,
  invdetailsReset,
  waitGrnDetailsReset,
  supplierdetailsReset,
  BUTTON_SUCCESSReset,
  BUTTON_FAILUREReset,
  poInvStatusChangeResponseReset,
  ApListingOnRowClickDataReset,
  financeResetStateField,
} from '@hp/mainstore';
import { dynamicListlimitAndOffset } from '@hp/mainstore';
import { globalutils } from '@hp/utils';
import { Button, formValidationUtil } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import { headerColumns } from '../aplisting-sub/APListingStaticColumn';
import { cloneDeep } from 'lodash';
import CreditModel from '../../invoices/invoice-common/credit_note/CreditModel';

import {
  CommonSpinner,
  Input,
  SearchComponent,
  getClientFormattedCurrentDate,
} from '@hp/components';
import { useAppRouterDom } from '@hp/utils';
import { Tooltip } from 'antd';
const APListing = (props) => {
  const dispatch = useDispatch();

  const { domParameters, navigate, location } = useAppRouterDom();

  // const menuData = domParameters?.menuData || '';
  // const submenu = domParameters?.submenu || '';
  const parameters = domParameters?.parameters || '';

  let user = globalutils.getDataFromStorage('all');
  const clientId = user?.clientId;
  const userId = parseInt(user?.userId);
  let todaysDate = globalutils.formatDateMonth(new Date());

  const [TableHead, setTableHead] = useState();
  const [TableList, setTableList] = useState({ TableListResponse: [] });
  const [Filter, setFilterData] = useState({ FilteredData: [] });
  const [TableListinglength, setTableListlength] = useState(0);
  const [isLoading, setLoading] = useState(true);
  const [Value, setParameter] = useState();
  const [pagenum, setPagenum] = useState(1);

  const [creditActive, setCreditActive] = useState(false);

  const [dateRangeDTO, setDateRangeDTO] = useState({
    fromDate: todaysDate,
    toDate: todaysDate,
  });
  const [subStatus, setsubStatus] = useState('');
  const [resetPage, setResetPage] = useState(false);
  const [onSearchData, setOnSearchData] = useState(false);

  // const [apListingOnRowClickData, setAPListingOnRowClickData] = useState();

  const [formDetails, setFormDetails] = useState(null);

  const {
    aplisting,
    pprejectResponse,
    invReceived,
    invUnmatchedOnMatchResponse,
    poInvStatusChangeResponse,
    invdetails,
    waitGrnDetails,
    approveOnMatchAndSubmitForApproval,
    approveInvoiceOnNthApproval,
    poLinkedResponse,
    onHoldFilterForm,
    onHoldSearchForm,
    serReqList,
    ApListingOnRowClickData,
    nonPoInvApprovalSucess,
    addCreditRes,
    invDetailsEditResponse,
    costInvSubmit,
  } = useSelector((store) => store.accpay);

  const { emailTemplateResponse } = useSelector((store) => store.email);
  const { buttonError } = useSelector((store) => store.buttons);
  const { supplierdetails } = useSelector((store) => store.suppliers);
  const { pageNumber, pageName } = useSelector((store) => store.table);
  const { innerMenuName, sideMenuName } = useSelector((store) => store.menu);

  let menuName = [];
  if (sideMenuName && sideMenuName !== undefined) {
    sideMenuName.indexOf(',') > -1 ? (menuName = sideMenuName.split(',')) : [];
  }

  const funcToSetResponseMessage = (type, resMessage, duration = 1500) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;

    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );

    if (autoHide) {
      setTimeout(() => {
        dispatch(showDialog({ showPopup: false }));
      }, duration);
    }
  };

  let Label = {
    PO: 'Purchase Order',
    INV: 'Invoice',
    PP: 'Invoice',
    transmitted: 'Payment Posting',
    'failed-posting': 'Payment Posting',
    'failed-to-create': 'Payment Posting',
  };
  //...............................................................

  useEffect(() => {
    dispatch(getOnHoldFilterFormDetails(userId));
  }, []);

  useEffect(() => {
    // return () => {
    //   dispatch({
    //     type: accPayConstants.SERV_REQ_LIST,
    //     serReqList: undefined,
    //   });
    // };
    dispatch(
      getSerReqLists({
        idName: 'INV',
        id: null,
        moduleCode: 'AP',
        userId: userId,
      })
    );
  }, [serReqList, dispatch, userId]);

  useEffect(() => {
    if (onHoldFilterForm && onHoldFilterForm.value) {
      let tempArray = null;
      if (Array.isArray(onHoldFilterForm.value)) {
        tempArray =
          onHoldFilterForm.value[0]?.formSubDetailsInternalDTOList?.map(
            (item) => {
              const newItem = { ...item };

              if (newItem.uniqueKey === 'period') {
                newItem.value = { fromDate: todaysDate, toDate: todaysDate };
              }

              return newItem;
            }
          );
      }
      setFormDetails(tempArray);
    }
  }, [onHoldFilterForm]);

  useEffect(() => {
    if (onHoldSearchForm && onHoldSearchForm.value) {
      setResponse(onHoldSearchForm.value);
    }
  }, [onHoldSearchForm]);

  useEffect(() => {
    if (!onHoldSearchForm && onSearchData) {
      setFilterData([]);
      funcToSetResponseMessage('error', 'Something went wrong');
      setOnSearchData(false);
    }
  }, [onHoldSearchForm]);

  useEffect(() => {
    if (aplisting && aplisting.value) {
      setResponse(aplisting.value);

      dispatch(getbreadCrumb(`${Label[menuName[0]]} / ${innerMenuName}`));
    }
    setResetPage(!resetPage);
    return () => {
      setTableListlength(null);
      setTableList([]);
      setFilterData({ FilteredData: [] });
      setTableHead([]);
    };
  }, [aplisting]);

  useEffect(() => {
    if (invReceived) {
      setFilterData({ FilteredData: invReceived.value });
      //setLoading(false);
    }
  }, [invReceived]);

  useEffect(() => {
    let isActive = true;
    if (isActive === true) {
      getParameter();
    }
    return () => {
      isActive = false;
    };
  }, [parameters]);

  useEffect(() => {
    let data = null;
    poLinkedResponse
      ? funcToSetResponseMessage('success', poLinkedResponse.value)
      : null;
    dispatch(poLinkedResponseClear(data));
  }, [poLinkedResponse]);

  useEffect(() => {
    if (nonPoInvApprovalSucess && nonPoInvApprovalSucess.value) {
      if (nonPoInvApprovalSucess.value.validationSuccess) {
        funcToSetResponseMessage(
          'success',
          nonPoInvApprovalSucess.value.postedSuccessfully
        ),
          dispatch(NonPoINVSubmitForApprovalReset());
      }
    }
  }, [nonPoInvApprovalSucess]);

  useEffect(() => {
    if (addCreditRes) {
      setCreditActive(false);
      resetDataTable();
    }
    if (addCreditRes?.value) {
      getParameter();
      funcToSetResponseMessage('sucess', addCreditRes.value);
    }
    return () => {
      dispatch(addCreditResReset(null));
    };
  }, [addCreditRes]);

  useEffect(() => {
    let invEditedDetailsResponse = null;
    if (invDetailsEditResponse && invDetailsEditResponse.value) {
      funcToSetResponseMessage('success', invDetailsEditResponse.value);
      dispatch(invDetailsEditResponseReset(invEditedDetailsResponse));
    }
  }, [invDetailsEditResponse]);

  //get class according to due flag.
  const getDueFlagClass = (dueFlag) => {
    if (dueFlag) {
      switch (dueFlag) {
        case 'RF':
          return 'red';
        case 'YF':
          return 'yellow';
        case 'OF':
          return 'orange';
        case 'BF':
          return 'black';
        default:
          return 'white';
      }
    }
    return '';
  };

  /**
* <AUTHOR> R B
conditionalRowStyles -> Order is given based on the priority, 
please don't change order unless required 
*/
  const conditionalRowStyles = [
    {
      //order:  1
      when: (row) => row && row.dueFlag,
      style: (row) => ({
        borderStyle: 'solid',
        borderWidth: '0 0 0 4px',
        position: 'absolute',
        borderColor: `${getDueFlagClass(row.dueFlag)}`,
      }),
    },
    {
      //order:  2
      when: (row) =>
        ApListingOnRowClickData &&
        row.serialNo === ApListingOnRowClickData.serialNo,
      style: {
        '*': {
          color: '#fff',
          backgroundColor: ' rgb(151 179 213) !important',
        },
      },
    },
    {
      //order:  3
      when: (row) =>
        ApListingOnRowClickData &&
        row.serialNo === ApListingOnRowClickData.serialNo &&
        row.dueFlag,
      style: (row) => ({
        '*': {
          color: '#fff',
          backgroundColor: ' rgb(151 179 213) !important',
        },
        borderStyle: 'solid',
        borderWidth: '0 0 0 4px',
        position: 'absolute',
        borderColor: `${getDueFlagClass(row.dueFlag)}`,
      }),
    },
  ];

  const onAddClick = () => {
    setCreditActive(true);
  };

  const creditClose = () => {
    setCreditActive(false);
    resetDataTable();
  };
  useEffect(() => {
    let unmatchOnMatchResponse = null;
    invUnmatchedOnMatchResponse
      ? (funcToSetResponseMessage('success', invUnmatchedOnMatchResponse.value),
        dispatch(invUnmatchedOnMatchResponseReset(unmatchOnMatchResponse)))
      : '';
    getParameter();
  }, [invUnmatchedOnMatchResponse]);

  useEffect(() => {
    if (
      approveOnMatchAndSubmitForApproval &&
      approveOnMatchAndSubmitForApproval.value
    ) {
      if (approveOnMatchAndSubmitForApproval.value.validationSuccess) {
        funcToSetResponseMessage(
          'success',
          approveOnMatchAndSubmitForApproval.value.postedSuccessfully
        );
      }
    }
    return () => {
      dispatch(ApprovalsOnMatchAndSubmitForApprovalReset());
    };
  }, [approveOnMatchAndSubmitForApproval, dispatch]);

  useEffect(() => {
    if (costInvSubmit && costInvSubmit.value) {
      if (costInvSubmit.value.validationSuccess) {
        funcToSetResponseMessage(
          'success',
          costInvSubmit.value.postedSuccessfully
        ),
          dispatch(SubmitCostInvoiceReset());
      }
    }
  }, [costInvSubmit, dispatch]);

  useEffect(() => {
    let data = null;
    if (approveInvoiceOnNthApproval) {
      if (approveInvoiceOnNthApproval.value) {
        funcToSetResponseMessage('success', approveInvoiceOnNthApproval.value),
          dispatch(approveInvoiceOnNthApprovalReset(data));
      }
    }
  }, [approveInvoiceOnNthApproval, dispatch]);

  useEffect(() => {
    if (emailTemplateResponse) {
      let emailTemplate = null;
      emailTemplateResponse.value.ppErrorResponseDto
        ? funcToSetResponseMessage(
            'success',
            emailTemplateResponse.value.ppErrorResponseDto.postedSuccessfully
          )
        : null;
      dispatch(emailTemplateResponseReset(emailTemplate));
    }
  }, [emailTemplateResponse, dispatch]);

  useEffect(() => {
    let rejectresponse = null;
    if (pprejectResponse) {
      funcToSetResponseMessage('success', pprejectResponse.value);
      getParameter();
    }
    dispatch(pprejectResponseReset(rejectresponse));
  }, [pprejectResponse, dispatch]);

  useEffect(() => {
    let invdetails = null;
    dispatch(invdetailsReset(invdetails));
  }, [invdetails, dispatch]);

  useEffect(() => {
    let waitGrnDetails = null;
    dispatch(waitGrnDetailsReset(waitGrnDetails));
  }, [waitGrnDetails, dispatch]);

  useEffect(() => {
    let supplierdetails = null;
    dispatch(supplierdetailsReset(supplierdetails));
  }, [supplierdetails, dispatch]);

  useEffect(() => {
    let buttonErr = null;
    if (buttonError !== undefined) {
      dispatch(BUTTON_SUCCESSReset());
    }
    dispatch(BUTTON_FAILUREReset(buttonErr));
  }, [buttonError, dispatch]);
  useEffect(() => {
    let statusChangeResponse = null;
    if (poInvStatusChangeResponse) {
      funcToSetResponseMessage('success', poInvStatusChangeResponse.value);
      getParameter();
    }
    dispatch(poInvStatusChangeResponseReset(statusChangeResponse));
    dispatch(resetPoInvStatus());
    dispatch(financeResetStateField({ fieldName: 'pdfFetch' }));
  }, [poInvStatusChangeResponse, dispatch, parameters]);

  useEffect(() => {
    setPagenum(pageNumber);
    // setConditionalStyles(rowStyle);
  }, [pageNumber]);

  useEffect(() => {
    if (innerMenuName) {
      dispatch(setPageName(innerMenuName));
      if (pageName && pageName !== innerMenuName) {
        dispatch(ApListingOnRowClickDataReset());
      }
    }
  }, [innerMenuName, dispatch, pageName]);

  useEffect(() => {
    if (subStatus && aplisting && aplisting.value) {
      setResponse(aplisting.value);
    }
  }, [subStatus]);
  //......................................................................
  const getParameter = () => {
    setLoading(true);
    const Parameter = domParameters?.parameters || '';
    if (Parameter.indexOf(',') > -1) {
      const Value = Parameter.split(',');
      setParameter(Value);
      getAPListingData(Value);
    }
  };

  function getAPListingData(Value) {
    let dueFlag = Value[1];
    dispatch(
      getApListing({
        tablename: Value[0],
        status: Value[1],
        clientId: clientId,
        date: getClientFormattedCurrentDate(),
        userId,
        dueFlag: dueFlag,
      })
    );
  }

  const setResponse = (ApListingData) => {
    let Headers = false;
    let TableListing = false;
    switch (ApListingData.dtoName) {
      case 'POTableDto': {
        Headers = ApListingData.poHeadingDto;
        TableListing = ApListingData.poTableDataDtoList;
        break;
      }
      case 'INVTableDto': {
        if (parameters && Value && Value[1] === 'on-hold') {
          if (subStatus) Headers = ApListingData.invTableHeadingOnHoldDtoSub;
          else Headers = ApListingData.invHeadingOnHoldDto;
        } else if (parameters && Value && Value[1] === 'credit_note') {
          Headers = ApListingData.creditNoteHeadingDto;
        } else Headers = ApListingData.invHeadingDto;
        TableListing = ApListingData.invTableDataDtoList;
        break;
      }
      case 'PPTableDto': {
        Headers = ApListingData.ppHeadingDto;
        TableListing = ApListingData.ppTableDataDtoList;
        break;
      }
    }

    if (TableListing) {
      setTableListlength(TableListing.length);
      setTableList({ TableListResponse: TableListing });
      setFilterData({ FilteredData: TableListing });
    }
    if (Headers) {
      const DisplayTitle = (row, key) => {
        return (
          <Tooltip
            classNames={{ root: 'ant-tooltip-container' }}
            title={row[key]}
          >
            <div className="display-title custom-overflow">{row[key]}</div>
          </Tooltip>
        ); //added from v2
      };
      const TableHead = Object.entries(Headers).map(([key, value]) => {
        let Width = '';
        let Right = '';
        let style = '';
        switch (key) {
          case 'serialNo': {
            Width = '5%';
            break;
          }
          case 'entity': {
            Width = '6%';
            break;
          }
          case 'rcvdAt': {
            Width = '12%';
            break;
          }
          case 'poNumber': {
            Width = '11%';
            break;
          }
          case 'relNumber': {
            Width = '9%';
            break;
          }
          case 'po_number': {
            Width = '10%';
            break;
          }
          case 'invNumber': {
            Width = '10%';
            break;
          }
          case 'inv_number': {
            Width = '10%';
            break;
          }
          case 'poDate': {
            Width = '8%';
            break;
          }
          case 'po_date': {
            Width = '8%';
            break;
          }
          case 'invDate': {
            Width = '9%';
            break;
          }
          case 'inv_date': {
            Width = '9%';
            break;
          }
          case 'deliveryDate': {
            Width = '8%';
            break;
          }
          case 'amountConverted': {
            Width = '12%';
            Right = true;
            break;
          }
          case 'currency': {
            Width = '5%';
            break;
          }
          case 'description': {
            style = { color: '#c74646' };
            Width = '10%';
            break;
          }
          case 'subStatusEventTime': {
            Width = '9%';
            break;
          }
          case 'supplierName': {
            Width = '31%';
            break;
          }
        }
        return {
          selector: key,
          name: value,
          width: Width,
          right: Right,
          sortable: true,
          style: style,
          cell: (row) => DisplayTitle(row, key),
        };
      });
      let sortedColumns = TableHead?.map((item) => {
        if (item?.selector.toLowerCase().includes('converted')) {
          return {
            ...item,
            sortFunction: (rowA, rowB) => {
              let a = parseFloat(rowA[item.selector].replace(/,/g, ''));
              let b = parseFloat(rowB[item.selector].replace(/,/g, ''));
              if (isNaN(a) || b > a) return -1;
              else if (isNaN(b) || a > b) return 1;
              else return 0;
            },
          };
        } else return item;
      });

      if (
        (sideMenuName && sideMenuName === 'PO,in-process') ||
        sideMenuName === 'PO,unprocessed' ||
        sideMenuName === 'INV,unprocessed'
      ) {
        setTableHead(headerColumns);
      } else {
        setTableHead(sortedColumns);
      }
    }
    setLoading(false);
  };

  // const handleDateChange = (date) => {
  //   const Parameter = props.match.params.parameters;
  //   let Value = '';
  //   if (Parameter.indexOf(',') > -1) {
  //     Value = Parameter.split(',');
  //   }
  //   setSearchDate(date);
  //   if (Value[0] === 'INV') {
  //     dispatch(accPayAction.getInvDetailsByDate(Value[1], date, clientId));
  //   } else if (Value[0] === 'PO') {
  //     // const body = {
  //     //   status: Value[1],
  //     //   commonDateForApListing: date,
  //     //   clientId: clientId,
  //     // };
  //     dispatch(accPayAction.getPoDetailsByDate(Value[1], date, clientId));
  //   }
  // };

  const fileterdDataFunc = (data) => {
    setFilterData({ FilteredData: data });
    dispatch(getapListingFilteredData(data));
  };
  const callback = (data) => {
    let poId = data.po_id;
    let invId = data.inv_id;
    let ppId = data.pp_id;
    let ppEventTime = data.eventTime;
    let suppId = data.supplierId;
    let ppstatus = data.status;
    let description = data.description;
    let currApprover = data.curApproverId;
    let Keyvalue = Object.keys(data)[1];
    let pymtReqId = data.pymtReqId;
    let rtPoId = data.rtPoId;

    dispatch(passApListingData(data));
    dispatch(setPage(Math.ceil(data.serialNo / 10)));
    dispatch(setPageName(innerMenuName));

    const { pathname } = location;
    const pathSegments = pathname.split('/');

    const baseIndex = pathSegments.findIndex(
      (segment) => segment === 'accounts-payable'
    );

    let basePath = '';
    if (baseIndex !== -1) {
      basePath = pathSegments.slice(0, baseIndex + 1).join('/');
    }

    if (data.status === 'unmatched') {
      navigate(`unmatched/${Keyvalue}/${invId}`, { suppId, poId });
    } else if (data.status === 'cost-inv') {
      navigate(`cost-inv/${invId}`, { suppId, invId });
    } else if (
      data.status === 'settled' ||
      data.status === 'wait-inv' ||
      data.status === 'wait-grn'
    ) {
      navigate(`PO/${Keyvalue}/${poId}`, { Keyvalue, poId });
    } else if (Keyvalue === 'pp_id') {
      if (pymtReqId !== null) {
        navigate(`${basePath}/PaymentReqList/for-approve/${pymtReqId}`);
      } else {
        navigate(`PP/${Keyvalue}/${ppId}`, {
          state: {
            Keyvalue,
            invId,
            ppId,
            ppEventTime,
            suppId,
            ppstatus,
            description,
            currApprover,
          },
        });
      }
    } else if (data.status === 'returned' && poId === null) {
      navigate(`${Value[0]}/${Keyvalue}/${poId}/${invId}`, {
        Keyvalue,
        invId,
        ppId,
        ppEventTime,
        suppId,
        ppstatus,
      });
    } else if (data.status === 'returned' && poId !== null) {
      navigate(`invoice/${Keyvalue}/${poId}/${invId}`, {
        state: { suppIds: suppId },
      });
    } else if (
      (data.status || Value[1]) === 'part-match' ||
      (data.status || Value[1]) === 'matched' ||
      (data.status || Value[1]) === 'for-approve' ||
      (data.status || Value[1]) === 'archived' ||
      (data.status || Value[1]) === 'rejected' ||
      (data.status || Value[1]) === 'approved' ||
      (data.status || Value[1]) === 'confirmed' ||
      (data.status || Value[1]) === 'on-hold'
    ) {
      navigate(`invoice/${Keyvalue}/${poId}/${invId}`, {
        state: { suppId, invId },
      });
    } else if (data.status === 'non-po' || Value[1] === 'non-po') {
      navigate(`${data.status}/${invId}`, { state: { suppId, invId } });
    } else if ((data.status || Value[1]) === 'credit_note') {
      if (rtPoId) {
        navigate(`invoice/${Keyvalue}/${poId}/${invId}`, {
          state: { suppId, invId },
        });
      } else {
        navigate(`non-po/${null}`, { suppId, invId: null });
      }
    } else if (Keyvalue === 'po_id' || Keyvalue === 'inv_id') {
      navigate(`
        ${parameters}`);
    }
  };

  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);

    tempArray?.map((items) => {
      if (items.uniqueKey === uniqueKey) {
        items.value = event.target.value;
        setsubStatus(event.target.value);
      }
    });

    setFormDetails(tempArray);
  };

  const handleDateRange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);

    tempArray?.map((items) => {
      if (items.uniqueKey === uniqueKey) {
        items.value = event;
        setDateRangeDTO(event);
      }
    });

    setFormDetails(tempArray);
  };

  const handleButtonClick = (event, uniqueKey) => {
    switch (uniqueKey) {
      case 'searchButton':
        let isValid = formValidationUtil.validateForm(formDetails);
        if (isValid.validSuccess && dateRangeDTO !== '') {
          dispatch(
            getOnHoldSearchData({
              userId,
              clientId,
              dateRangeDTO,
              subStatus,
            })
          );
          setOnSearchData(true);
          // dispatch(accPayAction.getAgingTableDetails(selected));
          // dispatch(accPayAction.getAgingTableTotalCount(selected));
        } else {
          let tempArray = cloneDeep(isValid.formList);
          setFormDetails(tempArray);
        }
        break;
      case 'clearButton':
        let tempArray = cloneDeep(formDetails);

        tempArray?.map((items) => {
          items.value =
            items.uniqueKey === 'period'
              ? { fromDate: todaysDate, toDate: todaysDate }
              : null;
          items.errorFlag = false;
        });
        setsubStatus('');
        setDateRangeDTO({ fromDate: todaysDate, toDate: todaysDate });
        setFormDetails(tempArray);
        break;
    }
  };

  const resetDataTable = () => {
    let obj = {
      limit: 10,
      offset: 1,
    };
    dispatch(dynamicListlimitAndOffset(obj, 'subscribe'));
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleDateRange,
    handleButtonClick,
  };

  // const formControlsBinding = (data) => {
  //   return data
  //     ? data.map((element, index) => {
  //         if (element.type === 'Button') {
  //           return (
  //             <div
  //               key={index}
  //               style={{ marginTop: '24px', marginLeft: '20px' }}
  //             >
  //               <Input
  //                 key={index}
  //                 formType={element}
  //                 onChangeHandler={(element, event) => {
  //                   onChangeHandlingFunctions[element.onChangeFunction](
  //                     event,
  //                     element.uniqueKey,
  //                     element
  //                   );
  //                 }}
  //               />
  //             </div>
  //           );
  //         } else {
  //           return (
  //             <div
  //               style={
  //                 element.uniqueKey === 'period'
  //                   ? { width: '100%', maxWidth: '300px' }
  //                   : { width: '100%', maxWidth: '300px', marginLeft: '20px' }
  //               }
  //               key={index}
  //             >
  //               <Input
  //                 key={index}
  //                 formType={element}
  //                 isEditable="notShowing"
  //                 onChangeHandler={(element, event) => {
  //                   onChangeHandlingFunctions[element.onChangeFunction](
  //                     event,
  //                     element.uniqueKey,
  //                     element
  //                   );
  //                 }}
  //               />
  //             </div>
  //           );
  //         }
  //       })
  //     : null;
  // };

  const formControlsBinding = (data) => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          position: 'absolute',
          width: '100%',
          maxWidth: '900px',
          top: '-15px',
          left: '230px',
        }}
      >
        {data
          ? data.map((element, index) => {
              if (element.type === 'Button') {
                return (
                  <div
                    key={index}
                    style={{ marginTop: '24px', marginLeft: '20px' }}
                  >
                    <Input
                      key={index}
                      formType={element}
                      onChangeHandler={(element, event) => {
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey,
                          element
                        );
                      }}
                    />
                  </div>
                );
              } else {
                return (
                  <div
                    style={
                      element.uniqueKey === 'period'
                        ? { width: '100%', maxWidth: '300px' }
                        : {
                            width: '100%',
                            maxWidth: '300px',
                            marginLeft: '20px',
                          }
                    }
                    key={index}
                  >
                    <Input
                      key={index}
                      formType={element}
                      isEditable="notShowing"
                      onChangeHandler={(element, event) => {
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey,
                          element
                        );
                      }}
                    />
                  </div>
                );
              }
            })
          : null}
      </div>
    );
  };

  const createInvoice = () => {
    navigate(`cost-inv/${null}`);
  };

  const refreshHandler = () => {
    getParameter();
    dispatch(setPage(Math.ceil(5 / 10)));
    dispatch(ApListingOnRowClickDataReset());
  };

  return (
    <>
      <h1 className="page-title group">
        {Label[menuName[0]]} / {innerMenuName}
        <span className="refresh-button">
          <span
            className="icon-refresh"
            onClick={() => {
              refreshHandler();
            }}
          ></span>
        </span>
      </h1>
      {parameters && Value && Value[1] === 'on-hold' ? (
        <div
          className="flex-row"
          style={{
            justifyContent: 'left',
            position: 'relative',
            marginTop: '30px',
          }}
        >
          {formDetails?.length ? formControlsBinding(formDetails) : ''}
        </div>
      ) : (
        ''
      )}

      {menuName.includes('credit_note') ? (
        <CreditModel creditActive={creditActive} creditClose={creditClose} />
      ) : (
        ''
      )}
      <div className="flex-row" style={{ justifyContent: 'left' }}>
        <SearchComponent
          style={{ marginTop: '8px', marginBottom: '8px', width: '200px' }}
          parameterProps={Value}
          tableListData={TableList.TableListResponse}
          getFilteredData={(data) => fileterdDataFunc(data)}
        />
        {innerMenuName === 'Cost Invoice' ? (
          <Button
            className="mr8 fr mt8 small outline"
            onClick={() => createInvoice()}
          >
            Create Invoice
          </Button>
        ) : null}
      </div>

      {/* <SearchComponent
        style={{ marginTop: '8px', marginBottom: '8px', width: '200px' }}
        parameterProps={Value}
        tableListData={TableList.TableListResponse}
        getFilteredData={(data) => fileterdDataFunc(data)}
      /> */}
      {/* {innerMenuName === 'Archived' ? (
        <DatePickerInput
          close
          value={searchDate}
          onChange={(date) => handleDateChange(date)}
        />
      ) : null} */}

      {menuName.includes('credit_note') ? (
        <Button
          onClick={() => {
            onAddClick(clientId);
          }}
          className="small mb8 outline add-button-custom flex-row vam fr"
          style={{
            marginTop: '-38px',
            position: 'relative',
            zIndex: 1000,
            pointerEvents: 'auto',
          }}
        >
          <i className="icon-add-button "> </i>Credit Note
        </Button>
      ) : (
        ''
      )}
      <div className="mb24 styledDatatable">
        <DataTable
          persistTableHead
          noHeader={true}
          dense={false}
          highlightOnHover={true}
          onRowClicked={(data) => callback(data)}
          striped={true}
          columns={TableHead}
          data={Filter.FilteredData}
          pagination={true}
          paginationDefaultPage={pagenum}
          paginationResetDefaultPage={!resetPage}
          paginationTotalRows={TableListinglength}
          paginationPerPage={10}
          progressPending={isLoading}
          conditionalRowStyles={conditionalRowStyles}
          // progressComponent={<CommonSpinner visible={isLoading} />}
          progressComponent={<Ellipsis color={globalutils.spinnerColor()} />}
        />
      </div>
    </>
  );
};

export { APListing };
