/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */

import { Button, Input, useConfirm } from '@hp/components';
import { memo, useState, useLayoutEffect } from 'react';
import Modal from 'react-modal';
import { useDispatch } from 'react-redux';
import { bbMPNADD, bbMPNEdit } from '@hp/mainstore';
function FormActions(props) {
  const {
    formInputs,
    isOpen,
    productFileMasterId,
    eventHandlers,
    isClose,
    id,
    action,
  } = props;
  const [formInputsFormAction, setformInputsFormAction] = useState([]);
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  useLayoutEffect(() => {
    const form = formInputs.filter((el) => el.uniqueKey === 'mpn-sub-table');
    if (form && form.length) {
      setformInputsFormAction(form[0].formSubDetailsInternalDTOList);
    }
  }, [formInputs]);

  const addProduct = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');

    if (isConfirmed) {
      if (action === 'add') {
        dispatch(
          bbMPNADD({
            productFileMasterId: productFileMasterId,
            obj: formInputsFormAction,
            reqType: 'subscribe',
          })
        );
      } else if (action === 'edit') {
        dispatch(
          bbMPNEdit({ obj: formInputsFormAction, productDetailsId: id })
        );
      }
      // isClose();
      // if (id) return dispatch(bbAction.bbMPNEdit(formInputsFormAction, id));
    }
  };

  return (
    <Modal
      className="Modal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={isOpen}
    >
      <div onClick={isClose} className="modal-close icon-close"></div>
      <div className="page-title">Add MPN</div>
      <div className="two-col-layout">
        {formInputsFormAction && formInputsFormAction.length
          ? formInputsFormAction.map((element, index) => {
              return (
                <Input
                  key={index}
                  onChangeHandler={(type, event) =>
                    eventHandlers.onChangeHandler(type, event, 'add')
                  }
                  isEditable="notShowing"
                  formType={element}
                />
              );
            })
          : ''}
      </div>
      <Button className="mr8  default" onClick={addProduct}>
        Submit
      </Button>
    </Modal>
  );
}
export default memo(FormActions);
