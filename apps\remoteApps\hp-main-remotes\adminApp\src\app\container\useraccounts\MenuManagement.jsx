/* eslint-disable no-empty */
/* eslint-disable react-hooks/exhaustive-deps */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-12-27 10:26:32
 * @modify date 2024-08-01 15:55:58
 * @desc [description]
 */
import { useEffect, useState } from 'react';
import Modal from 'react-modal';
import {
  getDashboardComboNameCodeList,
  createSubMenu,
  getMenuTree,
  getAppNamesCombo,
  editMenu,
  refreshRedis,
  menuMoveUp,
  menuMoveDown,
  deleteMenu,
  menuMovetoLocation,
  getModuleNamesCombo,
  showDialog,
  resetcreateSubMenu,
  resetmenuMoveUp,
  resetmenuMoveDown,
  resetmenuMovetoLocation,
  resetrefreshRedis,
  reseteditMenu,
  resetdeleteMenu,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { Scrollbars } from 'react-custom-scrollbars';
import { useAppRouterDom } from '@hp/utils';
import { AlertModalPopup, ButtonCommon, useConfirm } from '@hp/components';
import { Button, Select, TextInput } from '@hp/components';

function MenuManagement(props) {
  const { domParameters, navigate } = useAppRouterDom();

  const urlParam = domParameters?.parameters || '';
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';

  let module = urlParam;
  if (urlParam && urlParam.indexOf(',') > -1) {
    let moduleList = urlParam.split(',');
    module = moduleList[1];
  }
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const { moduleNameCombo, appNamesCombo, dashboardNameCombo } = useSelector(
    (store) => store.admin
  );

  const { menuTree, menuActionResponse, error, updateSettingsResponse } =
    useSelector((store) => store.portalAdmin);

  //this state will be updated when a row is clicked in a menutree to selected menu id - the selected row will be active
  const [rowClickedID, setRowClickedID] = useState(0);
  // this state is used to update menu objects and sending the state to backend to persist(saving) in database.
  const [state, setState] = useState({
    menuData: {
      menuId: '',
      menuName: '',
      appId: '',
      moduleId: '',
      parentMenuId: '',
      description: '',
      orderId: '',
      funcPath: '',
      parameter: '',
      slug: '',
      dashboard: '',
      expand: '',
      orderExpression: '',
      iconName: '',
      errorFlag: '',
      errorMsg: '',
      childMenu: [],
    },
  });
  const [saveState, setSaveState] = useState('');

  const [buttonsAndOperationsEnable, setButtonsAndOperationEnable] =
    useState(false);

  //Menu style/Expand combobox state
  const [expand] = useState([
    {
      expandId: 1,
      expandName: 'active',
    },
    {
      expandId: 2,
      expandName: 'close',
    },
    {
      expandId: 3,
      expandName: 'finalsub',
    },
  ]);

  //menu operations combo box
  const [menuOperations, setmenuOprations] = useState([
    {
      menuOperationId: 1,
      menuOperation: 'Create Submenu',
    },
    {
      menuOperationId: 2,
      menuOperation: 'Move Up',
    },
    {
      menuOperationId: 3,
      menuOperation: 'Move Down',
    },
    {
      menuOperationId: 4,
      menuOperation: 'Move To Location',
    },
    {
      menuOperationId: 5,
      menuOperation: 'Delete',
    },
  ]);

  const [selectValue, setSelectValue] = useState('');

  //this state maintains menutree
  const [menus, setMenus] = useState([]);
  const [appNames, setAppNames] = useState([]);
  const [moduleNames, setModuleNames] = useState([]);
  const [dashboardNames, setDashboardName] = useState([]);
  const [dashboardFlag, setDashboardFlag] = useState(false);
  let rowID = 0;
  //thi state helps in expanding and collapsing the menutree in screen(+)
  const [toggleState, setToggleState] = useState([]);
  //on clicking moveToLocation operation the state will be updated and a popup will open - and select the destination menu
  const [moveToLocation, setMoveToLocation] = useState(false);
  //this state will maintain the source menu details, which later updates with destination menu
  const [moveToLocationObj, setMoveToLocationObj] = useState([]);
  //enabling and disabling in screen
  const [editEnable, setEditEnable] = useState(true);
  //this state is to update whether its save or edit
  const [saveOrEditState, setSaveOrEditState] = useState('Save');
  const [menuAlertModal, setMenuAlertModal] = useState(false);
  const [alertMessage, setAlertMessage] = useState(null);
  const [roleIdList, setRoleIdList] = useState(null);
  const [menuActionDisabled, setMenuActionDisabled] = useState(false);

  useEffect(() => {
    getParameter();
    return () => {
      dispatch(resetmenuMoveUp());
      dispatch(resetmenuMoveDown());
      dispatch(resetmenuMovetoLocation());
      dispatch(resetdeleteMenu());
      dispatch(reseteditMenu());
      dispatch(resetrefreshRedis());
      dispatch(resetcreateSubMenu());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type: type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (updateSettingsResponse && updateSettingsResponse.value) {
      funcToSetResponseMessage(
        updateSettingsResponse.value.type,
        updateSettingsResponse.value.text
      );
    }
  }, [updateSettingsResponse]);

  useEffect(() => {
    if (error && error.response && error.response.data.type) {
      funcToSetResponseMessage(
        error.response.data.type,
        error.response.data.errorMessage
      );
    }
  }, [error]);

  useEffect(() => {
    if (menuTree && menuTree.value) {
      setMenus([menuTree.value]);
      setSelectValue('');
      if (menuTree?.data?.value) {
        funcToSetResponseMessage(
          menuTree.data.value.type,
          menuTree.data.value.message
        );
      }
    }
  }, [menuTree]);

  useEffect(() => {
    if (
      appNamesCombo &&
      appNamesCombo.value &&
      appNamesCombo.value.length > 0
    ) {
      const appNamesOptions = appNamesCombo.value.map((value) => {
        return {
          value: value.appId,
          display: value.appName,
        };
      });
      setAppNames(appNamesOptions);
    }
  }, [appNamesCombo]);

  useEffect(() => {
    if (moduleNameCombo && moduleNameCombo !== undefined) {
      const moduleNamesOptions = moduleNameCombo.map((value) => {
        return {
          value: value.moduleId,
          display: value.moduleName,
        };
      });
      setModuleNames(moduleNamesOptions);
    }
  }, [moduleNameCombo]);

  useEffect(() => {
    if (dashboardNameCombo && dashboardNameCombo !== undefined) {
      const dashboardNameOptions = dashboardNameCombo.value.map((value) => {
        return {
          value: value.commonCode,
          display: value.commonName,
        };
      });
      setDashboardName(dashboardNameOptions);
    }
  }, [dashboardNameCombo]);

  useEffect(() => {
    if (menuActionResponse && menuActionResponse.value) {
      if (
        menuActionResponse.value.type &&
        menuActionResponse.value.type === 'success'
      ) {
        funcToSetResponseMessage(
          menuActionResponse.value.type,
          menuActionResponse.value.message
            ? menuActionResponse.value.message
            : menuActionResponse.value.text
        );
        let newMenuTree = { value: menuActionResponse.value.menuTree };
        dispatch(getMenuTree(module, newMenuTree));
        dispatch(resetmenuMoveUp());
        setEditEnable(true);
      } else {
        // funcToSetResponseMessage(
        //   menuActionResponse.value.type,
        //   menuActionResponse.value.message
        // );
        setMenuAlertModal(true);
        setAlertMessage(menuActionResponse.value.message);
        setRoleIdList(menuActionResponse.value.roleIdList);
        dispatch(resetmenuMoveUp());
      }
    }
  }, [menuActionResponse]);

  const getParameter = () => {
    dispatch(getDashboardComboNameCodeList());
    dispatch(getMenuTree(module));
    dispatch(getAppNamesCombo());
  };

  //save or edit handler
  const saveMenus = () => {
    setmenuOprations(menuOperations);
    setSaveState(state);
    if (saveOrEditState && saveOrEditState === 'CreateSubmenu') {
      dispatch(createSubMenu({ state: state.menuData, module: module }));
    } else if (saveOrEditState && saveOrEditState === 'Edit') {
      dispatch(editMenu({ state: state.menuData, module: module }));
    }
  };

  //enabling and disabling and setting it to edit
  const editMenus = () => {
    setEditEnable(false);
    setSaveOrEditState('Edit');
    setMenuActionDisabled(false);
  };

  //Update Setting Operation( backend call to update menus to redis)
  const updateMenus = () => {
    dispatch(refreshRedis(module));
  };

  const handleCancel = async () => {
    if (editEnable === true) return;

    const isConfirmed = await confirm('Do you want to cancel editing?');
    if (isConfirmed) {
      setEditEnable(true);
      setState(saveState);
      setMenuActionDisabled(true);
      cancelHandler();
    }
  };

  const functionsName = {
    saveMenus,
    editMenus,
    updateMenus,
    handleCancel,
  };

  /* Handles the row accordions */
  const rolesToggler = (event, id, menu) => {
    if (editEnable === false) {
      handleCancel();
      return;
    }

    if (menu.dashboardFlag === 'T') {
      setDashboardFlag(true);
    } else {
      setDashboardFlag(false);
    }
    event.stopPropagation();

    setRowClickedID(id);
    toggleState[id] = toggleState[id] ? false : true;
    setToggleState(cloneDeep(toggleState));

    setButtonsAndOperationEnable(true);
    setMenuActionDisabled(true);
    if (moveToLocation) {
      setMoveToLocationObj(menu.menuId);
      return;
    }
    setSaveState({ menuData: menu });
    setState({ menuData: menu });
  };

  const handleOnChange = (event) => {
    let menu = cloneDeep(state);
    menu.menuData[event.target.name] = event.target.value;
    setState(menu);
    if (event.target.name === 'appId') {
      dispatch(getModuleNamesCombo(menu.menuData.appId));
    }
  };

  //actions on clicking different operations in combo box
  const handleSelectChange = (e) => {
    setSelectValue(e.target.value);
    switch (e.target.value) {
      case 'Create Submenu':
        setState({
          menuData: {
            ...state.menuData,
            description: '',
            funcPath: '',
            menuName: '',
            parameter: '',
            slug: '',
            expand: '',
            childMenu: [],
          },
        });
        setEditEnable(false);
        setSaveOrEditState('CreateSubmenu');
        break;

      case 'Move Up':
        dispatch(
          menuMoveUp({
            Id: state.menuData.menuId,
            module: module,
          })
        );
        break;

      case 'Move Down':
        dispatch(menuMoveDown({ Id: state.menuData.menuId, module: module }));
        break;

      case 'Move To Location':
        setMoveToLocation(true);
        break;

      case 'Delete':
        deleteHandler();

        break;
    }
  };

  //saving movetolocation popup object
  const saveMoveToLocation = () => {
    dispatch(
      menuMovetoLocation({
        IL: state.menuData.menuId,
        FL: moveToLocationObj,
        module: module,
      })
    );
    setMoveToLocation(false);
  };

  const deleteHandler = async () => {
    const isConfirmed = await confirm('Do you want to continue');
    if (isConfirmed) {
      setSelectValue('');
      dispatch(deleteMenu({ Id: state.menuData.menuId, module: module }));
    } else setSelectValue('');
  };

  const handleProceed = () => {
    navigate(
      `/${menuData}/${submenu}/CreateRolesTable/create-roles,${module}`,
      { message: alertMessage, roleIdList: roleIdList }
    );
    cancelHandler();
  };

  const cancelHandler = () => {
    setSelectValue('');
    setMenuAlertModal(false);
    setAlertMessage('');
    setMoveToLocation(false);
  };

  //displays menu in tree structure
  const RowPrint = (props) => {
    return props.data && props.data.length
      ? props.data.map((data, index) => {
          rowID++;

          let currentRowID = rowID;
          let newParent = props.parent + '[' + index + '].menuList';
          let currentToggleState = toggleState[currentRowID] ? 'open' : 'close';

          let hasChildren =
            data.childMenu && data.childMenu.length
              ? 'has-children'
              : 'no-children';

          return (
            <div
              onClick={(e) => rolesToggler(e, currentRowID, data)}
              key={index}
              id={'row-' + currentRowID}
              className={[
                'tr',
                currentRowID === rowClickedID ? 'active-tr' : '',
                currentToggleState,
              ].join(' ')}
            >
              <div className="flex-row">
                <div className={['td', 'menu', hasChildren].join(' ')}>
                  {data.menuName} {' ( '}
                  {data.menuId}
                  {data.orderExpression ? ',' : ''} {data.orderExpression}
                  {' )'}
                </div>
              </div>
              {data.childMenu && data.childMenu.length ? (
                <RowPrint data={data.childMenu} parent={newParent} />
              ) : null}
            </div>
          );
        })
      : null;
  };

  //this will display fields in the screen
  return (
    <>
      <div className="page-title">Menu Management</div>
      <div className="two-col-layout mb32">
        <div className="col">
          <Select
            label="Menu Actions"
            value={selectValue}
            options={
              menuOperations
                ? menuOperations.map((value) => {
                    return {
                      value: value.menuOperation,
                      display: value.menuOperation,
                    };
                  })
                : []
            }
            style={{ width: 250 + 'px' }}
            onChange={(event) => handleSelectChange(event)}
            disabled={!menuActionDisabled}
          />
        </div>
      </div>
      <div className="two-col-layout mb20">
        <div className="col">
          <div>
            <Scrollbars
              // This will activate auto-height
              autoHeight
              autoHeightMin={100}
              autoHeightMax={830}
            >
              <div className="access-control-table">
                <div className="thead">
                  <div className="tr flex-row"></div>
                </div>
                <div className="tbody">
                  {menus && menus.length ? (
                    <RowPrint data={menus} parent={''} />
                  ) : null}
                </div>
              </div>
            </Scrollbars>
          </div>
        </div>
        <div className="col">
          <div className="card">
            <h1 className="page-title">
              <b>Operations</b>
            </h1>
            <div className="two-col-layout mb20">
              <div className="col">
                <p className="mb8" style={{ fontSize: 13 + 'px' }}>
                  Menu Id : {state.menuData ? state.menuData.menuId : ''}
                </p>
              </div>
              <div className="col">
                <p className="mb8" style={{ fontSize: 13 + 'px' }}>
                  Parent Menu Id :{' '}
                  {state.menuData ? state.menuData.parentMenuId : ''}
                </p>
              </div>
            </div>
            <TextInput
              label="Menu Name"
              required
              className="mb20"
              name="menuName"
              value={state.menuData.menuName || ''}
              onChange={handleOnChange}
              disabled={editEnable}
            />
            <TextInput
              label="Slug"
              required
              className="mb20"
              name="slug"
              value={state.menuData.slug || ''}
              onChange={handleOnChange}
              disabled={editEnable}
            />
            <Select
              label="Application"
              className="mb20"
              value={state.menuData.appId || ''}
              name="appId"
              options={appNames}
              onChange={handleOnChange}
              disabled={editEnable}
            />
            <Select
              label="Module"
              className="mb20"
              value={state.menuData.moduleId || ''}
              name="moduleId"
              options={moduleNames}
              onChange={handleOnChange}
              disabled={editEnable}
            />
            <TextInput
              label="Description"
              className="mb20"
              name="description"
              value={state.menuData.description || ''}
              onChange={handleOnChange}
              disabled={editEnable}
            />
            <TextInput
              label="Function Path"
              className="mb20"
              name="funcPath"
              value={state.menuData.funcPath || ''}
              onChange={handleOnChange}
              disabled={editEnable}
            />

            {dashboardFlag ? (
              <Select
                label="Dashboard"
                className="mb20"
                name="dashboard"
                value={state.menuData.dashboard || ''}
                options={dashboardNames}
                onChange={handleOnChange}
                disabled={editEnable}
              />
            ) : null}
            <TextInput
              label="Paramater"
              className="mb20"
              name="parameter"
              value={state.menuData.parameter || ''}
              onChange={handleOnChange}
              disabled={editEnable}
            />
            <div className="two-col-layout mb20">
              <div className="col">
                <TextInput
                  label="Icon Name"
                  name="iconName"
                  value={state.menuData.iconName || ''}
                  onChange={handleOnChange}
                  disabled={editEnable}
                />
              </div>
              <div className="col">
                <Select
                  label="Menu style/Expand"
                  value={state.menuData.expand || ''}
                  options={
                    expand
                      ? expand.map((value) => {
                          return {
                            value: value.expandName,
                            display: value.expandName,
                          };
                        })
                      : []
                  }
                  name="expand"
                  onChange={handleOnChange}
                  disabled={editEnable}
                />
              </div>
            </div>
            <div className="two-col-layout mb20">
              <div className="col">
                <p style={{ fontSize: 13 + 'px' }}>
                  Order Id : {state.menuData ? state.menuData.orderId : ''}
                </p>
              </div>
              <div className="col">
                <p style={{ fontSize: 13 + 'px' }}>
                  Order Expression :{' '}
                  {state.menuData ? state.menuData.orderExpression : ''}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      {buttonsAndOperationsEnable === true ? (
        <ButtonCommon functionsName={functionsName} />
      ) : null}

      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={moveToLocation}
      >
        <div
          onClick={() => {
            setSelectValue('');
            setMoveToLocation(false);
          }}
          className="modal-close icon-close"
        ></div>
        <div className="mb20">
          <fieldset>
            <legend>Menus:</legend>
            <div className="access-control-table">
              <div className="thead">
                <div className="tr flex-row"></div>
              </div>
              <div className="tbody">
                {menus && menus.length ? (
                  <RowPrint data={menus} parent={''} />
                ) : null}
              </div>
            </div>
          </fieldset>
        </div>
        <div>
          <Button onClick={() => saveMoveToLocation()}>Move</Button>
        </div>
      </Modal>

      {alertMessage ? (
        <AlertModalPopup
          isAlertOpen={menuAlertModal}
          setAlertClose={cancelHandler}
          message={alertMessage}
          handleProceed={handleProceed}
          handleCancel={cancelHandler}
        />
      ) : null}
    </>
  );
}

export { MenuManagement };
