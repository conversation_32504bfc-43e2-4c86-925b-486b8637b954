/* eslint-disable react-hooks/exhaustive-deps */
import {
  <PERSON><PERSON>,
  <PERSON>ton<PERSON>om<PERSON>,
  CheckBoxInput,
  CommonSpinner,
  dataTableServiceProvider,
  Input,
  useConfirm,
} from '@hp/components';
import './ecr_eco_common.scss';
import { useLocation, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  deleteECRFormDetails,
  getECRListingDetails,
  showDialog,
  updateECRStatusListDetails,
  setPageName,
  setRowStyle,
  resetgetECRListingDetails,
  resetdeleteECRFormDetails,
  resetupdateECRStatusListDetails,
} from '@hp/mainstore';
import { Tooltip } from 'antd';
import { globalutils } from '@hp/components';

const ECRListingPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const { ecrListingForm, ecrChangeStatusRes, ecrFormDeleteRes } = useSelector(
    (store) => store.pdm
  );
  const buttonData = useSelector((state) => state.buttons.buttons || null);
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);

  const location = useLocation();
  const segments = location.pathname.split('/');
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;
  // const menuData = segments[1] || '';
  // const submenu = segments[segments.length - 2] || '';
  const parameter = segments[segments.length - 1] || '';
  const isApproval =
    parameter === 'pending-review' || parameter === 'for-approval';
  const currentTableKey = parameter ?? null;
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState(null);
  const [selectedEcrList, setSelectedEcrList] = useState([]);
  const [buttonCommonData, setButtonCommonData] = useState(null);

  useEffect(() => {
    dispatch(
      getECRListingDetails({
        userId: userId,
        clientId: clientId,
        status: parameter,
      })
    );
    dispatch(setPageName(innerMenuName));
    setIsLoading(true);
    return () => {
      setFormData(null);
      setSelectedEcrList([]);
      dispatch(resetgetECRListingDetails());
      dispatch(resetdeleteECRFormDetails());
      dispatch(resetupdateECRStatusListDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, [parameter]);

  useEffect(() => {
    if (ecrListingForm?.value) {
      const tempFormData = ecrListingForm.value.find(
        (item) => item.uniqueKey === currentTableKey
      );
      setFormData(tempFormData);
      setIsLoading(false);
    }
  }, [ecrListingForm]);

  useEffect(() => {
    if (ecrChangeStatusRes?.value) {
      funcToSetResponseMessage(
        ecrChangeStatusRes.value?.type,
        ecrChangeStatusRes.value?.text
      );
      dispatch(
        getECRListingDetails({
          userId: userId,
          clientId: clientId,
          status: parameter,
        })
      );
    }
  }, [ecrChangeStatusRes]);

  useEffect(() => {
    if (ecrFormDeleteRes?.value) {
      funcToSetResponseMessage(
        ecrFormDeleteRes.value?.type,
        ecrFormDeleteRes.value?.text
      );
      dispatch(
        getECRListingDetails({
          userId: userId,
          clientId: clientId,
          status: parameter,
        })
      );
    }
  }, [ecrFormDeleteRes]);

  useEffect(() => {
    if (buttonData && buttonData !== undefined) {
      let data = buttonData.filter((btn) => btn.funcPath !== 'handleReject');
      setButtonCommonData(data);
    }
  }, [buttonData]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  async function handleSubmit() {
    if (selectedEcrList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECRStatusListDetails({
            status: 'pending-review',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: selectedEcrList,
          })
        );
        setSelectedEcrList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECR');
  }

  async function handleSaveDraft() {
    if (selectedEcrList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECRStatusListDetails({
            status: 'draft',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: selectedEcrList,
          })
        );
        setSelectedEcrList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECR');
  }

  async function moveForApprovals() {
    if (selectedEcrList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECRStatusListDetails({
            status: 'approval1',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: selectedEcrList,
          })
        );
        setSelectedEcrList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECR');
  }

  async function handleReject() {
    if (selectedEcrList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECRStatusListDetails({
            status: 'rejected',
            userId: userId,
            clientId: clientId,
            flg: 'N',
            payload: selectedEcrList,
          })
        );
        setSelectedEcrList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECR');
  }

  // async function handleApprove() {
  //   const isConfirmed = await confirm('Are you sure you want to continue?');
  //   if (isConfirmed) {
  //     if (selectedEcrList?.length)
  //       dispatch(
  //         updateECRStatusListDetails({
  //           status: 'approved',
  //           userId: userId,
  //           clientId: clientId,
  //           flag: 'N',
  //           payload: selectedEcrList,
  //         })
  //       );
  //     else funcToSetResponseMessage('error', 'Please Select at least one ECR');
  //   }
  // }

  async function moveToDrafts() {
    if (selectedEcrList?.length) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        dispatch(
          updateECRStatusListDetails({
            status: 'draft',
            userId: userId,
            clientId: clientId,
            flag: 'Y',
            payload: selectedEcrList,
          })
        );
        setSelectedEcrList([]);
      }
    } else funcToSetResponseMessage('error', 'Please Select at least one ECR');
  }

  const functionsName = {
    handleSubmit,
    handleSaveDraft,
    moveForApprovals,
    handleReject,
    // handleApprove,
    moveToDrafts,
  };

  function handleEditClicked(row, operation) {
    let ecrId = row.ecrId;
    navigate(`${operation}/${ecrId}`, {
      state: { operation, ecrId }, // Pass state here
    });
  }

  function handleRowClicked(row, operation) {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    dispatch(setRowStyle(styleAttribute));
    let auth = false;
    if (row.curApproverId === userId) {
      auth = true;
    }
    let ecrId = row.ecrId;
    navigate(`${operation}/${ecrId}`, {
      state: { operation, ecrId, row, auth, isApproval }, // Pass state here
    });
  }

  function handleCheckbox(row) {
    const isRowSelected = selectedEcrList.some(
      (item) => item.ecrId === row.ecrId
    );
    let updatedList;
    if (isRowSelected) {
      updatedList = selectedEcrList.filter((item) => item.ecrId !== row.ecrId);
    } else {
      updatedList = [...selectedEcrList, { ...row, checked: true }];
    }
    setSelectedEcrList(updatedList);
  }

  const handleDeleteECR = async (ecrId) => {
    const isConfirmed = await confirm(
      'Are you sure you want to delete this record?'
    );
    if (isConfirmed)
      dispatch(deleteECRFormDetails({ ecrId: ecrId, userId: userId }));
  };

  const handleFormDetails = (element) => {
    let columns =
      element.formSubDetailsInternalDTOList &&
      element.formSubDetailsInternalDTOList.map((val, index) => {
        return {
          selector: val.selector,
          width:
            val.selector === 'editIcon' ||
            val.selector === 'deleteIcon' ||
            val.selector === 'completed'
              ? '4%'
              : val.displayWidth ?? '',
          name: val.displayName,
          cell:
            val.selector === 'editIcon'
              ? function displayCell(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title="Edit"
                    >
                      <span
                        className="icon-edit-button"
                        onClick={() => handleEditClicked(row, 'edit')}
                        key={index}
                      />
                    </Tooltip>
                  );
                }
              : val.selector === 'deleteIcon'
              ? function displayCell(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title="Delete"
                    >
                      <span
                        className="icon-2-trash"
                        onClick={() => {
                          if (row?.ecrId) handleDeleteECR(row.ecrId);
                        }}
                        key={index}
                      />
                    </Tooltip>
                  );
                }
              : val.selector === 'completed'
              ? function displayCell(row) {
                  const isChecked = selectedEcrList.some(
                    (item) => item.ecrId === row.ecrId
                  );
                  return row.isComplete ? (
                    <CheckBoxInput
                      checked={isChecked}
                      onChange={() => handleCheckbox(row)}
                    />
                  ) : (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title="Incomplete Draft"
                    >
                      <span
                        className="icon icon-notification"
                        style={{
                          color: '#FF6400',
                          marginLeft: '3px',
                        }}
                      />
                    </Tooltip>
                  );
                }
              : val.selector === 'checkBox'
              ? function displayCell(row) {
                  const isChecked = selectedEcrList.some(
                    (item) => item.ecrId === row.ecrId
                  );
                  return (
                    <CheckBoxInput
                      checked={isChecked}
                      onChange={() => handleCheckbox(row)}
                    />
                  );
                }
              : function displayTitle(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title={row[val.selector]}
                    >
                      <div className="display-title custom-overflow">
                        {row[val.selector]}
                      </div>
                    </Tooltip>
                  );
                },
        };
      });
    return (
      <div className="inv-div">
        <Input
          dataTablePersistHead
          formType={element}
          dataTableEventHandler={(row) => {
            handleRowClicked(row, 'view');
          }}
          dataTableColumn={columns}
          customTableStyles={{
            table: {
              style: {
                contain: 'inline-size',
              },
            },
          }}
        />
      </div>
    );
  };

  return (
    <>
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      <CommonSpinner visible={isLoading} />
      {formData ? handleFormDetails(formData) : ''}
      {parameter === 'draft' || parameter === 'supplier' ? (
        <div id="btn-bottom-white-bar" className="group fixed-button-bar">
          <Button
            onClick={() => handleSubmit()}
            className="default mr20 fl button mb20"
          >
            Submit For Review
          </Button>
        </div>
      ) : parameter === 'rejected' ? (
        <ButtonCommon
          tempButtonData={buttonCommonData}
          functionsName={functionsName}
        />
      ) : (
        ''
      )}
    </>
  );
};

export { ECRListingPage };
