import { useEffect, useState } from 'react';
import MainLogin from './Login/MainLogin';
import { useLocation } from 'react-router-dom';
export function App() {
  let history = useLocation().pathname;
  const [ColorPrimary, setColor] = useState('black');

  const [theme, setTheme] = useState(
    localStorage.getItem('hp-theme') || 'light'
  );
  useEffect(() => {
    const primaryColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--primary-color')
      .trim(); // Trim to remove extra spaces
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.setAttribute('name', 'theme-color');
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.setAttribute('content', primaryColor);

    // Set the meta theme color
    setColor(primaryColor);
  }, [theme, history]);
  return (
    <div>
      <MainLogin />
    </div>
  );
}
export default App;
