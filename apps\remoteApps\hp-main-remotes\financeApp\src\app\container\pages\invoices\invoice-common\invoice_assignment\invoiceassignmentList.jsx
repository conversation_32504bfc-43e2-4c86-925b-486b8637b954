/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
import React, { useState } from 'react';
import { Table, message, Tooltip, Popconfirm } from 'antd';
import './invoiceassignmentList.scss';
import { AlphabetDropdown, Select, Button, TextInput } from '@hp/components';
import {
  HolderOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import ReactDragListView from 'react-drag-listview';
import { useEffectOnce, useUpdateEffect } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import { Modal } from 'antd';
import { getInvoiceAssigment } from '@hp/mainstore';

const AssignInvoices = () => {
  const dispatch = useDispatch();
  const { invoicesDataForAnalyst } = useSelector((state) => state.accpay);
  const [addModalOpen, setAddModalOpen] = useState(false);
  const columns = [
    {
      title: '',
      dataIndex: '',
      key: '',
      render: () => (
        <a className="drag-handle" href="#">
          <Tooltip title="Move">
            <HolderOutlined size={20} className="icon" />
          </Tooltip>
        </a>
      ),
      width: '2%',
    },
    {
      title: 'Analyst',
      dataIndex: 'analyst',
      key: 'analyst',
      render: (analyst, record) => (
        <Select
          value={analyst}
          onChange={(value) => handleAssignmentChange(record.id, value)}
        ></Select>
      ),
      width: '30%',
    },

    {
      title: 'Starting Characters',
      dataIndex: 'supplier',

      render: (text, record, index) => (
        <AlphabetDropdown
          onSelectionHandler={onSelectionHandler}
          selection={record.supplier}
          isOpen={openStates[index]}
          onDropdownOpen={() => handleDropdownOpen(index)}
          onAlphabetsConfirm={() => onAlphabetsConfirm()}
        />
      ),
    },
    {
      title: '',
      dataIndex: '',
      key: '',
      render: () => (
        <span className="delete-handle">
          <Popconfirm
            title="Delete the task"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
            description="Are you sure to delete this task?"
            onConfirm={confirm}
            onCancel={cancel}
            okText="Yes"
            cancelText="No"
          >
            <DeleteOutlined />
          </Popconfirm>
        </span>
      ),
      width: '2%',
    },
  ];
  const [invoices, setInvoices] = useState([
    { id: 1, supplier: 'A,C,D', analyst: '', disabled: false },
    { id: 2, supplier: 'F,G,H,I,J,K,L', analyst: '' },
    { id: 3, supplier: 'N,O,P,Q,R,S,T', analyst: '' },
    { id: 4, supplier: 'B,E,N', analyst: '' },
  ]);
  const [invoiceData, setInvoiceData] = useState([]);
  const [dataColumns, setDataColumns] = useState([
    {
      title: 'User Id',
      dataIndex: 'userId',
      key: 'userId',
      width: '6%',

      render: (analyst, record) => (
        <Tooltip title={record.userId}>{record.userId}</Tooltip>
      ),
    },
    {
      title: 'Entity',
      dataIndex: 'entity',
      key: 'entity',
      width: '15%',
      render: (analyst, record) => (
        <Tooltip title={record.entity}>{record.entity}</Tooltip>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: '20%',
      render: (analyst, record) => (
        <Tooltip title={record.email}>{record.email}</Tooltip>
      ),
    },
    {
      title: 'User Name',
      dataIndex: 'userName',
      key: 'userName',

      render: (analyst, record) => (
        <Tooltip title={record.userName}>{record.userName}</Tooltip>
      ),
    },
    {
      title: 'Supplier Alpha Code',
      dataIndex: 'supplierAlphaCodeReq',
      key: 'supplierAlphaCodeReq',

      render: (analyst, record) => (
        <Tooltip title={record.supplierAlphaCodeReq}>
          {record.supplierAlphaCodeReq}
        </Tooltip>
      ),
    },
    {
      title: 'Status',
      dataIndex: '',
      key: '',
      width: '2%',
      render: (analyst, record) => (
        <Tooltip title={record.supplierAlphaCodeReq}>
          <CheckOutlined />
        </Tooltip>
      ),
    },
  ]);

  useEffectOnce(() => {
    dispatch(getInvoiceAssigment());
  }, []);

  useUpdateEffect(() => {
    if (
      invoicesDataForAnalyst &&
      invoicesDataForAnalyst?.value &&
      invoicesDataForAnalyst?.value?.length
    ) {
      setInvoiceData(invoicesDataForAnalyst?.value);
    }
  }, [invoicesDataForAnalyst]);

  const confirm = (e) => {
    message.success('Click on Yes');
  };
  const cancel = (e) => {
    message.error('Click on No');
  };
  const dragProps = {
    onDragEnd(fromIndex, toIndex) {
      const newData = [...invoices];
      const item = newData.splice(fromIndex, 1)[0];
      newData.splice(toIndex, 0, item);
      setInvoices(newData);
    },
    handleSelector: 'a',
  };

  const [openStates, setOpenStates] = useState(
    Array(invoices.length).fill(false)
  );

  const handleDropdownOpen = (index) => {
    const newOpenStates = openStates.map((state, i) =>
      i === index ? !state : false
    );
    setOpenStates(newOpenStates);
  };
  const onSelectionHandler = (alphabetsSelected) => {
    const selectedRowIndex = openStates.findIndex((item) => item === true);
    if (selectedRowIndex !== -1) {
      const updatedInvoices = [...invoices];
      alphabetsSelected.forEach((alphabet) => {
        const labelValue = updatedInvoices[selectedRowIndex].supplier;
        if (alphabet.selected && !labelValue.includes(alphabet.key)) {
          const updatedSupplier = labelValue
            .split(',')
            .map((item) => item.trim())
            .concat(alphabet.key)
            .join(', ');
          updatedInvoices[selectedRowIndex].supplier = updatedSupplier;
        } else if (!alphabet.selected) {
          const existingSupplierValues = labelValue
            .split(',')
            .map((item) => item.trim());
          const indexToRemove = existingSupplierValues.indexOf(alphabet.key);
          if (indexToRemove !== -1) {
            existingSupplierValues.splice(indexToRemove, 1);
            updatedInvoices[selectedRowIndex].supplier =
              existingSupplierValues.join(', ');
          }
        }
      });

      setInvoices(updatedInvoices);
    }
  };

  const handleAssignmentChange = (invoiceId, analystId) => {
    const updatedInvoices = invoices.map((invoice) =>
      invoice.id === invoiceId ? { ...invoice, analyst: analystId } : invoice
    );

    setInvoices(updatedInvoices);
  };

  const handleSaveAssignments = () => {
    // Implement the logic to save assignments here (e.g., make an API call).
    message.success('Assignments saved successfully!');
  };
  const onAlphabetsConfirm = () => {
    message.success('Assignments saved successfully!');
  };
  const onAddHandler = () => {
    setAddModalOpen(true);
  };

  return (
    <>
      <div className="page-title mb20">Emails & Invoice assignment</div>
      <div className="flex-row mb20">
        <Select label="Entity Code" value={''}></Select>

        <Button
          onClick={onAddHandler}
          style={{ marginTop: '4vh' }}
          className="small mb8 outline add-button-custom flex-row vam"
        >
          <i className="icon-add-button "> </i>Add
        </Button>
      </div>

      <div className="mb20">
        <Table
          className="styled-ant-table"
          columns={dataColumns}
          pagination={invoiceData.length > 10 ? true : false}
          dataSource={invoiceData}
        />
      </div>

      <div className="mb20 page-title">Details</div>

      <div className="mb20">
        <ReactDragListView {...dragProps}>
          <Table
            className="styled-ant-table"
            columns={columns}
            pagination={true}
            dataSource={invoices}
          />
        </ReactDragListView>
      </div>
      <Modal
        title="Details"
        centered
        open={addModalOpen}
        onOk={() => setAddModalOpen(false)}
        onCancel={() => setAddModalOpen(false)}
        footer={[
          <Button className="mr8 small default ">Submit</Button>,
          <Button className="mr8 small white">Cancel</Button>,
        ]}
      >
        <div className="two-col-layout">
          <TextInput label="Entity" required className="mb20" name="menuName" />
          <TextInput label="Email" required className="mb20" name="menuName" />
          <TextInput
            label="User Name"
            required
            className="mb20"
            name="menuName"
          />
          <TextInput
            label="Supplier Alpha Code"
            required
            className="mb20"
            name="menuName"
          />
        </div>
      </Modal>
    </>
  );
};

export default AssignInvoices;
