/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */

import React, { useEffect, useState } from 'react';
import { Input, CommonSpinner, dataTableServiceProvider } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';

import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { ExpenseListingComponent } from '../ListingComponent';
import { useLocation } from 'react-router-dom';
import { getExpenseListingAction } from '@hp/mainstore';
function ExpenseReportDetails(props) {
  let location = useLocation();
  let locations = location.pathname.split('/');
  let selectedURL = locations[4];
  const { expenseListingResponse, isExpReportFormloading } = useSelector(
    (store) => store.expense
  );
  const dispatch = useDispatch();
  const [selectedTab, setSelectedTab] = useState(0);
  const [conditionalRowStyles, setConditionalStyles] = React.useState([]);
  const [formElements, setFormElements] = useState([]);
  // useEffect(() => {
  //     if (selectedURL != "") {
  //         dispatch(expenseActions.getTravelRequestForm(selectedURL, false));
  //     }
  //     return () => {
  //         dispatch(expenseActions.getTravelRequestForm(selectedURL, true));
  //     }
  // }, [location.pathname])

  useEffect(() => {
    dispatch(
      getExpenseListingAction({ listName: 'ERList', listStatus: selectedURL })
    );
    return () => {
      setFormElements([]);
    };
  }, [selectedURL]);

  const tabChange = (index) => {
    setSelectedTab(index);
  };
  const onRowClick = (e) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      e.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  };

  useEffect(() => {
    if (expenseListingResponse) {
      setFormElements(expenseListingResponse);
    }
    return () => {
      setFormElements([]);
    };
  }, [expenseListingResponse, location.pathname]);

  const formControlsBinding = (data) => {
    return data && data.length
      ? data.map((element, index) => {
          if (
            element.type === 'PageTitle' &&
            element.formSubDetailsInternalDTOList &&
            element.formSubDetailsInternalDTOList.length
          ) {
            return (
              <div key={index}>
                <div key={index + 1} className="page-title">
                  {element.displayName}
                </div>
                <div className="card mb20">
                  <div key={index + 2} className="three-col-layout">
                    {' '}
                    {formControlsBinding(element.formSubDetailsInternalDTOList)}
                  </div>
                </div>
              </div>
            );
          }
          return (
            <Input
              key={index}
              formType={element}
              dataTableEventHandler={(e) => onRowClick(e)}
              conditionalRowStyles={conditionalRowStyles}
              dataTablePagination={false}
              // onChangeHandler={(onChangeFunction, e, uniqueKey) => onChangeHandlingFunctions[onChangeFunction](e, uniqueKey, index, element)}
            />
          );
        })
      : null;
  };
  return (
    <div>
    
        <CommonSpinner visible={isExpReportFormloading} />
        {formElements &&
        formElements.length &&
        selectedURL.includes('NewReport') ? (
          <section>
            {formControlsBinding(formElements)}
            <div className="mb20"></div>
            <Tabs
              onSelect={(tabIndex) => tabChange(tabIndex)}
              className="mb24"
              selectedIndex={selectedTab}
            >
              <TabList>
                {formElements && formElements.length
                  ? formElements.map((tabListElement, index) => {
                      if (
                        tabListElement.type.includes('TabPanel') &&
                        !tabListElement.uniqueKey.includes('ER-TR-ComparePage')
                      ) {
                        return (
                          <Tab onClick={() => tabChange(index)} key={index}>
                            {tabListElement.displayName}
                          </Tab>
                        );
                      }
                    })
                  : null}
              </TabList>
              {formElements && formElements.length
                ? formElements.map((tabPanelElement, index) => {
                    if (
                      tabPanelElement.type.includes('TabPanel') &&
                      !tabPanelElement.uniqueKey.includes('ER-TR-ComparePage')
                    ) {
                      return (
                        <TabPanel key={index}>
                          <div className="card mb40">
                            <div className={'two-col-layout'}>
                              {formControlsBinding(
                                tabPanelElement.formSubDetailsInternalDTOList
                              )}
                            </div>
                          </div>
                        </TabPanel>
                      );
                    }
                  })
                : null}
            </Tabs>
          </section>
        ) : (
          <section>
            {/* <ExpenseContext.Provider value={{ formControlsData: [formElements, setFormElements], propDatas: props }}> */}
            <ExpenseListingComponent formElements={formElements} />
            {/* </ExpenseContext.Provider> */}
          </section>
        )}
    
    </div>
  );
}

export { ExpenseReportDetails };
