@import '@hp/styles/variables.scss';
@import '@hp/styles/reset.scss';
.login-page-wrap {
  height: 100vh;
  background: #fff;
  display: flex;
  .login-image-wrap {
    width: calc(100% - 480px);
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .login-form-wrap {
    width: 480px;
    height: 100%;
    padding: 48px 72px 16px;
    justify-content: space-between;
  }
}
.ant-form-item-required {
  font-size: 12px !important;
}
.ant-form-item-explain-error {
  margin-bottom: 15px;
}
.ant-input {
  font-size: 14px !important;
}
.ant-input::placeholder {
  font-size: 14px !important;
  padding-left: 13px;
}
.input-text-for-login {
  height: 40px;
  border-color: $primary_color;
  margin: 0;
}

.client-logo-wrap {
  width: 100%;
  display: block;
  text-align: center;
  img {
    display: inline-block;
    vertical-align: middle;
  }
}

.form-wrap {
  width: 100%;
  h1 {
    font-size: 24px;
    text-align: left;
    line-height: 17px;
    margin-bottom: 40px;
    color: #3b73b9;
    letter-spacing: -0.02em;
  }
}

.largeSignIn {
  color: hsl(0, 0%, 100%);
  border: 0;
  width: 100%;
  height: 40px;
  display: block;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}
.largeSignInWhite {
  color: #000000;
  border: 1px solid black;
  width: 100%;
  height: 40px;
  display: block;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: 500ms;
  border-radius: 4px;
  text-align: center;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  background: $white_color;
  &:hover {
    transition: 300ms;
    background: rgba(203, 203, 203, 0.2);
    color: #000000;
  }
  &.disabled {
    background: $grey_color;
    // color: #9b9b9b;
    pointer-events: none;
  }
}
.forgotPassword {
  display: block;
  font-size: 12px;
  margin-top: 16px;
  transition: 300ms;
  text-align: right;
  color: $text_color;
  text-decoration: underline;
  &:hover {
    transition: 300ms;
    color: $secondary_color;
  }
}

.login-footer {
  width: 100%;
  text-align: center;

  a {
    display: block;
    transition: 300ms;
    margin: 0 auto 8px;
    &:hover {
      opacity: 0.8;
      transition: 300ms;
    }
  }
  img {
    display: block;
    margin: 0 auto 8px;
  }
  p {
    font-size: 12px;
    letter-spacing: -0.02em;
  }
}

.footer-disclaimer {
  display: block;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 50px;
  font-size: 15px;
  transition: 300ms;
  margin-top: 20px;
  /* background: #c74646; */
  padding: 13px;
  border-radius: 2px;
  color: red;
  text-align: center;
  min-width: auto;
}
.align-spinner {
  text-align: -webkit-center;
}
.disclaimer-text {
  display: block;
  font-size: 11px;
  transition: 300ms;
  text-align: justify;
  min-width: auto;
  color: $text_color;
}

.login-disclaimer-modal {
  top: 30%;
  left: 50%;
  width: 100%;
  height: 30%;
  bottom: 50%;
  outline: none;
  padding: 40px;
  overflow: auto;
  max-width: 1300px;
  background: #fff;
  border-radius: 4px;
  position: absolute;
  border: 3px solid red;
  transform: translate(-50%, 0);
  box-shadow: 0px 4px 6px -4px rgb(24 39 75 / 12%),
    0px 8px 8px -4px rgb(24 39 75 / 8%);
}
.login-modal-overlay {
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 9999;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.75);
}
.login-modal-content {
  top: 30%;
  left: 50%;
  width: 100%;
  height: 30%;
  bottom: 50%;
  outline: none;
  padding: 40px;

  max-width: 1300px;

  border-radius: 4px;
  position: absolute;

  transform: translate(-50%, 0);
  text-align: center;
  color: red;
  font-size: 26px;
}
.separator-line {
  display: flex;
  align-items: center;
}

.line {
  flex: 1;
  height: 1px;
  background-color: #333; /* Adjust color as needed */
  margin: 0 5px; /* Adjust margin as needed */
}

.separator-text {
  background-color: #fff; /* Set the background color based on your design */
  padding: 0 5px; /* Adjust padding as needed */
  color: #333; /* Set the text color based on your design */
}
