@import '@hp/styles/variables.scss';

.invoice-container {
  padding: 20px;
  // background: #c4cad2;
  height: 555px;
  .header {
    font-weight: 800;
    font-size: 20px;
    margin-bottom: 20px;
  }

  .invoice-list {
    list-style-type: none;
    padding-left: 20px;
  }

  .invoice-item {
    margin-bottom: 15px;
    font-size: 20px;
    width: 400px;
  }

  .subheader-list {
    list-style-type: none;
    padding-left: 40px;
  }

  .subheader {
    font-weight: 700;
    font-size: 18px;
    padding-left: 20px;
    margin-bottom: 20px;
    margin-top: 6px;
  }

  .subheader-item {
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 600;
  }

  .item-name {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 8px;
    margin-bottom: 15px;
    font-size: 17px;
    padding-left: 20px;
  }

  .subitem-name {
    margin-right: 5px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.55);
    margin-bottom: 15px;
    font-size: 17px;
    padding-left: 20px;
  }
  ul {
    list-style-type: disc; /* Default bullets */
    margin: 20px;
    padding: 0;
  }
  li {
    margin-bottom: 10px;
  }
}
