/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> AUGUSTINE
 * @email <EMAIL>
 * @create date 2023-06-12 12:10:54
 * @modify date 2024-07-29 16:34:22
 */
import { accPayConstants } from '@hp/mainstore';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAppRouterDom } from '@hp/utils';
import './PaymentReqList.scss';
import { getPaymentreqList, deletePaymentReq } from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import {
  resetPage,
  setRowStyle,
  setPageName,
  dynamicTableListingTotalCount,
} from '@hp/mainstore';
import {
  CommonSpinner,
  dataTableServiceProvider,
  globalutils,
  SearchComponent,
  useConfirm,
  Input,
} from '@hp/components';

function PaymentReqList(props) {
  const { domParameters, navigate } = useAppRouterDom();
  const parameter = domParameters?.parameter || '';
  const userId = globalutils.getDataFromStorage('userId');
  const state = props?.location?.state ? props.location.state : null;
  let dispatch = useDispatch();
  let { confirm } = useConfirm();

  const { innerMenuDetails, subMenuName, innerMenuName } = useSelector(
    (store) => store.menu
  );
  const { dynamicListLimitAndOffset } = useSelector((store) => store.util);
  const { paymentReqList, pymntReqAction, pymtReqError } = useSelector(
    (state) => state.accpay
  );

  const searchDataInitial = {
    limit: 10,
    offset: 0,
    searchItem: '',
  };
  const [searchData, setSearchData] = useState(searchDataInitial);
  const [paymentReqListData, setPaymentReqListData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchString, setSearchString] = useState('');

  useEffect(() => {
    if (dynamicListLimitAndOffset) {
      let tempSearchData = {
        ...searchData,
        limit: dynamicListLimitAndOffset.limit,
        offset: dynamicListLimitAndOffset.offset - 1,
      };
      searchHandler(tempSearchData);
      setSearchData(tempSearchData);
    }
  }, [dynamicListLimitAndOffset]);

  useEffect(() => {
    if (!parameter) return;
    setIsLoading(true);
    let tempSearchData = {
      ...searchData,
      offset: 0,
      searchItem: '',
    };
    searchHandler(tempSearchData);
    setSearchData(tempSearchData);
    if (state?.action === 'refresh') {
      dispatch(resetPage(1));
      dispatch(setRowStyle([]));
    }
  }, [parameter]);

  useEffect(() => {
    if (pymntReqAction?.value) {
      funcToSetResponseMessage('success', pymntReqAction.value);
      let tempSearchData = {
        ...searchData,
        offset: 0,
        searchItem: '',
      };
      searchHandler(tempSearchData);
      setSearchData(tempSearchData);
      setIsLoading(false);
    }
    //constants
    return () => {
      dispatch({
        type: accPayConstants.PAYMENT_REQ_ACTION_RES_CLEAR,
        payload: null,
      });
      dispatch({
        type: accPayConstants.GET_PAYMENT_REQ_LIST_SUCCESS,
        payload: null,
      });
    };
  }, [pymntReqAction]);

  useEffect(() => {
    if (!pymtReqError) return;
    setIsLoading(false);
    return () => {
      dispatch({
        type: accPayConstants.GET_PAYMENT_REQ_FORM_FAILURE,
        payload: null,
      });
    };
  }, [pymtReqError]);

  useEffect(() => {
    if (paymentReqList?.value) {
      setIsLoading(false);
      setPaymentReqListData(paymentReqList.value?.formDetailsDtoList);
      dispatch(
        dynamicTableListingTotalCount(paymentReqList.value?.count || null)
      );
    }
  }, [paymentReqList]);

  function searchHandler(payload) {
    dispatch(getPaymentreqList({ status: parameter, payload: payload }));
  }

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;

    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose: true,
        autoHide: true,
      })
    );
  };

  const tableColumn = (list) => {
    return list.formSubDetailsInternalDTOList &&
      Array.isArray(list.formSubDetailsInternalDTOList) &&
      list.formSubDetailsInternalDTOList.length
      ? list.formSubDetailsInternalDTOList.map((value) => {
          return {
            width: value.displayWidth ? value.displayWidth : '',
            name: value.displayName ? value.displayName : '',
            selector: value.selector ? value.selector : '',
            cell:
              value.selector === 'deleteIcon' && parameter !== 'in-progress'
                ? function displayCell(row) {
                    return (
                      <div
                        className="icon-2-trash"
                        onClick={() => {
                          deleteRowHandler(row);
                        }}
                      ></div>
                    );
                  }
                : '',
          };
        })
      : [];
  };

  const deleteRowHandler = async (row) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (!isConfirmed) return;
    setIsLoading(true);
    dispatch(deletePaymentReq({ userId: userId, pymtReqId: row.pymtReqId }));
  };

  const rowClickHandler = (data) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      data.serialNo,
      'serialNo'
    );
    dispatch(setRowStyle(styleAttribute));
    dispatch(setPageName(innerMenuName));

    navigate(`${data?.pymtReqId}`);
  };

  return (
    <>
      <CommonSpinner visible={isLoading} />
      <h1 className="page-title">
        {subMenuName ? subMenuName : ''}
        {' / '}
        {innerMenuDetails?.menuName}
      </h1>
      {paymentReqListData && paymentReqListData.length
        ? paymentReqListData.map((list) => {
            if (list.type === 'DataTableDynamicPagination') {
              return (
                <>
                  <div className="manual-search-box">
                    <SearchComponent
                      isManualSearch
                      style={{ borderRight: 'none' }}
                      tableListData={list.value}
                      filteredData={() => null}
                      searchText={(text) => {
                        if (text === '') {
                          let tempSearchData = {
                            ...searchData,
                            offset: 0,
                            searchItem: '',
                          };
                          searchHandler(tempSearchData);
                          setSearchData(tempSearchData);
                        }
                        setSearchString(text);
                      }}
                      isEnterPressed={(flag) => {
                        if (flag) {
                          let tempSearchData = {
                            ...searchData,
                            offset: 0,
                            searchItem: searchString,
                          };
                          searchHandler(tempSearchData);
                          setSearchData(tempSearchData);
                        }
                      }}
                    />
                    <span
                      className="search-button"
                      onClick={() => {
                        let tempSearchData = {
                          ...searchData,
                          offset: 0,
                          searchItem: searchString,
                        };
                        searchHandler(tempSearchData);
                        setSearchData(tempSearchData);
                      }}
                    >
                      <i className="icon icon-search"></i>
                    </span>
                  </div>

                  <Input
                    formType={list}
                    dataTableColumn={tableColumn(list)}
                    dataTablePersistHead={true}
                    dataTableEventHandler={rowClickHandler}
                  />
                </>
              );
            }
          })
        : ''}
    </>
  );
}

export { PaymentReqList };
