/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Button, TextInput, useConfirm } from '@hp/components';
import { changeInvItems } from '@hp/mainstore';
import { globalutils } from '@hp/components';
const ItemsExpandable = (props) => {
  const itemsData = props.data;
  const invId = itemsData.inv_id;
  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  let user = globalutils.getDataFromStorage('all');
  const userId = user.userId;
  const [invState, setInvState] = useState({
    inv_id: '',
    inv_item_id: '',
    description: '',
    unit: '',
    unit_type: '',
    tax: '',
    amount: '',
    quantity: '',
    unit_price: '',
    tax_amount: '',
    po_item_id: '',
    serial_no: '',
  });

  useEffect(() => {
    setInvState({
      inv_id: invId,
      inv_item_id: itemsData.inv_item_id,
      description: itemsData.description,
      unit: itemsData.unit,
      tax: itemsData.tax,
      amount: itemsData.amount,
      quantity: itemsData.quantity,
      unit_price: itemsData.unit_price,
      tax_amount: itemsData.tax_amount,
      serial_no: itemsData.serial_no,
      po_item_id: itemsData.po_item_id,
      unit_type: itemsData.unit_type,
    });
  }, []);

  const handleAll = (event) => {
    setInvState({ ...invState, [event.target.name]: event.target.value });
    //}
  };

  const OnClickSave = () => {
    // setisLoading(true);
    let body = [invState];
    let status = 'save';
    dispatch(
      changeInvItems({ userId: userId, invItemId: status, invId: body })
    );
  };

  const OnClickDelete = async () => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      let body = [invState];
      let status = 'delete';
      dispatch(
        changeInvItems({ userId: userId, invItemId: status, invId: body })
      );
    }

    // accpayservice.changeInvItems(userid, status, body).then(() => {
    //   passrefreshData(true);
    // });
  };

  return (
    <div>
      <div className="">
        <div className="flex-row padding-left-50 padding-top-20">
          <TextInput
            label="Description"
            name="description"
            className="mb20 mr8"
            value={invState.description || ''}
            onChange={(e) => handleAll(e)}
          />
          <TextInput
            label="Quantity"
            name="quantity"
            className="mb20 mr8"
            value={invState.quantity || ''}
            onChange={(e) => handleAll(e)}
          />
          <TextInput
            label="Unit"
            name="unit"
            className="mb20 mr8"
            value={invState.unit || ''}
            onChange={(e) => handleAll(e)}
          />
          <TextInput
            label="Unit Price"
            name="unit_price"
            className="mb20 mr8"
            value={invState.unit_price || ''}
            onChange={(e) => handleAll(e)}
          />
          <TextInput
            label="Amount"
            name="amount"
            className="mb20 mr8"
            value={invState.amount || ''}
            onChange={(e) => handleAll(e)}
          />
          <TextInput
            label="Tax"
            name="tax"
            className="mb20 mr8"
            value={invState.tax || ''}
            onChange={(e) => handleAll(e)}
          />
          <TextInput
            label="Tax Amount"
            name="tax_amount"
            className="mb20 mr8"
            value={invState.tax_amount || ''}
            onChange={(e) => handleAll(e)}
          />
        </div>
        <div className="flex-row padding-left-50  left-aligned mb16">
          <Button className="small secondary-outline" onClick={OnClickSave}>
            Save
          </Button>
          <Button className="small secondary-error" onClick={OnClickDelete}>
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
};

export { ItemsExpandable };
