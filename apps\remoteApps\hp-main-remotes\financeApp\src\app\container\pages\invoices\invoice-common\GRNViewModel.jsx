/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-key */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Ellipsis } from 'react-awesome-spinners';
import { accPayConstants } from '@hp/mainstore';
import { globalutils, Input } from '@hp/components';

const GRNViewModel = (props) => {
  const dispatch = useDispatch();
  const { viewGrnitems } = useSelector((store) => store.accpay);

  const [indexValue, setIndex] = useState(null);
  const [oldValue, setOldValue] = useState();
  const [loading, setloading] = useState(true);
  const [grnDetails, setGnDetails] = useState(null);

  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch({
        type: accPayConstants.GET_GRN_VIEW_SUCCESS,
        removeNotification,
      });
      setGnDetails(null);
    };
  }, []);
  useEffect(() => {
    if (viewGrnitems !== undefined) {
      setGnDetails(viewGrnitems);
    }
  }, [viewGrnitems]);

  useEffect(() => {
    if (grnDetails?.value) {
      setloading(false);
      setOldValue(grnDetails.value);
    }
  }, [grnDetails]);

  const handleDropdownOpen = (index) => {
    if (index === indexValue) {
      setIndex(null);
      return;
    }
    setIndex(index);
  };

  const viewGRN = (data) => {
    if (data.value) {
      return (
        <>
          {data.value.map((entry, index1) => {
            return (
              <div
                className="po-card alt-card mb20"
                onClick={() => {
                  handleDropdownOpen(index1);
                }}
                style={{
                  cursor: 'pointer',
                }}
              >
                <div className="user-wrap ">
                  <div
                    className="alt-card-items "
                    style={{
                      gridTemplateColumns: 'repeat(3,1fr)',
                    }}
                    title="Click to view details"
                  >
                    {entry.map((element, index) => {
                      if (element.uniqueKey !== 'itemList')
                        return (
                          <div
                            key={'alt-' + index}
                            className="alt-single-item"
                            style={{ flexShrink: 1 + 'em' }}
                          >
                            <div className="alt-label">
                              <div>{element.displayName}</div>
                            </div>
                            <div className="alt-text">
                              <div>{element.value}</div>
                            </div>
                          </div>
                        );
                    })}
                  </div>
                </div>

                {indexValue === index1 ? (
                  <div onClick={(e) => e.stopPropagation()}>
                    <h2 className="page-sub-title mb20 mt20">Item List</h2>

                    {grnDetails?.value?.length
                      ? grnDetails.value.map((entry, index) => {
                          return index === indexValue
                            ? formControlsBinding(entry, index)
                            : '';
                        })
                      : ''}
                  </div>
                ) : (
                  ''
                )}
              </div>
            );
          })}
        </>
      );
    }
  };

  const formControlsBinding = (element, index) => {
    if (element && index === indexValue) {
      return element.map((entry) => {
        if (entry.uniqueKey === 'itemList') {
          return (
            <Input key={index} formType={entry} dataTableSearchDisable={true} />
          );
        }
      });
    }
  };

  return (
    <div className="boxed ">
      <div
        style={{ float: 'right' }}
        onClick={props.isModalClose}
        className=" icon-close  "
      ></div>

      <h1 className="page-title ">GRN </h1>
      <div>
        {loading ? (
          <div style={{ textAlign: 'center' }}>
            <Ellipsis
              color={globalutils.spinnerColor()}
              size={100}
              sizeUnit={'px'}
            />
          </div>
        ) : oldValue.length === 0 ? (
          <h3 style={{ textAlign: 'center' }}>
            There are no GRN details to display!
          </h3>
        ) : (
          <>{viewGRN(grnDetails)}</>
        )}
      </div>
    </div>
  );
};
export { GRNViewModel };
