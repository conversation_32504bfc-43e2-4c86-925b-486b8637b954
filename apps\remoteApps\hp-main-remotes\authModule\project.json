{"name": "mainAuthApp", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/remoteApps/hp-main-remotes/authModule/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "dist/apps/remoteApps/hp-main-remotes/mainAuthApp", "main": "apps/remoteApps/hp-main-remotes/authModule/src/main.jsx", "tsConfig": "apps/remoteApps/hp-main-remotes/authModule/tsconfig.app.json", "rspackConfig": "apps/remoteApps/hp-main-remotes/authModule/rspack.config.js", "assets": ["apps/remoteApps/hp-main-remotes/authModule/src/favicon.ico", "apps/remoteApps/hp-main-remotes/authModule/src/assets"]}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "optimization": true, "sourceMap": false, "rspackConfig": "apps/remoteApps/hp-main-remotes/authModule/rspack.config.prod.js"}}}, "serve": {"executor": "@nx/rspack:module-federation-dev-server", "options": {"buildTarget": "mainAuthApp:build:development", "port": 4211}, "configurations": {"development": {"port": 4211}, "production": {"buildTarget": "mainAuthApp:build:production", "port": 4211}}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/rspack:module-federation-static-server", "defaultConfiguration": "production", "options": {"serveTarget": "mainAuthApp:serve"}, "configurations": {"development": {"serveTarget": "mainAuthApp:serve:development"}, "production": {"serveTarget": "mainAuthApp:serve:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/remoteApps/hp-main-remotes/authModule/jest.config.js"}}}}