/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:41:10
 * @modify date 22-02-2022 18:23:51
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { getUserListing } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { TextInput, ButtonCommon } from '@hp/components';
import { AP_USER } from '@hp/constants';
import { globalutils } from '@hp/components';
const ViewUsers = (props) => {
  const dispatch = useDispatch();
  const listUsers = useSelector((state) => state.admin.viewuserlist || []);
  let user = globalutils.getDataFromStorage('all');
  const client_Id = user?.clientId;
  const [TableListing, setTableList] = useState();
  const [searchBox, setSearchBox] = useState();
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });

  useEffect(() => {
    getParameter();
  }, [props]);

  useEffect(() => {
    if (listUsers && listUsers !== undefined) {
      setTableList(listUsers);
      setSearchBox(listUsers);
    }
  }, [listUsers]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const getParameter = () => {
    accessService();

    function accessService() {
      let clientId = client_Id;
      dispatch(getUserListing(clientId));
    }
  };
  // name: "First Name "+<br/>+" hello",
  const functionsName = () => {};
  const columns = [
    {
      name: 'First Name',
      selector: 'firstName',
      sortable: true,
      width: '200px',
    },
    {
      name: 'Middle Name',
      selector: 'middleName',
      sortable: true,
      width: '150px',
    },
    {
      name: 'Last Name',
      selector: 'lastName',
      sortable: true,
      width: '200px',
    },
    {
      name: 'User Name',
      selector: 'username',
      sortable: true,
      width: '200px',
    },
    {
      name: 'Email',
      selector: 'email',
      sortable: true,
    },
  ];

  return (
    <>
      <div className="page-title">View Users</div>
      <div className="two-col-layout mb20">
        <div className="three-col-layout">
          <TextInput
            placeholder="Search"
            style={{ width: 105 + '%' }}
            value={filterString.filterStr}
            onChange={(e) => handleChange(e)}
          />
        </div>
      </div>
      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          theme={'solarized'}
        />
      </div>
      <ButtonCommon functionsName={functionsName} />
    </>
  );
};

export { ViewUsers };
