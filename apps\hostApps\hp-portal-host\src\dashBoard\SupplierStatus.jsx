/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useEffect, useState } from 'react';
import { Intro, CommonSpinner, ProgressTitle } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import './SupplierStatus.scss';
import { SP_USER } from '@hp/constants';
import { getDashBoardCountBySupplier } from '@hp/portalstore';
import {
  getReasonStatement,
  showDialog,
  supplierDashBoardAction,
} from '@hp/portalstore';
import { globalutils } from '@hp/components';

function SupplierOnBoardingStatus(props) {
  const { isDashboardLoading, dashBoardSupplierDetails } = useSelector(
    (store) => store.admin
  );

  const { rejectStatusResponse } = useSelector(
    (store) => store.supplierRegister
  );

  const [listStatus, setListStatus] = useState([]);
  const [profileStatus, setProfileStatus] = useState({
    totalTabs: 0,
    completedTabs: 0,
  });
  const dispatch = useDispatch();

  // let user = userService.getStorageItem(SP_USER);
  let user = globalutils.getDataFromStorage('all');
  let clientName = user.clientName;
  const funcToSetResponseMessage = (showPopup, type, resMessage) => {
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,

        autoHide,
      })
    );
  };

  const status = user.status;
  const supplierStatus = user.supplierStatus;

  useEffect(() => {
    dispatch(getDashBoardCountBySupplier(user && user ? user.supplierId : ''));
    dispatch(supplierDashBoardAction('load'));
    return () => {
      dispatch(supplierDashBoardAction('unLoad'));
    };
  }, []);

  useEffect(() => {
    if (dashBoardSupplierDetails) {
      let completedCount = dashBoardSupplierDetails.filter(
        (item) => item.isCompleteIcon === 'icon-new-checkbox'
      );

      setProfileStatus({
        totalTabs: dashBoardSupplierDetails.length + 1,
        completedTabs: completedCount.length + 1,
      });

      setListStatus(dashBoardSupplierDetails);
    }
  }, [dashBoardSupplierDetails]);

  useEffect(() => {
    if (status !== undefined && status) {
      if (status === 'rejected' || status === 'client_rejected') {
        dispatch(getReasonStatement(user.supplierRegId));
      }
    }
  }, [status]);

  const ListStatus = ({ data }) => {
    return (
      <li>
        <a className="profile-status-number">
          <i className={data.isCompleteIcon}></i>
          <div>
            <span className="profile-status-url">{data.displayName || ''}</span>
            <p>{data.detailsDesc || ''}</p>
          </div>
        </a>
      </li>
    );
  };

  useEffect(() => {
    if (rejectStatusResponse && rejectStatusResponse) {
      let message = null;
      if (status === 'rejected') {
        message = 'Rejected from approver due to ' + rejectStatusResponse;
      } else if (status === 'client_rejected') {
        message =
          'Rejected from ' + clientName + ' due to ' + rejectStatusResponse;
      }
      funcToSetResponseMessage(true, 'error', message);
    }
    // return () => {
    //   dispatch({
    //     type: supplierRegisterConstants.Get_STATUS_SUCCESS,
    //     payload: null,
    //   });
    // };
  }, [rejectStatusResponse]);

  return (
    <>
      <CommonSpinner visible={isDashboardLoading} />
      <div className="onboarding-container">
        {/* {status && status==="client_rejected"?
          (
            <Intro
            className="mb32"
              title="Client has rejected the onboarding process"
              // description="Sorry, It Seems Server has no data to display"
              />
              ):status && status==="forapprove1"?
          (
            <Intro
            className="mb32"
              title="Supplier Profile Completed and Waiting for Approval from Supplier side"
              // description="Sorry, It Seems Server has no data to display"
              />
          ):status && status==="approved"?
          (
            <Intro
            className="mb32"
            title="Supplier Profile Completed and Waiting for Approval from Client side"
            // description="Sorry, It Seems Server has no data to display"
            />
            ):
            ""
            } */}

        {status === 'wait_reg' || status === 'pending_reg' ? (
          <Intro
            className="mb40"
            title="Welcome!"
            description="Welcome to supplier portal. Please complete the items in the checklist below to proceed."
            // status="Pending Client Approval"
          />
        ) : status === 'pending_approval' ? (
          <Intro
            className="mb32"
            title="Waiting for Approval from Customer"
            // description="Sorry, It Seems Server has no data to display"
          />
        ) : supplierStatus === 'forapprove1' ? (
          <Intro
            className="mb32"
            title="Waiting for Approval from Supplier Administrator"
            // description="Sorry, It Seems Server has no data to display"
          />
        ) : status === 'rejected' ? (
          <Intro
            className="mb32"
            title="Supplier Administrator has rejected the onboarding process"
            // description="Sorry, It Seems Server has no data to display"
          />
        ) : status === 'client_rejected' ? (
          <Intro
            className="mb32"
            title={`${clientName} has rejected the onboarding process`}
            // description="Sorry, It Seems Server has no data to display"
          />
        ) : (
          <Intro
            className="mb32"
            title="Supplier profile completed"
            // description="Sorry, It Seems Server has no data to display"
          />
        )}
        {listStatus && listStatus.length ? (
          <div className="onboarding-profile">
            <ProgressTitle
              className="mb20"
              text="Profile Completion"
              completedTabs={profileStatus.completedTabs}
              totalTabs={profileStatus.totalTabs}
            />
            <ul className="onboarding-checklist">
              <li>
                <a
                  href={'administration/user-accounts'}
                  className="profile-status-number"
                >
                  <i className={'icon-new-checkbox'}></i>
                  <div>
                    <span className="profile-status-url">
                      {'Create User' || ''}
                    </span>
                    <p>
                      {'Create additional users who can access the supplier portal.' ||
                        ''}
                    </p>
                  </div>
                </a>
              </li>
              {listStatus && listStatus.length
                ? listStatus.map((supplierStatus, i) => {
                    return (
                      <ListStatus key={'userlist-' + i} data={supplierStatus} />
                    );
                  })
                : ''}
            </ul>
          </div>
        ) : (
          ''
        )}
      </div>
    </>
  );
}

export { SupplierOnBoardingStatus };
