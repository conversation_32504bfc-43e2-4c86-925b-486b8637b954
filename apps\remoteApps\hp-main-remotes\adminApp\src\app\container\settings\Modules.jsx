/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> <PERSON>tian
 * @email <EMAIL>
 * @create date 03-02-2022 15:40:13
 * @modify date 21-02-2022 12:40:20
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import {
  PageWrap,
  useConfirm,
  Button,
  Input,
  dataTableServiceProvider,
} from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import {
  addModulesFormDetails,
  settingEditModulesFormDetails,
  deleteModulesFormDetails,
  editModulesFormDetails,
  getAdminModulesFormDetails,
  modulesFormDetailsResponseClear,
  showDialog,
} from '@hp/mainstore';

const Modules = (props) => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { modulesFormDetails, modulesFormDetailsResponse } = useSelector(
    (store) => store.settings
  );

  const [appId, setAppId] = useState(5);
  const [tableData, setTableData] = useState([]);

  // Fetch Modules details form
  const getModuleFormDetails = (appId) => {
    dispatch(getAdminModulesFormDetails(appId));
  };

  useEffect(() => {
    if (modulesFormDetailsResponse && modulesFormDetailsResponse.value) {
      funcToSetResponseMessage(
        modulesFormDetailsResponse.value.type,
        modulesFormDetailsResponse.value.text
      );
    }
    getModuleFormDetails(appId);
  }, [modulesFormDetailsResponse]);

  // Clear Notification
  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch(modulesFormDetailsResponseClear(removeNotification));
      // dispatch({
      //   type: settingsConstants.EDIT_MODULES_FORM_SUCCESS,
      //   removeNotification,
      // });
      // dispatch({
      //   type: settingsConstants.DELETE_MODULES_FORM_SUCCESS,
      //   removeNotification,
      // });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (modulesFormDetails && modulesFormDetails.value) {
      setFormDetails(modulesFormDetails.value);
      setTableData(
        modulesFormDetails.value.find(
          (val) => val.uniqueKey === 'moduleDatatable'
        ).value
      );
    }
  }, [modulesFormDetails]);

  const [formDetails, setFormDetails] = useState([]);
  const [selectedModule, setSelectedModule] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [conditionalSubRowStyles, setConditionalSubRowStyles] = useState([]);
  const [notify, setnotify] = useState(null);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let moduleNotExist = true;
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'moduleName') {
            if (element.value === null || element.value.trim() === '') {
              moduleNotExist = false;
              funcToSetResMessageInModal('error', 'Please Enter Module Name');
            } else {
              tempArray.map((item) => {
                if (item.uniqueKey === 'moduleDatatable') {
                  item.value.map((val) => {
                    if (val.moduleName === element.value) {
                      if (selectedModule.moduleName === element.value) {
                        return;
                      } else {
                        moduleNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Module already exists'
                        );
                      }
                    }
                  });
                }
              });
            }
          }
        });
    });
    if (moduleNotExist) {
      edit
        ? dispatch(
            settingEditModulesFormDetails({
              formDetails: formDetails,
              moduleId: selectedModule.moduleId,
            })
          )
        : dispatch(addModulesFormDetails(formDetails));
      setShowModal(false);
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (
            element.uniqueKey === 'moduleName' ||
            element.uniqueKey === 'description'
          ) {
            element.disableFlag = 'F';
            element.value = '';
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setEdit(true);
    setSelectedModule(obj);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.disableFlag = 'F';
          element.uniqueKey === 'moduleName'
            ? (element.value = obj.moduleName)
            : '';
          element.uniqueKey === 'description'
            ? (element.value = obj.description)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.moduleId,
      'moduleId'
    );
    setConditionalSubRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.moduleId
      ? handleConfirm(obj.moduleId)
      : funcToSetResponseMessage('error', 'Please Select a Module to Delete');
  };

  //Function to confirm delete
  const handleConfirm = async (moduleId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteModulesFormDetails(moduleId));
      setDisable(false);
      setShowModal(false);
      setSelectedModule({});
    }
  };

  // Select a value in the select combo-box
  const handleSelect = (event, uniqueKey) => {
    getModuleFormDetails(event.target.value);
    setAppId(event.target.value);
    setDisable(false);
    setConditionalSubRowStyles([]);
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event.target.value;
          }
        });
    });

    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleSelect,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelectedModule(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === 'moduleName') {
            entry.disableFlag = 'T';
            entry.value = obj.moduleName;
          }
          entry.uniqueKey === 'description'
            ? (entry.value = obj.description)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.moduleId,
      'moduleId'
    );
    setConditionalSubRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  const handleFilterData = (filterData) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) =>
      element.uniqueKey === 'moduleDatatable'
        ? (element.value = filterData)
        : ''
    );
    setFormDetails(tempArray);
  };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'pageSubtitle') {
            while (showModal) {
              return (
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                  key={index}
                >
                  <div
                    onClick={() => {
                      setEdit(false);
                      handleCancel();
                      setSelectedModule({});
                      setConditionalSubRowStyles([]);
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div className="boxed" style={{ margin: 0 }}>
                    <Input key={index} formType={element} />
                    <div>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList
                      )}
                    </div>
                    {showButtons && (
                      <div>
                        <Button
                          onClick={() => handleSave()}
                          className="default mr20"
                        >
                          Save
                        </Button>
                        <Button
                          onClick={() => handleCancel()}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-bar',
                        'type-' + notify.type,
                      ].join(' ')}
                      style={{ position: 'sticky !important', width: 90 + '%' }}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'applicationSelect') {
            return (
              <div className="three-col-layout" key={index}>
                {/* <div style={{ width: '200px', marginTop: '25px' }}>
                  <Input
                    formType={data.find((item) => item.uniqueKey === 'search')}
                    isEditable="notShowing"
                    tableListData={tableData}
                    getFilteredData={(filterData) =>
                      handleFilterData(filterData)
                    }
                  />
                </div> */}
                <Input
                  isEditable={'notShowing'}
                  key={index}
                  formType={element}
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey
                    );
                  }}
                />
              </div>
            );
          } else if (element.uniqueKey === 'search') {
            return;
          } else if (element.uniqueKey === 'add_button') {
            return (
              <div style={{ float: 'right', marginTop: 5 + '%' }} key={index}>
                <Button
                  onClick={() => {
                    handleCancel();
                    setSelectedModule({});
                    setConditionalSubRowStyles([]);
                    setEdit(false);
                    setShowModal(true);
                    setShowButtons(true);
                  }}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-add-button "> </i>Add
                </Button>
              </div>
            );
          } else if (element.uniqueKey === 'moduleDatatable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val, idx) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                              key={idx}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                              key={idx}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                isEditable="notShowing"
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalSubRowStyles}
              />
            );
          } else {
            return (
              <Input
                key={index}
                disabledFlag={disable}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey
                  );
                }}
              />
            );
          }
        })
      : null;
  };

  return (
    <div className="">
      <div className="">
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
    </div>
  );
};
export { Modules };
