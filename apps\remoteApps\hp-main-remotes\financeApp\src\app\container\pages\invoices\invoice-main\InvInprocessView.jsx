/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import React, { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { InputNumber, Checkbox, Switch } from 'antd';
import DataTable from 'react-data-table-component';
import {
  Button,
  dataTableServiceProvider,
  DocumentViewer,
  PageWrap,
} from '@hp/components';

const LAST_PAGE = 52;

const RangeSelectionCell = ({ row, index, lastPage, onChange }) => (
  <>
    <InputNumber
      className="mr8"
      min={1}
      max={lastPage}
      value={row.start || 1} // Default to 1 if undefined
      disabled
    />
    <InputNumber
      min={row.start + 1}
      max={lastPage}
      value={row.end || LAST_PAGE} // Default to LAST_PAGE if undefined
      onChange={(value) => onChange(value, index)}
      disabled={row.start >= lastPage}
    />
  </>
);

const CheckboxCell = ({ row, onToggle }) => (
  <Checkbox
    checked={row.isSelected}
    onChange={(e) => onToggle(row.id, e.target.checked)}
  />
);

const PreviewIcon = ({ row, index, iconStatus, onIconClick }) => {
  const isLastIconMinus = iconStatus[iconStatus.length - 1] === 'icon-minus'; // Check if the last icon is 'icon-minus'
  const isSecondLastIcon = index === iconStatus.length - 2; // Identify second last row
  const isClickable = iconStatus[index] !== 'icon-minus' && !isLastIconMinus; // Disable all when last icon is 'icon-minus'

  return (
    <i
      className={iconStatus[index]}
      title={row.file_name}
      onClick={() => isClickable && onIconClick(index)} // Only allow click if it's clickable
      style={{
        cursor: isClickable ? 'pointer' : 'not-allowed', // Change cursor based on clickability
        opacity: isClickable ? 1 : 0.6, // Dim the icons if not clickable
      }}
    />
  );
};

const InvInProcessView = ({ location, match }) => {
  const { filePath = '', tableData = [] } = location?.state || {};
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [pagesno, setPagesNo] = useState(1);
  const [data, setData] = useState(tableData);
  const [visibleRows, setVisibleRows] = useState(1);
  const [iconStatus, setIconStatus] = useState(
    tableData.map(() => 'icon-plus')
  );
  const [removableRows, setRemovableRows] = useState(
    tableData.map(() => false) // Initially, all rows are not removable
  );
  const [showDataTable, setShowDataTable] = useState(false); // Track DataTable visibility

  const handleEndPageChange = (value, rowIndex) => {
    setPagesNo(value);
    const endPageValue = Math.min(value, LAST_PAGE);
    setData((prevData) =>
      prevData.map((row, index) => {
        if (index === rowIndex) return { ...row, end: endPageValue };
        if (index === rowIndex + 1 && endPageValue < LAST_PAGE) {
          return { ...row, start: endPageValue + 1 };
        }
        return row;
      })
    );
  };

  const toggleRowSelection = (id, isSelected) => {
    setData((prevData) =>
      prevData.map((item) => (item.id === id ? { ...item, isSelected } : item))
    );
  };

  const handleIconClick = (index) => {
    setIconStatus((prevStatus) =>
      prevStatus.map((status, i) =>
        i === index ? 'icon-minus' : i > index ? 'icon-plus' : status
      )
    );
    setVisibleRows((prev) => Math.min(prev + 1, data.length)); // Update visible rows

    setRemovableRows((prevRows) => {
      return prevRows.map((removable, i) => {
        if (i <= index) return true; // Enable rows before or at the clicked index
        if (i === data.length - 2) return true; // Enable second-last row when its icon is clicked
        return removable; // Keep other rows unchanged
      });
    });
  };

  // Table Columns
  const aiTableColumns = useMemo(
    () => [
      { name: 'SI No', selector: 'srNo', sortable: true, width: '8%' },
      { name: 'Pages', selector: 'vendorName' },
      { name: 'File Name', selector: 'invDate' },
      { name: 'Preview', selector: 'dueDate', sortable: true },
    ],
    []
  );

  const manualTableColumns = useMemo(
    () => [
      {
        name: 'SI No',
        cell: (row, index) => index + 1, // Auto-increment based on row index
        sortable: true,
        width: '8%',
      },
      {
        name: 'Page Selection',
        cell: (row, index) => (
          <RangeSelectionCell
            row={row}
            index={index}
            lastPage={LAST_PAGE}
            onChange={handleEndPageChange}
          />
        ),
      },
      {
        name: 'Pages',
        cell: (row) => {
          // Default start to 1 and end to LAST_PAGE if not set
          const start = row.start || 1;
          const end = row.end || LAST_PAGE;
          return `${start}-${end}`;
        },
      },
      {
        name: 'Remove Pages',
        cell: (row) => <CheckboxCell row={row} onToggle={toggleRowSelection} />,
      },
      {
        name: 'File Preview',
        cell: () => <i className="icon-file-pdf"></i>,
      },
      {
        cell: (row, index) => (
          <PreviewIcon
            row={row}
            index={index}
            iconStatus={iconStatus}
            onIconClick={handleIconClick}
          />
        ),
      },
    ],
    [data, iconStatus]
  );

  const onRowClickHandler = (e) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      e.slNo,
      'slNo'
    );
    setConditionalStyles(styleAttribute);
  };

  return (
    <>
      <div className="page-title flex-row">
        <h1>
          {subMenuName} / {innerMenuName}
        </h1>
      </div>

      <div className="two-col-layout">
        <div>
          <div className="page-sub-title">Invoice</div>
          {pagesno && (
            <DocumentViewer
              fileURL={`${filePath}#page=${pagesno}`}
              fileType="iframe"
              iframeStyle={{ width: '100%', minHeight: '690px' }}
              pageNumber={pagesno}
            />
          )}
        </div>

        <div>
          <div className="page-sub-title">Current Split (AI Based)</div>
          <div className="styledDatatable mb20">
            <DataTable
              persistTableHead
              noHeader
              highlightOnHover
              striped
              columns={aiTableColumns}
              data={tableData?.map((item, index) => ({
                srNo: index + 1,
                vendorName: item.pages || 'N/A',
                invDate: item.file_name || 'N/A',
                dueDate: item.rcvd_at || 'N/A',
              }))}
              pagination={true}
              paginationDefaultPage={1}
              paginationResetDefaultPage={true}
              paginationPerPage={5}
            />
          </div>
          <div className="page-sub-title flex-row">
            Manually Split
            <Switch
              defaultChecked={showDataTable} // Default switch state based on showDataTable
              onChange={(checked) => setShowDataTable(checked)} // Toggle DataTable visibility
            />
          </div>
          <div className="styledDatatable mb20">
            {showDataTable && ( // Render DataTable only when showDataTable is true
              <DataTable
                persistTableHead
                noHeader
                highlightOnHover
                striped
                columns={manualTableColumns}
                data={data.slice(0, visibleRows)}
                onRowClicked={(event) => onRowClickHandler(event)}
                conditionalRowStyles={conditionalRowStyles}
                pagination={true}
                paginationDefaultPage={1}
                paginationResetDefaultPage={true}
                paginationPerPage={5}
              />
            )}
          </div>
        </div>

        <div id="btn-bottom-white-bar" className="group fixed-button-bar">
          <Button className="small default mr20">Reprocess</Button>
          <Button className="small default mr20">Submit</Button>
          <Button className="secondary">Edit</Button>
        </div>
      </div>
    </>
  );
};

export { InvInProcessView };
