import Modal from 'react-modal';
import { Button, TextAreaInput } from '@hp/components';

const MultiRejectModal = (props) => {
  const {
    checkedCpcCommentList,
    rejectClick,
    isModalOpen,
    isModalClose,
    onCommonReasonChangeHandler,
    onIndividualReasonChangeHandler,
    rejectComments,
    errorFlag,
    errorMessage,
  } = props;

  const commonHandler = () => {
    return (
      <TextAreaInput
        label={'Common Reason'}
        className="mb16"
        style={{ height: 100 + 'px' }}
        onChange={onCommonReasonChangeHandler}
        value={rejectComments}
        error={{
          flag: errorFlag,
          message: errorMessage ?? 'Enter valid common reject reason!!',
        }}
      />
    );
  };

  const individualHandler = () => {
    return (
      <div
        className="col"
        style={{
          maxHeight: 230 + 'px',
          padding: '0px 10px 0px 0px',
          overflowY: 'scroll',
        }}
      >
        {checkedCpcCommentList?.length
          ? checkedCpcCommentList.map((data, index) => {
              return (
                <div key={index}>
                  <TextAreaInput
                    required
                    label={data?.label}
                    className="mb16"
                    onChange={(e) => onIndividualReasonChangeHandler(e, data)}
                    value={data?.comment}
                    error={{
                      flag: data?.errorFlag,
                      message: errorMessage ?? 'Enter valid reject reason!!',
                    }}
                  />
                </div>
              );
            })
          : null}
      </div>
    );
  };

  return (
    <Modal
      className="RejectModal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      //isOpen={true}
      isOpen={isModalOpen}
      style={{
        content: {
          height: 'auto',
          right: 'auto',
          bottom: 'auto',
          top: 50,
        },
      }}
    >
      <div className="page-sub-title">
        <div style={{ fontWeight: 'bold', fontSize: '15px' }}>
          Reject Reason
        </div>
      </div>
      <div onClick={isModalClose} className="modal-close icon-close"></div>

      <div className="card mb16">{commonHandler()}</div>
      <div className="card mb16">{individualHandler()}</div>

      <div className="mb8"></div>
      <Button onClick={rejectClick} className="small outline mr20">
        Submit
      </Button>
    </Modal>
  );
};

export { MultiRejectModal };
