/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 27-05-2021 15:40:13
 * @modify date 27-05-2021 15:40:20
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
// import { TextInput, Select } from '@hp/components';
import DataTable from 'react-data-table-component';
import { getUserListing, getClientComboBox } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import { useNavigate } from 'react-router-dom';
import { Select, TextInput } from '@hp/components';
import { globalutils } from '@hp/components'

const EditUser = (props) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { viewuserlist } = useSelector((store) => store.admin);
  const { clientCombolist } = useSelector((store) => store.email);
  let user = globalutils.getDataFromStorage('all');
  const client_Id = user.clientId;
  const [searchBox, setSearchBox] = useState();
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [TableListing, setTableList] = useState();
  const [clientComboState, setClientComboState] = useState([]);
  const [clientIdSelect, setClientIdSelect] = useState(client_Id);

  useEffect(() => {
    getParameter();
    dispatch(getClientComboBox());
  }, []);

  useEffect(() => {
    if (clientIdSelect && clientIdSelect !== undefined) {
      getParameter();
    }
  }, [clientIdSelect]);

  useEffect(() => {
    if (
      clientCombolist &&
      clientCombolist.value &&
      clientCombolist.value.length > 0
    ) {
      const clientOptions = clientCombolist.value.map((value) => {
        return {
          value: value.clientId,
          display: value.clientName,
        };
      });
      setClientComboState(clientOptions);
    }
  }, [clientCombolist]);

  useEffect(() => {
    if (viewuserlist && viewuserlist.value) {
      setTableList(viewuserlist.value);
      setSearchBox(viewuserlist.value);
    }
  }, [viewuserlist]);

  const getParameter = () => {
    let clientId =
      clientIdSelect && clientIdSelect !== undefined
        ? clientIdSelect
        : client_Id;
    dispatch(getUserListing(clientId));
  };

  const DataTableEventHandler = (data) => {
    let userId = data.userId;
    if (userId !== null && userId !== undefined) {
      navigate(`/administration/user-accounts/EditUser/edit-users/${userId}`);
    }
  };

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const handleOnChange = (event) => {
    setClientIdSelect(event.target.value);
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const columns = [
    {
      name: 'First Name',
      selector: 'firstName',

      sortable: true,
      width: '17%',
    },
    {
      name: 'Middle Name',
      selector: 'middleName',

      sortable: true,
      width: '12%',
    },
    {
      name: 'Last Name',
      selector: 'lastName',

      sortable: true,
      width: '17%',
    },
    {
      name: 'User Name',
      selector: 'username',

      sortable: true,
      width: '17%',
    },
    {
      name: 'Roles',

      selector: 'roleName',
      sortable: true,
      maxWidth: '47%',
    },
  ];

  return (
    <>
      <div className="page-title">Edit Users</div>

      <div className="two-col-layout mb20">
        {client_Id === 1 && (
          <Select
            label="Client"
            style={{ width: 200 + 'px' }}
            value={clientIdSelect}
            name="clientId"
            options={clientComboState}
            onChange={handleOnChange}
          />
        )}
        <TextInput
          label="Search"
          // placeholder="Search"
          style={{ width: 200 + 'px' }}
          value={filterString.filterStr}
          onChange={(e) => handleChange(e)}
        />
      </div>

      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          theme={'solarized'}
          onRowClicked={(data) => DataTableEventHandler(data)}
        />
      </div>
    </>
  );
};

export { EditUser };
