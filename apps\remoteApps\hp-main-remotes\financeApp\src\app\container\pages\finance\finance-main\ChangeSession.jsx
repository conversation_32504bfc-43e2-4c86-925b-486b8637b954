/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react/jsx-key */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getSessionTimeSlotList, updateSession } from '@hp/mainstore';
import Modal from 'react-modal';
import { AP_USER } from '@hp/constants';
import { Button, CheckBoxInput, CommonSpinner, Input } from '@hp/components';
import { globalutils } from '@hp/utils';
import { cloneDeep } from 'lodash';

const ChangeSession = ({
  outwardsMaster,
  selectedOutwardsDetailsList,
  showChangeSessionModal,
  handleChangeSessionModal,
  isSelectAll,
  ...props
}) => {
  const dispatch = useDispatch();
  const { sessionChangeForm, sessionChangeTimeSlotList } = useSelector(
    (store) => store.finance
  );
  const [formDetails, setFormDetails] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedRowId, setSelectedRowId] = useState(null);

  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user.userId);
  const clientId = user.clientId;

  useEffect(() => {
    if (!sessionChangeForm?.value || !Array.isArray(sessionChangeForm.value)) {
      setIsLoading(false);
      return;
    }
    let newArr = sessionChangeForm.value.map((form) => {
      if (form.uniqueKey === 'subtitle_1') {
        return {
          ...form,
          displayName: `${outwardsMaster?.originBankName ?? ''} ${
            form.displayName
          }`,
        };
      } else return form;
    });

    setFormDetails(newArr);
    setIsLoading(false);
  }, [sessionChangeForm]);

  useEffect(() => {
    if (
      !sessionChangeTimeSlotList?.value ||
      !Array.isArray(sessionChangeTimeSlotList.value)
    ) {
      return;
    }

    setFormDetails((prev) =>
      prev.map((item) =>
        item.uniqueKey === 'newSessionTime'
          ? { ...item, comboBoxOptions: sessionChangeTimeSlotList.value }
          : item
      )
    );
  }, [sessionChangeTimeSlotList]);

  const handleOnChange = (event, index) => {
    let tempArray = [...formDetails];
    tempArray[index].value = event?.target?.value ?? null;
    setFormDetails(tempArray);
    setSelectedTime(event?.target?.value);
  };

  const handleDateChange = (event, index) => {
    let tempArray = cloneDeep(formDetails);
    tempArray[index].value = event;
    dispatch(
      getSessionTimeSlotList({
        userId: userId,
        clientId: clientId,
        bankId: outwardsMaster?.bankId ?? null,
        bankName: outwardsMaster?.originBankName ?? null,
        bankOutTxnId: outwardsMaster?.bankOutTxnId ?? null,
        sessionDate: event ?? null,
      })
    );
    setFormDetails(tempArray);
    setSelectedDate(event);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleDateChange,
  };

  function handleForm(data) {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'available_sessions') {
            let columns = element.formSubDetailsInternalDTOList.map((item) => {
              return {
                ...item,
                name: item.displayName,
                displayName: item.displayName,
                width: item.displayWidth,
                cell:
                  item.selector === 'checkBox'
                    ? function displayCell(row) {
                        const isSelected = selectedRowId === row.bankOutTxnId;

                        return (
                          <CheckBoxInput
                            checked={isSelected}
                            disabled={!isSelected && selectedRowId !== null}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedRowId(row.bankOutTxnId);
                                setSelectedRow(row);
                              } else {
                                setSelectedRowId(null);
                                setSelectedRow(null);
                              }
                            }}
                          />
                        );
                      }
                    : '',
              };
            });
            return (
              <Input
                dataTablePersistHead
                dataTableSearchDisable
                index={index}
                formType={element}
                dataTableColumn={columns}
              />
            );
          } else if (
            element.uniqueKey === 'newSessionDate' ||
            element.uniqueKey === 'newSessionTime'
          )
            return (
              <span className="sameRow" style={{ maxWidth: '300px' }}>
                <Input
                  index={index}
                  isEditable="notShowing"
                  disabledFlag={selectedRow ? true : false}
                  formType={{
                    ...element,
                    value: selectedRow ? null : element.value,
                  }}
                  minDate={new Date()}
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      index
                    );
                  }}
                />
              </span>
            );
          else return <Input index={index} formType={element} />;
        })
      : '';
  }
  return (
    <Modal
      className="Modal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={showChangeSessionModal ?? false}
    >
      <div
        onClick={() => {
          handleChangeSessionModal(false);
        }}
        className="modal-close icon-close"
        style={{ fontSize: 20 + 'px' }}
      ></div>
      <div className="boxed mb20">
        {formDetails?.length ? handleForm(formDetails) : ''}
        <Button
          onClick={() => {
            if (selectedRow?.bankOutTxnId || (selectedDate && selectedTime)) {
              const payload = {
                userId: userId,
                clientId: clientId,
                bankName: outwardsMaster.originBankName ?? '',
                bankOutMasterIdOld: outwardsMaster.bankOutTxnId ?? null,
                bankOutMasterId: selectedRow?.bankOutTxnId ?? null,
                bankOutDetailsId: selectedOutwardsDetailsList,
                sessionId: selectedRow?.bankOutTxnId
                  ? null
                  : parseInt(selectedTime),
                sessionDate: selectedRow?.bankOutTxnId ? null : selectedDate,
                selectAll: isSelectAll ?? false,
              };
              dispatch(updateSession(payload));
              handleChangeSessionModal(false);
            }
          }}
          className="small mb8 outline"
          style={{
            float: 'right',
            position: 'relative',
            top: '20px',
          }}
        >
          Change Session
        </Button>
      </div>
      <CommonSpinner visible={isLoading} />
    </Modal>
  );
};

export default ChangeSession;
