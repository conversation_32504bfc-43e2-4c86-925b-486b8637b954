/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */

/**
 * <AUTHOR> AUGUSTINE
 */

import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import './ApprovalModal.scss';
import {
  Button,
  CommonSpinner,
  formValidationUtil,
  Input,
} from '@hp/components';
import { updateApproverDetails } from '@hp/mainstore';

function ApprovalModal(props) {
  let { form, modalClose } = props;
  const [approvalForm, setApprovalForm] = useState(null);
  const [approverOptions, setApproverOptions] = useState([]);
  const [modalSpin, setModalSpin] = useState(true);
  const [commonNamePart, setCommonNamePart] = useState('');

  const dispatch = useDispatch();

  // initial setup for approver screen......................................................

  useEffect(() => {
    setModalSpin(false);
    if (
      form?.formSubDetailsInternalDTOList &&
      form.formSubDetailsInternalDTOList.length
    ) {
      let comboOptions =
        form.formSubDetailsInternalDTOList?.[0]?.comboBoxOptions;
      let values = [];
      let newArry = form.formSubDetailsInternalDTOList.map((list) => {
        if (list?.value?.[0]?.value) {
          values.push(parseInt(list.value[0].value));
        }
        return {
          ...list,
          displayName: list.displayName.replace(/\d/g, '').trim(),
        };
      });

      if (values?.length && comboOptions?.length) {
        comboOptions.map((option) => {
          if (values.some((val) => val === option.commonId))
            option.disabled = true;
        });
      }

      setApproverOptions(comboOptions ? comboOptions : []);
      setApprovalForm(newArry);
      setCommonNamePart(form.formSubDetailsInternalDTOList[0]?.displayName);
      return;
    }
    modalClose();
  }, [form]);
  ////................................................................................

  const multiselectFilter = (option, filter, id) => {
    let container = document.getElementById(id);
    container.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
    if (!filter) {
      return option;
    }
    const re = new RegExp(filter, 'i');
    return option.filter(({ label }) => label && label.match(re));
  };

  // multislect function
  const multiSelectHandler = (formType, event, index) => {
    if (!event?.length) return;
    let selectedOne = event.slice(-1)[0];

    const approvalFormClone = structuredClone(approvalForm);
    let selectForm = {};
    let comboOptions = [...approverOptions];
    comboOptions.map((option) => {
      if (option?.commonId === selectedOne?.value) {
        option.disabled = true;
        selectForm = {
          ...formType,
          value: [{ ...selectedOne, desc: option.desc }],
        };
      }
      if (event.length > 1 && option?.commonId === parseInt(event[0].value)) {
        option.disabled = false;
      }
    });

    setApproverOptions(comboOptions);
    approvalFormClone.splice(index, 1, selectForm);
    setApprovalForm(approvalFormClone);
  };

  const addApproverHandler = () => {
    if (approvalForm.length === approverOptions.length) return;
    let approvers = [...approvalForm];
    approvers.push({
      ...approvalForm[0],
      orderId: null,
      value: [],
      canDelete: true,
    });
    approvers.map((list, index) => {
      list.orderId = index + 1;
    });
    setApprovalForm(approvers);
  };

  const deleteHandler = (index, approverId) => {
    let approvals = [...approvalForm];

    let comboOptions = approverOptions.map((options) => {
      if (options.commonId === approverId) {
        return { ...options, disabled: false };
      } else return options;
    });

    setApproverOptions(comboOptions);

    approvals.splice(index, 1);
    approvals = approvals.map((list, index) => {
      return {
        ...list,
        orderId: index + 1,
      };
    });

    setApprovalForm(approvals);
  };

  const approverSaveHandler = () => {
    if (!props.poId || !props.invId) return;
    let validationCheck = formValidationUtil.checkMandatoryField(approvalForm);
    setApprovalForm([...validationCheck.formList]);
    if (validationCheck.validSuccess) {
      dispatch(
        updateApproverDetails({
          poId: props.poId,
          invId: props.invId,
          form: approvalForm,
        })
      );
      setModalSpin(true);
    }
  };

  return (
    <>
      <CommonSpinner visible={modalSpin} />
      <h1 className="page-sub-title" style={{ marginRight: '1px' }}>
        {form.displayName}
      </h1>
      <div className="mb20 approver-container">
        {approvalForm && approvalForm.length
          ? approvalForm.map((form, index) => {
              return (
                <div
                  key={index}
                  style={{
                    position: 'relative',
                    width: '50%',
                  }}
                  id={`apprrover${index}`}
                >
                  <Input
                    key={index}
                    formType={{
                      ...form,
                      displayName: `${commonNamePart} ${index + 1}`,
                      comboBoxOptions: approverOptions,
                    }}
                    onChangeHandler={(fromType, event) =>
                      multiSelectHandler(fromType, event, index)
                    }
                    multiselectFilter={(option, filter) =>
                      multiselectFilter(option, filter, `apprrover${index}`)
                    }
                    isEditable={'notShowing'}
                    closeOnChange={true}
                    disableAllClearBtn={true}
                  />

                  <span className="name-tag">
                    {form.value && form.value.length
                      ? ` ${form?.value[0]?.desc} `
                      : ''}
                  </span>

                  {form.canDelete && (
                    <span
                      className="icon-close close-btn"
                      onClick={() => deleteHandler(index, form.value[0]?.value)}
                    ></span>
                  )}
                </div>
              );
            })
          : ''}
      </div>

      {approvalForm?.length && (
        <div
          className="flex-right"
          style={{
            position: 'absolute',
            bottom: '16px',
            width: '85%',
          }}
        >
          <Button
            className={'small  outline add-button-custom flex-row vam button'}
            onClick={addApproverHandler}
          >
            <span className="icon-add-button mr8"></span>
            Approver Level
          </Button>

          <Button
            className={'small default'}
            onClick={() => approverSaveHandler()}
          >
            Save
          </Button>
        </div>
      )}
    </>
  );
}

export default ApprovalModal;
