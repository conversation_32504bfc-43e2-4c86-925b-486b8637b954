/* eslint-disable react/jsx-pascal-case */
/* eslint-disable @typescript-eslint/no-unused-vars */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PfReportDataList from '../../../_pf-reports/PfReportDataList';
import DataTable from 'react-data-table-component';
import { useAppRouterDom } from '@hp/utils';
import { getSummaryReports } from '@hp/mainstore';
const PfSummaryReport = ({ match }) => {
  const { pfReportDTO } = useSelector((state) => state.finance);
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();

  // const menuData = domParameters?.menuData || '';
  // const submenu = domParameters?.submenu || '';
  const parameters = domParameters?.parameters || '';

  const urlParam = parameters || '';
  const user = JSON.parse(localStorage.getItem('user'));
  const userId = parseInt(user.userId, 10);
  const clientId = user.clientId;
  const [reportData, setReportData] = useState(null);

  const [columns, setColumns] = useState([]);
  const [rows, setRows] = useState([]);
  const [tableHeader, setTableHeader] = useState('');

  useEffect(() => {
    dispatch(
      getSummaryReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        fromDate: '01-01-2021',
        toDate: '03-31-2023',
        type: 'subscribe',
      })
    );

    return () => {
      dispatch(
        getSummaryReports({
          para: urlParam,
          clientId: clientId,
          userId: userId,
          fromDate: '',
          toDate: '',
          type: 'unSubscribe',
        })
      );
    };
  }, [dispatch, urlParam, clientId, userId]);

  useEffect(() => {
    if (pfReportDTO?.value) {
      const { columns, value, heading, type } =
        pfReportDTO.value.graphDetails || {};

      if (type === 'CountList') {
        setColumns(columns);
        setRows(value || []);
        setTableHeader(heading);
      }

      setReportData(pfReportDTO.value.listView);
    }
  }, [pfReportDTO]);

  return (
    <>
      <div className="page-title">Payment Summary Report</div>
      {pfReportDTO && (
        <div className="report-body-main">
          <div className="report-data-body card mr20">
            <PfReportDataList
              data={reportData}
              summary={pfReportDTO?.value?.summary}
            />
          </div>

          <div className="report-Table-body card">
            <div className="page-sub-title">{tableHeader}</div>
            <div className="styledDatatable mb20">
              <DataTable
                persistTableHead
                noHeader
                highlightOnHover
                striped
                columns={columns}
                data={rows}
                pagination
                paginationDefaultPage={1}
                paginationResetDefaultPage
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export { PfSummaryReport };
