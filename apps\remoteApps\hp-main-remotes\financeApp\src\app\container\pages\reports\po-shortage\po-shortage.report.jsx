/* eslint-disable no-console */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CircularProgressbar } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import './reportstyles.scss';
import {
  Button,
  DateRangeInput,
  SearchInput,
  Select,
  MultiselectComponent,
  globalutils,
} from '@hp/components';
import { MasterDetail } from './shortage-master.report';
import { AP_API_URL } from '@hp/constants';
import { getIpnList } from '@hp/mainstore';
import { getMultiselectFilterData, poShortage } from '@hp/mainstore';
export function PoShortageReport(props) {
  const dispatch = useDispatch();
  const [listening, setListening] = useState(false);
  const [search, setSearch] = React.useState('');
  const [debounced, setDebounced] = React.useState(() => search);
  const [tableDetails, setTableDetails] = useState(null);
  const [searchClick, setSearchClick] = useState(false);

  const [searchRequest, setSearchRequest] = useState({
    clientId: 1000,
    supplierId: '',
    startDate: '2022-01-01',
    endDate: '2022-12-30',
    topLevel: [],
    reportView: 'd-view',
    pageNo: 0,
    size: 100,
  });

  const [supplierOptions, setSupplierOptions] = useState([]);
  const [cpnOptions, setcpnOptions] = useState([]);
  const [cpnSelectedValue, setcpnSelectedValue] = useState([]);
  const { multiselectFilterData, reportViewer } = useSelector(
    (store) => store.accpay
  );
  const { ipnList } = useSelector((store) => store.requisition);

  const [register, setRegister] = useState('');
  const [progress, setProgress] = useState('');
  let sse = undefined;

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebounced(search);
    }, 250);
    return () => {
      clearTimeout(timeout);
    };
  }, [search]);

  useEffect(() => {
    if (search !== '' && search !== null && search !== undefined) {
      dispatch(
        getMultiselectFilterData({ filterField: debounced, type: 'subscribe' })
      );
    }
  }, [debounced]);

  useEffect(() => {
    if (ipnList !== undefined && ipnList.length) {
      let cpnArray = [];
      ipnList.map((option) => {
        cpnArray.push({
          label: option.cpn,
          value: option.cpn,
        });
      });
      setcpnOptions(cpnArray);
    } else {
      setcpnOptions([]);
    }
  }, [ipnList]);

  useEffect(() => {
    if (multiselectFilterData?.value) {
      if (multiselectFilterData?.value.length) {
        var options = [];
        multiselectFilterData.value.map((option) => {
          options.push({ id: option.supplierId, value: option.supplierName });
        });
        setSupplierOptions(options);
      } else {
        options = [];
        setSupplierOptions(options);
      }
    }
  }, [multiselectFilterData]);

  const onsupplierChange = (e, key) => {
    if (key === 'not-present') {
      let updatedState = { ...searchRequest };
      updatedState.supplierId = 10684;
      setSearchRequest(updatedState);
      dispatch(getIpnList(10684));
    } else {
      setSearch(e.target.value);
    }
  };

  const onDateChange = (e) => {
    const startDate = globalutils.formatDate(e.fromDate, 'yyyy-mm-dd');
    const endDate = globalutils.formatDate(e.toDate, 'yyyy-mm-dd');
    let updatedState = { ...searchRequest };
    updatedState.startDate = startDate;
    updatedState.endDate = endDate;
    setSearchRequest(updatedState);
  };
  useEffect(() => {
    if (!listening) {
      sse = new EventSource(`${AP_API_URL}/ext_entity/register`);
      sse.addEventListener('register', (event) => {
        const registerID = JSON.parse(event.data);
        setRegister(registerID);
      });
      sse.addEventListener('Progress', (event) => {
        const result = JSON.parse(event.data);

        setProgress(result);
      });
      sse.onerror = (event) => {
        if (event.target.readyState === EventSource.CLOSED) {
          //console.log("SSE closed (" + event.target.readyState + ")");
        }
        sse.close();
      };
      sse.onopen = (event) => {
        console.log('connection opened');
      };
      setListening(true);
    }
    return () => {
      sse?.close();
    };
  }, []);

  useEffect(() => {
    if (reportViewer?.data) {
      setTableDetails(reportViewer?.data);
    }
  }, [reportViewer]);

  const ProgressBar = useCallback(() => {
    return (
      <div className="sr-cm-centered" style={{ width: 100, height: 100 }}>
        <CircularProgressbar value={progress} text={Math.ceil(progress)} />
      </div>
    );
  }, [progress]);

  const ReportTable = useCallback(() => {
    return tableDetails?.columnDefinitions != null ? (
      <MasterDetail
        columnDef={
          tableDetails.columnDefinitions == null
            ? []
            : tableDetails.columnDefinitions
        }
        records={tableDetails.row == null ? [] : tableDetails.row}
      />
    ) : null;
  }, [tableDetails]);

  function reportHandle() {
    setSearchClick(true);
    if (searchRequest.reportView === 's-view') {
      let sendObject = {
        clientId: 1000,
        supplierId: searchRequest.supplierId,
        startDate: searchRequest.startDate,
        endDate: searchRequest.endDate,
        pageNo: 0,
        size: 100,
        progressId: register,
        partList: cpnSelectedValue.map((e) => e.value),
      };
      dispatch(poShortage({ obj: sendObject, type: searchRequest.reportView }));
    } else {
      let sendObject = {
        clientId: 1000,
        supplierId: searchRequest.supplierId,
        startDate: searchRequest.startDate,
        endDate: searchRequest.endDate,
        pageNo: 0,
        size: 100,
        progressId: register,
      };
      dispatch(poShortage({ obj: sendObject, type: searchRequest.reportView }));
    }
  }
  const multiSelectHandler = (data) => {
    setcpnSelectedValue(data);
  };

  const selectFilter = (option, filter) => {
    const re = new RegExp(filter, 'i');
    return option.filter(({ label }) => label && label.match(re));
  };

  return (
    <>
      <div className="page-title mb20">Report / Shortage Report</div>
      <div className="flex-row">
        <DateRangeInput
          placeholder="Date"
          className="mr40 mb40"
          onChange={onDateChange}
        />

        <SearchInput
          value={search}
          options={supplierOptions}
          onChange={(e, key) => onsupplierChange(e, key)}
          className="mr40 mb40"
          placeholder="Supplier"
        />
        <MultiselectComponent
          className={'mr20'}
          isLoading={false}
          style={{ width: 100 + '%' }}
          setSelected={(data) => {
            multiSelectHandler(data);
          }}
          selected={cpnSelectedValue}
          options={cpnOptions}
          filterOptions={(option, filter) => selectFilter(option, filter)}
        />
        <Select
          placeholder="Report View"
          className="mr40 mb40"
          style={{ width: 200 + 'px' }}
          value={searchRequest.reportView}
          name="clientId"
          onChange={(e) =>
            setSearchRequest({ ...searchRequest, reportView: e.target.value })
          }
          options={[
            {
              value: 's-view',
              display: 'Summary view',
            },
            {
              value: 'd-view',
              display: 'Detailed view',
            },
          ]}
        />
        <Button className="default small" onClick={() => reportHandle()}>
          Search
        </Button>
      </div>

      {searchClick && progress <= 99 ? <ProgressBar /> : ''}

      {progress === 100 && tableDetails?.columnDefinitions != null ? (
        <ReportTable />
      ) : (
        ''
      )}
    </>
  );
}
