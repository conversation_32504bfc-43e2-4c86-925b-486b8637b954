import React from 'react';
import { Collapse } from 'antd';
import { SessionElement } from './SessionElement';
function BankCard({ details, key }) {
  const genExtra = (status = []) => {
    if (!status.length) return null;

    return (
      <div className="status-overview">
        {status.map(
          ({ statusType, statusName, count }, index) =>
            count && (
              <div style={{ position: 'relative' }} key={index}>
                <p key={index} className={statusType ?? 'status-ok'}>
                  {count}
                  {''} {statusName}
                </p>
                {/* <p className="status-count">{count}</p> */}
              </div>
            )
        )}
      </div>
    );
  };

  const getLabel = (name) => {
    return (
      <div className="bank-title">
        <p className="round ">{name ? name.charAt(0) : ''}</p>
        <span>{name ?? ''}</span>
      </div>
    );
  };

  const getSession = (sessionData) => {
    if (!sessionData.length) return null;

    return (
      <div className=" two-col-layout-20 session-container ">
        {sessionData.map((session, index) => (
          <SessionElement details={session} key={index} />
        ))}
      </div>
    );
  };
  const itemsHandler = (data) => {
    return [
      {
        key: key,
        label: getLabel(data.bankName),
        children: getSession(data?.pushOutFileDetails ?? []),
        extra: genExtra(data.fileStatusCount),
      },
    ];
  };
  return (
    <Collapse
      className="bank-custom-collapse mb8"
      expandIconPosition={'end'}
      items={itemsHandler(details)}
    />
  );
}

export { BankCard };
