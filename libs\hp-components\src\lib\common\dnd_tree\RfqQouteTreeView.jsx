/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/no-unknown-property */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */

import './rfqQuoteTree.scss';
import { useState, useEffect } from 'react';
import { Tree, useDragOver } from '@minoru/react-dnd-treeview';
import { useSelector, useDispatch } from 'react-redux';
import { DocumentViewer, TraceEvents } from '@hp/components';
import { showDialog, deleteQuotation, quoteEventLog } from '@hp/mainstore';
import Modal from 'react-modal';
import Checkbox from 'antd/es/checkbox/Checkbox';
import { MailOutlined } from '@ant-design/icons';
import { Popover, Tooltip } from 'antd';

function RfqQuoteTreeview(props) {
  const [treeData, setTreeData] = useState([]);
  const [initialNodeId, setInitialNodeId] = useState([]);
  const [isModal, setIsModal] = useState(false);
  const [toggle, setToggle] = useState([]);
  const [filepath, setFilepath] = useState('');
  const [fileTypeName, setFileTypeName] = useState('iframe-blob');
  const [formVisibility, setFormVisibility] = useState({});
  const [selectedNodeIds, setSelectedNodeIds] = useState(new Set());
  const [supplierId, setSupplierId] = useState();
  const [traceEventData, setTraceEventData] = useState([]);
  const {
    supplierQuatations,
    deleteQuotationslice,
    showIcon,
    quoteEventLogData,
  } = useSelector((store) => store.sourcing);

  const dispatch = useDispatch();

  const {
    disabled,
    form,
    suppNameList,
    parameter,
    compareModal,
    workFlowEvent,
  } = props;
  useEffect(() => {
    if (!compareModal) {
      setSelectedNodeIds(new Set());
    }
  }, [compareModal]);

  useEffect(() => {
    if (quoteEventLogData) {
      setTraceEventData(quoteEventLogData.value);
    }
  }, [quoteEventLogData]);

  useEffect(() => {
    if (!supplierQuatations || !supplierQuatations.value) return;

    const initialTreeData = supplierQuatations.value;
    const updatedTreeData = initialTreeData.map((node) => ({
      ...node,
      parent: node.parent === 0 ? -1 : node.parent,
    }));

    // Check if the new root node hasn't been added already
    const rootExists = updatedTreeData.some(
      (node) => node.id === -1 && node.parent === 0
    );

    if (!rootExists) {
      // Add the new root node
      const newNode = {
        id: -1,
        parent: 0,
        className: 'x',
        creationDate: null,
        curRevNum: null,
        detail: null,
        droppable: true,
        extension: null,
        fileName: null,
        filePath: null,
        fileTypeId: null,
        firstChild: true,
        hasChild: true,
        itemCode: null,
        orderId: null,
        orginatedBy: null,
        partNumber: null,
        qty: 5,
        sibilingNext: true,
        text: 'Quotations',
        uomId: 1,
        uploadedAt: null,
        uploadedBy: null,
      };

      setTreeData([...updatedTreeData, newNode]);

      let treeRoot;
      if (initialNodeId.length) return;
      treeRoot = treeData.filter((data) => data.parent === 0);
      if (!treeRoot || !treeRoot.length) return setInitialNodeId([-1]);
    } else {
      setTreeData(updatedTreeData);
    }
  }, [supplierQuatations]);

  useEffect(() => {
    props.setDeleteQuotationValue(deleteQuotationslice?.value);
  }, [deleteQuotationslice]);

  const toggleFormVisibility = (nodeId, quoteId) => {
    setFormVisibility((prevState) => {
      const newState = Object.keys(prevState).reduce((acc, key) => {
        acc[key] = false;
        return acc;
      }, {});

      return {
        ...newState,
        [nodeId]: !prevState[nodeId],
      };
    });

    const node = treeData.find((n) => n.id === nodeId);
    dispatch(quoteEventLog(quoteId));
  };
  const popoverContentRender = (node) => {
    return (
      <div
        style={{
          // position: 'absolute',
          top: '-10px',
          left: '417px',
          // marginTop: '10px',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          backgroundColor: '#fff',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.15)',
          zIndex: 1,
          height: 'auto',
          width: 'auto',
          // overflowY: 'auto',
        }}
      >
        <TraceEvents
          enableComments={false}
          data={traceEventData}
          // userId={userId}
        />
      </div>
    );
  };

  const popoverTitleWithCloseIconRender = (node) => {
    return (
      <>
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          <i
            className="icon-close"
            style={{ cursor: 'pointer' }}
            onClick={() => toggleFormVisibility(node.id)}
          ></i>
        </div>
      </>
    );
  };

  const nodeDeleteHandler = (quoteId) => {
    dispatch(deleteQuotation(quoteId));
  };
  // const WorkFlowHandler = (quoteId) => {
  //   if (workFlowEvent) {
  //     props.handleWorkFlowEvent(quoteId);
  //   }
  // };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const handleNodeSelect = (node, isChecked) => {
    const newSelectedNodeIds = new Set(props.selectedNodeIds); // not internal state

    if (!isChecked) {
      newSelectedNodeIds.delete(node.id);
      props.onSelectedNodesChange({
        selectedNodeIds: Array.from(newSelectedNodeIds),
        filePath: null,
        deselectedNodeId: node.id,
        text: null,
      });
    } else {
      if (newSelectedNodeIds.size < 2) {
        if (supplierId === node.supplierId || newSelectedNodeIds.size === 0) {
          newSelectedNodeIds.add(node.id);
          props.onSelectedNodesChange({
            selectedNodeIds: Array.from(newSelectedNodeIds),
            pathForCompare: node.pathForCompare,
            filePath: node.filePath,
            deselectedNodeId: null,
            text: node.text,
          });
          setSupplierId(node.supplierId);
        } else {
          funcToSetResponseMessage(
            'error',
            'Please select 2 versions of the same supplier'
          );
        }
      } else {
        funcToSetResponseMessage(
          'error',
          'You can only select up to two Quotes.'
        );
      }
    }
  };

  const handleAttachpopup = (suppID, quoteId, id) => {
    console.log(suppID);
    props.showPopup(suppID, quoteId, id);
    setIsModal(false);
  };

  const handleEmailpopup = (docId, emailId) => {
    props.showEmailPopup(docId, emailId);
    setIsModal(false);
  };

  const handleOpenDocumentModal = (e) => {
    if (e.parent !== 0 && e.parent !== -1) {
      props.openDocumentModal(e?.quoteId);
      // history.push({
      //   pathname: `/${props.menuData}/${props.submenu}/manage-quote/rfq-manage-quote/quotation/${e?.quoteId}`,
      //   state: props?.rfqId ,
      // });

      let filePath = e.filePath;

      setFilepath(e.filePath);
    }
  };

  function expandClass(isOpen, droppable, node) {
    var name = 'expand-icon treetitleright';
    if (isOpen) {
      name += ' open';
    }
    if (!droppable) {
      name += ' file';
    }
    return name;
  }

  return (
    <div
      className="quote-tree-component"
      style={{ height: 'calc(100% - 50px)', overflowY: 'auto' }}
    >
      {/* {initialNodeId.length ? ( */}
      {initialNodeId.length ? (
        <div style={{ width: parameter === 'req-final-quote' ? '45%' : '65%' }}>
          <Tree
            tree={treeData}
            rootId={0}
            // onDrop={handleDrop}
            sort={false}
            initialOpen={initialNodeId}
            insertDroppableFirst={false}
            classes={{
              root: 'dnd-tree-root',
              dragOver: 'dnd-tree-dragover',
              listItem: 'dnd-list-item',
            }}
            canDrop={(tree, { dragSource, dropTargetId, dropTarget }) => {
              if (dragSource?.parent === dropTargetId) {
                return true;
              }
            }}
            canDrag={() => (showIcon ? true : false)}
            dropTargetOffset={5}
            placeholderRender={(node, { depth }) => (
              <div
                style={{
                  backgroundColor: '#3d5791',
                  height: '1px',
                  width: 'calc(100% - ' + ((depth - 1) * 26 + 16) + 'px)',
                  position: 'relative',
                  left: (depth - 1) * 26 + 16,
                }}
              ></div>
            )}
            render={(node, { depth, isOpen, onToggle, hasChild }) => {
              let cname = ['tree-node'];
              if (isOpen) cname.push('tree-node-is-open');
              node.className = 'x';

              const shouldHighlightNode =
                !!props?.selectedSupplier &&
                node.text?.trim().toLowerCase() ===
                  props.selectedSupplier.trim().toLowerCase();

              return (
                <>
                  {props?.isPrTree &&
                  node.parent === -1 &&
                  node.id !== lastParentId ? (
                    <div
                      style={{
                        display: 'block',
                        position: 'absolute',
                        height: 'calc(100% + 9px)',
                        borderLeft: '1px solid rgba(0, 0, 0, 0.2)',
                        width: '0.8px',
                        marginLeft: '-10px',
                        top: '18px',
                      }}
                    ></div>
                  ) : (
                    ''
                  )}
                  <div
                    class="vertical"
                    style={{ marginLeft: depth * 26 + 8 }}
                  ></div>
                  <div
                    class="horizontal"
                    style={{ marginLeft: depth * 26 + 8 }}
                  ></div>
                  <div
                    className={cname.join(' ')}
                    style={{ marginLeft: depth * 26 + 8 }}
                    {...useDragOver(node.id, isOpen, onToggle)}
                    // onClick={onToggle}
                  >
                    {node.siblingNext && (
                      <div
                        className="sibling-next"
                        style={{ left: depth * 26 + 16 }}
                      ></div>
                    )}
                    {props?.isPrTree ? (
                      <div
                        // className={expandClass(isOpen, hasChild, node)}
                        style={{
                          transform: 'rotate(0deg)',
                          color: 'rgba(var(--text_color_rgb), 0.6)',
                        }}
                        onClick={onToggle}
                      >
                        {/* hasChild ? <i className="icon-tree-arrow"></i> : null */}
                        <i
                          className={
                            depth === 0
                              ? hasChild
                                ? isOpen
                                  ? 'icon icon-folder-minus'
                                  : 'icon icon-folder-plus'
                                : 'icon icon-folder'
                              : depth === 1
                              ? node.text.split('.').pop() === 'pdf'
                                ? 'icon icon-file-pdf'
                                : 'icon icon-document'
                              : ''
                          }
                          // onClick={() => handleSave()}
                        ></i>
                      </div>
                    ) : (
                      <div
                        className={expandClass(isOpen, hasChild, node)}
                        onClick={onToggle}
                      >
                        {hasChild ? (
                          <i
                            className={`icon-tree-arrow${isOpen ? '' : ''}`}
                            style={{
                              color: '#0f0700',
                              transform: isOpen
                                ? 'rotate(180deg)'
                                : 'rotate(0deg)',
                              transition: 'transform 0.3s ease',
                            }}
                          ></i>
                        ) : node.parent === -1 ? (
                          <i className="icon-tree-arrow"></i>
                        ) : (
                          <i
                            className="icon-file-text2"
                            style={{ color: '#e4e4e' }}
                          ></i>
                        )}
                      </div>
                    )}
                    <div
                      className={`treetitle ${
                        node.parent !== 0
                          ? formVisibility[node.id]
                            ? 'doc-info'
                            : 'selected'
                          : ''
                      } ${shouldHighlightNode ? 'highlighted-tree-node' : ''}`}
                      onClick={() => {
                        setToggle([node.id]);
                        form('', node);
                        // WorkFlowHandler(node.quoteId);
                      }}
                      style={{ width: '220px' }}
                    >
                      <div
                        className="treetitleleft"
                        style={{ display: 'flex', alignItems: 'center' }}
                      >
                        {node.parent !== 0 &&
                          node.droppable == true &&
                          node.parent !== -1 &&
                          parameter !== 'req-final-quote' &&
                          parameter !== 'rfq-finalized-quote' &&
                          !workFlowEvent && (
                            <div
                              title="Select 2 versions to compare"
                              style={{ marginRight: '10px' }}
                            >
                              <Checkbox
                                checked={props.selectedNodeIds.includes(
                                  node.id
                                )} // <- now controlled from parent
                                onChange={(e) =>
                                  handleNodeSelect(node, e.target.checked)
                                }
                              />
                            </div>
                          )}
                        {node.parent === -1 ? (
                          <a>
                            <h5
                              // onClick={() => handleSave(node)}
                              style={{
                                flex: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={node.text}
                            >
                              {node.text && node.text.length > 20
                                ? `${node.text.slice(0, 20)}...`
                                : node.text}
                            </h5>
                          </a>
                        ) : (
                          <h5
                            // onClick={() => handleSave(node)}
                            style={{
                              flex: 1,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                            title={node.text}
                          >
                            {node.text && node.text.length > 15
                              ? `${node.text.slice(0, 15)}...`
                              : node.text}
                          </h5>
                        )}
                      </div>

                      {!disabled &&
                        node.parent == -1 &&
                        parameter !== 'req-final-quote' &&
                        parameter !== 'rfq-finalized-quote' &&
                        !workFlowEvent && (
                          <div>
                            {/* {hasChild ? ( */}
                            {!disabled ? (
                              <div
                              // className={expandClass(isOpen, hasChild)}
                              >
                                <div>
                                  <a
                                    className="node-icon"
                                    onClick={(event) => {
                                      event.stopPropagation();
                                      form('add', node);
                                      handleAttachpopup(
                                        node.supplierId,
                                        node.quoteId,
                                        node.id
                                      );
                                    }}
                                  >
                                    <i className="icon-tree-add "></i>

                                    <span className="tooltip">Add Quote</span>
                                  </a>
                                </div>
                              </div>
                            ) : (
                              <div
                                className={expandClass(isOpen, hasChild)}
                                style={{ backgroundColor: 'transparent' }}
                              ></div>
                            )}
                          </div>
                        )}
                      {node.parent !== 0 &&
                        node.droppable === true &&
                        node.parent !== -1 &&
                        !workFlowEvent && (
                          <div
                            style={{
                              marginBottom: '2px',
                              display: 'flex',
                              gap: '8px',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                            }}
                          >
                            {/* Email Icon */}
                            {parameter !== 'req-final-quote' &&
                              parameter !== 'rfq-finalized-quote' && (
                                <a
                                  className="node-icon"
                                  style={{
                                    visibility:
                                      node.id === initialNodeId[0] ||
                                      node.mode !== 'E'
                                        ? 'hidden'
                                        : 'visible',
                                  }}
                                  onClick={() => {
                                    if (node.srcInFileId !== null) {
                                      handleEmailpopup(
                                        node.documentId,
                                        node.emailId
                                      );
                                    }
                                  }}
                                >
                                  <MailOutlined />
                                  <span className="tooltip">Email</span>
                                </a>
                              )}

                            {/* Document Icon */}
                            <a
                              className={
                                node.id === initialNodeId[0]
                                  ? 'hide'
                                  : 'node-icon'
                              }
                              onClick={() => handleOpenDocumentModal(node)}
                            >
                              <i className="icon icon-file-text2"></i>
                              <span className="tooltip">Document</span>
                            </a>

                            {/* Delete Icon */}
                            {parameter !== 'req-final-quote' &&
                              parameter !== 'rfq-finalized-quote' && (
                                <a
                                  className="node-icon"
                                  style={{
                                    visibility:
                                      node.id === initialNodeId[0]
                                        ? 'hidden'
                                        : 'visible',
                                  }}
                                  onClick={() => {
                                    nodeDeleteHandler(node.quoteId);
                                  }}
                                >
                                  <i className="icon-tree-delete"></i>
                                  <span className="tooltip">Delete</span>
                                </a>
                              )}
                          </div>
                        )}

                      {workFlowEvent &&
                        node.parent !== -1 &&
                        node.parent !== 0 && (
                          <div className="flex-row">
                            <Popover
                              overlayInnerStyle={{
                                marginLeft: '10px',
                                marginTop: '-7px',
                              }}
                              overlayClassName="custom-popover"
                              open={formVisibility[node.id] ? true : false}
                              placement="rightTop"
                              trigger="click"
                              onOpenChange={() =>
                                toggleFormVisibility(node.id, node.quoteId)
                              }
                              title={popoverTitleWithCloseIconRender(node)}
                              content={popoverContentRender(node)}
                            >
                              <a
                                className={
                                  node.id === initialNodeId[0]
                                    ? 'hide'
                                    : 'node-icon'
                                }
                                style={{ marginRight: '3px' }}
                              >
                                <Tooltip
                                  placement="bottom"
                                  overlayStyle={{ fontSize: '9px' }}
                                  overlayInnerStyle={{
                                    borderRadius: '3px',
                                    padding: '3px 4px',
                                    lineHeight: '17px',
                                    minHeight: '10px',
                                  }}
                                  title="Events"
                                >
                                  <i className="icon-file-text"></i>
                                </Tooltip>
                              </a>
                            </Popover>
                          </div>
                        )}
                    </div>
                  </div>
                </>
              );
            }}
          />{' '}
        </div>
      ) : (
        ''
      )}

      {isModal ? (
        <Modal
          className="ModalPoMatrix"
          overlayClassName="ModalOverlay"
          ariaHideApp={false}
          isOpen={isModal}
          // style={{ height: 200 + '%' }}
        >
          <div
            onClick={() => setIsModal(false)}
            className="modal-close icon-close"
          ></div>
          <DocumentViewer
            fileURL={filepath}
            fileType={fileTypeName}
            zoom={'#zoom=80'}
            iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
          />
        </Modal>
      ) : (
        ''
      )}
    </div>
  );
}

export { RfqQuoteTreeview };
