module.exports = {
        name: 'hp-main-host',
        remotes: [
  "mainAuthApp",
  "sourcingApp"
],
    shared: (name, config) => {
        if (name === 'react' || name === 'react-dom') {
          return {
            singleton: true,
            requiredVersion: '18.3.1',
            eager: true
          };
        }
     
      if (name === 'nanoid') {
          return {
            singleton: true,
            requiredVersion: '5.0.9', // Or specify the version you want to share
            eager: true
          };
        }
     
        if (name === 'moment') {
          return {
            singleton: true,
            requiredVersion: '2.30.1', // Or specify the version you want to share
            eager: true
          };
        }
     
        if (name === 'react-router') {
          return {
            singleton: true,
            requiredVersion: '7.0.2', // Or specify the version you want to share
            eager: true
          };
        }
        if (name === 'react-router-dom') {
          return {
            singleton: true,
            requiredVersion: '6.11.2', // Or specify the version you want to share
            eager: true
          };
        }
        if (name === 'react-redux') {
          return {
            singleton: true,
            requiredVersion: '7.2.9', // Or specify the version you want to share
            eager: true
          };
        }
        if (name === 'redux') {
          return {
            singleton: true,
            requiredVersion: '5.0.1', // Or specify the version you want to share
            eager: true
          };
        }
        return undefined; // Default behavior for other dependencies
      },
      };
      