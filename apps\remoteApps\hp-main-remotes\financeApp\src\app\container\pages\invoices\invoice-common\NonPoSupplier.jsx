/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { TextInput, Select } from '../@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { getSupplierNameforComboBox } from '@hp/mainstore';
import { getSuppliersById } from '@hp/mainstore';

const NonPoSupplier = () => {
  const dispatch = useDispatch();
  const { supplierdetails, supplierNameCombo } = useSelector(
    (store) => store.suppliers
  );
  // const [isDisabled, setisDisabled] = React.useState(true);
  const [supplierCombo, setSupplierCombo] = useState();
  const [state, setState] = useState({
    number: '',
    telephone: '',
    name: '',
    mobile: '',
    code: '',
    fax: '',
    email: '',
    website: '',
    primaryAddressId: '',
    primaryCountry: '',
    primaryAddress: '',
    primaryState: '',
    primaryCity: '',
    primaryPostcode: '',
    deliveryAddressId: '',
    deliveryAddress: '',
    deliveryCountry: '',
    deliveryState: '',
    deliveryCity: '',
    deliveryPostcode: '',
  });
  const [supplierAcc, setSupplierAcc] = useState({
    suppAccounts: {
      currency_id: '',
      currency: '',
      bankGlId: '',
      bankgl: '',
      accstatus: '',
      glacc: '',
      gl_acc_id: '',
      ctrl_gl_id: '',
      ctrl_gl_name: '',
      area: '',
      accgroup: '',
      accAreaId: '',
      department: '',
    },
  });

  useEffect(() => {
    dispatch(getSupplierNameforComboBox());
  }, []);

  useEffect(() => {
    if (supplierNameCombo) {
      const Options = supplierNameCombo.value.map((value) => {
        return {
          value: value.supplierId,
          display: value.supplierName,
        };
      });
      setSupplierCombo(Options);
    }
  }, [supplierNameCombo]);

  useEffect(() => {
    if (supplierdetails.value) {
      setState({
        number: supplierdetails.value.number,
        telephone: supplierdetails.value.telephone,
        name: supplierdetails.value.name,
        mobile: supplierdetails.value.mobile,
        code: supplierdetails.value.code,
        fax: supplierdetails.value.fax,
        email: supplierdetails.value.email,
        website: supplierdetails.value.website,
        primaryAddressId: supplierdetails.value.primaryAddressBean.address_id,
        primaryAddress: supplierdetails.value.primaryAddressBean.address,
        primaryCity: supplierdetails.value.primaryAddressBean.city,
        primaryState: supplierdetails.value.primaryAddressBean.state,
        primaryPostcode: supplierdetails.value.primaryAddressBean.post_code,
        primaryCountry: supplierdetails.value.primaryAddressBean.country,
        deliveryAddressId: supplierdetails.value.deliveryAddressBean.address_id,
        deliveryAddress: supplierdetails.value.deliveryAddressBean.address,
        deliveryCity: supplierdetails.value.deliveryAddressBean.city,
        deliveryState: supplierdetails.value.deliveryAddressBean.state,
        deliveryPostcode: supplierdetails.value.deliveryAddressBean.post_code,
        deliveryCountry: supplierdetails.value.deliveryAddressBean.country,
      });
      setSupplierAcc({
        bankgl: supplierdetails.value.bankGlBean.bank_gl_code,
        bankGlId: supplierdetails.value.bankGlBean.bank_gl_id,
        currency_id: supplierdetails.value.supplierAccountsBean.currency_id,
        currency: supplierdetails.value.supplierAccountsBean.currency,
        accstatus: supplierdetails.value.supplierAccountsBean.account_status,
        glacc: supplierdetails.value.glAccountsBean.gl_acc_name,
        gl_acc_id: supplierdetails.value.glAccountsBean.gl_acc_id,
        area: supplierdetails.value.accountAreaBean.acc_area_name,
        accAreaId: supplierdetails.value.accountAreaBean.acc_area_id,
        accgroup: supplierdetails.value.bankGlBean.bank_gl_name,
        department: supplierdetails.value.accountAreaBean.acc_area_desc,
      });
      if (supplierdetails.value.controlGLBean !== null) {
        setSupplierAcc({
          ctrl_gl_id: supplierdetails.value.controlGLBean.ctrl_gl_id
            ? supplierdetails.value.controlGLBean.ctrl_gl_id
            : '',
          ctrl_gl_name: supplierdetails.value.controlGLBean.ctrl_gl_name,
        });
      }
    }
  }, [supplierdetails]);

  const getSupplierId = (e) => {
    dispatch(getSuppliersById(e));
  };
  return (
    <div className="boxed low-contrast">
      <h1 className="page-title">Supplier Information</h1>
      <div className="three-col-layout">
        <Select
          style={{ width: 250 + 'px' }}
          label="Supplier"
          name="supplier"
          className="mb20"
          placeholder="search"
          onChange={(e) => getSupplierId(e.target.value)}
          options={supplierCombo}
        />
      </div>
      <div className="mb20"></div>
      <Tabs className="mb24">
        <TabList>
          <Tab>Details</Tab>
          <Tab>Accounts</Tab>
        </TabList>
        <TabPanel>
          <div className="flex-row two-col-layout">
            <div className="col">
              <TextInput
                label="Number"
                name="number"
                className="mb20"
                value={state.number}
              />
              <TextInput
                label="Name"
                name="name"
                className="mb20"
                value={state.name}
              />
              <TextInput
                label="Code"
                name="code"
                className="mb20"
                value={state.code}
              />
              <TextInput
                label="Email"
                name="email"
                className="mb20"
                value={state.email}
              />
            </div>
            <div className="col">
              <TextInput
                label="Telephone"
                name="telephone"
                className="mb20"
                value={state.telephone}
              />
              <TextInput
                label="Mobile"
                name="mobile"
                className="mb20"
                value={state.mobile}
              />
              <TextInput
                name="fax"
                label="Fax"
                className="mb20"
                value={state.fax}
              />
              <TextInput
                name="website"
                label="Website"
                className="mb20"
                value={state.website}
              />
            </div>
          </div>
          <div className="flex-row two-col-layout">
            <fieldset className="col">
              <legend>Primary Address</legend>
              <TextInput
                name="primaryAddress"
                label="Address"
                className="mb20"
                value={state.primaryAddress}
              />
              <TextInput
                name="primaryPostcode"
                label="Post-Code"
                className="mb20"
                value={state.primaryPostcode}
              />
              <TextInput
                name="primaryCountry"
                label="Country"
                className="mb20"
                value={state.primaryCountry}
              />
              <TextInput
                name="primaryState"
                label="State"
                className="mb20"
                value={state.primaryState}
              />
              <TextInput
                name="primaryCity"
                label="City"
                className="mb20"
                value={state.primaryCity}
              />
            </fieldset>
            <fieldset className="col">
              <legend>Delivery Address</legend>
              <TextInput
                name="deliveryAddress"
                label="Address"
                className="mb20"
                value={state.deliveryAddress}
              />
              <TextInput
                name="deliveryPostcode"
                label="Post-Code"
                className="mb20"
                value={state.deliveryPostcode}
              />
              <TextInput
                name="deliveryCountry"
                label="Country"
                className="mb20"
                value={state.deliveryCountry}
              />
              <TextInput
                name="deliveryState"
                label="State"
                className="mb20"
                value={state.deliveryState}
              />
              <TextInput
                name="deliveryCity"
                label="City"
                className="mb20"
                value={state.deliveryCity}
              />
            </fieldset>
          </div>
        </TabPanel>
        <TabPanel>
          <div className="flex-row two-col-layout">
            <fieldset className="col">
              <legend>Supplier Account</legend>
              {/* <Select
                style={{ width: 250 + "px" }}
                label="Currency"
                name="currency"
                className="mb20"
                onChange={(e) => getCurrency(e.target.value)}
                options={currencyCombo}
              /> */}
              <TextInput
                name="bankgl"
                label="BankGl"
                className="mb20"
                value={supplierAcc.bankgl}
              />
              <TextInput
                name="accstatus"
                label="Account Status"
                className="mb20"
                value={supplierAcc.accstatus}
              />
            </fieldset>
            <fieldset className="col">
              <legend>GL Settings</legend>
              <TextInput
                name="glacc"
                label="Gl Account"
                className="mb20"
                value={supplierAcc.glacc}
              />
              <TextInput
                name="ctrl_gl_name"
                label="Control GL"
                className="mb20"
                value={supplierAcc.ctrl_gl_name}
              />
            </fieldset>
          </div>
          <div className="mb20"></div>
          <div className="flex-row two-col-layout">
            <fieldset className="col">
              <legend>Account Dimensions</legend>
              <TextInput
                name="area"
                label="Area"
                className="mb20"
                value={supplierAcc.area}
              />
              <TextInput
                name="accgroup"
                label="Account Group"
                className="mb20"
                value={supplierAcc.accgroup}
              />
              <TextInput
                name="department"
                label="Department"
                className="mb20"
                value={supplierAcc.department}
              />
            </fieldset>
            <fieldset className="col">
              <legend>Purchase Order/Approval Settings</legend>
              <TextInput
                name="Require Order"
                label="Require Order"
                className="mb20"
              />
              <TextInput
                name="Require Approval"
                label="Require Approval"
                className="mb20"
              />
              <TextInput name="Approver" label="Approver" className="mb20" />
            </fieldset>
          </div>
        </TabPanel>
      </Tabs>
      <div className="flex-row two-col-layout">
        <Select
          style={{ width: 250 + 'px' }}
          label="Requestor"
          name="requestor"
          className="mb20"
          // onChange={(e) => getCurrency(e.target.value)}
          // options={currencyCombo}
        />
        <Select
          style={{ width: 250 + 'px' }}
          label="Manager"
          name="manager"
          className="mb20"
          // onChange={(e) => getCurrency(e.target.value)}
          // options={currencyCombo}
        />
      </div>
    </div>
  );
};

export { NonPoSupplier };
