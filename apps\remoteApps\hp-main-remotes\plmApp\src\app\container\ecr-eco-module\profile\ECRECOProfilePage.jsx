/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 27-09-2024 09:40:13
 * @modify date 01-10-2024 12:20:40
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import {
  CommonSpinner,
  ProcessFlow,
  Profileform,
  Header,
  RightSide,
} from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import { useConfirm } from '@hp/components';
import {
  getECRECOProfileDetailsForm,
  saveECRECOProfileDetailsForm,
  getECRECOProfileWorkFlow,
  activateECRECOProfile,
  showDialog,
  pdmConstants,
} from '@hp/mainstore';
import './ProfilePage.scss';

import { useAppRouterDom } from '@hp/utils';
import { useLocation } from 'react-router-dom';
import { globalutils } from '@hp/components';
import { pdmResetStateField } from '@hp/mainstore';
const ECRECOProfilePage = () => {
  let user = globalutils.getDataFromStorage('all');
  const userId = user.userId;
  const { domParameters, navigate } = useAppRouterDom();
  // const menuData = domParameters?.menuData || '';
  // const submenu = domParameters?.submenu || '';
  // const parameter = domParameters?.parameters || '';
  // const state = props?.location?.state ? props.location.state : null;
  const location = useLocation();
  const data = location.state || {};
  const datas = data?.data || '';
  const clientId = data?.clientId ?? null;
  const profileId = domParameters?.profileId ?? null;
  const category = data?.category || datas?.categoryCode || '';
  const readOnly = data?.readOnly || '';
  const displayName = data?.displayName || '';
  const [profileHeading, setProfileHeading] = useState('');
  const [basicInfo, setBasicInfo] = useState('');
  const [processControl, setProcessControl] = useState('');
  const [highLight, setHighLight] = useState('profileDetails');
  const [warnings, setWarnings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processFlow, setProcessFlow] = useState(null);
  const [hideRightSide, setHideRightSide] = useState(false);
  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  const {
    ecrEcoProfileDetails,
    ecrEcoProfileSaveRes,
    ecrEcoProfileActivationRes,
    ecrEcoProfileWorkFlowDetails,
  } = useSelector((store) => store.pdm);

  useEffect(() => {
    if (clientId && profileId) {
      dispatch(
        getECRECOProfileDetailsForm({
          profileId: profileId,
          userId: userId,
          category: category,
          clientId: clientId,
        })
      );
    }
    return () => {
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoProfileDetails' }));
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoProfileSaveRes' }));
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoProfileActivationRes' }));
      dispatch(
        pdmResetStateField({ fieldName: 'ecrEcoProfileWorkFlowDetails' })
      );
      dispatch({
        type: pdmConstants.ECR_ECO_PROFILE_SAVE_SUCCESS,
        payload: null,
      });
      dispatch({
        type: pdmConstants.ECR_ECO_PROFILE_WORKFLOW_SUCCESS,
        payload: null,
      });
      dispatch({
        type: pdmConstants.ECR_ECO_PROFILE_DELETE_SUCCESS,
        payload: null,
      });
      dispatch({
        type: pdmConstants.ECR_ECO_PROFILE_ACTIVATE_SUCCESS,
        payload: null,
      });
      dispatch({
        type: pdmConstants.ECR_ECO_PROFILE_DETAILS_SUCCESS,
        payload: null,
      });
    };
  }, []);

  useEffect(() => {
    if (ecrEcoProfileDetails?.value?.userNotificationDto) {
      funcToSetResponseMessage(
        ecrEcoProfileDetails.value.userNotificationDto?.type,
        ecrEcoProfileDetails.value.userNotificationDto?.text
      );
      setHideRightSide(true);
      setLoading(false);
    } else if (ecrEcoProfileDetails?.value?.profileForm?.length) {
      ecrEcoProfileDetails.value.profileForm.map((form) => {
        if (form.uniqueKey === 'profileHeading') {
          setProfileHeading(form);
        } else if (form.uniqueKey === 'profileDetails') {
          setBasicInfo(form);
        } else if (form.uniqueKey === 'profileProCtrls') {
          setProcessControl(form);
          let tempProcessFlowId =
            form?.formSubDetailsInternalDTOList
              ?.find((entry) => entry.uniqueKey === 'workflow')
              ?.formSubDetailsInternalDTOList?.find(
                (item) => item.uniqueKey === 'workflowSettings'
              )?.value?.id ?? null;
          tempProcessFlowId
            ? handleProcessFlow(tempProcessFlowId)
            : setProcessFlow(null);
        }
      });
      const onScroll = () => {
        const buttonList = ['profileDetails', 'prApprovalSettings'];
        var currentHighlight = 'profileDetails';
        buttonList.forEach((e) => {
          var element = document?.getElementById(e);
          var top = element?.getBoundingClientRect().top - 100;

          if (top < 0) {
            currentHighlight = e;
          }
        });
        setHighLight(currentHighlight);
      };
      setLoading(false);
      window.removeEventListener('scroll', onScroll);
      window.addEventListener('scroll', onScroll, { passive: true });
      return () => window.removeEventListener('scroll', onScroll);
    } else if (ecrEcoProfileDetails?.value?.profileValidationResultDTO) {
      setWarnings(ecrEcoProfileDetails.value.profileValidationResultDTO);
    }
  }, [ecrEcoProfileDetails]);

  useEffect(() => {
    if (ecrEcoProfileWorkFlowDetails?.value) {
      setProcessFlow({
        nodes:
          ecrEcoProfileWorkFlowDetails?.value?.pdmProfileWorkflowRenderingDtos,
        edges: ecrEcoProfileWorkFlowDetails?.value?.pdmProfileWorkflowEdgeDtos,
      });
    }
  }, [ecrEcoProfileWorkFlowDetails]);

  useEffect(() => {
    if (ecrEcoProfileSaveRes?.value?.profileValidationResultDTO) {
      setWarnings(ecrEcoProfileSaveRes.value.profileValidationResultDTO);

      let isInValid =
        ecrEcoProfileSaveRes.value.profileValidationResultDTO
          .validationResultFlag;
      if (isInValid) {
        funcToSetResponseMessage(
          'error',
          ecrEcoProfileSaveRes.value.profileValidationResultDTO
            .validationResultMessage || ''
        );
        return;
      }

      ecrEcoProfileActivation(
        ecrEcoProfileSaveRes.value.profileId ?? null,
        ecrEcoProfileSaveRes.value.profileValidationResultDTO
          ?.validationResultMessage,
        ecrEcoProfileSaveRes.value?.message ?? null
      );
    }
    return () => {
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoProfileSaveRes' }));
    };
  }, [ecrEcoProfileSaveRes]);

  useEffect(() => {
    if (ecrEcoProfileActivationRes?.value) {
      if (
        ecrEcoProfileActivationRes.value.tabsUpdateSuccessFlag &&
        ecrEcoProfileActivationRes.value.tabsUpdateSuccessMessage
      ) {
        funcToSetResponseMessage(
          'success',
          ecrEcoProfileActivationRes.value.tabsUpdateSuccessMessage
        );

        const timeout = setTimeout(() => {
          navigate(-1);
          clearTimeout(timeout);
        }, 2000);
      }
      // else if (
      //   ecrEcoProfileActivationRes.value?.emptyCheckFlag &&
      //   ecrEcoProfileActivationRes.value?.emptyCheckMessage
      // ) {
      //   emptyValidationHandler();
      // }
    }

    return () => {
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoProfileActivationRes' }));
    };
  }, [ecrEcoProfileActivationRes]);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  const SaveProfile = async () => {
    let form = [basicInfo, processControl];
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (!isConfirmed) return;
    dispatch(
      saveECRECOProfileDetailsForm({
        profileId: profileId,
        clientId: clientId,
        userId: userId,
        category: category,
        form: form,
      })
    );
  };

  const ecrEcoProfileActivation = async (profileIdNew, msg, cancelMessage) => {
    if (profileId) {
      const isConfirmed = await confirm(
        msg || 'Activate Profile?',
        'Yes',
        'No'
      );
      if (!isConfirmed) {
        if (cancelMessage) funcToSetResponseMessage('success', cancelMessage);
        return;
      }
      let form = [basicInfo, processControl];
      dispatch(
        activateECRECOProfile({
          profileId: profileIdNew,
          clientId: clientId,
          userId: userId,
          category: category,
          isEdit: profileId === '0' ? 'N' : 'Y',
          form: form,
        })
      );
    }
  };

  function handleProcessFlow(approvalWorkflowMasterId) {
    if (approvalWorkflowMasterId)
      dispatch(getECRECOProfileWorkFlow(approvalWorkflowMasterId));
  }

  return (
    <>
      <CommonSpinner visible={loading} />
      {profileHeading ? (
        <Header
          displayName={displayName}
          profileHead={profileHeading}
          profileId={Number(profileId)}
        />
      ) : (
        ''
      )}

      <div className="inner-main">
        <div className="left" disabled={readOnly}>
          {basicInfo ? (
            <Profileform
              profileform={basicInfo}
              styleName={'basic-form'}
              formHandler={(data) => setBasicInfo(data)}
            />
          ) : (
            ''
          )}

          <div className="process-control-inner" style={{ maxWidth: '1000px' }}>
            {processControl ? (
              <Profileform
                hideApprovalAmount
                showProcessFlow
                isEcrEcoProfile
                getProcessFlowId={(id) => handleProcessFlow(id)}
                profileform={processControl}
                formHandler={(data) => {
                  setProcessControl(() => data);
                }}
                styleName={'flex-row-wrap'}
                readOnly={readOnly}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    padding: '20px',
                    transition: '300ms',
                    boxShadow: '0 1px 3px #c4bebe',
                  }}
                >
                  <div
                    style={{
                      width: '100%',
                      maxHeight: '600px',
                      display: 'inline-block',
                      overflowY: 'clip',
                    }}
                  >
                    <ProcessFlow
                      dataFlowResponse={processFlow}
                      fixedCtrlWidth
                    />
                  </div>
                </div>
              </Profileform>
            ) : (
              ''
            )}
          </div>
        </div>
        {!hideRightSide ? (
          <div className="right">
            <RightSide
              errors={warnings}
              highlight={highLight}
              saveHandler={SaveProfile}
              active={readOnly}
              isPr={true}
              processControlKey={'profileProCtrls'}
            />
          </div>
        ) : (
          ''
        )}
      </div>
    </>
  );
};

export { ECRECOProfilePage };
