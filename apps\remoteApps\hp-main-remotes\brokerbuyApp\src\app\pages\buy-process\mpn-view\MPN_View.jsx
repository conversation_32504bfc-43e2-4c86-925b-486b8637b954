/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> sherlin
 * @email <EMAIL>
 */
import React, { useState, useLayoutEffect } from 'react';
import { Input, Button, useConfirm } from '@hp/components';

import { useDispatch, useSelector } from 'react-redux';
import FormActions from './MPN-form-actions';
import { CommonSpinner } from '@hp/components';
import cloneDeep from 'lodash.clonedeep';
import {
  bbForms,
  ClientList,
  bbMPNviewSearchSubList,
  bbMPNDelete,
  bbMPNviewSearch,
} from '@hp/mainstore';
import { dataTableServiceProvider } from '@hp/components';

const MpnView = (props) => {
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const {
    bbFormDetails,
    mpnAddSuccess,
    loading,
    mpnFiles,
    mpnSubFiles,
    mpnEditSuccess,
    MPNDelete,
  } = useSelector((store) => store.bb);
  //=========================================Local States Declaration Section Begins ===========================================//
  const [formInputs, setFormInputs] = useState([]);
  const [action, setAction] = useState('');
  const [notify, setnotify] = useState(null);

  const [isSearch, setIsSearch] = useState({
    isSearchDisabled: true,
    isClientValue: '',
    isDateValue: '',
    addOnchange: false,
  });
  const [addForm, setAddForm] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [productFileMasterId, setProductFileMasterId] = useState(null);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [conditionalRowStylesTwo, setConditionalRowStylesTwo] = useState([]);
  const [errorFlag, seterrorFlag] = useState(false);
  const [stockError, setStockError] = useState(false);
  const [priceError, setPriceError] = useState(false);

  const [productDetailId, setProductDetailId] = useState('');

  //=========================================Local States Declaration Section Ends===========================================//
  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Component did mount First call for this component.
     */

    dispatch(bbForms('subscribe'));
    dispatch(ClientList('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
      dispatch(ClientList('unSubscribe'));
    };
  }, []);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: form response.
     */

    if (
      bbFormDetails &&
      bbFormDetails !== undefined &&
      bbFormDetails.value &&
      bbFormDetails.value.length
    ) {
      var clientID = null;
      var selectedDate = null;
      const form = bbFormDetails?.value;
      if (form && form.length) {
        form.map((items) => {
          if (items.uniqueKey === 'page-title') {
            if (items.parameters === 'fileView') {
              const headerControls = form.filter(
                (e) => e.uniqueKey === 'page-title'
              );
              if (headerControls && headerControls.length) {
                headerControls[0].formSubDetailsInternalDTOList &&
                  headerControls[0].formSubDetailsInternalDTOList.length &&
                  headerControls[0].formSubDetailsInternalDTOList.map(
                    (element) => {
                      if (element.uniqueKey === 'client-control') {
                        clientID = element.value;
                      } else if (element.uniqueKey === 'date-control') {
                        selectedDate = element.value;
                      }
                    }
                  );
                setFormInputs(bbFormDetails.value);
                setIsSearch({
                  ...isSearch,
                  isDateValue: selectedDate,
                  isClientValue: clientID,
                });
              }
            }
          }
        });
      }
    }
  }, [bbFormDetails]);

  useLayoutEffect(() => {
    if (addForm) {
      setnotify(null);
    }
  }, [addForm]);

  // useLayoutEffect(() => {
  //   /**
  //    * <AUTHOR> sherlin A
  //    * @email <EMAIL>
  //    * @description: Component Refresh.
  //    */
  //   if (
  //     mpnAddSuccess &&
  //     mpnAddSuccess.httpStatus &&
  //     mpnAddSuccess.httpStatus === 'OK'
  //   ) {
  //     var timerId = setTimeout(() => {
  //       setAddForm(false);
  //     }, 3000);
  //     dispatch(bbAction.bbForms('subscribe'));

  //     return () => {
  //       clearTimeout(timerId);
  //     };
  //   }
  // }, [mpnAddSuccess, mpnEditSuccess]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: getting Main list based on client Search.
     */
    if (mpnFiles?.value) {
      const stateValues = [...formInputs];
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'fileTable'
      );
      if (foundIndex !== -1) {
        if (mpnFiles?.value && mpnFiles?.value.length) {
          stateValues[foundIndex].value = mpnFiles.value;
        } else {
          let subIndex = stateValues.findIndex(
            (e) => e.uniqueKey === 'mpndetails'
          );
          stateValues[foundIndex].value = [];
          stateValues[subIndex].value = [];
        }

        setFormInputs(stateValues);
        setProductFileMasterId(null);
      }
    }
  }, [mpnFiles]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: getting Sublist based on client Search.
     */
    if (mpnSubFiles?.value) {
      const stateValues = [...formInputs];
      const foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'mpndetails'
      );

      if (foundIndex !== -1) {
        // Create a new object with updated value to avoid mutation
        const updatedItem = {
          ...stateValues[foundIndex],
          value: mpnSubFiles.value,
        };
        stateValues[foundIndex] = updatedItem;

        setFormInputs(stateValues);
      }
    }
  }, [mpnSubFiles]);

  useLayoutEffect(() => {
    /**
     * @description: Clearing the values in add Dialogbox.
     */

    if (!addForm) {
      setFormInputs((prevFormInputs) => {
        // Create a new array of formInputs (immutability)
        const updatedFormInputs = prevFormInputs.map((input) => {
          if (input.uniqueKey === 'mpn-sub-table') {
            return {
              ...input, // Create a new object
              formSubDetailsInternalDTOList:
                input.formSubDetailsInternalDTOList?.map((el) => ({
                  ...el, // Create a new object for each item
                  value: '', // Reset the value
                })),
            };
          }
          return input; // Return the same object for other inputs
        });

        return updatedFormInputs; // Return the updated state
      });
    }
  }, [addForm]); // No 'formInputs' dependency to avoid re-triggering the effect

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description:subTable Refresh.
     */
    if (mpnAddSuccess) {
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    } else if (mpnEditSuccess) {
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    } else if (MPNDelete) {
      setAddForm(false);
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
  }, [mpnAddSuccess, mpnEditSuccess, MPNDelete]);

  // const DisplayTitle = (row, key) => {

  //   return (
  //     <div className="display-title custom-overflow" title={row[key]}>
  //       {' '}
  //       {row[key]}{' '}
  //     </div>
  //   );
  // };
  const editHandler = (data) => {
    /**
     * <AUTHOR> Augustine
     * @email <EMAIL>
     * @description: Function to edit row data.
     */
    const formData = formInputs
      .filter((el) => el.uniqueKey === 'mpn-sub-table')[0]
      .formSubDetailsInternalDTOList?.map((list) => {
        if (list.uniqueKey === 'mpn_no') {
          return { ...list, value: data.mpn };
        } else if (list.uniqueKey === 'ipn_no') {
          return { ...list, value: data.productName };
        } else if (list.uniqueKey === 'mfg') {
          return { ...list, value: data.mfgName };
        } else if (list.uniqueKey === 'stdprice') {
          return { ...list, value: data.stdPrice };
        } else if (list.uniqueKey === 'quantity') {
          return { ...list, value: data.quantity };
        } else if (list.uniqueKey === 'desc') {
          return { ...list, value: data.description };
        }
      });

    let newForm = formInputs.map((form) => {
      if (form.uniqueKey === 'mpn-sub-table') {
        return { ...form, formSubDetailsInternalDTOList: formData };
      } else return form;
    });

    setFormInputs(newForm);
    setProductDetailId(data.productFileDetailsId);
    setAddForm(true);
  };
  const deleteHandler = async (data) => {
    /**
     * <AUTHOR> Augustine
     * @email <EMAIL>
     * @description: Function to delete row data.
     */

    const isConfirmed = await confirm('Are you sure you want to delete');
    if (isConfirmed) dispatch(bbMPNDelete(data.productFileDetailsId));
  };

  const mpnViewTableCell = (rowData, key, row) => {
    if (rowData.selector === 'editIcon') {
      return function displayCell(row) {
        return (
          <div
            className="icon-edit-button"
            onClick={() => {
              editHandler(row);
              setAction('edit');
            }}
          ></div>
        );
      };
    }
    if (rowData.selector === 'deleteIcon') {
      return function displayCell(row) {
        return (
          <div
            className="icon-2-trash"
            onClick={() => {
              deleteHandler(row);
            }}
          ></div>
        );
      };
    }
    if (row) {
      return (
        <div className="display-title custom-overflow" title={row[key]}>
          {' '}
          {row[key]}{' '}
        </div>
      );
    }

    // if (rowData.selector === "status") {
    //   return function displayCell(row) {
    //     const statusColor =
    //       row.status === "Sent"
    //         ? "green"
    //         : row.status === "Draft"
    //         ? "yellow"
    //         : row.status === "Failed"
    //         ? "red"
    //         : "";
    //     return (
    //       <div className={`react-tbl-custom-status-cell ${statusColor}`}>
    //         {row?.status ? row.status : ""}
    //       </div>
    //     );
    //   };
    // }
    // return DisplayTitle(rowData, rowData.selector);
  };

  const forms = (formControl) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function to Render Form Element.
     */
    return formControl
      ? formControl.map((element, index) => {
          if (element.uniqueKey === 'page-title') {
            return (
              <div key={index}>
                <Input formType={element} />
                <div
                  className="two-col-layout-right-30"
                  style={{ marginBottom: '-40px' }}
                >
                  <div className="three-col-layout ">
                    {forms(element.formSubDetailsInternalDTOList)}
                  </div>
                  <div></div>
                </div>
              </div>
            );
          } else {
            if (element.uniqueKey === 'mpndetails') {
              return (
                <div key={index}>
                  {isShow && (
                    <div style={{ float: 'right', marginBottom: 10 + 'px' }}>
                      <Button
                        className="small mb8 outline add-button-custom flex-row vam"
                        onClick={() => {
                          setProductDetailId('');
                          setAddForm(true);
                          setAction('add');
                        }}
                      >
                        <i className="icon-add-button "> </i>Add Parts
                      </Button>
                    </div>
                  )}

                  <Input
                    formType={element}
                    dataTableEventHandler={(e) => handleRowClick(e)}
                    conditionalRowStyles={conditionalRowStyles}
                    // isCopy={true}
                    dataTableColumn={
                      element.formSubDetailsInternalDTOList &&
                      Array.isArray(element.formSubDetailsInternalDTOList) &&
                      element.formSubDetailsInternalDTOList.length
                        ? element.formSubDetailsInternalDTOList.map((value) => {
                            return {
                              width: value.displayWidth
                                ? value.displayWidth
                                : '',
                              name: value.displayName ? value.displayName : '',
                              selector: value.selector ? value.selector : '',
                              cell: mpnViewTableCell(value, value.selector),
                              // cell: (row) =>
                              //   mpnViewTableCell(value, value.selector, row),
                            };
                          })
                        : []
                    }
                  />
                </div>
              );
            }
            return (
              <Input
                onChangeHandler={(type, event) =>
                  handlers[element.onChangeFunction](type, event, 'search')
                }
                // isCopy={true}
                dataTableColumn={
                  element.formSubDetailsInternalDTOList &&
                  element.formSubDetailsInternalDTOList.length
                    ? element.formSubDetailsInternalDTOList.map((rowData) => {
                        return {
                          width: rowData.displayWidth
                            ? rowData.displayWidth
                            : '',
                          name: rowData.displayName ? rowData.displayName : '',
                          selector: rowData.selector ? rowData.selector : '',
                          cell: dataTableServiceProvider.bbModuleCustomTableCell(
                            rowData
                          ),
                        };
                      })
                    : []
                }
                dataTableEventHandler={handlers.handleRowClick}
                conditionalRowStyles={conditionalRowStylesTwo}
                isEditable="notShowing"
                formType={element}
                key={index}
              />
            );
          }
        })
      : null;
  };

  const onChangeHandler = (type, event, action) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for user input content change.
     */

    // let validatedElement = formValidationUtil.isFormcontrolValidDynamic(
    //   event,
    //   type
    // );
    // const stateValues = [...formInputs];
    // let tempArray = cloneDeep(stateValues);
    let tempArray = cloneDeep(formInputs);
    if (action === 'add') {
      tempArray.map((entry) => {
        entry.uniqueKey === 'mpn-sub-table' &&
          entry.formSubDetailsInternalDTOList.map((element) => {
            if (element.uniqueKey === type.uniqueKey) {
              element.value = event.target.value;
            }
          });
      });

      // let foundIndex = stateValues.findIndex(
      //   (e) => e.uniqueKey === 'mpn-sub-table'
      // );
      // tempArray.map((element) => {
      //   if (element.uniqueKey === 'mpn-sub-table') {
      //     element &&
      //       element.formSubDetailsInternalDTOList.map((items, elementIndex) => {
      //         if (items.uniqueKey === type.uniqueKey) {
      //           element.formSubDetailsInternalDTOList.splice(
      //             elementIndex,
      //             1,
      //             validatedElement
      //           );
      //           // items.value = event?.target?.value
      //           //   ? event.target.value
      //           //   : event.value;
      //         }
      //       });
      //   }
      // });
      // if (!foundIndex !== -1) {
      //   const subIndex = stateValues[
      //     foundIndex
      //   ].formSubDetailsInternalDTOList.findIndex(
      //     (e) => e.uniqueKey === type.uniqueKey
      //   );
      //   if (
      //     stateValues[foundIndex].formSubDetailsInternalDTOList[subIndex]
      //       .uniqueKey === 'stdprice'
      //   ) {
      //     stateValues[foundIndex].formSubDetailsInternalDTOList[
      //       subIndex
      //     ].value = parseFloat(event.target.value);
      //   } else if (
      //     stateValues[foundIndex].formSubDetailsInternalDTOList[subIndex]
      //       .uniqueKey === 'quantity'
      //   ) {
      //     stateValues[foundIndex].formSubDetailsInternalDTOList[
      //       subIndex
      //     ].value = parseFloat(event.target.value);
      //   } else {
      //     stateValues[foundIndex].formSubDetailsInternalDTOList[
      //       subIndex
      //     ].value = event.target.value;
      //   }
      // }
    } else {
      if (type.uniqueKey === 'client-control') {
        tempArray[0].formSubDetailsInternalDTOList[0].value =
          event.target.value;
        setIsSearch({
          ...isSearch,
          isClientValue: event.target.value,
        });
      } else if (type.uniqueKey === 'date-control') {
        tempArray[0].formSubDetailsInternalDTOList[1].value = event;
        setIsSearch({
          ...isSearch,
          isDateValue: event,
        });
      }
    }

    const searchButtonIndex =
      tempArray[0].formSubDetailsInternalDTOList.findIndex(
        (el) => el.uniqueKey === 'button-search'
      );
    // const clearButtonIndex = stateValues[0].formSubDetailsInternalDTOList.findIndex(
    //   (el) => el.uniqueKey === "clear-button"
    // );
    if (searchButtonIndex !== -1) {
      tempArray[0].formSubDetailsInternalDTOList[
        searchButtonIndex
      ].disableFlag = 'F';
    }
    // if (clearButtonIndex !== -1) {
    //   stateValues[0].formSubDetailsInternalDTOList[
    //     clearButtonIndex
    //   ].disableFlag = "F";
    // }

    setFormInputs(tempArray);
    // seterrorFlag(validatedElement.errorFlag);

    // if (validatedElement && validatedElement.uniqueKey === 'quantity') {
    //   setStockError(validatedElement.errorFlag);
    // } else if (validatedElement && validatedElement.uniqueKey === 'stdprice') {
    //   setPriceError(validatedElement.errorFlag);
    // }
  };

  const handleRowClick = (type, event) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      type.productFileDetailsId,
      'productFileDetailsId'
    );

    const styleAttributeTwo = dataTableServiceProvider.conditionalDataTableRow(
      type.productFileMasterId,
      'productFileMasterId'
    );
    setConditionalRowStyles(styleAttribute);
    setConditionalRowStylesTwo(styleAttributeTwo);
    setProductFileMasterId(type.productFileMasterId);
    setIsShow(true);

    dispatch(
      bbMPNviewSearchSubList({
        masterID: type.productFileMasterId,
        reqType: 'subscribe',
      })
    );
  };

  function searchMPN() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for Search.
     */
    const stateValues = [...formInputs];
    let tempArray = cloneDeep(stateValues);

    if (isSearch.isSearchDisabled) {
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
    tempArray.map((formDetails) => {
      if (formDetails.uniqueKey === 'mpndetails') {
        formDetails.value = null;
      }
    });
    setFormInputs(tempArray);
  }
  function onClearFunction() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: to clear all selected controls.
     */
    if (!isSearch.isSearchDisabled) {
      var updatedState = [...formInputs];
      updatedState.map((input) => {
        input.value = null;
        input.formSubDetailsInternalDTOList.map((subInput) => {
          subInput.value = null;
          if (subInput.uniqueKey === 'button-search') {
            subInput.disableFlag = 'T';
          }
          if (subInput.uniqueKey === 'clear-button') {
            subInput.disableFlag = 'T';
          }
        });
      });
    }
  }

  const handlers = {
    onChangeHandler,
    handleRowClick,
    searchMPN,
    onClearFunction,
  };

  return (
    <>
      {/* Loader for entire Component */}
      <CommonSpinner visible={loading} />

      {/* rendering Form Controls */}
      <div> {formInputs && formInputs.length ? forms(formInputs) : ''}</div>

      {/* child component for ADD UPDATE DELETE in Uploaded MPN LIST*/}
      <FormActions
        isClose={(close) => setAddForm(close)}
        isOpen={addForm}
        formInputs={formInputs}
        id={productDetailId}
        productFileMasterId={productFileMasterId}
        eventHandlers={handlers}
        action={action}
        isnotify={notify}
        errorFlag={errorFlag}
        stockError={stockError}
        priceError={priceError}
      />
    </>
  );
};
export { MpnView };
