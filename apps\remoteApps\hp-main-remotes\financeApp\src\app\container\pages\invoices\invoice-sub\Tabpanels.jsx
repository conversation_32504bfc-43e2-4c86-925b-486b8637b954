import { Tabs, Empty } from 'antd';
import Docviewer from './docviewer';
import { Tooltip } from 'react-tooltip';

import React, { useEffect, useState } from 'react';
import CreditNote from '../invoice-common/credit_note/CreditNote';
import { useDispatch } from 'react-redux';
// import { FetchDocFromServer } from '@hp/mainstore';
import { utils } from '@hp/utils';
const { TabPane } = Tabs;

export function Tabpanels(props) {
  const {
    reactDocViewerProp,
    getAnnotations,
    zoomHandler,
    fileViewer,
    setModelClose,
    attachmentTabs,
  } = props.tabsData;
  const dispatch = useDispatch();

  const [activeTabKey, setActiveTabKey] = useState('0');
  const [distoolTip, setDistoolTip] = useState(false);

  useEffect(() => {
    let timerId = setTimeout(() => {
      setDistoolTip(false);
      return () => {
        clearTimeout(timerId);
      };
    }, 20000);
  }, [distoolTip]);

  const handleTabChange = (key) => {
    setActiveTabKey(key);
  };

  const redirect = () => {
    if (activeTabKey !== null && attachmentTabs?.attachments) {
      const file = attachmentTabs?.attachments.find(
        (item) =>
          item.id ===
          (activeTabKey === '0' ? Number('1') : Number(activeTabKey))
      );

      if (file) {
        utils.redirectToPDF(file.filePath, dispatch);
        utils.redirectToPDF(file.filePath, dispatch);
      }
    }
  };

  function MouseOver(event) {
    event.target.style.color = '#3c75bc';
    document.body.style.cursor = 'pointer';
  }
  function MouseOut(event) {
    event.target.style.color = '';
    document.body.style.cursor = 'default';
  }
  const disableToolTip = () => {
    setDistoolTip(false);
  };

  const attachmentView = (
    <div style={{ display: 'flex' }}>
      {fileViewer === 'reactDocViewer' ? (
        <>
          {' '}
          <span
            data-tooltip-id="extraction-info"
            className="icon-notification mr8  "
            style={{
              cursor: 'pointer',
              fontSize: '13px',
              color: '#c74646',
              padding: '4px',
            }}
          ></span>
          <Tooltip
            clickable
            isOpen={distoolTip}
            setIsOpen={(val) => setDistoolTip(val)}
            id="extraction-info"
            place="top"
            className="extraction-info-tool-tip"
            style={{
              color: '#20202a',
              backgroundColor: '#f3f3f3',
              fontWeight: 'normal',
              fontSize: '13px',
              zIndex: 9999,
              opacity: 1,
              WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
              border: '1px solid #E8E8E8',
            }}
          >
            <b style={{ fontSize: '15px' }}>Note:</b>
            <div
              onClick={disableToolTip}
              onMouseOver={MouseOver}
              onMouseOut={MouseOut}
              className="icon-modal-close flex-row fr"
              style={{ fontSize: '20px' }}
            ></div>
            <ul>
              <li>
                <b>How to select an area.</b>{' '}
              </li>
              <li>
                Step 1: Select an input field from the screen (E.g. text box,
                dropdown).
              </li>

              <li>
                Step 2: Use your mouse to draw an area over the content on the
                document. This will map the selected area content to the input
                box you chose in Step 1.
              </li>
              <li>
                <b>How to delete an area.</b>{' '}
              </li>

              <li>
                To delete the selected area, click on the box drawn over the
                content on the document.
              </li>
            </ul>
          </Tooltip>
        </>
      ) : (
        ''
      )}
      <div
        onClick={redirect}
        style={{ cursor: 'pointer' }}
        title="Click to open in new Tab"
      >
        <span
          style={{ cursor: 'pointer !important' }}
          className="icon-attachment"
        ></span>
      </div>{' '}
    </div>
  );

  return (
    <Tabs
      activeKey={activeTabKey}
      onChange={handleTabChange}
      defaultActiveKey={'0'}
      tabBarExtraContent={attachmentView}
      tabBarStyle={{ marginTop: '-20px', fontFamily: 'Roboto' }}
    >
      {attachmentTabs && attachmentTabs?.attachments ? (
        attachmentTabs?.attachments.map((tabData, index) => {
          return (
            <TabPane className="th " tab={tabData.label} key={index}>
              <div style={{ height: '100vh' }}>
                {tabData?.filePath ? (
                  <Docviewer
                    reactDocprop={reactDocViewerProp}
                    actionHandlers={{ getAnnotations, zoomHandler }}
                    fileURL={tabData.filePath}
                    fileType={fileViewer}
                    zoom={'#zoom=100'}
                    fullScreen={true}
                    setModelClose={setModelClose}
                  />
                ) : (
                  <CreditNote
                    invData={
                      tabData?.formDetailsDto?.formSubDetailsInternalDTOList ||
                      []
                    }
                    Active={false}
                  />
                )}
              </div>
            </TabPane>
          );
        })
      ) : (
        <Empty />
      )}
    </Tabs>
  );
}
