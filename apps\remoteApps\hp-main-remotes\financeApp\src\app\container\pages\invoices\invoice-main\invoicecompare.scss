@import '@hp/styles/variables.scss';
.compare-layout-mask {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
  transition: 300ms;
}

.padding-left-20 {
  padding-left: 20;
}

div[disabled] {
  pointer-events: none;
  opacity: 1;
}

.view-switcher {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  justify-content: flex-end;

  .label {
    font-size: 8px;
    line-height: 16px;
    margin-right: 8px;
    font-weight: bold;
    display: inline-block;
    text-transform: uppercase;
  }

  span {
    cursor: pointer;
    font-size: 20px;
    margin-right: 8px;
    transition: 300ms;
    display: inline-block;

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      color: $primary_color;
    }

    &.active {
      opacity: 0.5;
      transition: 300ms;
    }
  }
}
.richContentContainer {
  transition: 300ms;

  img {
    display: block;
    width: 100%;
    height: auto;
    object-fit: contain;
  }
}

.boxed {
  padding: 20px;
  margin-right: 20px;
  background: #fff;
  transition: 300ms;
  box-shadow: 0 1px 3px #c4bebe;

  &.low-contrast {
    background-color: #fffff0;
  }

  &.disable-form,
  &.low-contrast.disable-form {
    background-color: #fff;
  }

  &:nth-child(2n + 0) {
    margin-right: 0;
  }
}

.custom-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

html body .boxed.disable-form {
  .display-title {
    padding: 10px 0;
  }

  .input-datepicker input:disabled,
  .input-text input:disabled,
  .input-text input {
    border: 0;
    padding: 0;
    color: black;
    font-size: 14px;
    background: transparent;
    border-bottom: 2px solid #00000010;
  }

  .input-datepicker .icon-calendar {
    &:before {
      display: none;
    }
  }

  .input-textarea textarea {
    border: 0;
    padding: 0;
    color: black;
    font-size: 14px;
    background: transparent;
    border-bottom: 2px solid #00000010;
  }

  .custom-select-wrap .custom-select {
    border: 0;
    padding: 0;
    background: transparent;
    border-bottom: 2px solid #00000010;

    select {
      border: 0;
      padding: 0;
      font-size: 16px;
      background-color: transparent;
    }

    &:before {
      display: none;
    }
  }
}

.viewHolder {
  transition: 300ms;
}

.view-1,
.view-2,
.view-3,
.view-4 {
  .compare-layout-mask {
    > * {
      &.amount-field {
        flex: 0 0 auto;
        margin-right: 0;
        width: calc((100% - 252px) / 2);
      }

      &.auto-adjust {
        flex: 0 0 auto;
        width: auto;

        &.flex-grow {
          flex-grow: 1;
        }

        &.mr0 {
          margin-right: 0;
        }
      }
    }
  }
}

.compare-layout-mask {
  > * {
    transition: 300ms;

    &.auto-adjust {
      flex-shrink: 1;
      flex-basis: auto;
      width: auto;
    }
  }
}

.view-1 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;

  .richContentContainer {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    padding: 50px;
    position: absolute;
    background: rgba($color: #000000, $alpha: 0.8);

    &.hide {
      width: 0;
      height: 0;
      opacity: 0;
      padding: 0;
      visibility: hidden;
      transition: 300ms;
    }

    &.show {
      height: 1;
      opacity: 1;
      transition: 300ms;
    }

    img {
      width: auto;
      height: auto;
      margin: 0 auto;
      display: block;
      max-width: 90%;
      max-height: 90%;
      object-fit: contain;
      object-position: 50% 50%;
    }
  }

  .boxed {
    width: calc((100% - 20px) / 2);
  }

  .compare-layout-mask {
    > * {
      width: calc((100% - 20px) / 2);
      margin-right: 20px;

      &:nth-child(2n + 0):not(.auto-adjust) {
        margin-right: 0;
      }

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-2 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;

  .is-view-2 {
    display: none;
  }

  .form-and-image {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .compare-layout-mask,
    .richContentContainer {
      width: calc((100% - 20px) / 2);
      background-color: transparent;
    }

    .richContentContainer {
      cursor: pointer;

      &.show {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        padding: 50px;
        position: absolute;
        background: rgba($color: #000000, $alpha: 0.8);

        img {
          width: auto;
          height: auto;
          margin: 0 auto;
          display: block;
          max-width: 90%;
          max-height: 90%;
          object-fit: contain;
          object-position: 50% 50%;
        }
      }
    }
  }

  .unboxed,
  .boxed {
    width: calc((100% - 20px) / 2);
  }

  .compare-layout-mask {
    > * {
      width: 100%;

      &.amount-field {
        width: calc(100% - 96px - 20px);
        margin-right: 0;
      }

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-3 {
  display: block;

  .is-view-2 {
    display: none;
  }

  .form-and-image {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .compare-layout-mask,
    .richContentContainer {
      width: calc((100% - 20px) / 2);
      background-color: transparent;
    }

    .richContentContainer {
      cursor: pointer;

      &.show {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        padding: 50px;
        position: absolute;
        background: rgba($color: #000000, $alpha: 0.8);

        img {
          width: auto;
          height: auto;
          margin: 0 auto;
          display: block;
          max-width: 90%;
          max-height: 90%;
          object-fit: contain;
          object-position: 50% 50%;
        }
      }
    }
  }

  .boxed {
    width: 100%;
    margin-bottom: 40px;
  }

  .compare-layout-mask {
    > * {
      width: 100%;

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-4 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;

  // .force-full-width {
  //   display: none;
  // }

  .is-view-2 {
    display: none;
  }

  .form-and-image {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .compare-layout-mask {
      display: none;
    }

    .richContentContainer {
      width: 100%;
      margin-bottom: 40px;
      background-color: transparent;
    }

    .richContentContainer {
      cursor: pointer;

      &.show {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        padding: 50px;
        position: absolute;
        background: rgba($color: #000000, $alpha: 0.8);

        img {
          width: auto;
          height: auto;
          margin: 0 auto;
          display: block;
          max-width: 90%;
          max-height: 90%;
          object-fit: contain;
          object-position: 50% 50%;
        }
      }
    }
  }

  .boxed {
    width: calc((100% - 20px) / 2);
  }

  .compare-layout-mask {
    > * {
      width: 100%;

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.switcher-constainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .step-constiner {
    margin-bottom: 8px;
  }

  .view-switcher {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
    justify-content: flex-end;
    width: 60%;

    .label {
      font-size: 8px;
      line-height: 16px;
      margin-right: 8px;
      font-weight: bold;
      display: inline-block;
      text-transform: uppercase;
    }

    span {
      cursor: pointer;
      font-size: 20px;
      margin-right: 8px;
      transition: 300ms;
      display: inline-block;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: $primary_color;
      }

      &.active {
        opacity: 0.5;
        transition: 300ms;
      }
    }
  }
}

// .view-switcher {
//   display: flex;
//   margin-bottom: 8px;
//   align-items: center;
//   justify-content: flex-end;

//   .label {
//     font-size: 8px;
//     line-height: 16px;
//     margin-right: 8px;
//     font-weight: bold;
//     display: inline-block;
//     text-transform: uppercase;
//   }

//   span {
//     cursor: pointer;
//     font-size: 20px;
//     margin-right: 8px;
//     transition: 300ms;
//     display: inline-block;

//     &:last-child {
//       margin-right: 0;
//     }

//     &:hover {
//       color: $primary_color;
//     }

//     &.active {
//       opacity: 0.5;
//       transition: 300ms;
//     }
//   }
// }
// .view-switcher-step {
//   display: flex;
//   margin-bottom: 8px;
//   align-items: center;
//   justify-content: space-evenly;

//   .label {
//     font-size: 8px;
//     line-height: 16px;
//     margin-right: 8px;
//     font-weight: bold;
//     display: inline-block;
//     text-transform: uppercase;
//   }

//   span {
//     cursor: pointer;
//     font-size: 20px;
//     margin-right: 8px;
//     transition: 300ms;
//     display: inline-block;

//     &:last-child {
//       margin-right: 0;
//     }

//     &:hover {
//       color: $primary_color;
//     }

//     &.active {
//       opacity: 0.5;
//       transition: 300ms;
//     }
//   }
// }

.view-2,
.view-3,
.view-4 {
  .low-contrast {
    .force-full-width {
      display: block;
    }

    .form-and-image .compare-layout-mask {
      display: flex;
      width: 100%;

      > * {
        width: calc((100% - 20px) / 2);
        margin-right: 20px;

        &.amount-field {
          width: calc(100% - 381px - 40px);
          margin-right: 0;
          margin-left: 20px;
        }

        &:nth-child(2n + 0) {
          margin-right: 0;
        }
      }
    }

    .richContentContainer {
      display: none;
    }
  }
}

.ai-based-status-match {
  .main-table {
    overflow: hidden;
    border-radius: 5px;
    border: 0.5px solid rgba(32, 32, 42, 0.15);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);

    .rdt_TableHeadRow,
    .rdt_TableRow {
      border-bottom-color: #f5f5f5;
    }

    .rdt_TableHeadRow {
      font-weight: bold;
      border-bottom: 0.5px solid rgba($text_color_rgb, 0.15);
    }
  }

  .accordion-component-wrap {
    overflow: hidden;
    border-radius: 4px;
    border: 0.5px solid rgba($text_color_rgb, 0.25);

    .accordion-single {
      border-bottom: 0.5px solid rgba($text_color_rgb, 0.25);

      &:last-child {
        border-bottom: 0;
      }

      .matched-checked {
        font-size: 14px;
      }

      .accordion-header {
        cursor: pointer;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        padding: 8px 16px;

        .matched-checked {
          font-size: 16px;
          margin: 0 10px 0 0;
          display: inline-block;
          vertical-align: middle;
        }

        i.icon-caret {
          margin: 0;
          font-size: 8px;
          transition: 300ms;
          line-height: 18px;
        }
      }

      .accordion-content {
        opacity: 0;
        max-height: 0;
        overflow: hidden;

        .matched-checked {
          font-size: 12px;
        }

        .rdt_TableHeadRow,
        .rdt_TableRow {
          border-bottom-color: #f5f5f5;

          &:hover {
            outline: none;
          }
        }
      }

      &.has-error {
        .rdt_TableHeadRow,
        .rdt_TableRow {
          border-bottom-color: rgba($error_color_rgb, 0.1);
        }
      }
    }

    .accordion-single.active {
      .accordion-header {
        border-bottom: 0.5px solid rgba($text_color_rgb, 0.25);

        i.icon-caret {
          transform: rotate(180deg);
        }
      }

      .accordion-content {
        opacity: 1;
        max-height: 10000px;
      }
    }
  }
}

.invoice-compare {
  .invoice-view-switcher {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    justify-content: space-between;

    h2 {
      font-size: 24px;
      margin-top: 0px;
      color: #20202a;
      font-weight: 400;
      line-height: 36px;
      margin-right: 32px;
      margin-bottom: 0px;
    }

    .checkbox-div {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .custom-checkbox-new {
        margin-left: 40px;

        &:first-child {
          margin-left: 0px;
        }
      }
    }
  }
}

.inv-div {
  .force-full-width {
    .searchbar {
      width: 100%;
      max-width: 320px;
      position: relative;
      border: 1px solid #dcdcdc;
      border-radius: 20px;

      input[type='text'] {
        width: 100%;
        height: 40px;
        border: none;
        font-size: 12px;
        font-weight: 700;
        line-height: 20px;
        border-radius: 40px;
        background-color: #fff;
        padding: 0px 64px 0px 24px;
      }

      i {
        top: 0px;
        right: 0px;
        width: 40px;
        height: 40px;
        display: block;
        font-size: 16px;
        color: #4f4f4f;
        line-height: 40px;
        position: absolute;
        text-align: center;
        border-radius: 40px;
        pointer-events: none;
        background: #dcdcdc;
      }
    }
  }

  .styledDatatable .rdt_Table .rdt_TableHeadRow {
    min-height: 40px;
    background: rgba($primary_color_rgb, 0.7);

    .rdt_TableCol {
      color: #fff;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .styledDatatable .rdt_Table .rdt_TableRow {
    border-bottom: none;

    .rdt_TableCell {
      margin-right: 0px;
    }

    &:hover {
      &:nth-of-type(2n + 1) .rdt_TableCell,
      .rdt_TableCell {
        color: #fff;
        background-color: rgba($primary_color_rgb, 0.6);
      }
    }

    &:nth-of-type(2n + 1) {
      .rdt_TableCell {
        background-color: #f6f6f6;
      }
    }
  }
}

.inv-table-view {
  padding: 24px;
  margin-bottom: 40px;
  background-color: #fff;

  &.modal-class {
    padding: 0px;
    margin-bottom: 20px;
  }

  .inv-table-header {
    display: flex;
    margin-bottom: 8px;
    align-items: center;

    .th {
      width: 100%;
      font-size: 16px;
      font-weight: 700;
      padding: 0px 24px;
      line-height: 24px;
      color: $text_color;

      &.label {
        flex: 2;
      }

      &.invoice {
        flex: 3;
      }

      &.po {
        flex: 3;
      }
    }
  }

  .inv-table-body {
    width: 100%;
    display: flex;
  }

  .inv-table-format {
    width: 100%;
    display: flex;
    padding: 8px;
    background-color: rgb(246 246 246 / 65%);
  }

  .primary-secondary-table-wrap {
    width: 100%;
  }

  .inv-table-body-format {
    width: 100%;

    .single {
      display: flex;
      align-items: stretch;
      background-color: #f6f6f6;

      .td {
        flex: 3;
        padding: 00px;
        display: flex;
        align-items: center;
        border-left: 1px solid #e5e5e5;

        > div {
          width: 100%;
        }

        &.this-is-empty {
          display: none;
        }

        .label {
          display: none;
        }

        .m20 {
          margin-bottom: 0;
        }

        .icon-calendar {
          &::before {
            display: none;
          }
        }

        p {
          font-size: 13px;
          line-height: 20px;
          color: $text_color;
          padding: 10px 24px;
        }
      }

      .td.label {
        flex: 2;
        display: flex;
        padding: 5px;
        align-items: center;

        i {
          display: block;
          font-size: 15px;
          margin-right: 10px;

          &.icon-not-checked {
            color: #c74646;
          }

          &.icon-check-alert {
            color: #ff9700;
            border-radius: 50%;
            background-color: #ffeb3c;
          }

          &.icon-check-success.grey .path1:before {
            content: '\eba0';
            color: #777;
          }
        }

        p {
          padding: 0px;
          font-weight: 700;
        }
      }

      &:nth-child(2n-0) {
        background-color: #fff;
      }
    }
  }

  .inv-image,
  .inv-image.th {
    width: 100%;
    padding: 0px;
    max-width: 50%;
    min-width: 50%;
    margin-left: 24px;

    &.bothimage {
      max-width: 100%;
    }
  }
}

.inv-itemlist {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .inv-item {
    width: calc(50% - 12px);
  }
}

.boxed.unbox:not(.edit-only-half) {
  &.is-not-editing {
    .single .td {
      .mb20 {
        margin-bottom: 0;
      }

      textarea,
      input[type='text'] {
        border: none;
        height: 40px !important;
        font-size: 13px;
        padding: 0px 24px;
        line-height: 20px;
        color: $text_color;
        resize: none;
        background-color: transparent;
      }

      .value-only {
        min-height: 40px;
        display: block;
      }

      .edit-only {
        display: none;
      }

      .multiselect-wrap {
        .value-only {
          display: block;
        }

        .input-text {
          display: none;
        }
      }

      .simple-select {
        .value-only {
          display: block;
        }

        .custom-select-wrap {
          display: none;
        }
      }
    }
  }

  &.is-editing {
    .inv-table-view .inv-table-body-format .single .td {
      padding: 5px;

      .mb20 {
        margin-bottom: 0;
      }

      .multiselect-wrap {
        .value-only {
          display: none;
        }

        .input-text {
          display: block;
        }
      }

      .value-only {
        display: none;
      }

      .edit-only {
        display: block;
      }

      .simple-select {
        .value-only {
          display: none;
        }

        .custom-select-wrap {
          display: block;
        }
      }
    }
  }
}

.boxed.unbox.edit-only-half {
  &.is-not-editing {
    .inv-table-view .inv-table-body-format .single .td {
      .mb20 {
        margin-bottom: 0;
      }

      textarea,
      input[type='text'] {
        border: none;
        height: 40px !important;
        font-size: 13px;
        padding: 0px 24px;
        line-height: 20px;
        color: $text_color;
        resize: none;
        background-color: transparent;
        // pointer-events: none;
      }

      .value-only {
        min-height: 40px;
        display: block;
      }

      .edit-only {
        display: none;
      }

      .multiselect-wrap {
        .value-only {
          display: block;
        }

        .input-text {
          display: none;
        }
      }

      .simple-select {
        .value-only {
          display: block;
        }

        .custom-select-wrap {
          display: none;
        }
      }
    }
  }

  &.is-editing {
    .inv-table-view
      .inv-table-body-format:not(.reference-table)
      .single
      .td:nth-of-type(3) {
      .mb20 {
        margin-bottom: 0;
      }

      textarea,
      input[type='text'] {
        border: none;
        height: 40px !important;
        font-size: 13px;
        padding: 0px 24px;
        line-height: 20px;
        color: $text_color;
        resize: none;
        background-color: transparent;
        pointer-events: none;
      }

      .value-only {
        min-height: 40px;
        display: block;
      }

      .edit-only {
        display: none;
      }

      .multiselect-wrap {
        .value-only {
          display: block;
        }

        .input-text {
          display: none;
        }
      }

      .simple-select {
        .value-only {
          display: block;
        }

        .custom-select-wrap {
          display: none;
        }
      }
    }

    .inv-table-view .inv-table-body-format .single .td {
      padding: 5px;
    }

    .inv-table-view .inv-table-body-format .single .td:nth-of-type(2) {
      padding: 5px;

      .mb20 {
        margin-bottom: 0;
      }

      .multiselect-wrap {
        .value-only {
          display: none;
        }

        .input-text {
          display: block;
        }
      }

      .value-only {
        display: none;
      }

      .edit-only {
        display: block;
      }

      .simple-select {
        .value-only {
          display: none;
        }

        .custom-select-wrap {
          display: block;
        }
      }
    }
  }
}

.data-single-view-wrap .inv-table-body-format .single .td.label,
.inv-table-view .inv-table-body-format.reference-table .single .td.label {
  flex-grow: 1;

  i {
    display: none;
  }
}

.reference-title {
  font-size: 16px;
  margin-top: 32px;
  margin-bottom: 8px;
}

.single-table-wrap {
  .inv-item {
    .mb20 {
      margin-bottom: 0;
    }
  }
}

.data-table-wrap {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  width: 100%;
  align-items: stretch;

  .inv-item {
    width: calc((100% - 20px) / 2);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .searchbar {
      .label {
        display: none;
      }
    }

    .mb20 {
      align-self: stretch;
      flex-grow: 1;
      background: white;
    }
  }
}

.custom-add-button {
  display: flex;
  font-size: 13px;
  align-items: center;
  justify-content: center;
  min-width: 80px !important;

  span {
    font-size: 12px;
    margin-right: 8px;
    display: inline-block;
    vertical-align: middle;
  }
}

.required-asterisk-field::after {
  content: '*';
  color: $red_color;
}

.new-service-request {
  padding: 20px 16px;
  border-radius: 4px;
  border: 1px solid rgba($text_color_rgb, 1);
}

.rsc .rsc-float-button {
  background: none;
}

.rsc-container .rsc-header {
  background: #3b73b9;
}

.rsc-container .rsc-footer {
  display: none;
}

.rsc-container .rsc-content {
  overflow: hidden;
}

.rsc-container .rsc-os-option-element {
  background: #3b73b9;
}

.rsc-ts-bubble {
  color: white;
  background: #3b73b9;
}

.custom-floating-robot {
  display: flex;
  margin-right: 200px;
  margin-bottom: 55px;
}

.custom-floating-robot .robot-avatar img {
  width: 48px;
}

.custom-floating-robot .robot-text {
  margin: 5px;
  background-color: #3b73b9;
  color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  width: 256px;
  border-radius: 10px;
  padding: 10px;
  text-align: center;
}

.inv-ext-pagination-button-container {
  font-size: 12px;

  &:hover {
    color: rgba($primary_color_rgb, 0.6);
  }
}

.inv-ext-zoom-button-container {
  font-size: 12px;
  margin-top: 20px;

  &:hover {
    color: rgba($primary_color_rgb, 0.6);
  }
}

.button-container {
  display: flex;

  span {
    &:hover {
      color: rgba($primary_color_rgb, 0.6);
    }
  }
}

.doc-viewer-toolbar {
  display: flex;
  justify-content: space-around;
}

.zoom-button-container {
  // position: absolute;
  // right: 60px;
  span {
    &:hover {
      color: rgba($primary_color_rgb, 0.6);
    }
  }
}

.react-pdf__doc-holder {
  width: 675px !important;
  height: 792px !important;
  overflow: scroll;
}

.icon-tree-arrow {
  padding: 5px;
  font-size: 13px;
  float: right;
  transform: rotate(0deg);

  &.active {
    float: right;
    transform: rotate(180deg);
  }
}

.email-tracetvents-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.email-dropdown-div {
  display: flex;
  float: right;
  margin-top: 5px;
}

// .inv-prev-next-icons{
//   //border-radius: 5px;
//   // height: 100px;
//   // width: 22px;

//   &.next{
//     //padding: 35px 3px 35px 3px;
//     font-size: 26px;
//     color: #000;
//     position: relative;
//     top: 325px;
//     transform: rotate(180deg);
//     //border: solid 1px rgba($primary_color_rgb, 0.6);
//     cursor: pointer;
//     // &:hover{
//     //   background-color: rgba($primary_color_rgb, 0.9);
//     //   color: #fff;
//     // }
//   }
//   &.prev{
//     //padding: 35px 3px 35px 3px;
//     padding-left: 3px;
//     font-size: 26px;
//     color: #000;
//     position: absolute;
//     top: 15%;
//     cursor: pointer;
//     &:hover{
//       font-size: 30px;
//       transition: transform 250ms;
//       text-shadow: 2px 2px 2px rgba($primary_color_rgb, 0.9);
//     }
//   }

// }
.inv-prev-next-icons {
  cursor: pointer;
  font-size: 22px;
  position: relative;
  margin-left: -24px;
  margin-right: -20px;
  .prev-wrap {
    float: left;
    height: 50vh;
    width: 20px;
    padding-bottom: 100%;
  }
  .next-wrap {
    float: right;
    height: 50vh;
    width: 23px;
    padding-bottom: 100%;
  }

  .prev-container {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    z-index: 999; /* Adjust as needed */
    width: 25px;
    height: 25px;
    background-color: $white_color;
    border-radius: 50%;
    margin-right: 50px;
    // display: flex;
    align-items: center;
    justify-content: center;
    // top: 30vh;
    box-shadow: rgba(0, 0, 0, 0.3) 0px 19px 38px,
      rgba(0, 0, 0, 0.22) 0px 15px 12px;
    &:hover {
      background-color: $primary_color;
    }
  }

  .next-container {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    z-index: 999; /* Adjust as needed */
    width: 25px;
    height: 25px;
    background-color: $white_color;
    border-radius: 50%;
    // position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    // top: 30vh;
    box-shadow: rgba(0, 0, 0, 0.3) 0px 19px 38px,
      rgba(0, 0, 0, 0.22) 0px 15px 12px;
    &:hover {
      background-color: $primary_color;
    }
  }

  .next {
    margin-right: 4px; // Add spacing between icons if needed
    position: absolute;
    color: $primary_color;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding-top: 23%;
    padding-left: 20%;
    font-size: 15px;

    &:hover {
      // transition: transform 250ms;
      text-shadow: 2px 2px 2px rgba($primary_color_rgb, 0.9);
      color: $white_color;
    }
  }

  .prev {
    margin-right: 4px; // Add spacing between icons if needed
    position: absolute;
    color: $primary_color;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    padding-top: 23%;
    padding-left: 20%;
    font-size: 15px;

    &:hover {
      // transition: transform 250ms;
      text-shadow: 2px 2px 2px rgba($primary_color_rgb, 0.9);
      color: $white_color;
    }
  }
}
.icon-check-alert-light {
  color: #ff9700;
  border-radius: 50%;
  background-color: #3c4fff;
}

.inv-page-header {
  border-radius: 0px;
  background-color: #fff;
  padding: 12px 10px 5px 10px !important;
  border: $grey_color;

  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  .ant-descriptions-item-label {
    font-weight: bold; /* Make labels bold */
  }
  .cpc-id {
    font-size: 18px;
    font-weight: bold;
    padding: 13px;
  }
}

#Invoice-Page-Header {
  transition: top 0.3s ease-in-out; /* Smooth transition */
  width: 100%; /* Ensure it spans full width */
}
