/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useCallback } from 'react';

export const APFileStatusTable = ({
  formData,
  newPrList,
  handleEdit,
  handleDelete,
  handleSubRowClicked,
  params,
}) => {
  let hidePrNo = false;

  const innerMenuName = params?.location?.pathname?.split('/').pop();
  if (innerMenuName === 'draft' || innerMenuName === 'cart') hidePrNo = true;

  const [toggle, setToggle] = useState([]);

  const [paginationRows, setPaginationRows] = useState([
    {
      slNo: 1,
      fileId: 123,
      fileName: 'File 1',
      flag: true,
    },
    {
      slNo: 2,
      fileId: 456,
      fileName: 'File 2',
      flag: true,
    },
    {
      slNo: 3,
      fileId: 789,
      fileName: 'File 3',
      flag: false,
    },
  ]);

  const subTableRows = [
    {
      slNo: 1,
      fileId: 123,
      fileName: 'Sub-File 1',
      flag: true,
    },
    {
      slNo: 2,
      fileId: 456,
      fileName: 'Sub-File 2',
      flag: true,
    },
    {
      slNo: 3,
      fileId: 789,
      fileName: 'Sub-File 3',
      flag: false,
    },
  ];

  let tableColumns = [
    { displayName: '#', selector: 'slNo' },
    { displayName: 'File Id', selector: 'fileId' },
    { displayName: 'File Name', selector: 'fileName' },
    { displayName: 'Flag', selector: 'flag' },
  ];

  const Header = useCallback(({ headerCol, headerIndex }) => {
    return (
      <div
        key={headerIndex}
        className="header-single"
        style={{
          width: headerCol?.displayWidth || '10%',
        }}
      >
        <span style={{ textAlign: 'center' }}>
          {headerCol.displayName || ''}
        </span>
      </div>
    );
  }, []);

  const Rows = useCallback(
    ({ rows, header, isMain, rowIndex }) => {
      const toggleHandler = (event, id) => {
        let toggleState = [...toggle];
        if (toggleState.includes(id)) {
          toggleState = toggleState.filter((item) => item !== id);
        } else {
          toggleState = [];
          toggleState.push(id);
        }
        setToggle(toggleState);
        if (event) event.stopPropagation();
      };

      if (header) {
        return (
          <div
            className={`table-row ${
              toggle.includes(rowIndex) ? 'open' : 'close'
            } `}
            key={rowIndex}
          >
            <div
              className={'table-row-flex'}
              style={{
                backgroundColor: isMain ? 'white' : 'rgb(240 240 240 / 30%)',
                borderLeft: isMain ? '4px solid #3d5791' : 'none',
              }}
            >
              {header.map((item, index) => {
                if (item.selector === 'flag') {
                  return (
                    <div
                      className="row-single"
                      style={{ width: '10%' }}
                      key={index}
                    >
                      <span
                        className={
                          rows[item.selector]
                            ? 'icon icon-checked'
                            : 'icon icon-menu-failures'
                        }
                        style={{
                          position: 'relative',
                          border: 'none',
                          margin: 'auto',
                          width: '50%',
                        }}
                      />
                    </div>
                  );
                } else
                  return (
                    <div
                      key={index}
                      className="row-single"
                      style={{
                        width: item.displayWidth || '10%',
                        textAlign: 'center',
                      }}
                    >
                      {rows[item.selector]}
                    </div>
                  );
              })}
              {isMain && (
                <div
                  className="row-single has-open-button"
                  style={{
                    width: '4.00%',
                    textAlign: 'center',
                    position: 'absolute',
                    right: '20px',
                  }}
                >
                  <div
                    className="openButton"
                    onClick={(event) => toggleHandler(event, rowIndex)}
                  >
                    <i className="icon-caret"></i>
                  </div>
                </div>
              )}
            </div>
            {isMain && (
              <div
                className="table-row-content right"
                style={{ padding: '8px 8px 16px 24px' }}
              >
                <div
                  style={{
                    padding: '8px',
                    borderRadius: '5px',
                    border: '1px solid var(--border_white_color)',
                    boxShadow:
                      '0px 1px 2px 0px rgba(var(--black_color_rgb), 0.16)',
                  }}
                >
                  <div className="table-header" style={{ height: '20px' }}>
                    {tableColumns?.length
                      ? tableColumns.map((headerColumn, headerIndex) => {
                          return (
                            <Header
                              headerCol={headerColumn}
                              key={headerIndex}
                              headerIndex={headerIndex}
                            />
                          );
                        })
                      : ''}
                  </div>
                  {subTableRows && subTableRows.length
                    ? subTableRows.map((row, index) => {
                        return (
                          <Rows
                            rows={row}
                            header={tableColumns}
                            rowIndex={index}
                            key={index}
                          />
                        );
                      })
                    : ''}
                </div>
              </div>
            )}
          </div>
        );
      }
    },
    [toggle]
  );

  return (
    <div className="table-div">
      <div className="table-header">
        {tableColumns && tableColumns.length
          ? tableColumns.map((headerColumn, indx) => {
              return <Header headerCol={headerColumn} key={indx} />;
            })
          : ''}
      </div>
      <div className="table-body mb20">
        {paginationRows && paginationRows.length
          ? paginationRows.map((row, index) => {
              return (
                <Rows
                  rows={row}
                  header={tableColumns}
                  rowIndex={index}
                  key={index}
                  isMain
                />
              );
            })
          : ''}
      </div>

      {/* {paginationRows && paginationRows.length ? (
          <Pagination
            dataPerPage={attributes.dataPerPage}
            totalPosts={paginationRows.length}
            paginate={paginate}
          />
        ) : (
          ''
        )} */}
    </div>
  );
};
