import { useEffect, useState } from 'react';
import {
  Button,
  CommonSpinner,
  formValidationUtil,
  Input,
} from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import Modal from 'react-modal';
import './ecr_eco_common.scss';
import {
  showDialog,
  updateApproverDetailsECR,
  resetupdateApproverDetailsECR,
  resetupdateApproverDetailsECO,
  updateApproverDetailsECO,
} from '@hp/mainstore';
function ECRECOApprovers({
  isViewOnly,
  userId,
  ecrId,
  ecoId,
  approvalFormData,
  modalOpen,
  handleModalClose,
  handleApprovalChangeSave,
  ...props
}) {
  const [approvalForm, setApprovalForm] = useState([]);
  const [approverOptions, setApproverOptions] = useState([]);
  const [commonNamePart, setCommonNamePart] = useState('');

  const dispatch = useDispatch();
  const { ecrApproversChangeRes, ecoApproversChangeRes } = useSelector(
    (store) => store.pdm
  );

  useEffect(() => {
    if (approvalFormData?.formSubDetailsInternalDTOList) {
      handleSetApproversOptions();
    }
  }, [approvalFormData]);

  useEffect(() => {
    if (ecrApproversChangeRes?.value) {
      handleApprovalChangeSave();
      handleModalClose(false);
      funcToSetResponseMessage('success', ecrApproversChangeRes.value);
    }
    return () => {
      dispatch(resetupdateApproverDetailsECR());
    };
  }, [ecrApproversChangeRes]);

  useEffect(() => {
    if (ecoApproversChangeRes?.value) {
      handleApprovalChangeSave();
      handleModalClose(false);
      funcToSetResponseMessage('success', ecoApproversChangeRes.value);
    }
    return () => {
      dispatch(resetupdateApproverDetailsECO());
    };
  }, [ecoApproversChangeRes]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  function handleSetApproversOptions() {
    let comboOptions =
      approvalFormData.formSubDetailsInternalDTOList?.[0]?.comboBoxOptions;
    let values = [];
    let newArray = approvalFormData.formSubDetailsInternalDTOList.map(
      (list) => {
        if (list?.value?.[0]?.value) {
          values.push(parseInt(list.value[0].value));
        }
        return {
          ...list,
          displayName: list.displayName.replace(/\d/g, '').trim(),
        };
      }
    );

    let updatedOptions = [];
    if (comboOptions?.length) {
      updatedOptions = comboOptions.map((option) => {
        const isDisabled =
          values?.length && values.some((val) => val === option.commonId);
        return {
          ...option,
          disabled: isDisabled,
        };
      });
    }

    setApprovalForm(newArray);
    setApproverOptions(updatedOptions);
    setApprovalForm(approvalFormData.formSubDetailsInternalDTOList);
    setCommonNamePart(
      approvalFormData.formSubDetailsInternalDTOList?.[0]?.displayName
    );
  }

  const multiselectFilter = (option, filter, id) => {
    let container = document.getElementById(id);
    container.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
    if (!filter) {
      return option;
    }
    const re = new RegExp(filter, 'i');
    return option.filter(({ label }) => label && label.match(re));
  };

  // multislect function
  const multiSelectHandler = (formType, event, index) => {
    if (!event?.length) return;
    let selectedOne = event.slice(-1)[0];
    const approvalFormClone = structuredClone(approvalForm);
    let selectForm = {};
    let comboOptions = [...approverOptions];
    comboOptions.forEach((option) => {
      if (option?.commonId === selectedOne?.value) {
        option.disabled = true;
        selectForm = {
          ...formType,
          value: [{ ...selectedOne, desc: option.desc }],
        };
      }
      if (event.length > 1 && option?.commonId === parseInt(event[0].value)) {
        option.disabled = false;
      }
    });
    setApproverOptions(comboOptions);
    approvalFormClone.splice(index, 1, selectForm);
    setApprovalForm(approvalFormClone);
  };

  const addApproverHandler = () => {
    if (approvalForm.length >= 4) return;
    let approvers = [...approvalForm];
    approvers.push({
      ...approvalForm[0],
      orderId: null,
      value: [],
      canDelete: true,
    });
    approvers.map((list, index) => {
      list.orderId = index + 1;
    });
    setApprovalForm(approvers);
  };

  const deleteHandler = (index, approverId) => {
    let approvals = [...approvalForm];
    let comboOptions = approverOptions.map((options) => {
      if (options.commonId === parseInt(approverId)) {
        return { ...options, disabled: false };
      } else return options;
    });
    setApproverOptions(comboOptions);
    approvals.splice(index, 1);
    approvals = approvals.map((list, index) => {
      return {
        ...list,
        orderId: index + 1,
      };
    });

    setApprovalForm(approvals);
  };

  const approverSaveHandler = () => {
    let validationCheck = formValidationUtil.checkMandatoryField(approvalForm);
    setApprovalForm([...validationCheck.formList]);
    if (validationCheck.validSuccess) {
      if (ecrId)
        dispatch(updateApproverDetailsECR({ ecrId, userId, approvalForm }));
      else if (ecoId)
        dispatch(updateApproverDetailsECO({ ecoId, userId, approvalForm }));
      // setModalSpin(true);
    }
  };

  return (
    <Modal
      className={'ItemListModal'}
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={modalOpen}
    >
      <div
        onClick={() => {
          handleSetApproversOptions();
          handleModalClose(false);
        }}
        className="modal-close icon-close"
      />
      {/* <CommonSpinner visible={modalSpin} /> */}
      <h1 className="page-sub-title" style={{ marginRight: '1px' }}>
        {approvalFormData?.displayName}
      </h1>
      <div className="ecr-eco-approver-container">
        {approvalForm?.length
          ? approvalForm.map((form, index) => {
              return (
                <div
                  key={index}
                  style={{
                    position: 'relative',
                    width: '50%',
                  }}
                  id={`apprrover${index}`}
                  disabled={isViewOnly ?? false}
                >
                  <Input
                    key={index}
                    formType={{
                      ...form,
                      displayName: `${commonNamePart} ${index + 1}`,
                      comboBoxOptions: approverOptions,
                    }}
                    onChangeHandler={(fromType, event) =>
                      multiSelectHandler(fromType, event, index)
                    }
                    multiselectFilter={(option, filter) =>
                      multiselectFilter(option, filter, `apprrover${index}`)
                    }
                    isEditable={'notShowing'}
                    closeOnChange={true}
                    disableAllClearBtn={true}
                  />

                  <span className="name-tag">
                    {form?.value?.length ? ` ${form?.value[0]?.desc} ` : ''}
                  </span>

                  {form?.canDelete ? (
                    <span
                      className="icon-close close-btn"
                      onClick={() => deleteHandler(index, form.value[0]?.value)}
                    ></span>
                  ) : (
                    ''
                  )}
                </div>
              );
            })
          : ''}
      </div>

      {!isViewOnly && approvalForm?.length ? (
        <div
          className="flex-right"
          style={{
            position: 'absolute',
            bottom: '16px',
            width: '85%',
          }}
        >
          <Button
            className={'small  outline add-button-custom flex-row vam button'}
            onClick={addApproverHandler}
          >
            <span className="icon-add-button mr8"></span>
            Approver Level
          </Button>

          <Button
            className={'small default'}
            onClick={() => approverSaveHandler()}
          >
            Save
          </Button>
        </div>
      ) : (
        ''
      )}
    </Modal>
  );
}

export default ECRECOApprovers;
