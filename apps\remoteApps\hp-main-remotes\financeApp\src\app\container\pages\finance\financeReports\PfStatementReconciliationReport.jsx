/* eslint-disable react/jsx-pascal-case */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from 'react';
import { useAppRouterDom } from '@hp/utils';
import { useDispatch, useSelector } from 'react-redux';
import { getSummaryReports } from '@hp/mainstore';
import PfReportDataList from '../../../_pf-reports/PfReportDataList';
import DataTable from 'react-data-table-component';

const PfStatementReconciliationReport = () => {
  const { pfReportDTO } = useSelector((state) => state.finance);
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();

  // const menuData = domParameters?.menuData || '';
  // const submenu = domParameters?.submenu || '';
  const parameters = domParameters?.parameters || '';

  const urlParam = parameters || '';
  const user = JSON.parse(localStorage.getItem('user')) || {};
  const userId = parseInt(user.userId, 10);
  const clientId = user.clientId;

  const [reportData, setReportData] = useState();
  const [columns, setColumns] = useState();
  const [rows, setRows] = useState();
  const [tableHeader, setTableHeader] = useState();

  useEffect(() => {
    dispatch(
      getSummaryReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        fromDate: null,
        toDate: null,
        type: 'subscribe',
      })
    );

    return () => {
      dispatch(
        getSummaryReports({
          para: urlParam,
          clientId: clientId,
          userId: userId,
          fromDate: '',
          toDate: '',
          type: 'unSubscribe',
        })
      );
    };
  }, [dispatch, urlParam, clientId, userId]);

  // useEffect(() => {
  //   if (pfReportDTO?.value) {
  //     setReportData(pfReportDTO.value.listView);
  //   }
  // }, [pfReportDTO]);

  useEffect(() => {
    if (pfReportDTO?.value?.graphDetails) {
      const { graphDetails, listView } = pfReportDTO.value;

      if (Array.isArray(graphDetails)) {
        graphDetails.forEach(({ columns, value = [], heading, type }) => {
          if (type === 'CountList') {
            setColumns(value.columnValue);
            setRows(value.rowValue);
            setTableHeader(heading);
          }
        });
      } else {
        console.error('graphDetails is not an array:', graphDetails);
      }

      setReportData(listView);
    }
  }, [pfReportDTO]);

  return (
    <>
      <div className="page-title">Statement Reconciliation Report</div>
      {pfReportDTO && (
        <div className="report-body-main">
          <div className="report-data-body card mr20">
            <PfReportDataList
              data={reportData}
              summary={pfReportDTO?.value?.summary}
            />
          </div>
          <div className="report-Table-body card">
            <div className="page-sub-title">{tableHeader}</div>
            <div className="styledDatatable mb20">
              <DataTable
                persistTableHead
                noHeader
                highlightOnHover
                striped
                columns={columns}
                data={rows}
                pagination
                paginationDefaultPage={1}
                paginationResetDefaultPage
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export { PfStatementReconciliationReport };
