/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 29-08-2022 10:41:10
 * @modify date 2024-07-30 10:35:44
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { getClientComboBox, getUserListing, showDialog } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';

import { AP_USER } from '@hp/constants';
import { useAppRouterDom } from '@hp/utils';
import { ButtonCommon, dataTableServiceProvider } from '@hp/components';
import { Select, TextInput } from '@hp/components';
import { globalutils } from '@hp/components'

const CreateUserMain = (props) => {
  const dispatch = useDispatch();
  const listUsers = useSelector((state) => state.admin.viewuserlist || []);
  const { clientCombolist } = useSelector((store) => store.email);
  const buttonData = useSelector((state) => state.buttons.buttons || null);
  const responseMsg =
    props.location && props.location.state ? props.location.state : null;
  const [userId, setUserId] = useState(null);
  const [buttonFlag, setButtonFlag] = useState(null);
  let user = globalutils.getDataFromStorage('all');
  const client_Id = user?.clientId;
  const [TableListing, setTableList] = useState();
  const [searchBox, setSearchBox] = useState();
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [clientComboState, setClientComboState] = useState([]);
  const [clientIdSelect, setClientIdSelect] = useState(client_Id);
  const { domParameters, navigate } = useAppRouterDom();

  // const menuData = domParameters?.menuData || '';
  useEffect(() => {
    getParameter();
    dispatch(getClientComboBox());
  }, []);

  useEffect(() => {
    if (buttonData && buttonData !== undefined) {
      let data = buttonData
        .filter((btn) => {
          return btn?.parameter?.length > 0;
        })
        .map((btn) => {
          return {
            ...btn, // Create a copy of the object
            accessFlag: 'Y', // Modify the copied object
          };
        });
      setButtonFlag(data);
    }
  }, [buttonData]);

  useEffect(() => {
    if (listUsers && listUsers.value && listUsers.value !== undefined) {
      setTableList(listUsers.value);
      setSearchBox(listUsers.value);
    }
  }, [listUsers]);

  useEffect(() => {
    if (
      clientCombolist &&
      clientCombolist.value &&
      clientCombolist.value.length > 0
    ) {
      const clientOptions = clientCombolist.value.map((value) => {
        return {
          value: value.clientId,
          display: value.clientName,
        };
      });
      setClientComboState(clientOptions);
    }
  }, [clientCombolist]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const handleOnChange = (event) => {
    setClientIdSelect(event.target.value);
    dispatch(getUserListing(event.target.value));
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const getParameter = () => {
    let clientId =
      clientIdSelect && clientIdSelect !== undefined ? clientIdSelect : null;
    dispatch(getUserListing(clientId));
    responseMsg && funcToSetResponseMessage(responseMsg.type, responseMsg.text);
  };

  const columns = [
    {
      name: 'First Name',
      selector: 'firstName',
      sortable: true,
      width: '200px',
    },
    {
      name: 'Middle Name',
      selector: 'middleName',
      sortable: true,
      width: '150px',
    },
    {
      name: 'Last Name',
      selector: 'lastName',
      sortable: true,
      width: '200px',
    },
    {
      name: 'User Name',
      selector: 'username',
      sortable: true,
      width: '200px',
    },
    {
      name: 'Email',
      selector: 'email',
      sortable: true,
    },
  ];

  const handleRowClick = (row) => {
    let userId = row.userId ? row.userId : null;
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.userId,
      'userId'
    );
    setUserId(userId);
    setConditionalRowStyles(styleAttribute);
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type: type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const handleCreateUser = () => {
    navigate(`${clientIdSelect}/0`);
  };

  const handleCreateUserAs = () => {
    userId
      ? navigate(`${clientIdSelect}/${userId}`)
      : funcToSetResponseMessage('error', 'Please Select a User');
  };

  const functionsName = {
    handleCreateUser,
    handleCreateUserAs,
  };

  return (
    <>
      <div className="page-title">View Users</div>
      <div className="two-col-layout mb20">
        {client_Id === 1 && (
          <Select
            label="Client"
            style={{ width: 200 + 'px' }}
            value={clientIdSelect}
            name="clientId"
            options={clientComboState}
            onChange={handleOnChange}
          />
        )}
        <TextInput
          label="Search"
          style={{ width: 200 + 'px' }}
          value={filterString.filterStr}
          onChange={(e) => handleChange(e)}
        />
      </div>
      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          theme={'solarized'}
          onRowClicked={(row) => handleRowClick(row)}
          conditionalRowStyles={conditionalRowStyles}
        />
      </div>
      <ButtonCommon tempButtonData={buttonFlag} functionsName={functionsName} />
    </>
  );
};

export { CreateUserMain };
