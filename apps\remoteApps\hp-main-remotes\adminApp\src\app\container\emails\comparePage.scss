@import '@hp/styles/variables.scss';
.admin-main-container {
  .compare-layout-mask {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: flex-start;
    transition: 300ms;
  }
}

.richContentContainer {
  transition: 300ms;

  img {
    display: block;
    width: 100%;
    height: auto;
    object-fit: contain;
  }
}

.boxed {
  padding: 20px;
  // margin-right: 20px;
  background: $white_color;
  transition: 300ms;
  box-shadow: 0 1px 3px $border_white_color;

  &.low-contrast {
    background-color: $white_color;
  }

  &:nth-child(2n + 0) {
    margin-right: 0;
  }
}

.viewHolder {
  transition: 300ms;
}

.view-1,
.view-2,
.view-3,
.view-4 {
  .compare-layout-mask {
    > * {
      &.amount-field {
        flex: 0 0 auto;
        margin-right: 0;
        width: calc((100% - 252px) / 2);
      }

      &.auto-adjust {
        flex: 0 0 auto;
        width: auto;

        &.flex-grow {
          flex-grow: 1;
        }

        &.mr0 {
          margin-right: 0;
        }
      }
    }
  }
}

.compare-layout-mask {
  > * {
    transition: 300ms;

    &.auto-adjust {
      flex-shrink: 1;
      flex-basis: auto;
      width: auto;
    }
  }
}

.view-1 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;

  .richContentContainer {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    padding: 50px;
    position: absolute;
    background: rgba($color: $black_color, $alpha: 0.8);

    &.hide {
      width: 0;
      height: 0;
      opacity: 0;
      padding: 0;
      visibility: hidden;
      transition: 300ms;
    }

    &.show {
      height: 1;
      opacity: 1;
      transition: 300ms;
    }

    img {
      width: auto;
      height: auto;
      margin: 0 auto;
      display: block;
      max-width: 90%;
      max-height: 90%;
      object-fit: contain;
      object-position: 50% 50%;
    }
  }

  .boxed {
    width: calc((100% - 20px) / 2);
  }

  .compare-layout-mask {
    > * {
      width: calc((100% - 20px) / 2);
      margin-right: 20px;

      &:nth-child(2n + 0):not(.auto-adjust) {
        margin-right: 0;
      }

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-2 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;

  .is-view-2 {
    display: none;
  }

  .form-and-image {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .compare-layout-mask,
    .richContentContainer {
      width: calc((100% - 20px) / 2);
      background-color: transparent;
    }

    .richContentContainer {
      cursor: pointer;

      &.show {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        padding: 50px;
        position: absolute;
        background: rgba($color: $black_color, $alpha: 0.8);

        img {
          width: auto;
          height: auto;
          margin: 0 auto;
          display: block;
          max-width: 90%;
          max-height: 90%;
          object-fit: contain;
          object-position: 50% 50%;
        }
      }
    }
  }

  .boxed {
    width: calc((100% - 20px) / 2);
  }

  .compare-layout-mask {
    > * {
      width: 100%;

      &.amount-field {
        width: calc(100% - 96px - 20px);
        margin-right: 0;
      }

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-3 {
  display: block;

  .is-view-2 {
    display: none;
  }

  .form-and-image {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .compare-layout-mask,
    .richContentContainer {
      width: calc((100% - 20px) / 2);
      background-color: transparent;
    }

    .richContentContainer {
      cursor: pointer;

      &.show {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        padding: 50px;
        position: absolute;
        background: rgba($color: $black_color, $alpha: 0.8);

        img {
          width: auto;
          height: auto;
          margin: 0 auto;
          display: block;
          max-width: 90%;
          max-height: 90%;
          object-fit: contain;
          object-position: 50% 50%;
        }
      }
    }
  }

  .boxed {
    width: 100%;
    margin-bottom: 40px;
  }

  .compare-layout-mask {
    > * {
      width: 100%;

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-4 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;

  // .force-full-width {
  //   display: none;
  // }

  .is-view-2 {
    display: none;
  }

  .form-and-image {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .compare-layout-mask {
      display: none;
    }

    .richContentContainer {
      width: 100%;
      margin-bottom: 40px;
      background-color: transparent;
    }

    .richContentContainer {
      cursor: pointer;

      &.show {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        padding: 50px;
        position: absolute;
        background: rgba($color: $black_color, $alpha: 0.8);

        img {
          width: auto;
          height: auto;
          margin: 0 auto;
          display: block;
          max-width: 90%;
          max-height: 90%;
          object-fit: contain;
          object-position: 50% 50%;
        }
      }
    }
  }

  .boxed {
    width: calc((100% - 20px) / 2);
  }

  .compare-layout-mask {
    > * {
      width: 100%;

      &.force-full-width {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

.view-2,
.view-3,
.view-4 {
  .low-contrast {
    .force-full-width {
      display: block;
    }

    .form-and-image .compare-layout-mask {
      display: flex;
      width: 100%;

      > * {
        width: calc((100% - 20px) / 2);
        margin-right: 20px;

        &.amount-field {
          width: calc(100% - 381px - 40px);
          margin-right: 0;
          margin-left: 20px;
        }

        &:nth-child(2n + 0) {
          margin-right: 0;
        }
      }
    }

    .richContentContainer {
      display: none;
    }
  }
}

.avoid-clicks {
  pointer-events: none;
}

.ai-based-status-match {
  .main-table {
    overflow: hidden;
    border-radius: 5px;
    border: 0.5px solid rgba($black_color_rgb, 0.15);
    box-shadow: 0 1px 3px rgba($black_color_rgb, 0.06);

    .rdt_TableHeadRow,
    .rdt_TableRow {
      border-bottom-color: $white_color;
    }

    .rdt_TableHeadRow {
      font-weight: bold;
      border-bottom: 0.5px solid rgba($text_color_rgb, 0.15);
    }
  }

  .accordion-component-wrap {
    overflow: hidden;
    border-radius: 4px;
    border: 0.5px solid rgba($text_color_rgb, 0.25);

    .accordion-single {
      border-bottom: 0.5px solid rgba($text_color_rgb, 0.25);

      &:last-child {
        border-bottom: 0;
      }

      .matched-checked {
        font-size: 14px;
      }

      .accordion-header {
        cursor: pointer;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        padding: 8px 16px;

        .matched-checked {
          font-size: 16px;
          margin: 0 10px 0 0;
          display: inline-block;
          vertical-align: middle;
        }

        i.icon-caret {
          margin: 0;
          font-size: 8px;
          transition: 300ms;
          line-height: 18px;
        }
      }

      .accordion-content {
        opacity: 0;
        max-height: 0;
        overflow: hidden;

        .matched-checked {
          font-size: 12px;
        }

        .rdt_TableHeadRow,
        .rdt_TableRow {
          border-bottom-color: $white_color;

          &:hover {
            outline: none;
          }
        }
      }

      &.has-error {
        .rdt_TableHeadRow,
        .rdt_TableRow {
          border-bottom-color: rgba($error_color_rgb, 0.1);
        }
      }
    }

    .accordion-single.active {
      .accordion-header {
        border-bottom: 0.5px solid rgba($text_color_rgb, 0.25);

        i.icon-caret {
          transform: rotate(180deg);
        }
      }

      .accordion-content {
        opacity: 1;
        max-height: 10000px;
      }
    }
  }
}

/* Remove all table borders */
.custom-table {
  margin-bottom: 50px;
}

.custom-table.no-hover .ant-table-tbody > tr.ant-table-row:hover > td {
  background-color: transparent !important;
}
.custom-table .ant-table,
.custom-table .ant-table-cell,
.custom-table .ant-table-thead > tr > th,
.custom-table .ant-table-tbody > tr > td {
  border: none !important;
}
.custom-table .ant-table,
.custom-table .ant-table-cell {
  font-size: 12px !important;
}
.custom-table .ant-table-cell-content {
  font-family: 'Roboto', Verdana, Arial, 'Tahoma', sans-serif !important;
}
.custom-table .ant-table-cell {
  font-family: 'Roboto', Verdana, Arial, 'Tahoma', sans-serif !important;
}
/* Header background */
.custom-table .ant-table-thead > tr > th {
  background: #ccc;
  font-weight: 600;
}

/* Prevent text wrapping */
.custom-table .ant-table-cell {
  white-space: nowrap;
  text-align: left;
}

/* Table hover and spacing */
// .custom-table .ant-table-tbody > tr:hover > td {
//   background: #fafafa;
// }

// /* Scrollbar styling (Webkit) */
// .custom-table .ant-table-content::-webkit-scrollbar {
//   height: 8px;
// }

// .custom-table .ant-table-content::-webkit-scrollbar-track {
//   background: #f1f1f1;
//   border-radius: 10px;
// }

// .custom-table .ant-table-content::-webkit-scrollbar-thumb {
//   background: #888;
//   border-radius: 10px;
// }

.custom-table .ant-table-container {
  border-radius: 0 !important;
}
.custom-table .ant-table-header {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
/* Remove top-left border radius */
.ant-table-wrapper
  .ant-table-container
  table
  > thead
  > tr:first-child
  > *:first-child {
  border-start-start-radius: 0 !important;
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important; /* for broader support */
}
.custom-table .ant-table thead > tr > th {
  background-color: #ccc !important; /* match your desired header background */
  border-left: none !important;
  border-right: none !important;
  box-shadow: none !important;
}

.custom-table .ant-pagination {
  justify-content: flex-end;
}

.custom-table th.ant-table-cell::before,
.custom-table td.ant-table-cell::before,
.custom-table th.ant-table-cell-fix-left::before,
.custom-table td.ant-table-cell-fix-left::before,
.custom-table th.ant-table-cell-fix-right::before,
.custom-table td.ant-table-cell-fix-right::before {
  background-color: transparent !important;
  box-shadow: none !important;
}
.custom-table .ant-table-tbody > tr:nth-child(odd) > td {
  background-color: #ffffff; /* White */
}

.custom-table .ant-table-tbody > tr:nth-child(even) > td {
  background-color: #f9f9f9; /* Light grey */
}
.custom-table .ant-table-cell-fix-left {
  border-right: none !important;
}
.custom-table .ant-table-cell-fix-left {
  box-shadow: none !important;
}
/* Add vertical lines between columns */
.custom-table .ant-table-thead > tr > th,
.custom-table .ant-table-tbody > tr > td {
  border-right: 1px solid #ffffff !important; /* light gray border for separation */
}
.custom-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid #eae6e6 !important;
}

.custom-table .ant-table-tbody > tr:hover {
  background-color: none !important;
}
/* Remove border from last column to avoid double-border effect */
.custom-table .ant-table-thead > tr > th:last-child,
.custom-table .ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}
/* Add a grey line below rows */
.custom-table .ant-table-tbody > tr {
  border-bottom: 1px solid #f0f0f0; /* Light grey color */
}
.right-align-header {
  text-align: right !important; /* Use !important to override any default styles */
}
.Sptable .ant-table thead > tr > th:nth-child(4) {
  text-align: right !important;
}
.td.invoice.readonly-mode input {
  border: none;
  background: transparent;
  box-shadow: none;
  pointer-events: none; /* Disable interaction */
}
.td.invoice.readonly-mode select {
  border: none;
  background: transparent;
  box-shadow: none;
  pointer-events: none; /* Disable interaction */
}
.td.invoice.readonly-mode .ant-picker {
  border: none !important;
  background: transparent !important;
  box-shadow: none;
  pointer-events: none; /* Disable interaction */
}
.td.invoice.readonly-mode .custom-select {
  border: none !important;
  background: transparent !important;
  box-shadow: none;
  pointer-events: none; /* Disable interaction */
}
.td.invoice .ant-datepicker {
  margin-top: 20px !important;
}
.td.invoice.readonly-mode .ant-datepicker {
  margin-top: 20px !important;
}

.custom-table .ant-table-tbody > tr.ant-table-row:hover > td {
  background-color: #89abd5 !important; /* Replace with your desired hover color */
}
.headingFont {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.view-switcher {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  justify-content: flex-end;

  .label {
    font-size: 8px;
    line-height: 16px;
    margin-right: 8px;
    font-weight: bold;
    display: inline-block;
    text-transform: uppercase;
  }

  span {
    cursor: pointer;
    font-size: 20px;
    margin-right: 8px;
    transition: 300ms;
    display: inline-block;

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      color: $primary_color;
    }

    &.active {
      opacity: 0.5;
      transition: 300ms;
    }
  }
}
