/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable eqeqeq */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-empty */
/* eslint-disable no-undef */
/* eslint-disable no-case-declarations */
/* eslint-disable no-dupe-else-if */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
import React, { useState, useEffect, useMemo } from 'react';
import {
  CheckBoxInput,
  TextInput,
  TraceEvents,
  TextAreaInput,
  DocumentViewer,
  formValidationUtil,
  RfqQuoteTreeview,
  CompareDocs,
  NewVendor,
  NavBlocker,
} from '@hp/components';
import { motion, AnimatePresence } from 'framer-motion';
import { EmailContent } from '../container/emailUi/EmailContent.jsx';
import { AP_USER } from '@hp/constants';
import { Card, Table } from 'antd';
import { Input, DocUpload, Button, useConfirm } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { ButtonCommon, CommonSpinner } from '@hp/components';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import TabPane from 'antd/es/tabs/TabPane';
import {
  resetCompareQuote,
  showDialog,
  getDetailsByRfqId,
  getRFQItemTree,
  getRFQEventLog,
  saveRFQDetailsDraftForm,
  saveDraftRFQDetailsForm,
  generateRfqPdf,
  createRfqEmailPreview,
  saveRFQDetailsForm,
  saveTemplateManualpage,
  saveRejectCommentForm,
  previewRfqEmail,
  setRowStyle,
  getInvEmailDetailsByInOutTyps,
  editEventLogByRfqId,
  getManageQuoteDetailsByRfqId,
  saveApprovalDetail,
  sendRfqEmail,
  compareQuote,
  updateStatus,
  getSupplierQuotations,
  getDraftDataList,
  sendGeneratePr,
  getBestQuoteChartDetails,
  getSupplierListing,
  getMultiselectFilterData,
  getQuoteDataTables,
  quotationEditForm,
  getSourcingInvEmailDetailsByInOutType,
  saveTemplateRFQDetailsForm,
  preprUploadAttachment,
  getReasonForRejectionInSourcing,
  getEventTime,
  quoteEventLog,
  resetUpdateStatus,
  resetsaveApprovalDetail,
  resetsaveSupplierDetails,
  sourcingResetStateField,
  clearQuotationDeleteValue,
  clearAssignQuoteValue,
  saveDraft,
} from '@hp/mainstore';
import { cloneDeep } from 'lodash';
import AddNewSupplier from './AddNewSupplier';
import OldQuotePopUp from './OldQuotePopUp';
import TemplatePopUp from './TemplatePopUp';
import FinalizeQuoteDashBoard from './FinalizeBestQuote';
import { dataTableServiceProvider } from '@hp/components';
import { Link } from 'react-router-dom';
import { useAppRouterDom } from '@hp/utils';
import './QuoteModal.scss';
import {
  getOldRfqTemplatesForm,
  getRFQDetailsForm,
} from '../../../../../../../libs/store/hp-main-store/src/redux/middlewares/sourcingThunk';
import Modal from 'react-modal';
import { Modal as AntdModal } from 'antd';
import { RfqTree } from './RfqTree';
import PreviewRfqEmailModal from './PreviewRfqEmailModal';
import QuoteHeader from './QuoteHeader';
import { ManualSelectModal } from './ManualSelectModal';
import { AddRFQModal } from './AddRFQModal.jsx';
import { Tabs, Tooltip } from 'antd';
import { ManualRFQ } from './rfq/ManualRFQ.jsx';
import RfqPreview from './rfq/RfqPreview.jsx';
import RfqQuotations from './rfq/RfqQuotations.jsx';
import MissingFieldsDrawer from './MissingFieldsDrawer.jsx';

const ManualRfq = (props) => {
  const { domParameters, navigate, location } = useAppRouterDom();
  let rfqId = parseInt(domParameters.rfqId) || '';
  const parameter = domParameters.parameter;
  let userId = JSON.parse(localStorage.getItem(AP_USER));
  const lastVal = domParameters.rfqId || '';
  const { confirm } = useConfirm();
  let statusVal = domParameters.parameter;
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';
  const dispatch = useDispatch();
  const {
    detailsData,
    rfqdetailsForm,
    saveSupplierRes,
    saveRfqResponse,
    submitRfqResponse,
    docUploadAttachment,
    eventlogResponse,
    saveDraftRfqResponse,
    getRfqTemplateResponse,
    saveTemplateRfqResponse,
    rejectResponse,
    oldRfqTemplateResponse,
    generatePrResponse,
    editeventLogResponse,
    manageQuoteDetailsData,
    showEmailView,
    saveApprovalDetails,
    updateStatusValue,
    compareQuoteResponse,
    bestQuoteChartDetails,
    supplierQuatations,
    manualSelection,
    deleteQuotationslice,
    editedEmail,
    quotationDataTables,
    rejectResponseRfq,
    quoteEventLogData,
    rfqEventTime,
    assignedQuote,
    saveApprovalDetailsFailed,
  } = useSelector((store) => store.sourcing);
  const [checkbox, setCheckbox] = useState(false);
  const [formDetails, setFormDetails] = useState([]);
  const [supplierFormDetails, setSupplierFormDetails] = useState([]);
  const [oldRfqTemplateForm, setOldRfqTemplateForm] = useState([]);
  const [quotationDataTable, setquotationDataTables] = useState([]);
  const [disabled, setDisabled] = useState(false);
  const [manualAddData, setManualAddData] = useState();
  const [addFormDetails, setAddFormDetails] = useState();
  const [editFormDetails, setEditFormDetails] = useState();
  const [getItemNumber, setItemNumber] = useState();
  const [getEditItemNum, setEditItemNum] = useState();
  const [previewEmailModal, setPreviewEmailModal] = useState(false);
  const [fileDataValue, setFileDataValue] = useState();
  const [viewEmail, setviewEmail] = useState();
  const [selectedRow, setSelectedRow] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [addFn, setAddFn] = useState(false);
  const [itemId, setItemId] = useState(0);
  const [oldQuoteFlag, setOldQuoteFlag] = useState(false);
  const [attachmentFlag, setAttachmentFlag] = useState(false);
  const [show, setShow] = useState(false);
  const [getAddPopup, setAddPopup] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [attachPopup, setAttachPopup] = useState(false);
  const [templatePopUp, setTemplatePopUp] = useState(false);
  const [manualSelectionPopup, setManualSelectionPopup] = useState(false);
  const [isModal, setIsModal] = useState(false);
  const [filepath, setFilepath] = useState('');
  const [getEditPopup, setEditPopup] = useState(false);
  const [saveTemplatePopup, setSaveTemplatePopup] = useState(false);
  const [saveRejectPopup, setSaveRejectPopup] = useState(false);
  const [getTemplateName, setTemplateName] = useState();
  const [getTemplateDesc, setTemplateDesc] = useState();
  const [heading, setHeading] = useState();
  const [rejectComment, setRejectComment] = useState();
  const [indexVal, setIndexVal] = useState();
  const [quoteFlag, setQuoteFlag] = useState(false);
  const [multiselectFilterValue, setMultiselectFilterValue] =
    React.useState('');
  const [isLoading, setisLoading] = useState(false);
  const [onSaveClicked, setOnSaveClicked] = useState(false);
  const [disabledFlag, setDisabledFlag] = useState(true);
  const [flagValue, setFlagValue] = useState(true);
  const [saveFlag, setSaveFlag] = useState(false);
  const [saveStatus, setSaveStatus] = useState();
  const [saveCheckFlag, setSaveCheckFlag] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState();
  const [isRFQModalOpen, setRFQModalOpen] = useState(false);
  const [isEmailView, setIsEmailView] = useState(false);
  const [openNewUI, setOpenNewUI] = useState(false);
  const [suppId, setSuppId] = useState(null);
  const [bestSupplier, SetBestSupplier] = useState();
  const [bestSuppNum, setBestSuppNum] = useState();
  const [deleteQuotationValue, setDeleteQuotationValue] = useState();
  const [quoteId, setQuoteId] = useState();
  const [quoteIdList, setQuoteIdList] = useState(false);
  const [version, setVersion] = useState();
  const [rfqNum, setRfqNum] = useState();
  const [suplierName, setSupplierName] = useState();
  const [activeTab, setActiveTab] = useState();
  const [tableData, setTableData] = useState([]);
  const [itemTableData, setItemTableData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [itemColumns, setItemColumns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [traceEvents, setTraceEvents] = useState([]);
  const [addCommentsValue, setAddCommentsValue] = useState('');
  const [errorMsg, setErrorMsg] = useState({});
  const [selectedNodeIds, setSelectedNodeIds] = useState([]);
  const [pathForCompare, setPathForCompare] = useState({});
  const [filePathForView, setFilePathForView] = useState([]);
  const [myArray, setMyArray] = useState([]);
  const [compareDocModal, setCompareDocModal] = useState(false);
  const [array, setArray] = useState(0);
  const [bestQuoteModal, setBestQuoteModal] = useState(false);
  const [secondMethodFlag, setSecondMethodFlag] = useState(false);
  const [documentModal, setOpenDocumentModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isNavBlocked, setIsNavBlocked] = useState(true);
  const [enableEdit, setEnableEdit] = useState(false);
  const { buttons } = useSelector((state) => state.buttons || '');
  const [childTabKey, setChildTabKey] = useState('Quote Events');
  const [isMissingFieldsDrawerOpen, setIsMissingFieldsDrawerOpen] =
    useState(false);
  const [listValues, setListValues] = useState([
    { label: 'RFQ No', value: null, key: 'rfq_num' },
    { label: 'RFQ Date', value: null, key: 'rfq_date' },
    { label: 'Requestor', value: null, key: 'requestor' },
    { label: 'Buyer', value: null, key: 'buyer' },
    {
      label: parameter === 'rfq-finalized-quote' ? 'Selected Supplier' : '', // Add a default label if neither condition is met
      value: null,
      key: parameter === 'rfq-finalized-quote' ? 'selected_supplier' : '', // Add a default key if neither condition is met
    },
  ]);

  const [rejectedListValues, setRejectedListValues] = useState([
    { label: 'Rejected Reason', value: null, key: 'rejected_reason' },
    { label: 'Rejected By', value: null, key: 'rejected_by' },
  ]);
  const [newSuppIdList, setNewSuppIdList] = useState([]);
  let addState = {
    itemNum: '',
    unit: '',
    qnty: '',
    itemDesc: '',
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 9000);

    return () => clearTimeout(timer);
  }, []);

  const onAddCommentsChangeHandler = (event) => {
    setAddCommentsValue(event.target.value);
  };

  useEffect(() => {
    if (rfqId) {
      dispatch(getRFQEventLog(rfqId));
    }
  }, [rfqId, statusVal]);

  useEffect(() => {
    if (manualSelection?.value) {
      funcToSetResponseMessage('success', ' succesfully selected supplier');
      setQuoteId(manualSelection.value.quoteId);
      setSuppId(manualSelection.value.supplierId);
      setManualSelectionPopup(false);
    }
  }, [manualSelection?.value]);

  useEffect(() => {
    if (rfqId) {
      dispatch(getEventTime(rfqId)).then((action) => {
        if (action.payload?.value) {
          // setRfqEventTime(action.payload.value);
        }
      });
    }
  }, [rfqId]);

  useEffect(() => {
    if (quotationDataTables?.value) {
      setquotationDataTables(quotationDataTables.value);
      const quoteSupplierListing = quotationDataTables.value.find(
        (element) => element.uniqueKey === 'quoteSupplierListing'
      );

      const orderedPolicyBeans =
        quoteSupplierListing?.value?.orderedPolicyBeans || [];

      const quoteDTOList = quoteSupplierListing?.value?.quoteDTOList || [];
      const dynamicColumns = [
        {
          title: '#',
          width: 'auto',
          dataIndex: 'serialNo',
          fixed: 'left',
        },
        {
          title: 'Supplier Name',
          width: 100,
          dataIndex: 'supplierName',

          ellipsis: true,
          render: (text) => {
            const isObject = typeof text === 'object' && text !== null;
            const rawText = isObject ? JSON.stringify(text) : text;
            const displayText =
              rawText === null ||
              rawText === undefined ||
              rawText === 'null' ||
              String(rawText).trim() === ''
                ? ''
                : rawText;

            return (
              <Tooltip title={displayText}>
                <div
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '150px',
                  }}
                >
                  {displayText}
                </div>
              </Tooltip>
            );
          },
        },
        ...orderedPolicyBeans
          .filter((key) => key.trim() !== '') // Remove empty string keys
          .map((key) => ({
            title:
              key
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .charAt(0)
                .toUpperCase() +
              key
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .slice(1),
            dataIndex: key,
            width: 100,
            ellipsis: true,
            render: (text) => {
              const isObject = typeof text === 'object' && text !== null;
              const rawText = isObject ? JSON.stringify(text) : text;
              const displayText =
                rawText === null ||
                rawText === undefined ||
                rawText === 'null' ||
                String(rawText).trim() === ''
                  ? ''
                  : rawText;

              const num = Number(displayText);
              const isDecimal =
                !isNaN(num) &&
                displayText !== '' &&
                displayText.toString().includes('.') &&
                isFinite(num);

              return (
                <Tooltip title={displayText}>
                  <div
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '150px',
                      textAlign: 'left',
                    }}
                  >
                    {displayText}
                  </div>
                </Tooltip>
              );
            },
          })),
      ];

      setColumns(dynamicColumns);

      const formatter = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });

      const formattedData = quoteDTOList.map((quote) => ({
        serialNo: quote.serialNo || ' ',
        supplierName: quote.supplierName || ' ',
        ...orderedPolicyBeans.reduce((acc, key) => {
          const value = quote[key];
          acc[key] =
            typeof value === 'number' ? formatter.format(value) : value ?? ' ';
          return acc;
        }, {}),
      }));

      setTableData(formattedData);
      const quoteItemListing = quotationDataTables.value.find(
        (element) => element.uniqueKey === 'quoteItemListing'
      );

      const supplierData = quoteItemListing?.value?.supplierNames || [];

      const quoteItemList = quoteItemListing?.value?.items || [];
      const ItemColumns = [
        {
          name: '#',
          selector: (row) => row.serialNo,
          width: '40px',
          fixed: 'left',
          style: {
            position: 'sticky',
            left: 0,
            background: '#fff',
            zIndex: 2,
            borderRight: '1px solid #ddd',
          },
        },
        {
          name: 'Items',
          selector: (row) => row.item,
          width: 200,
          fixed: 'left',
          style: {
            position: 'sticky',
            left: '10px',
            background: '#fff',
            zIndex: 2,
            borderRight: '1px solid #ddd',
          },
          ellipsis: true,
          render: (text) => (
            <Tooltip title={text}>
              <div
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: '150px',
                }}
              >
                {text}
              </div>
            </Tooltip>
          ),
        },
        {
          name: 'Quantity',
          selector: (row) => row.quantity,
          width: 100,
          fixed: 'left',
          style: {
            position: 'sticky',
            left: '260px',
            background: '#fff',
            zIndex: 2,
            borderRight: '1px solid #ddd',
          },
        },
        ...supplierData.map((key) => ({
          name: key.includes(' ')
            ? key.trim()
            : key
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .replace(/^./, (c) => c.toUpperCase()),
          selector: (row) => row[key] ?? ' ',
          width: 'auto',
          alignRight: true, // Flag for alignment
        })),
      ];

      const antItemColumns = ItemColumns.map((col, index) => ({
        title: col.name,
        key: col.name || `col-${index}`,
        width: col.width,
        fixed: col.fixed,
        align: col.alignRight ? 'right' : 'left', // Align header
        render: col.render
          ? (text, record) => col.render(col.selector(record))
          : (text, record) => {
              const displayText = col.selector(record);
              const num = Number(displayText);
              const isDecimal =
                !isNaN(num) &&
                displayText.toString().includes('.') &&
                displayText !== '';

              return (
                <div
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    textAlign: col.alignRight || isDecimal ? 'right' : 'left', // Align body cells
                  }}
                >
                  {displayText}
                </div>
              );
            },
      }));

      setItemColumns(antItemColumns);

      const transformedList = quoteItemList.map(
        ({ serialNo, item, supplierAmounts, supplierQuantities }) => {
          const flattenedSuppliers = Object.entries(supplierAmounts).reduce(
            (acc, [supplier, amount]) => {
              if (typeof amount === 'number') {
                acc[supplier] = formatter.format(amount);
              } else {
                acc[supplier] = amount ?? ' ';
              }
              return acc;
            },
            {}
          );

          const firstQuantity =
            Object.values(supplierQuantities).find((qty) => qty > 0) || 0;

          return {
            serialNo,
            item,
            ...flattenedSuppliers,
            quantity: firstQuantity,
          };
        }
      );

      setItemTableData(transformedList);
    }
  }, [quotationDataTables?.value]);

  useEffect(() => {
    if (assignedQuote?.value && newSuppIdList) {
      dispatch(
        getSupplierQuotations({ suppIdList: newSuppIdList, rfqId: rfqId })
      );
      dispatch(getQuoteDataTables(rfqId));
      dispatch(getEventTime(rfqId));
    }
    dispatch(clearAssignQuoteValue());
  }, [assignedQuote]);

  useEffect(() => {
    if (bestSupplier != null) {
      let tempArray = cloneDeep(formDetails);

      tempArray &&
        tempArray.map((elements) => {
          if (elements.uniqueKey === 'supplierTabManage') {
          }
        });
    }
  }, [bestSupplier]);

  useEffect(() => {
    if (deleteQuotationValue != null) {
      funcToSetResponseMessage('success', deleteQuotationValue);
    }
  }, [deleteQuotationslice]);

  useEffect(() => {
    if (
      statusVal === 'rfq-manage-quote' ||
      statusVal === 'req-final-quote' ||
      statusVal === 'rfq-finalized-quote'
    ) {
      dispatch(getQuoteDataTables(rfqId));
      dispatch(getEventTime(rfqId));
    }
  }, []);

  useEffect(() => {
    if (
      bestQuoteChartDetails?.value &&
      bestQuoteChartDetails.value.length > 0
    ) {
      const updatedChartData = bestQuoteChartDetails.value.map((chartItem) => {
        if (chartItem.quoteMasterDto && chartItem.quoteMasterDto.length > 0) {
          const bestQuoteItem = chartItem.quoteMasterDto.find(
            (quote) => quote.bestSupplier === true
          );

          if (bestQuoteItem) {
            const BestsupplierName = bestQuoteItem.supplierName;
            const supplierId = parseInt(bestQuoteItem.supplierId, 10);
            const quoteId = parseInt(bestQuoteItem.quoteId, 10);
            const bestSuppNum = bestQuoteItem.supplierNum;
            setSuppId(supplierId);
            setQuoteId(quoteId);
            SetBestSupplier(BestsupplierName);
            setBestSuppNum(bestSuppNum);
          }
        }
      });
    }
  }, [bestQuoteChartDetails]);

  useEffect(() => {
    if (supplierQuatations && supplierQuatations.value) {
      setSupplierFormDetails(supplierQuatations.value);
    }
  }, [manageQuoteDetailsData]);

  useEffect(() => {
    if (saveSupplierRes && saveSupplierRes.value) {
      funcToSetResponseMessage('success', 'Details saved successfully');
    }

    return () => {
      dispatch(resetsaveSupplierDetails());
    };
  }, [saveSupplierRes]);

  const handleCloseModal = () => {
    dispatch(resetCompareQuote());
    setCompareDocModal(false);
    setFilePathForView([]);
    setSelectedNodeIds([]);
    setPathForCompare({});
    setMyArray([]);
  };

  useEffect(() => {
    if (suppId && suppId != null) {
      let tempArray = cloneDeep(supplierFormDetails);
      tempArray &&
        tempArray.map((elements) => {
          if (elements.suppId === suppId) {
            if (elements.text.startsWith('V')) {
              setArray(...elements.text.charAt(1));
            }
          }
        });
    }
  });

  useEffect(() => {
    let tempArray = cloneDeep(formDetails);

    tempArray &&
      tempArray.map((elements) => {
        if (elements.uniqueKey === 'supplierTabManage') {
          const selectedSupplier = elements.value?.find(
            (supplier) => supplier.selectedVendorFlag === true
          );

          if (selectedSupplier) {
            setSelectedVendor(selectedSupplier);
          }
        }
      });
  }, [formDetails]);
  useEffect(() => {
    if (editeventLogResponse && editeventLogResponse.value) {
      funcToSetResponseMessage('success', editeventLogResponse.value.text);
      dispatch(getDetailsByRfqId(rfqId));
      dispatch(getRFQEventLog(rfqId));
      dispatch(getEventTime(rfqId));
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'editeventLogResponse' }));
    };
  }, [editeventLogResponse]);

  useEffect(() => {
    if (updateStatusValue && updateStatusValue.value) {
      if (statusVal === 'rejected') {
        if (updateStatusValue.value === 'Error during adding data') {
          funcToSetResponseMessage('error', updateStatusValue.value);
          return;
        }

        funcToSetResponseMessage('success', 'Moved to draft successfully');

        setTimeout(() => {
          navigate({
            pathname: `/sourcing/rfq/rfqListing/draft`,
          });
          dispatch(resetUpdateStatus());
        }, 1000);
      } else {
        funcToSetResponseMessage('success', 'Quote moved to finalized quotes');

        setTimeout(() => {
          navigate(-1);
          dispatch(resetUpdateStatus());
        }, 1000);
      }
    }
  }, [updateStatusValue]);

  useEffect(() => {
    if (generatePrResponse && generatePrResponse.value) {
      funcToSetResponseMessage('success', generatePrResponse.value);
    }

    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'generatePrResponse' }));
    };
  }, [generatePrResponse]);

  useEffect(() => {
    const parameter = domParameters.parameter;
    let heading = parameter.charAt(0).toUpperCase() + parameter.slice(1);

    heading = 'Bidding /    ' + heading + '   (RFQ)';

    if (parameter == 'req-final-quote') {
      setHeading('Bidding / Final Quotation');
    } else if (parameter == 'rfq-manage-quote') {
      setHeading('Bidding / Manage Quotation');
    } else if (parameter == 'rfq-finalized-quote') {
      setHeading('Bidding / Finalized Quotation');
    } else {
      setHeading(heading);
    }

    if (parameter === 'rfq-manage-quote') {
      setActiveTab('Quote');
    } else {
      setActiveTab('RFQ');
    }
    dispatch(sourcingResetStateField({ fieldName: 'saveDraftRfqResponse' }));
  }, [props, parameter]);
  useEffect(() => {
    if (lastVal == null || lastVal === '0') {
      setQuoteFlag(true);
    }
  }, [rfqId]);
  useEffect(() => {
    if (saveRfqResponse && saveRfqResponse.value) {
      setTemplatePopUp(false);
      if (saveRfqResponse.value === 'Error during adding data') {
        funcToSetResponseMessage('error', saveRfqResponse.value);
      } else {
        funcToSetResponseMessage('success', 'RFQ details saved successfully ');
        // dispatch(getRFQDetailsForm());
        setFlagValue(true);
        setTemplateName();
      }
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'saveRfqResponse' }));
    };
  }, [saveRfqResponse]);

  useEffect(() => {
    if (rejectResponse && rejectResponse.value) {
      setTemplatePopUp(false);
      if (rejectResponse.value === 'Error during adding data') {
        funcToSetResponseMessage('error', rejectResponse.value);
        setSaveRejectPopup(false);
      } else {
        funcToSetResponseMessage('success', rejectResponse.value);
        dispatch(getDetailsByRfqId(rfqId));
        dispatch(getEventTime(rfqId));
        setSaveRejectPopup(false);
      }
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'rejectResponse' }));
    };
  }, [rejectResponse]);

  const addValueToArray = (value) => {
    setMyArray((prevArray) => {
      const updatedArray = [...prevArray, value];
      return updatedArray.slice(-2);
    });
  };

  const handleSelectedNodesChange = ({
    selectedNodeIds,
    pathForCompare,
    filePath,
    deselectedNodeId,
  }) => {
    setFilePathForView((prevFilePaths) => {
      const newFilePaths = [...prevFilePaths, filePath]; // Add new filePath
      const cleanedFilePaths = newFilePaths.filter(
        (path) => path !== null && path !== undefined && path !== ''
      );
      return cleanedFilePaths;
    });

    setSelectedNodeIds(selectedNodeIds);

    addValueToArray(pathForCompare);

    if (pathForCompare) {
      setPathForCompare((prevFilePaths) => ({
        ...prevFilePaths,
        [selectedNodeIds[selectedNodeIds.length - 1]]: pathForCompare,
      }));
    }

    if (deselectedNodeId) {
      setPathForCompare((prevFilePaths) => {
        const newFilePaths = { ...prevFilePaths };

        delete newFilePaths[deselectedNodeId];
        const updatedFilePaths = Object.values(newFilePaths).filter(
          (path) => path !== null && path !== undefined && path !== ''
        );

        setFilePathForView(updatedFilePaths);
        return newFilePaths;
      });
    }
    setEnableEdit(true);
  };

  useEffect(() => {
    if (submitRfqResponse && submitRfqResponse.value) {
      funcToSetResponseMessage('success', ' RFQ details saved successfully  ');
    }
    return () => {
      dispatch(
        sourcingResetStateField({ fieldName: 'saveRFQDetailsDraftForm' })
      );
    };
  }, [submitRfqResponse]);

  useEffect(() => {
    if (docUploadAttachment && docUploadAttachment.value) {
      funcToSetResponseMessage('success', docUploadAttachment.value);
      dispatch(getRFQItemTree(lastVal));
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'docUploadAttachment' }));
    };
  }, [docUploadAttachment]);

  const callPopup = () => {
    let tempArray = cloneDeep(formDetails);

    tempArray?.forEach((elements) => {
      if (elements.uniqueKey === 'itemTab') {
        elements.formSubDetailsInternalDTOList?.forEach((items) => {
          let item = elements.value.length + 1;
          addState = { ...addState, itemNum: item }; // ✅ Creating a new object
          setManualAddData(addState);
        });
      }
    });

    setAddFn('Add');
    setAddPopup(true);
    const updatedAddFormDetails = addFormDetails.map((items) => ({
      ...items,
      value: '',
    }));

    setAddFormDetails(updatedAddFormDetails);
  };

  const callEditItemPopup = (obj, ind) => {
    setSaveStatus(statusVal);
    setSaveFlag(true);
    setIndexVal(ind);
    setEditItemNum(obj.itemNum);
    let tempArray = cloneDeep(formDetails);

    tempArray &&
      tempArray.map((elements) => {
        if (elements.uniqueKey === 'ItemDetails') {
          elements.formSubDetailsInternalDTOList &&
            elements.formSubDetailsInternalDTOList.map((entry) => {
              entry.uniqueKey === 'unit' ? (entry.value = obj.unit) : '';
              entry.uniqueKey === 'description'
                ? (entry.value = obj.itemDesc)
                : '';
              entry.uniqueKey === 'quantity' ? (entry.value = obj.qnty) : '';
            });
        }
      });
    setFormDetails(tempArray);

    setAddPopup(false);
    setEditPopup(true);
  };

  useEffect(() => {
    let body = {
      suppName: '',
      suppNum: '',
      flagValue: '',
    };
    if (fileDataValue && fileDataValue.name) {
      body.suppName = fileDataValue.name;
      body.flagValue = true;
      body.suppNum = fileDataValue.suppNumber;

      let supplierTabArray = cloneDeep(formDetails);

      supplierTabArray &&
        supplierTabArray !== undefined &&
        supplierTabArray.map((items) => {
          if (items.uniqueKey === 'supplierTab') {
            items.value.push(body);
          }
        });
      setFormDetails(supplierTabArray);
      setFileDataValue('undefined');
    }
  }, []);

  useEffect(() => {
    if (lastVal != 0) {
      if (
        statusVal === 'rfq-manage-quote' ||
        statusVal === 'req-final-quote' ||
        statusVal === 'approval' ||
        statusVal === 'approved' ||
        statusVal === 'createdRfq' ||
        statusVal == 'rfq-finalized-quote'
      ) {
        dispatch(getManageQuoteDetailsByRfqId(lastVal));
        dispatch(getDetailsByRfqId(lastVal));
        dispatch(getEventTime(rfqId));
        setOpenNewUI(true);
      } else {
        setOpenNewUI(true);
        dispatch(getDetailsByRfqId(lastVal));
        dispatch(getRFQItemTree(lastVal));
        dispatch(getRFQEventLog(lastVal));
        setAttachPopup(false);
      }
    } else {
      setOpenNewUI(true);
      dispatch(getRFQDetailsForm());
      dispatch(getOldRfqTemplatesForm());
      //  window.location.reload(true);
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'rfqItemTreeList' }));
    };
  }, [statusVal]);

  useEffect(() => {
    if (rfqdetailsForm && rfqdetailsForm.value) {
      setFormDetails(rfqdetailsForm.value);
    }
  }, [rfqdetailsForm]);

  useEffect(() => {
    if (manageQuoteDetailsData && manageQuoteDetailsData.value) {
      setFormDetails(manageQuoteDetailsData.value);
    }
  }, [manageQuoteDetailsData]);

  useEffect(() => {
    if (saveApprovalDetails && saveApprovalDetails.value) {
      if (saveApprovalDetails.value === 'Error during adding data') {
        funcToSetResponseMessage('error', saveApprovalDetails.value);
      } else {
        if (saveApprovalDetails.value === 'Values saved successfully')
          funcToSetResponseMessage(
            'success',
            'RFQ approved and sent to suppliers.'
          );
        dispatch(getDraftDataList(statusVal));
      }
      setTimeout(() => {
        navigate({
          pathname: `/sourcing/rfq/rfqListing/approval`,
        });
      }, 2000);
    }
    return () => {
      dispatch(resetsaveApprovalDetail());
    };
  }, [saveApprovalDetails]);

  useEffect(() => {
    if (saveApprovalDetailsFailed?.message)
      funcToSetResponseMessage('error', saveApprovalDetailsFailed.message);
  }, [saveApprovalDetailsFailed]);

  useEffect(() => {
    if (saveDraftRfqResponse?.value && statusVal === 'approval') {
      funcToSetResponseMessage('success', 'Data Saved Successfully');
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'saveDraftRfqResponse' }));
    };
  }, [saveDraftRfqResponse]);

  useEffect(() => {
    oldRfqTemplateResponse && oldRfqTemplateResponse.value
      ? setOldRfqTemplateForm(oldRfqTemplateResponse.value)
      : '';
  }, [oldRfqTemplateResponse]);

  useEffect(() => {
    getRfqTemplateResponse && getRfqTemplateResponse.value
      ? setFormDetails(getRfqTemplateResponse.value)
      : '';
  }, [getRfqTemplateResponse]);

  useEffect(() => {
    if (eventlogResponse) {
      setTraceEvents(eventlogResponse.value);
      let data = null;
      dispatch(sourcingResetStateField({ fieldName: 'eventlogResponse' }));
    }
  }, [eventlogResponse]);

  useEffect(() => {
    if (rfqId == null) {
      setTraceEvents('');
    }
  });

  const showPopup = (suppID, quoteId, id) => {
    setRFQModalOpen(true);

    setSuppId(suppID);
    setQuoteId(quoteId);
    setVersion(id);
  };

  const handleWorkFlowEvent = (quoteId) => {
    dispatch(quoteEventLog(quoteId));
  };

  useEffect(() => {
    if (formDetails && formDetails !== undefined) {
      formDetails.map((items) => {
        if (items.uniqueKey === 'ItemDetails') {
          if (
            items.formSubDetailsInternalDTOList &&
            items.formSubDetailsInternalDTOList !== undefined
          ) {
            let tempArray = cloneDeep(items.formSubDetailsInternalDTOList);
            tempArray &&
              tempArray.map((elements) => {
                if (elements.displayName === 'Item') {
                  elements.value = item;
                }
              });

            setAddFormDetails(items.formSubDetailsInternalDTOList);
            setEditFormDetails(items.formSubDetailsInternalDTOList);
          }
        } else if (items.uniqueKey === 'itemTab') {
          setItemNumber(items.value.length + 1);
        }
      });
    }
  }, [formDetails]);

  useEffect(() => {
    let tempArray = cloneDeep(formDetails);
    if (tempArray && tempArray !== undefined) {
      tempArray.map((element) => {
        if (element.uniqueKey === 'itemQuoteTab') {
          if (element.value && element.value !== undefined) {
            element.value.map((items) => {
              if (items && items.flag) {
                items.flag === true ? (items.rfqTermsFlag = items.flag) : '';
                setFormDetails(tempArray);
              }
            });
          }
        }
      });
    }
  }, [checkbox]);

  useEffect(() => {
    if (detailsData?.value) {
      setFormDetails(detailsData.value);
      detailsData.value
        .flatMap((item) => item.formSubDetailsInternalDTOList || [])
        .forEach((element) => {
          switch (element.uniqueKey) {
            case 'rfq_num':
              listValues[0].value = element?.value;
              break;
            case 'rfq_date':
              listValues[1].value = element?.value;
              break;
            case 'requestor':
              listValues[2].value = element?.value;
              break;
            case 'buyer':
              const buyerName =
                element.comboBoxOptions?.find(
                  (option) => option.commonId === element?.value
                )?.commonName || '';
              listValues[3].value = buyerName;
              break;
            default:
              break;
          }
        });
    }
  }, [detailsData]);

  useEffect(() => {
    if (
      !manageQuoteDetailsData ||
      !Array.isArray(manageQuoteDetailsData.value)
    ) {
      return;
    }

    const supplierData =
      manageQuoteDetailsData.value.find(
        (item) => item.uniqueKey === 'supplierTabManage'
      )?.value || [];

    const newSuppIdList = supplierData
      .map((supplier) => supplier.suppId)
      .filter((id) => id != null);

    const quoteIdListData = supplierData
      .map((supplier) => supplier.quoteId)
      .filter((id) => id != null);

    setNewSuppIdList(newSuppIdList);

    if (newSuppIdList.length > 0) {
      dispatch(
        getSupplierQuotations({ suppIdList: newSuppIdList, rfqId: rfqId })
      );
      const method = secondMethodFlag ? 'method2' : 'method1';

      dispatch(
        getBestQuoteChartDetails({
          supplierId: newSuppIdList,
          rfqId: rfqId,
          method: method,
        })
      );
    }

    SetBestSupplier('');
  }, [manageQuoteDetailsData, deleteQuotationslice, secondMethodFlag]);

  const hasAllParentsWithChildren = (data) => {
    const parents = data?.filter((item) => item.parent === 0);
    return parents.every((parent) =>
      data?.some((item) => item.parent === parent.id)
    );
  };

  useEffect(() => {
    if (
      !manageQuoteDetailsData ||
      !Array.isArray(manageQuoteDetailsData.value)
    ) {
      return;
    }

    const supplierData =
      manageQuoteDetailsData.value.find(
        (item) => item.uniqueKey === 'supplierTabManage'
      )?.value || [];

    const newSuppIdList = supplierData
      .map((supplier) => supplier.suppId)
      .filter((id) => id != null);
  }, [manageQuoteDetailsData]);

  const template = (data) => {
    setTemplateName(data);
    setOldQuoteFlag(true);
  };

  const supplierNames = useMemo(
    () => tableData.map((item) => item.supplierName.trim()),
    []
  );

  // Get selected supplier name
  const selectedSupplier = useMemo(() => {
    const selectedItem = tableData.find(
      (item) => item.serialNo === selectedRow
    );
    return selectedItem ? selectedItem.supplierName.trim() : null;
  }, [selectedRow]);

  const highlightedColumns = useMemo(() => {
    return itemColumns.map((col) => ({
      ...col,
      className: col.title === selectedSupplier ? 'highlight-column' : '',
      onCell: () => ({
        className: col.title === selectedSupplier ? 'highlight-column' : '',
      }),
    }));
  }, [selectedSupplier, itemColumns]);

  const updateProjectValue = (formDetails) => {
    formDetails.forEach((formDetail) => {
      // Check for the object with uniqueKey "Create RFQ"
      if (
        formDetail.uniqueKey === 'rfqDetails' &&
        Array.isArray(formDetail.formSubDetailsInternalDTOList)
      ) {
        // Iterate over formSubDetailsInternalDTOList to find the sub-detail with uniqueKey "project"
        formDetail.formSubDetailsInternalDTOList.forEach((subDetail) => {
          if (
            subDetail.uniqueKey === 'project' &&
            typeof subDetail.value === 'string'
          ) {
            // Find the matching option in comboBoxOptions
            const matchingOption = subDetail.comboBoxOptions?.find(
              (option) => option.commonName === subDetail.value
            );

            // Update the value to commonId if a match is found
            if (matchingOption) {
              subDetail.value = matchingOption.commonId;
            }
          }
        });
      }
    });
  };

  const handleRejectApprove = () => {
    // let status="approval";
    let statusValue = 'Approval';

    dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: statusValue }));
  };
  const handleSave = () => {
    dispatch(
      saveDraftRFQDetailsForm({ formDetails: formDetails, rfqId: rfqId })
    );
  };

  // const handleDraftSave = () => {
  //   if (statusVal === 'manual') {
  //     let rfqId = 0;
  //     if (projectName.length === 0 && requestorName.length === 0) {
  //       funcToSetResponseMessage(
  //         'error',
  //         'Fill in the project name and  requestor name'
  //       );
  //       return;
  //     }

  //     dispatch(sourcingAction.saveDraftRFQDetailsForm(formDetails, rfqId));
  //   } else {
  //     //updating the value of project from project name to project id
  //     updateProjectValue(formDetails);
  //     dispatch(sourcingAction.saveDraftRFQDetailsForm(formDetails, rfqId));
  //   }

  //   // setTimeout(() => {
  //   //   history.push({
  //   //     pathname: `/${menuData}/${submenu}/rfqListing/draft`,
  //   //   });
  //   // }, 2000);
  // };

  const openDocumentModal = (quoteId) => {
    setQuoteId(quoteId);
    setOpenDocumentModal(true);
  };

  const handleManualSelection = () => {
    setManualSelectionPopup(true);
  };

  const handleTemplate = () => {
    setSaveTemplatePopup(true);
    // dispatch(sourcingAction.saveTemplateRFQDetailsForm(formDetails));
  };

  const handleSubmit = async () => {
    const isConfirmed = await confirm(' Are you sure you want to submit?');

    if (isConfirmed) {
      if (rfqId != null && rfqId !== undefined) {
        let status = 'Approval';
        dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: status }));
      }
    }
  };

  const handleFinalizeQoute = async () => {
    // const hasNullQuoteId = quoteIdList.some((quoteId) => quoteId === null || quoteId === undefined);

    let response = await dispatch(getEventTime(rfqId)).unwrap();
    const latestEventTime = response?.value;

    const result = hasAllParentsWithChildren(supplierQuatations?.value);
    if (!result) {
      funcToSetResponseMessage(
        'error',
        'Quote ID is null or undefined for some suppliers'
      );

      setQuoteIdList(false);
    } else {
      const isConfirmed = await confirm(
        'Are you sure you want to move this to Finalize Quote?'
      );
      if (!isConfirmed) return;
      if (isConfirmed) {
        const EventDateAndTimeDTO = {
          eventDateAndTime: latestEventTime,
        };
        dispatch(
          updateStatus({
            eventData: EventDateAndTimeDTO,
            rfqId: rfqId,
            status: statusVal,
          })
        );

        setQuoteIdList(false);
      }
    }
  };
  const handleFinalizeQuote = async () => {
    // if (suppId === null || quoteId === null) {
    //   const isConfirmed = await confirm(
    //     'The supplier or quote is not selected. Please select them manually before proceeding.'
    //   );
    //   if (isConfirmed) {
    //     setManualSelectionPopup(true);
    //   }
    // } else {
    //   dispatch(sourcingAction.saveSelectedQuote(suppId, quoteId, rfqId));

    //   funcToSetResponseMessage('success', 'Quote is finalized successfully');
    // }

    setBestQuoteModal(true);
  };

  const handleNewVendor = () => {
    dispatch(getSupplierListing('registration'));
    setIsModalOpen(true);
  };

  const handleDraftSubmit = async () => {
    if (saveCheckFlag === true) {
      const isConfirmed = await confirm('Do you want to save the updates  ?');
      if (isConfirmed) {
        const errors = [];
        const validate = (condition, message) =>
          !condition && errors.push(message);

        const supplierTab = formDetails.find(
          (item) => item.uniqueKey === 'supplierTab'
        );
        const itemsTable = formDetails.find(
          (item) => item.uniqueKey === 'itemTab'
        );
        const itemsQuote = formDetails.find(
          (item) => item.uniqueKey === 'itemQuoteTab'
        );

        validate(supplierTab?.value?.length > 0, 'Add Supplier');
        validate(itemsTable?.value?.length > 0, 'Add line items');
        validate(
          itemsQuote?.value?.some((row) => row.rfqTermsFlag === true),
          'select atleast  one item to be included in quote'
        );

        if (errors.length) {
          funcToSetResponseMessage('error', errors.join(', '));
        } else {
          dispatch(
            saveDraftRFQDetailsForm({ formDetails: formDetails, rfqId: rfqId })
          );
          let status = 'pdm';
          dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: status }));
          navigate(`${statusVal}`, { state: rfqId });
        }
      }
    } else {
      const isConfirmed = await confirm(' Are you sure you want to submit?');
      if (rfqId != null && rfqId !== undefined) {
        const errors = [];
        const validate = (condition, message) =>
          !condition && errors.push(message);

        const supplierTab = formDetails.find(
          (item) => item.uniqueKey === 'supplierTab'
        );
        const itemsTable = formDetails.find(
          (item) => item.uniqueKey === 'itemTab'
        );
        const itemsQuote = formDetails.find(
          (item) => item.uniqueKey === 'itemQuoteTab'
        );

        validate(supplierTab?.value?.length > 0, 'Add Supplier');
        validate(itemsTable?.value?.length > 0, 'Add line items');
        validate(
          itemsQuote?.value?.some((row) => row.rfqTermsFlag === true),
          'select atleast  one item to be included in quote'
        );

        // Show errors or proceed with actions
        if (errors.length) {
          funcToSetResponseMessage('error', errors.join(', '));
        } else {
          let status = 'pdm';
          dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: status }));
          navigate(`${statusVal}`, { state: rfqId });
        }
      }
    }
  };

  const handlePdmSubmit = async () => {
    if (saveCheckFlag === true) {
      const isConfirmed = await confirm('Do you want to save the updates  ?');
      if (isConfirmed) {
        const errors = [];
        const validate = (condition, message) =>
          !condition && errors.push(message);

        const supplierTab = formDetails.find(
          (item) => item.uniqueKey === 'supplierTab'
        );
        const itemsTable = formDetails.find(
          (item) => item.uniqueKey === 'itemTab'
        );
        const itemsQuote = formDetails.find(
          (item) => item.uniqueKey === 'itemQuoteTab'
        );

        validate(supplierTab?.value?.length > 0, 'Add Supplier');
        validate(itemsTable?.value?.length > 0, 'Add line items');
        validate(
          itemsQuote?.value?.some((row) => row.rfqTermsFlag === true),
          'select atleast  one item to be included in quote'
        );

        if (errors.length) {
          funcToSetResponseMessage('error', errors.join(', '));
        } else {
          let status = 'approval';
          dispatch(
            saveDraftRFQDetailsForm({ formDetails: formDetails, rfqId: rfqId })
          );
          dispatch(generateRfqPdf(rfqId));
          setTimeout(() => {
            dispatch(generateRfqPdf(rfqId));
            dispatch(createRfqEmailPreview(rfqId));
            dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: status }));
          }, 2000);
          setTimeout(() => {
            navigate(`approval`);
          }, 3000);
        }
      } else {
        if (rfqId != null && rfqId !== undefined) {
          const errors = [];
          const validate = (condition, message) =>
            !condition && errors.push(message);

          const supplierTab = formDetails.find(
            (item) => item.uniqueKey === 'supplierTab'
          );
          const itemsTable = formDetails.find(
            (item) => item.uniqueKey === 'itemTab'
          );
          const itemsQuote = formDetails.find(
            (item) => item.uniqueKey === 'itemQuoteTab'
          );

          validate(supplierTab?.value?.length > 0, 'Add Supplier');
          validate(itemsTable?.value?.length > 0, 'Add line items');
          validate(
            itemsQuote?.value?.some((row) => row.rfqTermsFlag === true),
            'select atleast  one item to be included in quote'
          );

          // Show errors or proceed with actions
          if (errors.length) {
            funcToSetResponseMessage('error', errors.join(', '));
          } else {
            let status = 'approval';
            dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: status }));
            dispatch(generateRfqPdf(rfqId));
            dispatch(createRfqEmailPreview(rfqId));
          }
        }
      }
    } else {
      const isConfirmed = await confirm(' Are you sure you want to submit?');
      if (isConfirmed) {
        if (rfqId != null && rfqId !== undefined) {
          const errors = [];
          const validate = (condition, message) =>
            !condition && errors.push(message);

          const supplierTab = formDetails.find(
            (item) => item.uniqueKey === 'supplierTab'
          );
          const itemsTable = formDetails.find(
            (item) => item.uniqueKey === 'itemTab'
          );
          const itemsQuote = formDetails.find(
            (item) => item.uniqueKey === 'itemQuoteTab'
          );

          validate(supplierTab?.value?.length > 0, 'Add Supplier');
          validate(itemsTable?.value?.length > 0, 'Add line items');
          validate(
            itemsQuote?.value?.some((row) => row.rfqTermsFlag === true),
            'select atleast  one item to be included in quote'
          );

          if (errors.length) {
            funcToSetResponseMessage('error', errors.join(', '));
          } else {
            let status = 'approval';
            dispatch(saveRFQDetailsDraftForm({ rfqId: rfqId, status: status }));
            dispatch(generateRfqPdf(rfqId));
            dispatch(createRfqEmailPreview(rfqId));

            setTimeout(() => {
              navigate(`approval`);
            }, 2000);
          }
        }
      }
    }
  };

  const handleCancel = () => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'supp_comment' ? (entry.value = '') : '';
      entry.type === 'PageSubTitle' &&
        entry.formSubDetailsInternalDTOList &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.value = '';
        });
    });
    setFormDetails(tempArray);
  };

  const handleMoveToDraft = async () => {
    dispatch(getEventTime(rfqId));

    const isConfirmed = await confirm(
      'Are you sure you want to move this to Draft?'
    );
    if (!isConfirmed) return;
    if (isConfirmed) {
      const EventDateAndTimeDTO = {
        eventDateAndTime: rfqEventTime?.value,
      };
      dispatch(
        updateStatus({
          eventData: EventDateAndTimeDTO,
          rfqId: rfqId,
          status: 'draft',
        })
      );
    }
  };

  // Add handleBack function
  const handleBack = async () => {
    if (statusVal === 'rfq-manage-quote') {
      navigate({
        pathname: `/sourcing/rfq/manage-quote/${statusVal}`,
      });
    } else if (statusVal === 'rfq-finalized-quote') {
      navigate({
        pathname: `/sourcing/rfq/finalized-quote/${statusVal}`,
      });
    } else if (statusVal === 'req-final-quote') {
      navigate({
        pathname: `/sourcing/rfq/rfq-quote/${statusVal}`,
      });
    } else {
      navigate({
        pathname: `/sourcing/rfq/rfqListing/${statusVal}`,
      });
    }
  };

  const handleApprove = async () => {
    const isConfirmed = await confirm(' Are you sure you want to Approve?');
    if (isConfirmed) {
      if (rfqId !== null && rfqId !== undefined) {
        dispatch(sendRfqEmail(rfqId));

        // dispatch(sourcingAction.generateRfqPdf(rfqId));
        // dispatch(sourcingAction.createRfqEmailPreview(rfqId));
        // let status = 'Approved';
        // dispatch(sourcingAction.saveRFQDetailsDraftForm(rfqId, status));
      }
    }
  };

  const handleApproval = async () => {
    let status = 'approved';

    // dispatch(sourcingAction.generateRfqPdf(rfqId));
    // dispatch(sourcingAction.createRfqEmailPreview(rfqId));
    if (rfqId !== null && rfqId !== undefined) {
      // dispatch(sourcingAction.sendRfqEmail(rfqId));
      const EventDateAndTimeDTO = {
        eventDateAndTime: rfqEventTime?.value,
      };

      const isConfirmed = await confirm('Are you sure you want to proceed?');

      if (isConfirmed) {
        dispatch(
          saveApprovalDetail({
            eventData: EventDateAndTimeDTO,
            rfqId: rfqId,
            status: status,
          })
        );
      }
    }
  };

  const hadleGenerate = async () => {
    if (rfqId !== null && rfqId !== undefined) {
      dispatch(sendGeneratePr(rfqId));
      // let status = 'Approved';
      // dispatch(sourcingAction.saveRFQDetailsDraftForm(rfqId, status));
    }
  };

  const handleReject = async () => {
    setSaveRejectPopup(true);
    // const isConfirmed = await confirm(' Are you sure you want to reject?');
    // if (isConfirmed) {
    // if (rfqId != null && rfqId != undefined) {
    //   let status = 'Rejected';
    //   dispatch(sourcingAction.saveRFQDetailsDraftForm(rfqId, status));
    // }
    // }
  };

  const handleCompare = () => {
    if (Array.isArray(filePathForView) && filePathForView.length === 2) {
      dispatch(compareQuote(filePathForView));
      setCompareDocModal(true);
    } else {
      dispatch(
        showDialog({
          showPopup: true,
          type: 'info',
          responseMessage: 'Please select 2 versions of quote !!',
          canClose: true,
          autoHide: false,
        })
      );
      setFilePathForView([]);
      setMyArray([]);
      setSelectedNodeIds([]);
    }
  };
  useEffect(() => {
    if (
      compareDocModal && // only act if modal is open due to compare
      filePathForView.length === 2 &&
      compareQuoteResponse?.value === null
    ) {
      dispatch(
        showDialog({
          showPopup: true,
          type: 'info',
          responseMessage: 'Files selected are similar',
          canClose: true,
          autoHide: false,
        })
      );

      setCompareDocModal(false);
      setFilePathForView([]);
      setMyArray([]);
      setSelectedNodeIds([]);
    }
  }, [compareQuoteResponse]);

  const handleCancelRfq = (e) => {
    navigate(-1);
  };
  const handleRejectSave = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');

    if (isConfirmed) {
      setSaveCheckFlag(true);
      let statusValue = 'rejected';
      dispatch(saveRejectCommentForm({ rfqId, statusValue, rejectComment }));

      setTimeout(() => {
        navigate({
          pathname: `/sourcing/rfq/rfqListing/approval`,
        });
      }, 1000);
    }
  };

  const handleManualSubmit = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');

    if (isConfirmed) {
      let status = 'approval';
      const EventDateAndTimeDTO = {
        eventDateAndTime: rfqEventTime?.value,
      };

      dispatch(
        saveApprovalDetail({
          eventData: EventDateAndTimeDTO,
          rfqId: rfqId,
          status: status,
        })
      );
    }
  };

  const handleEditApprove = () => {
    dispatch(getDetailsByRfqId(rfqId));
    setIsEditing(true);
  };

  const functionsName = {
    handleFinalizeQoute,
    handleSave,
    handleCancel,
    handleTemplate,
    handleSubmit,
    handleDraftSubmit,
    handlePdmSubmit,
    handleApproval,
    //handleDraftSave,
    handleReject,
    //handleManualSave,
    handleManualSubmit,
    handleRejectSave,
    handleRejectApprove,
    handleCancelRfq,
    hadleGenerate,
    handleCompare,
    handleApprove,
    handleManualSelection,
    handleNewVendor,
    handleMoveToDraft,
    handleBack,
    handleFinalizeQuote,
    handleEditApprove,
  };

  const fileVersionUpload = (fileWithDescription) => {
    setAttachPopup(false);
    dispatch(
      preprUploadAttachment({
        itemId: itemId,
        rfqId: rfqId,
        file: fileWithDescription.file,
      })
    );
  };

  const addPopupClose = () => {
    setAddFn(false);
    setAddPopup(false);
  };

  const editPopupClose = () => {
    setAddFn(false);
    setEditPopup(false);
  };
  useEffect(() => {
    if (multiselectFilterValue) {
      dispatch(
        getMultiselectFilterData({
          filterField: multiselectFilterValue,
          type: 'subscribe',
        })
      );
    }
  }, [multiselectFilterValue]);

  const invpartmatchsave = () => {
    setisLoading(true);
    setOnSaveClicked(true);
    setDisabledFlag(true);
  };
  const handleChange = (event) => {
    //
  };
  useEffect(() => {
    if (editedEmail?.value) {
      funcToSetResponseMessage('success', 'email edited successfully');
      setPreviewEmailModal(false);
    }
    return () => {
      dispatch(sourcingResetStateField({ fieldName: 'editedEmail' }));
    };
  }, [editedEmail?.value]);
  useEffect(() => {
    if (rejectResponseRfq?.value && statusVal === 'rejected') {
      funcToSetResponseMessage('error', rejectResponseRfq?.value);
    }
  }, [rejectResponseRfq?.value]);

  useEffect(() => {
    if (rejectResponseRfq?.value) {
      setRejectedListValues((prev) =>
        prev.map((item) => {
          if (item.key === 'rejected_reason') {
            return {
              ...item,
              value: rejectResponseRfq?.value?.displayMessage || null,
            };
          }
          if (item.key === 'rejected_by') {
            return {
              ...item,
              value: rejectResponseRfq?.value?.userName || null,
            };
          }
          return item;
        })
      );
    }
  }, [rejectResponseRfq]);
  useEffect(() => {
    if (statusVal === 'rejected') {
      dispatch(
        getReasonForRejectionInSourcing({ rfqId: rfqId, status: statusVal })
      );
    }
  }, [rfqId]);

  const handleTemplateChange = (event) => {
    setTemplateName(event.target.value);
  };

  const handleTemplateDescChange = (event) => {
    setTemplateDesc(event.target.value);
  };

  const handleRejectChange = (event) => {
    setRejectComment(event.target.value);
  };

  const handleTemplateSave = () => {
    if (rfqId != null && rfqId !== undefined) {
      if (getTemplateName === undefined) {
        setFlagValue(false);
      } else {
        dispatch(
          saveTemplateRFQDetailsForm({
            rfqId: rfqId,
            templateName: getTemplateName,
            description: getTemplateDesc,
            formDetails: formDetails,
          })
        );
        setSaveTemplatePopup(false);
      }
    }
  };
  const onEnterKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      onCommentSendClick();
    }
  };
  const onCommentSendClick = () => {
    if (addCommentsValue && addCommentsValue.replace(/\s/g, '').length) {
      dispatch(
        editEventLogByRfqId({ rfqId: rfqId, comment: addCommentsValue })
      );

      invpartmatchsave();
    }
  };

  const handleCancelDocumentModal = async () => {
    if (isEditMode) {
      const isConfirmed = await confirm(
        'Unsaved changes will be lost. Do you want to proceed?'
      );
      if (isConfirmed) {
        dispatch(quotationEditForm(quoteId));
        setOpenDocumentModal(false);
      }
    } else {
      setOpenDocumentModal(false);
    }
  };

  useEffect(() => {
    if (showEmailView?.value) {
      setviewEmail(showEmailView);
    }
  }, [showEmailView]);

  const showEmailPopup = (docId, emailId) => {
    dispatch(
      getSourcingInvEmailDetailsByInOutType({ docId: docId, emailId: emailId })
    );
    setIsEmailView(true);
  };

  const indexLookup = listValues.reduce((acc, item, idx) => {
    acc[item.key] = idx; // map each item's key to its index in the array
    return acc;
  }, {});

  const customStyles = {
    headStyle: {
      style: {
        position: 'sticky',
        left: 0,
        fontSize: '20px',
        top: 0,
        background: '#f9f9f9',

        borderBottom: '2px solid #ddd',
      },
    },
  };
  useEffect(() => {
    setChildTabKey('Quote Events');
  }, [activeTab]);
  useEffect(() => {
    if (
      statusVal === 'rfq-manage-quote' ||
      statusVal === 'req-final-quote' ||
      statusVal === 'rfq-finalized-quote'
    ) {
      setActiveTab('Quote');
    } else if (statusVal) {
      setActiveTab('RFQ');
    }
  }, [statusVal]);
  return (
    <DndProvider backend={HTML5Backend}>
      {/* <NavBlocker
        isDataChanged={enableEdit}
        isNavBlocked={(flag) => {
          // setIsNavBlocked(flag);
        }}
      /> */}
      <CommonSpinner visible={loading} />
      {!isLoading && (
        <>
          {' '}
          {compareDocModal &&
            compareQuoteResponse?.value &&
            filePathForView?.length === 2 && (
              <CompareDocs
                open={compareDocModal}
                diff={compareQuoteResponse?.value}
                files={filePathForView}
                onClose={handleCloseModal}
              />
            )}
          <div
            className="page-title"
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: statusVal === 'rfq-manage-quote' ? '0' : '16px',
            }}
          >
            <div>{heading}</div>
          </div>
          {activeTab && (
            <Tabs
              tabBarStyle={
                statusVal === 'rfq-manage-quote' ||
                statusVal === 'req-final-quote' ||
                statusVal === 'rfq-finalized-quote'
                  ? {}
                  : { display: 'none' }
              }
              activeKey={activeTab}
              onChange={setActiveTab}
            >
              {(statusVal === 'rfq-manage-quote' ||
                statusVal === 'req-final-quote' ||
                statusVal == 'rfq-finalized-quote') && (
                <TabPane
                  tab="Quote"
                  key="Quote"
                  style={{ fontFamily: 'Roboto' }}
                >
                  {statusVal === 'rfq-manage-quote' ||
                  statusVal === 'req-final-quote' ||
                  statusVal === 'approval' ||
                  statusVal === 'approved' ||
                  statusVal === 'createdRfq' ||
                  statusVal == 'rfq-finalized-quote' ? (
                    <QuoteHeader
                      listValues={listValues}
                      selectedVendor={selectedVendor ? selectedVendor : null}
                      noOfFields={selectedVendor ? 5 : 4}
                      setBestQuoteModal={setBestQuoteModal}
                      supplierName={bestSupplier}
                      compareModal={compareDocModal}
                    />
                  ) : (
                    ''
                  )}

                  {!isEditing ? (
                    <ButtonCommon functionsName={functionsName} />
                  ) : (
                    ''
                  )}
                  <div className="invoice-compare mt16">
                    <div className="manual-two-col-layout-right-30">
                      <div className="boxed">
                        {/* <div className="styledDatatable"> */}
                        {/* Replace your existing table with this */}
                        <div
                          className="page-title"
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                          }}
                        >
                          <span>Supplier Quote Comparison</span>
                          <Button
                            className="small mr20"
                            size="small"
                            onClick={() => setIsMissingFieldsDrawerOpen(true)}
                            style={{
                              fontSize: '13px',
                              padding: '4px 8px',
                              height: 'auto',
                            }}
                          >
                            Missing Fields
                          </Button>
                        </div>
                        <Table
                          columns={columns}
                          dataSource={tableData}
                          scroll={{ x: 'max-content' }}
                          rowKey="serialNo"
                          className="custom-table Sptable "
                          bordered={false}
                          pagination={false}
                          onRow={(record) => ({
                            onClick: () => setSelectedRow(record.serialNo),
                          })}
                          rowClassName={(record) =>
                            record.serialNo === selectedRow
                              ? 'selected-row'
                              : ''
                          }
                        />
                        <div className="page-title">Line item comparison</div>
                        {/* For the item table */}
                        <Table
                          columns={highlightedColumns}
                          dataSource={itemTableData}
                          scroll={{ x: 'max-content' }}
                          pagination={false}
                          rowKey="serialNo"
                          className="custom-table no-hover"
                        />
                      </div>
                      <div className="boxed" style={{ height: '120%' }}>
                        {(statusVal === 'rfq-manage-quote' ||
                          statusVal === 'req-final-quote' ||
                          statusVal === 'rfq-finalized-quote') && (
                          <div
                            className=""
                            style={{
                              flex: 1,
                              height: '55vh',
                              overflow: 'hidden',
                            }}
                          >
                            <div
                              className="page-title"
                              style={{
                                fontFamily: 'Roboto',
                              }}
                            >
                              {/* {isEmailView ? 'Email view' : 'Quotations'} */}
                              Quotations
                            </div>

                            <RfqQuoteTreeview
                              disabled={false}
                              form={() => null}
                              showPopup={showPopup}
                              showEmailPopup={showEmailPopup}
                              parameter={parameter}
                              submenu={submenu}
                              menuData={menuData}
                              rfqId={rfqId}
                              onSelectedNodesChange={handleSelectedNodesChange}
                              setDeleteQuotationValue={setDeleteQuotationValue}
                              compareModal={compareDocModal}
                              openDocumentModal={openDocumentModal}
                              workFlowEvent={false}
                              selectedSupplier={selectedSupplier}
                              selectedNodeIds={selectedNodeIds}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  {isRFQModalOpen ? (
                    <AddRFQModal
                      isRFQModalOpen={isRFQModalOpen}
                      suppId={suppId}
                      rfqId={rfqId}
                      quoteId={quoteId}
                      versionId={version}
                      newSuppIdList={newSuppIdList}
                      isModalClose={() => setRFQModalOpen(false)}
                    />
                  ) : (
                    ''
                  )}
                  <Modal
                    isOpen={documentModal}
                    onClick={handleCancelDocumentModal}
                    ariaHideApp={false}
                    style={{
                      content: {
                        width: '95vw',
                        maxWidth: '1800px',
                        height: '95vh',
                        margin: 'auto',
                        padding: 0,
                        borderRadius: '12px',
                        border: '1px solid #e0e0e0',
                        overflow: 'hidden',
                        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
                        background: '#ffffff',
                      },

                      overlay: {
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        zIndex: 9999,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      },
                    }}
                  >
                    {/* Modal Header */}
                    <div
                      style={{
                        background: '#ffffff',
                        borderBottom: '1px solid #e8e8e8',
                        padding: '16px 24px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        minHeight: '60px',
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          width: '100%',
                        }}
                      >
                        <div style={{ flex: 1 }}>
                          <h2
                            style={{
                              margin: 0,
                              fontSize: '16px',
                              fontWeight: '600',
                              color: '#262626',
                              letterSpacing: '0.2px',
                            }}
                          >
                            {statusVal === 'rfq-finalized-quote' ||
                            statusVal === 'req-final-quote'
                              ? 'Quotation'
                              : 'Quotation Edit'}
                          </h2>
                          <p
                            style={{
                              margin: '2px 0 0 0',
                              fontSize: '12px',
                              color: '#8c8c8c',
                            }}
                          >
                            Review and edit supplier quotation details
                          </p>
                        </div>
                        <div style={{ flex: 1, textAlign: 'center' }}>
                          <h3
                            style={{
                              margin: 0,
                              fontSize: '16px',
                              fontWeight: '600',
                              color: '#262626',
                              letterSpacing: '0.2px',
                            }}
                          >
                            Quote Document
                          </h3>
                        </div>
                      </div>
                    </div>
                    <div
                      onClick={handleCancelDocumentModal}
                      className="modal-close icon-close"
                      style={{
                        position: 'absolute',
                        right: '20px',
                        top: '20px',
                        fontSize: '18px',
                        cursor: 'pointer',
                        padding: '8px',
                        borderRadius: '50%',
                        background: '#fff',
                        border: '1px solid #e0e0e0',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                        zIndex: 10,
                        transition: 'all 0.2s ease',
                        color: '#8c8c8c',
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.background = '#f5f5f5';
                        e.target.style.color = '#262626';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = '#fff';
                        e.target.style.color = '#8c8c8c';
                      }}
                    ></div>

                    {/* Scrollable Content */}
                    <div
                      style={{
                        height: 'calc(95vh - 90px)', // subtract header height
                        overflowY: 'auto',
                        backgroundColor: '#fff',
                        padding: '24px',
                      }}
                    >
                      {/* Main Content */}
                      <div
                        style={{
                          background: '#fff',
                          borderRadius: '8px',
                          minHeight: '100%',
                        }}
                      >
                        <RfqQuotations
                          quoteId={quoteId}
                          parameter={parameter}
                          setIsEditMode={setIsEditMode}
                          rfqId={rfqId}
                          setOpenDocumentModal={setOpenDocumentModal}
                        />
                      </div>
                    </div>
                  </Modal>

                  <AntdModal
                    className="custom-ant-modal"
                    wrapClassName="custom-modal-wrapper"
                    open={bestQuoteModal}
                    onCancel={() => setBestQuoteModal(false)}
                    footer={null}
                    centered
                    width="95%"
                    maskClosable={false}
                    closable={true}
                    zIndex={2000}
                    bodyStyle={{
                      height: '90vh', // 👈 control height
                      overflowY: 'auto', // 👈 scroll if content overflows
                    }}
                  >
                    <FinalizeQuoteDashBoard
                      data={bestQuoteChartDetails}
                      supplierName={bestSupplier}
                      parameter={parameter}
                      supplierNumber={bestSuppNum}
                      setBestQuoteModal={setBestQuoteModal}
                      setSecondMethodFlag={setSecondMethodFlag}
                      rfqid={lastVal}
                      suppId={suppId}
                      quoteId={quoteId}
                    />
                  </AntdModal>

                  {isEmailView ? (
                    <Modal
                      className="ModalPoMatrix"
                      overlayClassName="ModalOverlay"
                      ariaHideApp={false}
                      isOpen={isEmailView}
                    >
                      <EmailContent
                        emailDetails={viewEmail}
                        onClose={() => {
                          setIsEmailView(false);
                          setviewEmail(null);
                        }}
                      />
                    </Modal>
                  ) : null}
                </TabPane>
              )}

              <TabPane tab="RFQ" key="RFQ" style={{ fontFamily: 'Roboto' }}>
                {statusVal === 'rfq-manage-quote' ||
                statusVal === 'req-final-quote' ||
                statusVal === 'approval' ||
                statusVal === 'approved' ||
                statusVal === 'createdRfq' ||
                statusVal == 'rfq-finalized-quote' ||
                statusVal == 'rejected' ? (
                  <QuoteHeader
                    listValues={
                      statusVal == 'rejected' ? rejectedListValues : listValues
                    }
                    selectedVendor={selectedVendor ? selectedVendor : null}
                    isQuoteRejected={statusVal == 'rejected' ? true : false}
                    noOfFields={
                      statusVal == 'rejected' ? 2 : selectedVendor ? 5 : 4
                    }
                    setBestQuoteModal={setBestQuoteModal}
                    supplierName={bestSupplier}
                  />
                ) : (
                  ''
                )}
                <Modal
                  className="ItemListModal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={isModal}
                  style={{ height: 200 + '%' }}
                >
                  <div
                    onClick={() => setIsModal(false)}
                    className="modal-close icon-close"
                  >
                    <DocumentViewer
                      fileURL={filepath}
                      fileType={'iframe'}
                      iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
                    />
                  </div>
                </Modal>
                <Modal
                  className="RejectModal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={saveTemplatePopup}
                  style={{
                    color: '#20202a',
                    backgroundColor: '#f3f3f3',
                    fontWeight: 'normal',
                    fontSize: '13px',
                    zIndex: 9999,
                    opacity: 1,
                    WebkitBoxShadow: `rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px`,
                    border: '1px solid #E8E8E8',
                  }}
                >
                  <div
                    onClick={() => setSaveTemplatePopup(false)}
                    className="modal-close icon-close"
                  ></div>{' '}
                  <div className="page-title"> Template Details </div>{' '}
                  <div>
                    <TextInput
                      label="Template Name"
                      name="template_name"
                      disabled={false}
                      required={true}
                      value={getTemplateName ? getTemplateName : ''}
                      onChange={(e) => handleTemplateChange(e)}
                      error={{
                        flag: flagValue === false ? true : false,
                        message: 'Please fill Template Name',
                      }}
                    />
                    <div className="mb20"></div>
                    <TextAreaInput
                      label="Template Description"
                      name="template_desc"
                      disabled={false}
                      value={getTemplateDesc ? getTemplateDesc : ''}
                      onChange={(e) => handleTemplateDescChange(e)}
                    />
                    <div className="mb20"></div>
                    <Button
                      className="small default mr20 mb20 fl button"
                      onClick={() => handleTemplateSave()}
                      // onClick={getTemplateName ? handleTemplateSave() : ''}
                    >
                      Save{' '}
                    </Button>{' '}
                  </div>
                </Modal>{' '}
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={saveRejectPopup}
                  style={{
                    content: {
                      width: '60%',

                      height: '60%',

                      top: '50%',

                      left: '50%',

                      right: 'auto',

                      bottom: 'auto',

                      marginRight: '-50%',

                      transform: 'translate(-50%, -50%)',

                      overflow: 'hidden',
                    },
                  }}
                >
                  <div
                    onClick={() => setSaveRejectPopup(false)}
                    className="modal-close icon-close"
                  ></div>{' '}
                  <div className="page-title"> Reject Reason </div>{' '}
                  <div>
                    <TextAreaInput
                      label="Comment"
                      name="comment"
                      disabled={false}
                      // value={comment ? comment : ''}
                      onChange={(e) => handleRejectChange(e)}
                    />
                    <div className="mb20"></div>
                    <Button
                      className="small default mr20 mb20 fl button"
                      onClick={() => handleRejectSave()}
                    >
                      Submit{' '}
                    </Button>{' '}
                  </div>
                </Modal>{' '}
                <PreviewRfqEmailModal
                  isModalOpen={previewEmailModal}
                  supplierName={suplierName}
                  isModalClose={() => setPreviewEmailModal(false)}
                  suppId={suppId}
                  rfqId={rfqId}
                  status={statusVal}
                />
                <div>
                  <DocUpload
                    labels={{
                      headLbl: 'Drag File or Click to upload',
                      formatLbl:
                        'Accept format: JPG, PNG, GIF, WebP, PDF, TXT, CSV, XLS, XLSX, DOC, DOCX, ZIP',
                      sizeLbl: 'Size: Max 5mb',
                    }}
                    isShow={attachPopup}
                    onCancel={() => setAttachPopup(false)}
                    onsubmitHandler={fileVersionUpload}
                  />
                </div>
                {isRFQModalOpen ? (
                  <AddRFQModal
                    isRFQModalOpen={isRFQModalOpen}
                    suppId={suppId}
                    rfqId={rfqId}
                    quoteId={quoteId}
                    versionId={version}
                    newSuppIdList={newSuppIdList}
                    isModalClose={() => setRFQModalOpen(false)}
                  />
                ) : (
                  ''
                )}
                {manualSelectionPopup ? (
                  <ManualSelectModal
                    isModalOpen={manualSelectionPopup}
                    isModalClose={() => setManualSelectionPopup(false)}
                    rfqId={rfqId}
                    quoteId={quoteId}
                    suppID={suppId}
                  />
                ) : null}
                {!isEditing ? (
                  <ButtonCommon functionsName={functionsName} />
                ) : (
                  ''
                )}
                <Modal
                  className="Modal-compareBox"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={bestQuoteModal}
                  style={{
                    content: {
                      width: '100%', // Set your desired width
                    },
                  }}
                >
                  {' '}
                  <div className="modal-close-fixed">
                    <div
                      onClick={() => setBestQuoteModal(false)}
                      className="modal-close rt icon-close"
                    ></div>
                  </div>
                  <FinalizeQuoteDashBoard
                    data={bestQuoteChartDetails}
                    supplierName={bestSupplier}
                    parameter={parameter}
                    supplierNumber={bestSuppNum}
                    setBestQuoteModal={setBestQuoteModal}
                    setSecondMethodFlag={setSecondMethodFlag}
                    suppId={suppId}
                    rfqId={lastVal}
                  />
                </Modal>
                <Modal
                  className="ModalPoMatrix"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={isModalOpen}
                  style={{
                    content: {
                      height: 'fit-content',
                    },
                  }}
                >
                  <span
                    className="modal-close icon-close fr "
                    onClick={() => setIsModalOpen(false)}
                  ></span>
                  {isModalOpen && (
                    <NewVendor
                      countryList={
                        supplierMastertableList?.value?.countryDtoList
                      }
                      supplierClass={
                        supplierMastertableList?.value?.supplierClassBeanList
                      }
                      supplierTypes={
                        supplierMastertableList?.value?.supplierTypesBeanList
                      }
                      supplierCategory={
                        supplierMastertableList?.value?.supplierCategoryBeanList
                      }
                    />
                  )}
                </Modal>
                <Modal
                  className="Modal-compareBox"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={documentModal}
                  style={{
                    content: {
                      width: '98%',
                    },
                  }}
                >
                  <h3
                    className="rfq-reference-title page-title"
                    style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#333',
                      letterSpacing: '0.5px',
                      borderBottom: '2px solid #ccc',
                      position: 'absolute',
                      top: '15px',
                    }}
                  >
                    {statusVal === 'rfq-finalized-quote' ||
                    statusVal === 'req-final-quote'
                      ? 'Quotation'
                      : 'Quotation Edit'}
                  </h3>
                  <div
                    onClick={() => handleCancelDocumentModal()}
                    className="modal-close icon-close"
                  ></div>
                  <RfqQuotations
                    quoteId={quoteId}
                    parameter={parameter}
                    setIsEditMode={setIsEditMode}
                    rfqId={rfqId}
                    handleCancelDocumentModal={handleCancelDocumentModal}
                  />
                </Modal>
                {isEditing ? (
                  <ManualRFQ
                    rfqId={lastVal}
                    status={statusVal}
                    edit={isEditing}
                    onSaveComplete={() => setIsEditing(false)}
                  />
                ) : [
                    'approved',
                    'rfq-manage-quote',
                    'req-final-quote',
                    'rfq-finalized-quote',
                    'approval',
                    'createdRfq',
                    'rejected',
                  ].includes(statusVal) ? (
                  <RfqPreview
                    rfqId={lastVal}
                    status={statusVal}
                    parentActiveTab={activeTab}
                  />
                ) : (
                  <ManualRFQ rfqId={lastVal} status={statusVal} />
                )}
              </TabPane>

              {statusVal === 'rfq-manage-quote' ||
              statusVal === 'req-final-quote' ||
              statusVal === 'rfq-finalized-quote' ? (
                <TabPane
                  tab="Workflow Events"
                  key="WorkFlow Events"
                  style={{ fontFamily: 'Roboto' }}
                >
                  <div className="boxed">
                    <Tabs activeKey={childTabKey} onChange={setChildTabKey}>
                      <TabPane tab="Quote Events" key="Quote Events">
                        <div className=" " style={{ width: 100 + '%' }}>
                          <Card>
                            <div
                              style={{
                                display: 'flex',
                                gap: '16px',
                                alignItems: 'flex-start',
                              }}
                            >
                              <RfqQuoteTreeview
                                disabled={false}
                                form={() => null}
                                showPopup={showPopup}
                                showEmailPopup={showEmailPopup}
                                parameter={parameter}
                                submenu={submenu}
                                menuData={menuData}
                                rfqId={rfqId}
                                onSelectedNodesChange={
                                  handleSelectedNodesChange
                                }
                                setDeleteQuotationValue={
                                  setDeleteQuotationValue
                                }
                                compareModal={compareDocModal}
                                openDocumentModal={openDocumentModal}
                                workFlowEvent={true}
                                handleWorkFlowEvent={handleWorkFlowEvent}
                              />

                              {/* <TraceEvents
                            enableComments={false}
                            data={traceEventData}
                            userId={userId}
                          /> */}
                            </div>
                          </Card>
                        </div>
                      </TabPane>
                      <TabPane tab="Rfq Events" key="RFQ Events">
                        <div className=" " style={{ width: 100 + '%' }}>
                          <Card>
                            <div className=" page-sub-title">Events</div>
                            <div className="boxed">
                              <TraceEvents
                                enableComments={false}
                                data={traceEvents}
                                userId={userId}
                              />{' '}
                            </div>
                          </Card>
                        </div>
                      </TabPane>
                    </Tabs>
                  </div>
                </TabPane>
              ) : (
                ''
              )}
            </Tabs>
          )}
        </>
      )}

      {/* Missing Fields Drawer */}
      <MissingFieldsDrawer
        isOpen={isMissingFieldsDrawerOpen}
        onClose={() => setIsMissingFieldsDrawerOpen(false)}
        tableData={tableData}
        orderedPolicyBeans={
          quotationDataTables?.value?.find(
            (element) => element.uniqueKey === 'quoteSupplierListing'
          )?.value?.orderedPolicyBeans || []
        }
        rfqId={rfqId}
      />
    </DndProvider>
  );
};
export { ManualRfq };
