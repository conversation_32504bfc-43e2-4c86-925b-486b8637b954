/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/**
 * <AUTHOR>
 * @email <EMAIL>
 */
import React, { useState, useEffect, useLayoutEffect } from 'react';
import { Input, Button, useConfirm, formValidationUtil } from '@hp/components';
import { dataTableServiceProvider } from '@hp/components';
import Modal from 'react-modal';
import {
  MpnUploadClients,
  bbForms,
  FileUploadMPN,
  bbMPNviewSearch,
} from '@hp/mainstore';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { CommonSpinner, globalutils } from '@hp/components';
import { useNavigate } from 'react-router-dom';

const MpnUpload = () => {
  const [formDetails, setFormDetails] = useState([]);
  const [formInputs, setFormInputs] = useState([]);
  const [notify, setnotify] = useState(null);
  const [flag, setFlag] = useState(true);
  const [productFileMasterId, setProductFileMasterId] = useState(null);
  const [clientId, setclientId] = useState();
  const [clientSearchId, setclientSearchId] = useState();
  const [showUpload, setShowUpload] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [tempObj, setTempObj] = useState();
  const [tempFile, setTempFile] = useState();
  const [initialDataLoaded, setInitialDataLoaded] = useState(false); // Add this state

  const { confirm } = useConfirm();

  const [isSearch, setIsSearch] = useState({
    isSearchDisabled: true,
    isClientValue: '',
    isDateValue: '',
    addOnchange: false,
  });
  const dispatch = useDispatch();

  const {
    bbFormDetails,
    loading,
    MPNuploadClient,
    uploadMPNSuccess,
    mpnFiles,
  } = useSelector((store) => store.bb);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  useLayoutEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: Component did mount First call of the component.
     */
    // navigate(`/bb/buy-process/mpn/mpn-upload`);
    dispatch(bbForms('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
    };
  }, []);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: form client response.
     */
    if (bbFormDetails && bbFormDetails !== undefined && bbFormDetails?.value) {
      var clientID = null;
      var selectedDate = null;
      const form = bbFormDetails?.value;
      if (form && form.length) {
        form.map((items, index) => {
          <div key={index}></div>;
          if (items.uniqueKey === 'page-title') {
            if (items.parameters === 'upload-shortage') {
              const headerControls = form.filter(
                (e) => e.uniqueKey === 'page-title'
              );
              if (headerControls && headerControls.length) {
                headerControls[0].formSubDetailsInternalDTOList &&
                  headerControls[0].formSubDetailsInternalDTOList.length &&
                  headerControls[0].formSubDetailsInternalDTOList.map(
                    (element, index) => {
                      <div key={index}></div>;
                      if (element.uniqueKey === 'client-control') {
                        clientID = element.value;
                      } else if (element.uniqueKey === 'date-control') {
                        selectedDate = element.value;
                      }
                    }
                  );
                setFormDetails(bbFormDetails?.value);
                setIsSearch({
                  ...isSearch,
                  isDateValue: selectedDate,
                  isClientValue: clientID,
                });
                setInitialDataLoaded(true); // Mark initial data as loaded
              }
            }
          }
        });
      }
    }
  }, [bbFormDetails]);

  // Add this new useEffect to trigger auto-search when initial data is loaded
  useEffect(() => {
    /**
     * @description: Auto-search when default date and client values are present
     */
    if (initialDataLoaded && isSearch.isClientValue && isSearch.isDateValue) {
      // Trigger search automatically if both client and date values are present
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
  }, [initialDataLoaded, isSearch.isClientValue, isSearch.isDateValue]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: getting Main list based on client Search.
     */
    if (mpnFiles && mpnFiles?.value && formDetails && formDetails.length) {
      const stateValues = [...formDetails];
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'fileTable'
      );

      if (foundIndex !== -1) {
        // Create a shallow copy of the object before modifying it
        stateValues[foundIndex] = {
          ...stateValues[foundIndex],
          value:
            mpnFiles?.value && mpnFiles?.value.length
              ? [...mpnFiles.value]
              : [],
        };

        setFormDetails(stateValues);
      }
    }
  }, [mpnFiles]);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin A
     * @email <EMAIL>
     * @description: Component Refresh.
     */
    if (
      uploadMPNSuccess &&
      uploadMPNSuccess.httpStatus &&
      uploadMPNSuccess.httpStatus === 'OK'
    ) {
      var timerId = setTimeout(() => {
        setShowModal(false);
      }, 2000);
      dispatch(bbForms('subscribe'));

      return () => {
        clearTimeout(timerId);
      };
    }
  }, [uploadMPNSuccess]);

  useEffect(() => {
    if (
      uploadMPNSuccess &&
      uploadMPNSuccess.httpStatus &&
      uploadMPNSuccess.httpStatus === 'OK'
    ) {
      funcToSetResMessageInModal('success', uploadMPNSuccess.value);
    } else if (
      uploadMPNSuccess &&
      uploadMPNSuccess.response &&
      uploadMPNSuccess.response.data &&
      uploadMPNSuccess.response.data.httpStatus &&
      uploadMPNSuccess.response.data.httpStatus === 'BAD_REQUEST' &&
      clientId
    ) {
      sameFileUpload(uploadMPNSuccess.response.data);
    }
    dispatch(MpnUploadClients(clientSearchId));
  }, [uploadMPNSuccess]);

  useEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: form response.
     */
    if (
      MPNuploadClient &&
      MPNuploadClient !== undefined &&
      MPNuploadClient.value
    ) {
      let formControl = cloneDeep(formDetails);
      formControl &&
        formControl !== undefined &&
        formControl.map((element, index) => {
          <div key={index}></div>;
          if (element.uniqueKey === 'fileTable') {
            element.value = MPNuploadClient.value;
          }
        });
      setFormDetails(formControl);
    }
  }, [MPNuploadClient]);

  const sameFileUpload = async (data) => {
    const isConfirmed = await confirm(data.message);
    if (isConfirmed) {
      dispatch(
        FileUploadMPN({
          productFileMasterDTO: tempObj,
          UploadedFile: tempFile,
          reqType: 'subscribe',
          bbClientId: clientId,
          flag: false,
        })
      );
    }
  };

  const handleRowClick = (obj) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.serialNo,
      'serialNo'
    );
    setConditionalRowStyles(styleAttribute);
  };

  const onChangeHandler = (type, event, element) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for user input content change.
     */
    const stateValues = formDetails.map((item) => ({
      ...item,
      formSubDetailsInternalDTOList: item.formSubDetailsInternalDTOList?.map(
        (subItem) => ({ ...subItem })
      ),
    }));

    // Handle client-control input change
    if (type.uniqueKey === 'client-control') {
      stateValues[0].formSubDetailsInternalDTOList[0].value =
        event.target.value;

      // Update isSearch state
      setIsSearch({
        ...isSearch,
        isClientValue: event.target.value,
      });

      // Show upload
      setShowUpload(true);
    }
    // Handle date-control input change
    else if (type.uniqueKey === 'date-control') {
      stateValues[0].formSubDetailsInternalDTOList[1].value = event;

      // Update isSearch state
      setIsSearch({
        ...isSearch,
        isDateValue: event,
      });
    }

    // Enable the search button if it exists
    const searchButtonIndex =
      stateValues[0].formSubDetailsInternalDTOList.findIndex(
        (el) => el.uniqueKey === 'button-search'
      );

    if (searchButtonIndex !== -1) {
      stateValues[0].formSubDetailsInternalDTOList[
        searchButtonIndex
      ].disableFlag = 'F';
    }

    // Update form inputs state with the modified copy
    setFormInputs(stateValues);

    // Reset product file master ID
    setProductFileMasterId(null);
  };

  function mpnSearch() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for Search.
     */
    if (isSearch.isSearchDisabled) {
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
      //dispatch(bbAction.MpnUploadClient(clientSearchId));
    }
  }

  function onClearFunction() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: to clear all selected controls.
     */
    if (!isSearch.isSearchDisabled) {
      dispatch(bbForms('subscribe'));
      setShowButton(false);
    }
  }

  const onSelectHandler = (event, element, uniqueKey) => {
    let validatedElement = formValidationUtil.isFormcontrolValidDynamic(
      event,
      element
    );
    let updateFormElement = cloneDeep(formDetails);
    updateFormElement.map((entry, index) => {
      <div key={index}></div>;
      entry.uniqueKey === 'mpnSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element, elementIndex) => {
          if (element.uniqueKey === uniqueKey) {
            setclientId(event.target.value);

            entry.formSubDetailsInternalDTOList.splice(
              elementIndex,
              1,
              validatedElement
            );
          }
        });
    });
    setFormDetails(updateFormElement);
  };

  const handlers = {
    onChangeHandler,
    onClearFunction,
    mpnSearch,
    handleRowClick,
    onSelectHandler,
  };

  const fileUpload = async (UploadedFile) => {
    let validate = formValidationUtil.checkMandatoryField(formDetails);
    let validatedFc = cloneDeep(validate.formList);
    setFormDetails(validatedFc);

    const userId = globalutils.getDataFromStorage('userId');

    let obj = {
      bbClientId: clientId,
      description: UploadedFile.Description,
      fileName: UploadedFile.file.name,
      productFileMasterId: 0,
      serialNo: 0,
      uploadedBy: userId,
      uploadedOn: globalutils.formatDate(new Date(), 'yyyy-mm-dd'),
      userId: userId,
    };
    const productFileMasterDTO = JSON.stringify(obj);

    if (validate.validSuccess) {
      setTempObj(productFileMasterDTO);
      setTempFile(UploadedFile);

      dispatch(
        FileUploadMPN({
          productFileMasterDTO: productFileMasterDTO,
          UploadedFile: UploadedFile,
          reqType: 'subscribe',
          bbClientId: clientId,
          flag: flag,
        })
      );
    } else {
      funcToSetResMessageInModal('error', 'Select Client');
    }
  };

  return (
    <>
      <CommonSpinner visible={loading} />
      <div className="">
        {formDetails
          ? formDetails.map((element, index) => {
              if (element.uniqueKey === 'page-title') {
                return (
                  <div key={index}>
                    <Input formType={element} />
                    <div
                      className="two-col-layout-right-30"
                      style={{ marginBottom: '-40px' }}
                    >
                      <div className="three-col-layout ">
                        {element.formSubDetailsInternalDTOList &&
                          element.formSubDetailsInternalDTOList.length > 0 &&
                          element.formSubDetailsInternalDTOList.map(
                            (element, index) => {
                              return (
                                <Input
                                  key={index}
                                  dataTableEventHandler={(e) =>
                                    handleRowClick(e)
                                  }
                                  onChangeHandler={(type, event) =>
                                    handlers[element.onChangeFunction](
                                      type,
                                      event,
                                      'search'
                                    )
                                  }
                                  conditionalRowStyles={conditionalRowStyles}
                                  isEditable="notShowing"
                                  formType={element}
                                />
                              );
                            }
                          )}
                      </div>
                    </div>
                  </div>
                );
              } else if (element?.uniqueKey === 'fileTable') {
                return (
                  <Input
                    formType={element}
                    dataTableEventHandler={(e) => handleRowClick(e)}
                    dataTableColumn={
                      element.formSubDetailsInternalDTOList &&
                      element.formSubDetailsInternalDTOList.length
                        ? element.formSubDetailsInternalDTOList.map(
                            (rowData) => {
                              return {
                                width: rowData.displayWidth
                                  ? rowData.displayWidth
                                  : '',
                                name: rowData.displayName
                                  ? rowData.displayName
                                  : '',
                                selector: rowData.selector
                                  ? rowData.selector
                                  : '',
                                cell: dataTableServiceProvider.bbModuleCustomTableCell(
                                  rowData
                                ),
                              };
                            }
                          )
                        : []
                    }
                    conditionalRowStyles={conditionalRowStyles}
                    // dataTableColumn={editDeleteIcon}
                    linkPath={'/'}
                    isEditable={'notShowing'}
                    onsubmitHandler={fileUpload}
                    key={index}
                  />
                );
              } else if (element?.uniqueKey === 'mpnSubtitle') {
                if (showModal) {
                  return (
                    <Modal
                      className="Modal"
                      overlayClassName="ModalOverlay"
                      ariaHideApp={false}
                      isOpen={showModal}
                      key={index}
                    >
                      <div
                        onClick={() => setShowModal(false)}
                        className="modal-close icon-close"
                        style={{ fontSize: 20 + 'px' }}
                      ></div>
                      <div>
                        <Input formType={element} />
                        <div>
                          {element.formSubDetailsInternalDTOList.map(
                            (element, index) => {
                              return (
                                <Input
                                  key={index}
                                  formType={element}
                                  // dataTableEventHandler={(e) => onRowClick(e)}
                                  conditionalRowStyles={conditionalRowStyles}
                                  // dataTableColumn={editDeleteIcon}
                                  disabled={false}
                                  linkPath={'/'}
                                  downloadLink={
                                    'https://dev.highpointsuite.com/inpinn/sample/bb/sample_mpn_list_doc.xlsx'
                                  }
                                  isEditable={'notShowing'}
                                  onsubmitHandler={fileUpload}
                                  handleCancel={(isClose) =>
                                    setShowModal(isClose)
                                  }
                                  disableUpload={clientId ? false : true}
                                  onChangeHandler={(element, event) =>
                                    handlers[element.onChangeFunction](
                                      event,
                                      element,
                                      element.uniqueKey,
                                      index
                                    )
                                  }
                                />
                              );
                            }
                          )}
                        </div>
                      </div>
                      {notify != null ? (
                        <div
                          className={[
                            'notification-bar',
                            'type-' + notify.type,
                          ].join(' ')}
                          style={{
                            position: 'sticky !important',
                            width: 90 + '%',
                          }}
                        >
                          <i
                            className="icon-close"
                            onClick={() => setnotify(null)}
                          ></i>
                          {notify.resMessage}
                        </div>
                      ) : (
                        ''
                      )}
                    </Modal>
                  );
                }
              } else if (element?.uniqueKey === 'client_add') {
                return (
                  <div style={{ float: 'right', marginTop: 10 }} key={index}>
                    <Button
                      onClick={() => setShowModal(true)}
                      className="small mb8 outline add-button-custom flex-row vam"
                    >
                      <i className="icon-upload"> </i>Upload
                    </Button>
                  </div>
                );
              }
            })
          : null}
      </div>
    </>
  );
};
export { MpnUpload };
