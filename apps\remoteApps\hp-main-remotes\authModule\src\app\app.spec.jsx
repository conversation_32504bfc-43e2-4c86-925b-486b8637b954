import React from 'react';
import { render } from '@testing-library/react';

// Mock the app component to avoid circular dependencies
jest.mock('./app', () => {
  return {
    __esModule: true,
    default: () => <div>Mock App</div>,
  };
});

describe('App', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<div>Mock App Test</div>);
    expect(baseElement).toBeTruthy();
  });
});
