/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { TextInput, Select, Button, DatePickerInput } from '@hp/components';

const SearchReport = () => {
  const [state, setState] = useState({
    DetailsData: {
      supplier: '',
      report: [],
    },
  });

  useEffect(() => {
    getReport();
  }, []);

  const getReport = () => {
    // let clientId = localStorage.getItem('clientId');
    // reports.searchReport(clientId).then((response) => {
    //   setState({
    //     report: response.data,
    //   });
    // });
  };
  // const DataTableEventHandler = (e) => {
  //   setState({ DetailsData: e });
  // };

  const columns = [
    { name: 'Invoice #', selector: 'invNumber', sortable: true },
    { name: 'Invoice Date', selector: 'invDate', sortable: true },
    { name: 'PO #', selector: 'poNumber', sortable: true },
    { name: 'Supplier Name', selector: 'supplierName', sortable: true },
    { name: 'Currency', selector: 'currency', sortable: true },
    { name: 'Amount', selector: 'amount', sortable: true },
    { name: 'Status', selector: 'status', sortable: true },
  ];
  return (
    <>
      {/* <div className="page-title">Report Page </div> */}
      <div className="flex-row three-col-layout">
        <div className="col">
          <div className="flex-row align-end ">
            <Select
              label="Document"
              className="mr20"
              style={{ width: 96 + 'px' }}
              onChange={''}
              options={[
                { value: 'INV', display: 'INV' },
                { value: 'PO', display: 'PO' },
                { value: 'Reference #', display: 'Reference #' },
              ]}
            />
            <TextInput
              label="#"
              className="flex-shrink"
              style={{ width: '75%' }}
            />
          </div>
          <div className="mb20"></div>
          <div className="flex-row align-end">
            <Select
              label="Currency"
              className="mr20"
              style={{ width: 96 + 'px' }}
              value={'USD'}
              options={[{ value: 'USD', display: 'USD' }]}
            />
            <Select
              label="Range"
              className="mr20"
              style={{ width: 96 + 'px' }}
              // value={"="}
              options={[
                { value: '=', display: '=' },
                { value: '>', display: '>' },
                { value: '<', display: '<' },
              ]}
            />
            <TextInput
              label="Amount "
              className="flex-shrink"
              placeholder=""
              //value={"4700"}
              style={{ width: '60%' }}
            />
          </div>
        </div>
        <div className="col">
          <TextInput label="Supplier" className="mb20" />
        </div>
        <div className="col">
          <div className="flex-row align-end">
            <DatePickerInput
              label="From"
              className="flex-shrink"
              value={'10/06/2020'}
            />

            <DatePickerInput
              label="To"
              className="flex-shrink padding-left-20"
              value={'10/06/2020'}
            />
          </div>
        </div>
      </div>
      <div className="mb40"></div>
      <Button className="outline mr20">Search</Button>

      <div className="mb20" />
      <div className="mb24 force-max-width styledDatatable">
        {' '}
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          pagination={true}
          columns={columns}
          data={state.report}
        />
      </div>

      <Button
        className="text_color"
        onClick={() =>
          window.open('http://**************/inpinn/pinnacle_inv_68.pdf')
        }
      >
        Print
      </Button>
    </>
  );
};
export { SearchReport };
