import React, { useState } from 'react';
import Modal from 'react-modal';

import AntSteps from '@hp/components';
import OrganizationalChart from '@hp/components';
import Scrollbars from 'react-custom-scrollbars';
function ProcessTree() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div onClick={() => setIsOpen(true)}>
        <AntSteps view="normal" />
      </div>{' '}
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isOpen}
      >
        <div
          onClick={() => setIsOpen(false)}
          className="modal-close icon-close"
        ></div>
        <div className="page-sub-title">Events</div>
        <div className="mb20">
          <Scrollbars autoHeight style={{ width: 100 + '%' }}>
            <AntSteps view="detailed" />
          </Scrollbars>
        </div>

        <Scrollbars autoHeight autoHeightMax={600}>
          <OrganizationalChart />
        </Scrollbars>
      </Modal>
    </>
  );
}

export default ProcessTree;
