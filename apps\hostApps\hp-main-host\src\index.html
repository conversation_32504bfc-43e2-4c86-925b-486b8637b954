<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>High Point</title>
    <base href="/" />
    <meta name="theme-color" content="#000000" id="theme-color-meta" />
    <link rel="manifest" href="./assets/manifest.json" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="./assets/logo.png" />
    <link
      rel="preload"
      href="https://fonts.gstatic.com/s/roboto/v47/KFO7CnqEu92Fr1ME7kSn66aG.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div id="main-module"></div>

    <!-- Loader -->
 <div class="html-loader" id="html-loader">
        <div class="html-rings-container">
            <!-- Outer Ring -->
            <svg class="html-ring-svg html-ring-outer" viewBox="0 0 120 120">
                <circle
                    cx="60"
                    cy="60"
                    r="49"
                    fill="none"
                    stroke="#e7e7e7"
                    stroke-width="6"
                    stroke-dasharray="82 245"
                    stroke-linecap="round"
                />
            </svg>
            
            <!-- Inner Ring -->
            <svg class="html-ring-svg html-ring-inner" viewBox="0 0 120 120">
                <circle
                    cx="60"
                    cy="60"
                    r="40"
                    fill="none"
                    stroke="#e7e7e7"
                    stroke-width="6"
                    stroke-dasharray="58 174"
                    stroke-linecap="round"
                />
            </svg>
            
            <!-- Center Image -->
            <img src="assets/loading.png" alt="Center Image" class="html-center-image">
        </div>
        
        <div class="html-loading-text">Loading...</div>
    </div>

    <div id="hp-user-nav"></div>
    <div id="dialog-root"></div>
    <div id="notification-root"></div>

    <!-- Loader Styles -->
    <style>
    .html-loader {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .html-rings-container {
            position: relative;
            width: 120px;
            height: 120px;
            margin-bottom: 8px;
        }

        .html-ring-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .html-ring-outer {
            animation: spin-clockwise 1.5s linear infinite;
            transform-origin: 60px 60px;
        }

        .html-ring-inner {
            animation: spin-anticlockwise 1.5s linear infinite;
            transform-origin: 60px 60px;
        }

        .html-center-image {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 55px;
            height: 50px;
            z-index: 10;
        }

        @keyframes spin-clockwise {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes spin-anticlockwise {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(-360deg);
            }
        }

        .html-loading-text {
            color: #464545;
            font-family: 'Roboto';
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 0.5px;
            margin-top: -10px;
        }
    </style>
  </body>
</html>
