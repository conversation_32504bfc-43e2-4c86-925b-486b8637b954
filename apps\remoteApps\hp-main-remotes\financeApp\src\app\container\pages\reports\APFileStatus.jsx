/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 16-05-2023 15:40:13
 * @modify date 20-05-2023 15:40:20
 * @desc [description]
 */

import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { APFileStatusTable } from './APFileStatusTable';
import { accPayConstants } from '@hp/mainstore';
import {
  Button,
  DateTimePickerInput,
  SearchInput,
  Select,
  TextInput,
} from '@hp/components';
import { getAPFileStatusDetails, getTimeZone } from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import { getClientComboBox } from '@hp/mainstore';
import { globalutils } from '@hp/components'
const APFileStatus = () => {
  let user = globalutils.getDataFromStorage('all');
  let clientId = user?.clientId;
  const dispatch = useDispatch();

  const { apFileStatusDetails, timeZoneList } = useSelector(
    (store) => store.accpay
  );
  const { clientCombolist } = useSelector((store) => store.email);

  let d = new Date(),
    month = '' + (d.getMonth() + 1),
    day = '' + d.getDate(),
    year = d.getFullYear(),
    hours = d.getHours(),
    mins = d.getMinutes();
  if (month.length < 2) month = '0' + month;
  if (day.length < 2) day = '0' + day;
  if (hours < 10) hours = '0' + hours;
  if (mins < 10) mins = '0' + mins;
  const dateToday = [month, day, year].join('-') + ` ${hours}:${mins}`;

  const [invStatusList, setInvStatusList] = useState([]);
  const [fileId, setFileId] = useState(null);
  const [clientIdVal, setClientIdVal] = useState(clientId);
  const [clientOptions, setClientOptions] = useState([]);
  const [timeZone, setTimeZone] = useState(null);
  const [timeZoneOptions, setTimeZoneOptions] = useState([]);
  const [errorFlag, setErrorFlag] = useState(false);
  const [dateRange, setDateRange] = useState({
    fromDate: dateToday,
    toDate: dateToday,
  });
  const [dateSelect, setDateSelect] = useState({
    fromDate: d,
    toDate: d,
  });

  const tableRef = useRef(null);

  const styleIssueFix = document.querySelectorAll(
    '.react-datepicker__aria-live'
  );
  styleIssueFix.forEach((item) => (item.style.display = 'none'));

  useEffect(() => {
    let tempList = apFileStatusDetails?.invStatusCounts;
    setInvStatusList(tempList);
    if (
      apFileStatusDetails?.invEntriesProcessedToInvTable ===
      apFileStatusDetails?.invEntriesReachedInvTable
    )
      setErrorFlag(false);
    else setErrorFlag(true);
  }, [apFileStatusDetails]);

  useEffect(() => {
    if (clientCombolist?.value?.length > 0) {
      let tempOptions = clientCombolist.value.map((val) => {
        return {
          value: val.clientId,
          display: val.clientName,
        };
      });
      setClientOptions(tempOptions);
    }
  }, [clientCombolist]);

  useEffect(() => {
    if (timeZoneList?.value?.length > 0) {
      let tempOptions = timeZoneList.value.map((val) => {
        return {
          id: val.timeZoneId,
          value: val.timeZoneName,
        };
      });
      setTimeZoneOptions(tempOptions);
    }
  }, [timeZoneList]);

  useEffect(() => {
    dispatch(getClientComboBox());
    dispatch(getTimeZone());

    return () => {
      let removeNotification = null;
      dispatch({
        type: accPayConstants.GET_AP_FILE_STATUS_SUCCESS,
        removeNotification,
      });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  function handleDateChange(event, type) {
    let d = new Date(event),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear(),
      hours = d.getHours(),
      mins = d.getMinutes();
    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;
    if (hours < 10) hours = '0' + hours;
    if (mins < 10) mins = '0' + mins;

    let dateTime = [month, day, year].join('-') + ` ${hours}:${mins}`;
    let tempDateSelect = dateSelect;
    let tempDateRange = dateRange;
    if (type === 'from') {
      tempDateSelect.fromDate = event;
      tempDateRange.fromDate = dateTime;
    } else {
      tempDateSelect.toDate = event;
      tempDateRange.toDate = dateTime;
    }
    setDateSelect({ ...tempDateSelect });
    setDateRange({ ...tempDateRange });
  }
  return (
    <>
      <div className="flex-row">
        <Select
          label="Client"
          className="mb20 mr20"
          style={{ width: '200px' }}
          value={clientIdVal}
          options={clientOptions}
          onChange={(e) => setClientIdVal(e.target.value)}
        />
        <div style={{ width: '100px' }} className="mb20 mr20">
          <TextInput
            label="File Internal Id"
            value={fileId}
            onChange={(e) => {
              if (!isNaN(e.target.value)) {
                setFileId(parseInt(e.target.value));
              }
            }}
          />
        </div>
        <div style={{ width: '200px' }} className="mb20 mr20">
          <DateTimePickerInput
            className="mb20"
            label="From"
            isEditable="notShowing"
            selected={dateSelect.fromDate}
            value={dateRange.fromDate}
            onChange={(event) => handleDateChange(event, 'from')}
          />
        </div>
        <div style={{ width: '200px' }} className="mb20 mr20">
          <DateTimePickerInput
            className="mb20"
            label="To"
            isEditable="notShowing"
            selected={dateSelect.toDate}
            value={dateRange.toDate}
            onChange={(event) => handleDateChange(event, 'to')}
          />
        </div>
        <div className="mb20 mr20" style={{ width: '200px' }}>
          <SearchInput
            label="Time Zone"
            value={timeZone}
            options={timeZoneOptions}
            onChange={(e, key) => {
              if (key === 'not-present') setTimeZone(e?.value);
            }}
          />
        </div>
        <Button
          className="add-button-custom vam outline small button"
          style={{ margin: '22px auto 20px 0px' }}
          onClick={() => {
            if (fileId && timeZone && !isNaN(fileId)) {
              dispatch(
                getAPFileStatusDetails({
                  apInFileId: fileId,
                  clientId: clientIdVal,
                  fromDate: dateRange.fromDate,
                  timeZone: timeZone,
                  toDate: dateRange.toDate,
                })
              );
            }
          }}
        >
          Search
        </Button>
      </div>
      <hr className="mb20" />
      {apFileStatusDetails ? (
        <div className="three-col-layout-60" style={{ flexWrap: 'nowrap' }}>
          <div className="col" style={{ width: 'calc((100% - 60px) / 2)' }}>
            <div className="two-col-layout-small-tile-20 mb20">
              <div className="col-tile">
                <div
                  style={{ cursor: 'pointer' }}
                  className={'db-icon hpmain-home-widget count-card'}
                  title={'Total Files Received'}
                >
                  <div className="module-content">
                    <p>Total Files Received</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.totalFileCount}
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-tile">
                <div
                  style={{ cursor: 'pointer' }}
                  className={'db-icon hpmain-home-widget count-card'}
                  title={'Total no. of file entries created'}
                >
                  <div className="module-content">
                    <p>Total no. of file entries created</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.totalFileEntries}
                    </div>
                  </div>
                </div>
              </div>
              <div
                className="col-tile"
                onClick={() =>
                  tableRef.current.scrollIntoView({ behavior: 'smooth' })
                }
              >
                <div
                  style={{ cursor: 'pointer' }}
                  className={`${
                    apFileStatusDetails?.failedEntries > 0
                      ? 'db-icon-danger'
                      : 'db-icon'
                  } hpmain-home-widget count-card`}
                  title={'Failed file entries'}
                >
                  <div className="module-content">
                    <p>Failed file entries</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.failedEntries}
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-tile">
                <div
                  style={{ cursor: 'pointer' }}
                  className={'db-icon hpmain-home-widget count-card'}
                  title={'No. of files divided'}
                >
                  <div className="module-content">
                    <p>No. of files divided</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.originalFilesSplitted}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="two-col-layout-small-tile-20">
              <div className="col-tile">
                <div
                  style={{ cursor: 'pointer' }}
                  className={`${
                    errorFlag ? 'db-icon-danger' : 'db-icon'
                  } hpmain-home-widget count-card`}
                  title={'No. of files processed to Invoice'}
                >
                  <div className="module-content">
                    {errorFlag && (
                      <div className="db-module-icon ">
                        <i className="icon-check-alert"></i>
                      </div>
                    )}
                    <p>No. of files processed to Invoice</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.invEntriesProcessedToInvTable}
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-tile">
                <div
                  style={{ cursor: 'pointer' }}
                  className={`${
                    errorFlag ? 'db-icon-danger' : 'db-icon'
                  } hpmain-home-widget count-card`}
                  title={'No. of files reached at Invoice'}
                >
                  <div className="module-content">
                    {errorFlag && (
                      <div className="db-module-icon ">
                        <i className="icon-check-alert"></i>
                      </div>
                    )}
                    <p>No. of files reached at Invoice</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.invEntriesReachedInvTable}
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-tile">
                <div
                  style={{ cursor: 'pointer' }}
                  className={'db-icon hpmain-home-widget count-card'}
                  title={'No. of new divided entries created'}
                >
                  <div className="module-content">
                    <p>No. of new divided entries created</p>
                    <div className="db-large-number">
                      {apFileStatusDetails?.splittedEntriesCreated}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            className="col"
            style={{ width: 'calc((100% - 60px) / 2)', marginRight: '0px' }}
          >
            <div className="bar-chart-block summary-col">
              <h2>Invoice Breakdown - {apFileStatusDetails?.totalInvCount}</h2>
              <div style={{ height: '500px', overflow: 'auto' }}>
                <ul>
                  {invStatusList?.map((data, i) => {
                    let colour =
                      data.status === 'part-match' || data.status === 'on-hold'
                        ? 'var(--ready_color)'
                        : data.status === 'approved' ||
                          data.status === 'matched'
                        ? 'var(--verified_color)'
                        : data.status === 'rejected' ||
                          data.status === 'unmatched'
                        ? 'var(--error_color)'
                        : 'var(--primary_color)';
                    return (
                      <li key={i}>
                        <a>
                          <div className="label">{data.status}</div>
                          <div
                            className="count"
                            style={{ backgroundColor: colour }}
                          >
                            {data.count}
                          </div>
                        </a>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          </div>
        </div>
      ) : (
        ''
      )}
      <div ref={tableRef}>
        <APFileStatusTable />
      </div>
    </>
  );
};
export { APFileStatus };
