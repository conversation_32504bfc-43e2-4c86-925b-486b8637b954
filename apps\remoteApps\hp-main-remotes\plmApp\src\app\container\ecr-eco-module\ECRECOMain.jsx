import { ECRDetailsPage } from './ECRDetailsPage';
import { ECRListingPage } from './ECRListingPage';
import { ECOListingPage } from './ECOListingPage';
import { ECODetailsPage } from './ECODetailsPage';
import { ECRViewPage } from './ECRViewPage';
import { ECOViewPage } from './ECOViewPage';
import { useAppRouterDom } from '@hp/utils';
const ECRECOMain = () => {
  const { domParameters } = useAppRouterDom();

  const parameter = domParameters?.parameter || ''; // Last item in the array
  const submenu = domParameters?.submenu || ''; // Second item in the array
  const method = domParameters?.method || ''; // Second-to-last item if needed

  if (submenu === 'ecr') {
    if (parameter === 'create-chg') return <ECRDetailsPage />;
    else if (method === 'edit') return <ECRDetailsPage />;
    else if (method === 'view') return <ECRViewPage />;
    else return <ECRListingPage />;
  } else {
    if (parameter === 'create') {
      return <ECODetailsPage />;
    } else if (method === 'edit') return <ECODetailsPage />;
    else if (method === 'view') return <ECOViewPage />;
    else return <ECOListingPage />;
  }
};

export { ECRECOMain };
