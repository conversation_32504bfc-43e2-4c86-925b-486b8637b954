import React from "react";
import Modal from "react-modal";

const DialogComponent = (props) => {
  return (
    <Modal
      className="Modal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={props.modalIsOpen}
      onRequestClose={props.openModal}
    >
      <div onClick={props.openModal} className="modal-close icon-close"></div>
    </Modal>
  );
};

export { DialogComponent };
