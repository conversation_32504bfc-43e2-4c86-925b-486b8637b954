import React from 'react';
import './ReportStyle.scss';
import { SnippetsFilled } from '@ant-design/icons';
import Scrollbars from 'react-custom-scrollbars';

const ReportDataList = ({ data, summary }) => {
  return (
    <div className="invoice-container">
      <Scrollbars autoHeight autoHeightMin={100} autoHeightMax={500}>
        {data &&
          data.map((item, index) => (
            <>
              <div className="header">
                <SnippetsFilled style={{ marginRight: '8px' }} />
                {item.headerDisplay}
              </div>
              <div className="invoice-list">
                {item.value &&
                  item.value.map((subItem, subIndex) => (
                    <div className="invoice-item" key={subIndex}>
                      <div
                        className="item-name"
                        title={subItem.amount ? `${subItem.amount}` : ''}
                      >
                        {subItem.displayStatus} : {subItem.count}
                      </div>
                      {subItem.subHeaderDto && (
                        <>
                          <div className="subheader">
                            {/* <CopyOutlined style={{ marginRight: '8px' }} /> */}
                            <ul>
                              <li>{subItem.subHeaderDto.headerDisplay}</li>{' '}
                            </ul>
                          </div>
                          <div className="subheader-list">
                            {subItem.subHeaderDto.value.map(
                              (subSubItem, subSubIndex) => (
                                <div
                                  className="subheader-item"
                                  key={subSubIndex}
                                >
                                  <div
                                    className="subitem-name"
                                    title={
                                      subSubItem.amount
                                        ? `${subSubItem.amount}`
                                        : ''
                                    }
                                  >
                                    {subSubItem.displayStatus} :{' '}
                                    {subSubItem.count}
                                  </div>
                                  {subSubItem.subHeaderDto && (
                                    <>
                                      <div className="subheader">
                                        {subSubItem.subHeaderDto.headerDisplay}
                                      </div>
                                      <div className="subheader-list">
                                        {subSubItem.subHeaderDto.value.map(
                                          (dataItem, dataIndex) => (
                                            <div
                                              className="subheader-item"
                                              key={dataIndex}
                                            >
                                              <div
                                                className="subitem-name"
                                                title={
                                                  dataItem.amount
                                                    ? `${dataItem.amount}`
                                                    : ''
                                                }
                                              >
                                                {dataItem.displayStatus} :{' '}
                                                {dataItem.count}
                                              </div>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </>
                                  )}
                                </div>
                              )
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  ))}
              </div>
            </>
          ))}
        {summary ? (
          <>
            <div className="header">Summary:</div>
            <div className="subitem-name">{summary}</div>
          </>
        ) : (
          ''
        )}
      </Scrollbars>
    </div>
  );
};

export { ReportDataList };
