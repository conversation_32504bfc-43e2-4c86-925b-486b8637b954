/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-key */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:39:49
 * @modify date 27-05-2021 15:39:52
 * @desc [description]
 */
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { saveAccountPolicyDetails as SAPDetails } from '@hp/mainstore';
import cloneDeep from 'lodash.clonedeep';
import { AP_USER } from '@hp/constants';
import { ButtonCommon, formValidationUtil, Input } from '@hp/components';
import { showDialog } from '@hp/mainstore';
import {
  getAccountPolicyformDetails,
  getClientComboBox,
  resetsaveAccountPolicyDetails,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';

const AccountPolicySettings = (props) => {
  const dispatch = useDispatch();

  const { accountPolicyformDetails } = useSelector((store) => store.settings);
  const { saveAccountPolicyDetails } = useSelector((store) => store.admin);
  const { clientCombolist } = useSelector((store) => store.email);

  let user = globalutils.getDataFromStorage('all');
  const userId = user?.userId;
  const [formDetails, setFormDetails] = useState([]);
  const [clientId, setClientId] = useState(user?.clientId);
  const [editFlag, setEditFlag] = useState(false);
  const [showSSO, setShowSSO] = useState(false);
  const [showMFA, setShowMFA] = useState(false);
  const [showFourEye, setShowFourEye] = useState(false);

  const clientOptions = useMemo(() => {
    if (clientCombolist?.value?.length > 0) {
      let tempOptions = clientCombolist.value.map((value) => {
        return {
          commonId: value.clientId,
          commonName: value.clientName,
        };
      });
      return tempOptions;
    } else return null;
  }, [clientCombolist]);

  useEffect(() => {
    if (accountPolicyformDetails?.value && clientOptions) {
      let tempForm = accountPolicyformDetails.value.map((entry) => {
        if (entry.uniqueKey === 'clientSelect') {
          return { ...entry, comboBoxOptions: [...clientOptions] };
        }
        return { ...entry };
      });

      setFormDetails(tempForm);
    }
  }, [accountPolicyformDetails, clientOptions]);

  useEffect(() => {
    if (saveAccountPolicyDetails && saveAccountPolicyDetails.value) {
      funcToSetResponseMessage(
        saveAccountPolicyDetails.value?.type,
        saveAccountPolicyDetails.value?.text
      );
      setEditFlag(false);
    }
  }, [saveAccountPolicyDetails]);

  useEffect(() => {
    dispatch(getClientComboBox());
    dispatch(
      getAccountPolicyformDetails({ userId: userId, clientId: user?.clientId })
    );
    return () => {
      dispatch(resetsaveAccountPolicyDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const handleAll = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      if (entry.type === 'fieldset') {
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            if (element.comboSelectValue !== 'commonCode') {
              element.value = parseInt(event.target.value);
            } else element.value = event.target.value;
          }
        });
      } else if (entry.uniqueKey === uniqueKey) {
        if (entry.comboSelectValue !== 'commonCode') {
          entry.value = parseInt(event.target.value);
        } else entry.value = event.target.value;
      }
    });
    if (uniqueKey === 'clientSelect') {
      dispatch(
        getAccountPolicyformDetails({
          userId: userId,
          clientId: event.target.value,
        })
      );
      setClientId(event.target.value);
    } else if (uniqueKey === 'authUsingSSO') {
      setShowSSO(event.target.value === 'Y' ? true : false);
    } else if (uniqueKey === 'mfauth') {
      setShowMFA(event.target.value === 'Y' ? true : false);
    } else if (uniqueKey === 'fourEyeValidation') {
      setShowFourEye(event.target.value === 'Y' ? true : false);
    }
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleAll,
  };

  const handleSave = () => {
    if (editFlag) {
      let isValid = formValidationUtil.validateForm(formDetails);
      if (isValid.validSuccess) {
        dispatch(
          SAPDetails({ form: formDetails, userId: userId, clientId: clientId })
        );
      } else {
        setFormDetails([...isValid.formList]);
      }
    }
  };

  const handleEdit = () => {
    setEditFlag(true);
  };

  const functionsName = {
    handleSave,
    handleEdit,
  };

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.type === 'fieldset') {
            return (
              <div>
                <fieldset
                  className="fieldset"
                  style={{ marginBottom: 20 + 'px' }}
                >
                  {formControlsBinding(element.formSubDetailsInternalDTOList)}
                </fieldset>
              </div>
            );
          } else if (element.uniqueKey === 'ssoType') {
            return showSSO ? (
              <Input
                indexKey={index}
                formType={element}
                disabledFlag={!editFlag}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            ) : (
              ''
            );
          } else if (element.uniqueKey === 'mfaSource') {
            return showMFA ? (
              <Input
                indexKey={index}
                formType={element}
                disabledFlag={!editFlag}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            ) : (
              ''
            );
          } else if (element.uniqueKey === 'fourEye') {
            return showFourEye ? (
              <Input
                indexKey={index}
                formType={element}
                disabledFlag={!editFlag}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            ) : (
              ''
            );
          } else if (element.uniqueKey === 'pageTitle') {
            return (
              <div
                style={{
                  width: 'calc(200% + 40px)',
                }}
              >
                <Input indexKey={index} formType={element} />
              </div>
            );
          } else
            return (
              <Input
                indexKey={index}
                formType={element}
                disabledFlag={
                  element.uniqueKey === 'clientSelect' ? editFlag : !editFlag
                }
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            );
        })
      : null;
  };

  return (
    <>
      <div className=" two-col-layout mb20">
        <div className="col">
          {formDetails && formDetails.length
            ? formControlsBinding(formDetails)
            : ''}
        </div>
      </div>
      <ButtonCommon functionsName={functionsName} />
    </>
  );
};

export { AccountPolicySettings };
