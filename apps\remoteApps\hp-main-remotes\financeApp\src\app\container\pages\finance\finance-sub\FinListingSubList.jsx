/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import DataTable from 'react-data-table-component';
import { Ellipsis } from 'react-awesome-spinners';

import {
  CheckBoxInput,
  globalutils,
  TraceEvents,
  dataTableServiceProvider,
  TextInput,
  Button,
  CommonSpinner,
} from '@hp/components';
import { useAppRouterDom } from '@hp/utils';
import Modal from 'react-modal';
import { Tooltip } from 'antd';

import {
  getStatmentOutwardDetailsCount,
  getPaymentOutwardDetailsCount,
  getStatmentSourceDetailsForm,
  getPaymentSourceDetailsForm,
  getPaymentSourceDetailsCount,
  getStatmentSourceDetailsCount,
  getPaymentOutwardDetailsForm,
  getSessionTransactionTraceEvents,
  getStatmentOutwardDetailsForm,
  financeResetStateField,
} from '@hp/mainstore';

import './FinListingSub.scss';

const paymentHeaderDetails = (origin, receiving) => {
  const detailGroups = [
    {
      primary: origin,
      fallback: receiving,
      details: [
        {
          label: 'Origin Bank',
          pymntKey: 'originBankName',
          stmtKey: 'source',
        },
        {
          label: 'Origin Acct',
          pymntKey: 'originAccountName',
          stmtKey: 'accountName',
        },
      ],
    },
    {
      primary: receiving,
      fallback: origin,
      details: [
        {
          label: 'Receiving Bank',
          pymntKey: 'receivingBank',
          altKey: 'recvBank', // renamed Key2 -> altKey
          stmtKey: 'bankName',
        },
        {
          label: 'Receiving Acct',
          pymntKey: 'receivingAccountName',
          stmtKey: 'accountName',
        },
      ],
    },
  ];

  return detailGroups.flatMap(({ primary, fallback, details }) =>
    details.map(({ label, pymntKey, stmtKey, altKey }, index) => {
      const value =
        primary?.[pymntKey] ??
        (altKey ? primary?.[altKey] : undefined) ??
        primary?.[stmtKey] ??
        fallback?.[pymntKey] ??
        (altKey ? fallback?.[altKey] : undefined) ??
        fallback?.[stmtKey] ??
        '-';

      return (
        <div key={`${label}-${index}`} className="details-element">
          <p>{label}</p>:<p style={{ fontWeight: 500 }}>{value}</p>
        </div>
      );
    })
  );
};

const FinListingSubList = (props) => {
  const {
    statmentSourceDetailsForm,
    paymentOutwardDetailsForm,
    statmentOutwardDetailsForm,
    paymentSourceDetailsForm,
    paymentsTraceEventRes,
    finListingDetailsTableCount,
    commonLoading,
  } = useSelector((store) => store.finance);

  const {
    subMenuName,
    innerMenuName,
    detailsForm,
    handleSelectedRows,
    selectAllRows,
    allRowSelected,
  } = props;

  const { domParameters, navigate, location } = useAppRouterDom();

  const dispatch = useDispatch();

  let user = globalutils.getDataFromStorage('all');
  const parametersValue = props?.tableName || '';
  const table_Data = props.tableData ? props.tableData : '';

  const userId = parseInt(user.userId);
  const paramsArray = domParameters.parameters.split(',');

  const [finSubListingData, setFinSubListingData] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [traceEvents, setTraceEvents] = useState([]);
  const [conditionalRowStyles, setConditionalStyles] = React.useState([]);
  const [tableColumns, setTableColumns] = useState([]);

  const [columnTobeSorted, setColumnTobeSorted] = useState({
    sortingVar: '',
    ascOrDesc: 'asc',
  });
  const [offset, setOffset] = useState(1);
  const [pageLimit, setPageLimit] = useState(10);
  const [tableCount, setTableCount] = useState(0);
  const [search, setSearch] = useState('');
  const [searchInput, setSearchInput] = useState('');

  const [selectedRow, setSelectedRow] = useState({});

  //----------------PaymentSource----------------------------

  const validStatuses = [
    'for-authorize',
    'authorized',
    'for-xmsn',
    'transmitted',
    'ack-rcvd',
    'rescheduled',
    'erp-rcvd',
    'in-process',
    'fail-file',
    'fail-xmsn',
    'rejected',
  ];

  const resetTableState = () => {
    setPageLimit(10);
    setOffset(1);
    setColumnTobeSorted({
      sortingVar: '',
      ascOrDesc: 'asc',
    });
    setTableColumns([]);
    setFinSubListingData([]);
    setSearchInput('');
    setSearch('');
  };
  useEffect(() => {
    if (parametersValue && table_Data && detailsForm) {
      detailsTableApiHandler();
    }
  }, [parametersValue, table_Data, detailsForm]);

  useEffect(() => {
    resetTableState();
  }, [parametersValue]);

  useEffect(() => {
    if (finListingDetailsTableCount?.value) {
      setLoading(false);
      setTableCount(finListingDetailsTableCount?.value);
    }
  }, [finListingDetailsTableCount]);

  useEffect(() => {
    if (paymentsTraceEventRes?.value?.length) {
      setTraceEvents(paymentsTraceEventRes.value);
      setShowModal(true);
    } else setTraceEvents([]);
  }, [paymentsTraceEventRes]);

  useEffect(() => {
    if (parametersValue) {
      if (
        parametersValue[1] === 'erp' ||
        parametersValue[1] === 'sap' ||
        parametersValue[1] === 'ap-rcvd'
      ) {
        if (paymentSourceDetailsForm && paymentSourceDetailsForm.value) {
          setFinSubListingData(paymentSourceDetailsForm.value.value);
          columnHandler(paymentSourceDetailsForm.value);
        }
      } else if (parametersValue[1] === 'bank') {
        if (statmentSourceDetailsForm && statmentSourceDetailsForm.value) {
          setFinSubListingData(statmentSourceDetailsForm.value.value);
          columnHandler(statmentSourceDetailsForm.value);
        }
      }
      setConditionalStyles([]);
      setLoading(false);
    }
  }, [paymentSourceDetailsForm, statmentSourceDetailsForm]);

  //----------------PaymentOutwards----------------------------

  useEffect(() => {
    if (parametersValue) {
      if (validStatuses.includes(parametersValue[1])) {
        if (parametersValue[0] === 'bankOut') {
          if (paymentOutwardDetailsForm && paymentOutwardDetailsForm.value) {
            setFinSubListingData(paymentOutwardDetailsForm?.value?.value);
            columnHandler(paymentOutwardDetailsForm.value);
          }
        } else if (parametersValue[0] === 'bankIn') {
          if (statmentOutwardDetailsForm && statmentOutwardDetailsForm.value) {
            setFinSubListingData(statmentOutwardDetailsForm?.value?.value);
            columnHandler(statmentOutwardDetailsForm.value);
          }
        }
      }
      setConditionalStyles([]);
      setLoading(false);
    }
  }, [paymentOutwardDetailsForm, statmentOutwardDetailsForm]);

  useEffect(() => {
    if (detailsForm) columnHandler(detailsForm);
  }, [detailsForm, finSubListingData]);
  // useEffect(() => {
  //   if (!finSubListingData.length) isAllChecked();
  // }, [finSubListingData]);

  const checkBoxHandler = (chekData, row) => {
    if (parametersValue[0] === 'bankOut') {
      handleSelectedRows({ ...row, flag: chekData }, finSubListingData.length);
    } else handleSelectedRows({ ...row, flag: chekData });
    setFinSubListingData((prevData) => {
      let tempData = prevData.map((data) => {
        if (data.serialNo === row.serialNo) {
          return { ...data, flag: chekData };
        } else return data;
      });

      return tempData;
    });
  };

  const handleSelectAll = () => {
    if (!finSubListingData.length) return;

    // Check if all rows are currently selected
    const allCurrentlySelected = isAllChecked();

    setFinSubListingData((prevData) => {
      return prevData.map((data) => ({
        ...data,
        flag: !allCurrentlySelected,
      }));
    });

    if (paramsArray[1] === 'for-authorize') {
      let rows = finSubListingData.filter(
        (item) => item?.authorizeflag === 'N'
      );
      selectAllRows(!allCurrentlySelected ? rows : []);
      return;
    }

    selectAllRows(!allCurrentlySelected ? finSubListingData : []);
  };

  const isAllChecked = () => {
    if (!Array.isArray(finSubListingData) || finSubListingData.length === 0) {
      allRowSelected(false);
      return false;
    }

    const itemsToCheck =
      paramsArray[1] === 'for-authorize'
        ? finSubListingData.filter((item) => item.authorizeflag === 'N')
        : finSubListingData;

    const checkStatus =
      itemsToCheck.length > 0 &&
      itemsToCheck.every((item) => item.flag === true);

    allRowSelected(checkStatus);

    return checkStatus;
  };

  const alignedSubSelectors = ['amount', 'bankCharge'];
  const columnHandler = (data) => {
    let columns = [...data.formSubDetailsInternalDTOList];
    if (parametersValue[1] === 'for-authorize') {
      //
    } else if (parametersValue[1] === 'authorized') {
      if (table_Data?.dueFlag !== 'Y')
        columns = columns.filter((col) => col.selector !== 'checkbox');
    } else {
      columns = columns.filter((col) => col.selector !== 'checkbox');
    }
    let noSorting = ['checkbox', 'serialNo'];

    columns = columns.map((item) => {
      const isSortable = !noSorting.includes(item.selector);
      let dataAligned = alignedSubSelectors.includes(item.selector);
      return {
        selector: item.selector,
        name:
          item.selector === 'checkbox' ? (
            <CheckBoxInput
              checked={isAllChecked()}
              onChange={() => handleSelectAll()}
            />
          ) : (
            item.displayName
          ),
        style: { paddingLeft: '16px' },
        width: item.displayWidth,
        sortable: isSortable,
        headerStyle: dataAligned
          ? {
              width: '100%',
            }
          : {},
        // right: ['availableBalance', 'amount', 'creditAmt', 'debitAmt'].includes(
        //   item.selector
        // ),
        // center: ['currency'].includes(item.selector),
        // removes table default sorting fn, for sortable clms,
        ...(isSortable && {
          sortFunction: () => 0,
        }),
        cell: (row) => {
          const shouldShowCheckbox =
            item.selector === 'checkbox' &&
            ((paramsArray[1] === 'for-authorize' &&
              row.authorizeflag === 'N') ||
              paramsArray[1] !== 'for-authorize');

          if (shouldShowCheckbox) {
            return (
              <CheckBoxInput
                checked={!!row.flag}
                onChange={(e) => checkBoxHandler(e.target.checked, row)}
              />
            );
          } else if (item.selector === 'amount') {
            return row?.exchangeRate ? (
              <Tooltip
                classNames={{ root: 'ant-tooltip-container' }}
                title={`${row?.exchangeRate ?? ''}`}
              >
                <div
                  className="display-amount custom-overflow"
                  onClick={() => handleRowClick(row)}
                  style={{
                    width: dataAligned ? '100%' : '',
                    textAlign: dataAligned ? 'end' : '',
                  }}
                >
                  {`${row.amount}`}
                </div>
              </Tooltip>
            ) : (
              <div
                className="display-amount custom-overflow"
                onClick={() => handleRowClick(row)}
              >
                {`${row.amount}`}
              </div>
            );
          } else {
            return (
              <p
                title={row[item?.selector]}
                style={{
                  margin: 0,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  width: dataAligned ? '100%' : '',
                  textAlign: dataAligned ? 'end' : '',
                }}
                onClick={() => handleRowClick(row)}
              >
                {row[item?.selector]}
              </p>
            );
          }
        },
      };
    });

    setTableColumns(columns);
  };

  useEffect(() => {
    if (subMenuName || innerMenuName) {
      setConditionalStyles([]);
    }
  }, [subMenuName, innerMenuName]);

  function handleRowClick(row) {
    setSelectedRow(row);
    let tableName = '';
    let tableId = '';
    if (subMenuName === 'Payments') {
      if (
        innerMenuName === 'Acc. Payables' ||
        innerMenuName === 'ERP' ||
        innerMenuName === 'SAP'
      ) {
        tableName = 'internal_in_txn_master';
        tableId = 'intInTxnDetailsId';
      } else {
        tableName = 'bank_out_txn_master';
        tableId = 'bankOutDetailsId';
      }
    } else if (subMenuName === 'Statements') {
      if (innerMenuName === 'Bank') {
        tableName = 'bank_in_txn_master';
        tableId = 'bankInTxnDtlsId';
      } else {
        tableName = 'internal_out_txn_master';
        tableId = 'intOutTxnDetailsId';
      }
    }
    if (tableName && tableId && row[tableId])
      dispatch(
        getSessionTransactionTraceEvents({ tableName, tableId: row[tableId] })
      );
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  }

  // Handle Page Change
  const handlePageChange = (newOffset) => {
    setOffset(newOffset);
    detailsTableApiHandler({ offset: newOffset });
  };

  // Handle Rows per Page Change
  const handlePerRowsChange = (newLimit, page) => {
    setPageLimit(newLimit);
    detailsTableApiHandler({ limit: newLimit });
  };

  const sortHandler = (data, direction) => {
    if (!data?.selector) return;

    setColumnTobeSorted({ sortingVar: data.selector, ascOrDesc: direction });
    detailsTableApiHandler({ sortingVar: data.selector, ascOrDesc: direction });
    setLoading(true);
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    if (value.trim() === '') {
      setSearch('');
      detailsTableApiHandler({ searchVariable: '' });
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && searchInput !== '') {
      handleSearch();
    }
  };

  const handleSearch = () => {
    if (searchInput !== '') {
      setLoading(true);
      if (searchInput.trim() === '') {
        setSearch('');
        detailsTableApiHandler({ searchVariable: searchInput });
      } else {
        setSearch(searchInput);
        detailsTableApiHandler({ searchVariable: searchInput });
      }
    } else {
      return;
    }
  };

  const detailsTableApiHandler = (parameter = {}) => {
    setLoading(true);

    let formObj = {
      formDetailsDto: detailsForm,
      txnId: '',
      limit: pageLimit,
      offset: offset,
      status: parametersValue[1] ?? '',
      searchVariable: search,
      ...columnTobeSorted,
      ...parameter,
    };

    let countObj = {
      txnId: '',
      searchVariable: search,
      status: parametersValue[1] ?? '',
      formDetailsDto: detailsForm,
      ...parameter,
    };

    if (
      parametersValue[1] === 'erp' ||
      parametersValue[1] === 'sap' ||
      parametersValue[1] === 'ap-rcvd'
    ) {
      dispatch(
        getPaymentSourceDetailsCount({ ...countObj, txnId: table_Data.inTxnId })
      );
      dispatch(
        getPaymentSourceDetailsForm({ ...formObj, txnId: table_Data.inTxnId })
      );
    } else if (
      parametersValue?.[0] === 'bankOut' &&
      validStatuses.includes(parametersValue[1])
    ) {
      dispatch(
        getPaymentOutwardDetailsCount({
          ...countObj,
          txnId: table_Data.bankOutTxnId,
        })
      );
      dispatch(
        getPaymentOutwardDetailsForm({
          ...formObj,
          txnId: table_Data.bankOutTxnId,
        })
      );
    } else if (
      parametersValue?.[0] === 'bankIn' &&
      validStatuses.includes(parametersValue[1])
    ) {
      dispatch(
        getStatmentOutwardDetailsCount({
          ...countObj,
          txnId: table_Data.internalOutTransactionId,
        })
      );
      dispatch(
        getStatmentOutwardDetailsForm({
          ...formObj,
          txnId: table_Data.internalOutTransactionId,
        })
      );
    } else if (parametersValue[1] === 'bank') {
      dispatch(
        getStatmentSourceDetailsCount({
          ...countObj,
          txnId: table_Data.bankInTxnMasterBeanId,
        })
      );
      dispatch(
        getStatmentSourceDetailsForm({
          ...formObj,
          txnId: table_Data.bankInTxnMasterBeanId,
        })
      );
    }
  };

  const authorizeConditionalRowStyles = [
    {
      when: (row) =>
        row.authorizeflag === 'Y' && paramsArray[1] === 'for-authorize',
      style: {
        opacity: '.7',
      },
    },
  ];

  return (
    <div className="finSubList-container" id="finSubList-container">
      {/* <CommonSpinner visible={commonLoading} /> */}
      <div className="page-sub-title mb20 group">
        {parametersValue && parametersValue[0] === 'bankOut'
          ? 'Payment Details'
          : 'Statement / Acknowledgment'}
      </div>

      {(finSubListingData.length > 0 || searchInput.trim() !== '') && (
        <div
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '12px',
            marginBottom: '8px',
          }}
        >
          <TextInput
            placeholder="Search"
            value={searchInput}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            style={{
              width: '200px',
            }}
            label={false}
          />

          <Button className="small default button" onClick={handleSearch}>
            Search
          </Button>
        </div>
      )}

      <div className="mb24 styledDatatable">
        <DataTable
          persistTableHead
          noHeader={true}
          dense={false}
          highlightOnHover={true}
          striped={true}
          columns={tableColumns}
          data={finSubListingData}
          pagination={true}
          paginationResetDefaultPage={true}
          paginationTotalRows={tableCount}
          onChangePage={handlePageChange}
          progressPending={isLoading}
          progressComponent={<Ellipsis color={globalutils.spinnerColor()} />}
          // updated={checkBoxEnable}
          onSort={sortHandler}
          onChangeRowsPerPage={handlePerRowsChange}
          paginationPerPage={pageLimit}
          paginationDefaultPage={offset}
          key={pageLimit}
          onRowClicked={(data) => handleRowClick(data)}
          conditionalRowStyles={[
            ...authorizeConditionalRowStyles,
            ...conditionalRowStyles,
          ]}
        />
      </div>

      {showModal ? (
        <Modal
          className="Modal"
          overlayClassName="ModalOverlay"
          ariaHideApp={false}
          isOpen={showModal}
        >
          <div
            onClick={() => {
              setShowModal(false);
            }}
            className="modal-close icon-close"
            style={{ fontSize: 20 + 'px' }}
          ></div>

          <p className="page-title">Payment Event Logs</p>

          <div className="pymt-log-details-container">
            {paymentHeaderDetails(table_Data, selectedRow)}
          </div>

          <TraceEvents
            key="traceEventsEditor"
            data={traceEvents}
            userId={userId}
            maxHeight={450}
          />
        </Modal>
      ) : (
        ''
      )}
    </div>
  );
};

export { FinListingSubList };
