/* eslint-disable react/jsx-key */
/* eslint-disable react/jsx-no-useless-fragment */

import React from 'react';
import { Input } from '@hp/components';
import {  useLocation, useNavigate } from 'react-router-dom';
function ExpenseListingComponent(props) {
const navigate =useNavigate()
  let location = useLocation();
  const locationSplit = location.pathname.split('/');
  const [conditionalRowStyles] = React.useState([]);
  const { formElements } = props;

  const onRowClick = (e) => {
    if (locationSplit[2] === 'expense-travel-request') {
      navigate({
        pathname: `${location.pathname}/updateReqNo?=${e.trNumber}`,
        state: { selectedRowData: e },
      });
    } else if (locationSplit[2] === 'expenses-report') {
      navigate({
        pathname: `${location.pathname}/reportNumber?=${e.erNumber}`,
        state: { selectedRowData: e },
      });
    }
  };
  const formControlsBinding = (data) => {
    return data && data.length
      ? data.map((element, index) => {
          if (
            element.type === 'PageTitle' &&
            element.formSubDetailsInternalDTOList &&
            element.formSubDetailsInternalDTOList.length
          ) {
            return (
              <div>
                <div key={index} className="page-title">
                  {element.displayName}
                </div>
                <div className="card mb20">
                  <div key={index + 1} className="three-col-layout">
                    {' '}
                    {formControlsBinding(element.formSubDetailsInternalDTOList)}
                  </div>
                </div>
              </div>
            );
          }
          return (
            <Input
              key={index}
              formType={element}
              dataTableEventHandler={(e) => onRowClick(e)}
              conditionalRowStyles={conditionalRowStyles}
              dataTablePagination={false}
              // onChangeHandler={(onChangeFunction, e, uniqueKey) => onChangeHandlingFunctions[onChangeFunction](e, uniqueKey, index, element)}
            />
          );
        })
      : null;
  };
  return (
    <>
      {formElements && formElements.length
        ? formControlsBinding(formElements)
        : null}
    </>
  );
}
export { ExpenseListingComponent };
