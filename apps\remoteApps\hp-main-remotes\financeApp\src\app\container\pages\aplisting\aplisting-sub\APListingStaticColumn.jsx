import React from 'react';

const DisplayTitle = (row, key) => {
  return (
    <div className="display-title" title={row[key]}>
      {row[key]}
    </div>
  );
};

export const headerColumns = [
  {
    name: 'Serial #',
    selector: '',
    cell: (row) => DisplayTitle(row, ''),
  },
  {
    name: 'Received At',
    selector: 'rcvdAt',
    cell: (row) => DisplayTitle(row, 'rcvdAt'),
  },
  {
    name: 'File Name',
    selector: 'fileName',
    cell: (row) => DisplayTitle(row, 'fileName'),
  },
];
