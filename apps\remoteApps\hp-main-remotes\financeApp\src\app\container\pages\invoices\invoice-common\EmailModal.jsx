/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @create date 20-01-2023 13:47:38
 * @modify date 12-10-2023 09:54:32
 * @desc [description]
 */

import { DocumentViewer, ImageSwitcher, TextInput } from '@hp/components';
import { useEffect, useState } from 'react';
import Modal from 'react-modal';

const EmailModal = (props) => {
  const { isModalOpen, isModalClose, emailDetails } = props;
  const [emailTemplateDetails, setEmailTemplate] = useState({
    toAddress: [],
    subject: '',
    content: '',
  });

  useEffect(() => {
    setEmailTemplate(emailDetails);
  }, [emailDetails]);

  return (
    <Modal
      className="ModalPoMatrix"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={isModalOpen}
    >
      <div className="page-sub-title">Email Content</div>
      <div
        onClick={() => isModalClose()}
        className="modal-close icon-close"
        style={{ fontSize: 20 + 'px' }}
      ></div>
      <div className="col">
        <TextInput
          label="To"
          className="mb16"
          defaultValue={
            emailTemplateDetails ? emailTemplateDetails.toAddress[0] : ''
          }
          disabled={true}
          value={emailTemplateDetails.toAddress[0]}
        />
        <TextInput label="Cc" className="mb16" />
        <TextInput
          label="Subject"
          className="mb16"
          defaultValue={
            emailTemplateDetails ? emailTemplateDetails.subject : ''
          }
          value={emailTemplateDetails.subject}
        />
      </div>
      <div className="two-col-layout-20">
        <div>
          <ImageSwitcher
            className={'mr20'}
            style={{ fontSize: 24 + 'px' }}
            label="Invoice"
          />
        </div>
      </div>
      <div className="col mb20"> </div>

      <DocumentViewer
        fileURL={emailTemplateDetails.content}
        fileType={'iframe-html'}
        zoom={'#zoom=100'}
        iframeStyle={{ border: 'ridge', height: '100%', width: '100%' }}
      />
    </Modal>
  );
};

export { EmailModal };
