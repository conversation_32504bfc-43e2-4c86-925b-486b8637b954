/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */

import React, { useEffect, useState } from 'react';
import {
  
  CompareComponent,
  dataTableServiceProvider,
} from '@hp/components';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { getDetailsScreenAction } from '@hp/mainstore';
import '../../_invoices/InvoiceCompare.scss';
function ExpenseCompareComponent(props) {
  const { state, search } = props.location;
  const { expenseDetailsResponse, isExpReportFormloading } = useSelector(
    (store) => store.expense
  );
  const [conditionalRowStyles, setConditionalStyles] = React.useState([]);
  const [formElements, setFormElements] = useState([]);
  const [leftBoxControls, setLeftBoxControls] = useState([]);
  const [rightBoxControls, setRightBoxControls] = useState([]);
  let location = useLocation();
  let pageTitle = location.pathname.split('/');
  var erID =
    state && state !== undefined
      ? state.selectedRowData.erNumber
      : search.slice(2);

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(
      getDetailsScreenAction({ listName: 'ERItem', itemID: parseInt(erID) })
    );
    return () => {
      // dispatch(expenseActions.getTravelRequestForm("", true));
    };
  }, []);

  useEffect(() => {
    if (expenseDetailsResponse) {
      const filteredArray = expenseDetailsResponse.filter(
        (item) => item.uniqueKey === 'ER-TR-ComparePage'
      );
      filteredArray.forEach((data) => {
        if (
          data.displayName.includes('Expense Report') ||
          data.displayName.includes('Expenses')
        ) {
          if (
            data.formSubDetailsInternalDTOList &&
            data.formSubDetailsInternalDTOList.length
          ) {
            data.formSubDetailsInternalDTOList.map((leftControls) => {
              leftBoxControls.push(leftControls);
            });
          }
        } else if (
          data.displayName.includes('Travel Request') ||
          data.displayName.includes('Travel Request Expense')
        ) {
          if (
            data.formSubDetailsInternalDTOList &&
            data.formSubDetailsInternalDTOList.length
          ) {
            data.formSubDetailsInternalDTOList.map((rightControls) => {
              rightBoxControls.push(rightControls);
            });
          }
        }
      });
      setLeftBoxControls(leftBoxControls);
      setRightBoxControls(rightBoxControls);
    }
    return () => {
      setFormElements([]);
    };
  }, [expenseDetailsResponse]);

  const onDataTableRowClick = (e) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      e.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  };

  return (
    <>
      <CompareComponent
        pageTitle={pageTitle[4] || ''}
        autoMatchSwitch={false}
        changeViewSwitch={true}
        isFormloading={isExpReportFormloading}
        leftBoxName={'Expense Report'}
        leftBoxControls={leftBoxControls}
        leftBoxDocumentViewVisible={false}
        rightBoxName={'Travel Request - '}
        rightBoxControls={rightBoxControls}
        rightBoxDocumentViewVisible={false}
        conditionalRowStyles={conditionalRowStyles}
        onDataTableRowClick={(e) => onDataTableRowClick(e)}
      />
    </>
  );
}
export { ExpenseCompareComponent };
