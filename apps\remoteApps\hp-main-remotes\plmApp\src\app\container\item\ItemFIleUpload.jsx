/* eslint-disable array-callback-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable jsx-a11y/iframe-has-title */

import { useState, useLayoutEffect, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Modal from 'react-modal';
import {
  ButtonCommon,
  Input,
  TextAreaInput,
  Select,
  Button,
  TextInput,
  useConfirm,
  DocUpload,
  ButtonDropDown,
  DocumentViewer,
} from '@hp/components';
import { AP_file_url } from '@hp/constants';
import PdmTree from '../PdmTree';
import { dataTableServiceProvider } from '@hp/components';
import { Link } from 'react-router-dom';
import { pdmConstants } from '@hp/mainstore';
import {
  addDocumentType,
  editFileUpload,
  getFileVersionDetails,
  getPDMItemDetails,
  showDialog,
  deleteFileUpload,
  generateRFQ,
  getVersionNumber,
  pdmItemDetailsAddOrEdit,
  uploadPdm,
  docCategoryAction,
} from '@hp/mainstore';

function ItemFileUpload(props) {
  const [table, setTable] = useState([]);
  const [tableHead, setTableHead] = useState([]);
  const [itemID, setItemID] = useState('');
  const [formDetils, setFormDetils] = useState([]);
  const [dataTable1, setDataTable1] = useState({});
  const [isModal, setIsModal] = useState(false);
  const [filepath, setFilepath] = useState('');
  const [versionNo, setVersionNo] = useState('');
  const [enableEdit, setEnableEdit] = useState(true);
  const [action, setAction] = useState('');
  const [upload, setUpload] = useState(false);
  const [fileUpload, setFileUpload] = useState(false);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [serialNo, setSerialNo] = useState('');
  const [fileId, setFileId] = useState('');
  const { confirm } = useConfirm();

  const [addDocumentKeys, setAddDocumentKeys] = useState({
    fileId: '',
    fileType: '',
    documentCategory: '',
    docComments: '',
  });
  const [category, setCategory] = useState([]);
  const [fileAddOrEdit, setFileAddOrEdit] = useState('');
  const [fileColumn, setFileColumn] = useState([]);

  const dispatch = useDispatch();
  const fileInput = useRef(null);

  const { innerMenuName } = useSelector((store) => store.menu);
  const {
    pdmFileVersionDetails,
    docCategory,
    pdmItemDetails,
    uploadPdmSuccess,
    fileEdited,
    fileDeleted,
    versionNumber,
    generateRfqResponse,
    addDocTypeResponse,
  } = useSelector((store) => store.pdm);

  let fileTempColumn = [
    {
      name: '',
      selector: 'uploadIcon',
      width: '6%',
      cell: function displayCell(row) {
        return (
          <div className="icon-upload" onClick={() => handleUpload(row)}></div>
        );
      },
    },
    {
      name: '',
      selector: 'editIcon',
      width: '6%',
      cell: function displayCell(row) {
        return (
          <div
            className="icon-edit-button"
            onClick={() => onFileEdit(row)}
          ></div>
        );
      },
    },
    {
      name: '',
      selector: 'deleteIcon',
      width: '6%',
      cell: function displayCell(row) {
        return (
          <div
            className="icon icon-2-trash"
            onClick={() => onFiledelete(row)}
          ></div>
        );
      },
    },
  ];

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  useEffect(() => {
    if (addDocTypeResponse?.value) {
      funcToSetResponseMessage('success', addDocTypeResponse.value);
      dispatch(
        getPDMItemDetails({ itemId: itemID, parameter: props.parameter })
      );
    }
  }, [addDocTypeResponse]);

  useEffect(() => {
    formHandeler(props.Action, props.Node);
  }, [props.Action, props.Node]);

  useLayoutEffect(() => {
    if (versionNumber && versionNumber.value) {
      setVersionNo(versionNumber.value);
    }
  }, [versionNumber]);

  useEffect(() => {
    if (fileEdited && fileEdited !== undefined) {
      funcToSetResponseMessage(fileEdited.type, fileEdited.value);
      dispatch(
        getPDMItemDetails({ itemId: itemID, parameter: props.parameter })
      );
    }
  }, [fileEdited]);

  useEffect(() => {
    if (generateRfqResponse && generateRfqResponse !== undefined) {
      funcToSetResponseMessage('success', generateRfqResponse.value);
    }
    return () => {
      dispatch({
        type: pdmConstants.GENERATE_RFQ_SUCCESS,
        payload: null,
      });
    };
  }, [generateRfqResponse]);

  useEffect(() => {
    if (fileDeleted && fileDeleted !== undefined) {
      funcToSetResponseMessage(fileDeleted.type, fileDeleted.value);
      dispatch(
        getPDMItemDetails({ itemId: itemID, parameter: props.parameter })
      );
    }
  }, [fileDeleted]);

  useLayoutEffect(() => {
    if (docCategory && docCategory.value) {
      var combo = docCategory.value.map((option) => {
        return {
          value: option.commonId ? option.commonId : null,
          display: option.commonCode ? option.commonCode : null,
          comments: option.comments,
          fileType: option.fileType,
        };
      });
      setCategory(combo);
    }
  }, [docCategory]);

  useLayoutEffect(() => {
    if (uploadPdmSuccess) {
      dispatch(getFileVersionDetails({ fileId: fileId, itemId: itemID }));
    }
  }, [uploadPdmSuccess]);

  useLayoutEffect(() => {
    if (pdmItemDetails?.value?.formDetailsDtoList?.length) {
      pdmItemDetails.value.formDetailsDtoList.map((detailList) => {
        if (detailList.uniqueKey === 'details') {
          return setFormDetils(
            Array.isArray(detailList.formSubDetailsInternalDTOList)
              ? detailList.formSubDetailsInternalDTOList
              : []
          );
        }

        if (detailList.uniqueKey === 'files') {
          let fileObj =
            detailList.formSubDetailsInternalDTOList &&
            detailList.formSubDetailsInternalDTOList.length > 0
              ? detailList.formSubDetailsInternalDTOList.filter(
                  (data) => data.uniqueKey === 'file'
                )[0]
              : null;

          let tempData = fileObj.formSubDetailsInternalDTOList.map((cols) => {
            return {
              selector: cols.selector,
              width: cols.displayWidth,
              name: cols.displayName,
            };
          });
          fileTempColumn.map((cols) => tempData.push(cols));
          setFileColumn(tempData);
          return setDataTable1(detailList);
        }
      });
    }
  }, [pdmItemDetails]);

  useLayoutEffect(() => {
    if (
      pdmFileVersionDetails &&
      pdmFileVersionDetails.value &&
      pdmFileVersionDetails.value.formDetailsDtoList &&
      pdmFileVersionDetails.value.formDetailsDtoList.length
    ) {
      let originalVersion = pdmFileVersionDetails.value.formDetailsDtoList[0];

      let version = { ...originalVersion };

      var result = version.value.map(function (el, i) {
        var obj = Object.assign({}, el);
        obj.fileName = (
          <Link
            onClick={() => {
              setFilepath(el.filePath);
              setIsModal(true);
            }}
          >
            {el.fileName}
          </Link>
        );
        return obj;
      });

      version.value = result;
      setTable(version);

      setTableHead(
        version.formSubDetailsInternalDTOList.map((dtoList) => {
          return {
            width: dtoList.displayWidth,
            name: dtoList.displayName,
            selector: dtoList.selector,
          };
        })
      );
    }
  }, [pdmFileVersionDetails]);

  const handleRowClick = (obj) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.serialNo,
      'serialNo'
    );

    setConditionalStyles(styleAttribute);
    setSerialNo(obj.serialNo);
    setFileId(obj.fileId);
    dispatch(getFileVersionDetails({ fileId: obj.fileId, itemId: itemID }));
  };

  // const versionTableClick = (obj) => {
  //   dispatch(pdmAction.versionDelete(fileId, obj.versionId));
  // };

  const inputHandler = (index, event) => {
    const inputValue = [...formDetils];
    inputValue[index].value = event.target.value;
    setFormDetils(inputValue);
  };
  const selectHandler = (index, event) => {
    const inputValue = [...formDetils];
    inputValue[index].value = parseInt(event.target.value);
    setFormDetils(inputValue);
  };
  const onSubmitHandler = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');

    if (isConfirmed) {
      if (fileAddOrEdit === 'add') {
        let saveObject = {
          docCategoryId: Number(addDocumentKeys.documentCategory),
          type: addDocumentKeys.fileType,
          comments: addDocumentKeys.docComments,
        };
        dispatch(addDocumentType({ itemID: itemID, reqObject: saveObject }));
        setUpload(false);
      } else if (fileAddOrEdit === 'edit') {
        let editObj = {
          docCategoryId: Number(addDocumentKeys.documentCategory),
          fileId: addDocumentKeys.fileId,
          comments: addDocumentKeys.docComments,
        };
        dispatch(editFileUpload(editObj));
        setUpload(false);
      }
    }
  };

  const dateHandler = (index, date) => {
    let Details = [...formDetils];
    Details[index].value = date;
    setFormDetils(Details);
  };
  const onChangeHandlingFunctions = {
    inputHandler,
    dateHandler,
    selectHandler,
  };

  const onFiledelete = async (e) => {
    const isConfirmed = await confirm(' Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteFileUpload(e.fileId));
    }
  };
  const onFileEdit = (e) => {
    dispatch(docCategoryAction());
    setFileAddOrEdit('edit');

    setAddDocumentKeys({
      fileId: e.fileId,
      fileType: e.type,
      docComments: e.comments,
      documentCategory: e.docCategoryId,
    });
    setUpload(true);
  };
  const formHandeler = (action, node) => {
    setAction(action);
    setTable({});
    setItemID(node.id);
    dispatch(
      getPDMItemDetails({ itemId: node.id, parameter: props.parameter })
    );
  };

  const handleSave = () => {
    if (enableEdit) return;

    for (let form of formDetils) {
      if (form.type === 'Select') continue;
      if (!form.value) {
        funcToSetResponseMessage('info', `${form.displayName} can't be empty`);
        return;
      }
    }

    const form = { ...pdmItemDetails.value };
    form.formDetailsDtoList.map((list) => {
      if (list.uniqueKey === 'details') {
        list.formSubDetailsInternalDTOList = formDetils;
      }
    });

    if (action === 'edit') {
      dispatch(pdmItemDetailsAddOrEdit(action, form));
    }
  };

  const handleUpload = (row) => {
    if (fileInput.current !== null) {
      fileInput.current.click();
    }
    if (!row.fileId) return;
    dispatch(getVersionNumber(row.fileId));
    setFileUpload(true);
  };

  const addDocLoad = () => {
    setFileAddOrEdit('add');
    dispatch(docCategoryAction());
    setAddDocumentKeys({
      ...addDocumentKeys,
      fileType: '',
      docComments: '',
      documentCategory: '',
    });

    setUpload(true);
  };
  const onFileUpload = (e) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append('file', e.target.files[0]);
    dispatch(
      uploadPdm({ txnId: itemID, serialNo: serialNo, formData: formData })
    );
  };

  const handleGenRfq = () => {
    dispatch(generateRFQ(itemID));
  };

  const fileVersionUpload = (fileWithDescription) => {
    dispatch(
      uploadPdm({
        txnId: itemID,
        serialNo: serialNo,
        formData: fileWithDescription.file,
      })
    );
    setFileUpload(false);
  };

  const functionsName = {
    handleSave,
    handleGenRfq,
  };

  const getForms = (formList) => {
    return formList && formList.length
      ? formList.map((form, index) => {
          if (form.type !== 'DataTable') {
            return (
              <div className="single" key={index}>
                <div className="td label" style={{ flex: 1 }}>
                  <p
                    style={{
                      fontWeight: '100',
                    }}
                  >
                    {form.displayName}
                  </p>
                </div>
                <div className="td po">
                  <Input
                    disabledFlag={true}
                    formType={form}
                    // isEditable="notShowing"
                  />
                </div>
              </div>
            );
          }
        })
      : '';
  };
  return (
    <div className="pdm-main-container">
      <div
        className="mb20 boxed unbox edit-only-half is-not-editing"
        key={'right'}
        style={{ flex: 2 }}
      >
        {/* <div className=" three-col-layout mb20">
            {formDetils.map((form, index) => {
              if (form.uniqueKey === "file_type" || form.uniqueKey === "cost")
                return "";
              else
                return (
                  <Input
                    onChangeHandler={(type, event) =>
                      onChangeHandlingFunctions[form.onChangeFunction](
                        index,
                        event
                      )
                    }
                    disabledFlag={enableEdit}
                    formType={form}
                    key={index}
                    isEditable="notShowing"
                  />

                );
            })}
          </div> */}
        <div className="inv-table-view">
          <div className="inv-table-body-format mb20">
            {getForms(formDetils)}
          </div>
        </div>
        <div>
          <h1 className="page-sub-title">
            {dataTable1.displayName ? dataTable1.displayName : ''}
          </h1>
          <div style={{ float: 'right' }}>
            {formDetils && formDetils.length ? (
              <Button
                onClick={() => addDocLoad()}
                className="small mb16 outline add-button-custom flex-row vam"
              >
                <i className="icon-add-button "> </i>Add Document Type
              </Button>
            ) : (
              ''
            )}
          </div>

          {dataTable1.formSubDetailsInternalDTOList &&
          dataTable1.formSubDetailsInternalDTOList.length
            ? dataTable1.formSubDetailsInternalDTOList.map((form, index) => {
                if (form.uniqueKey === 'file') {
                  return (
                    <Input
                      formType={form}
                      conditionalRowStyles={conditionalRowStyles}
                      key={index}
                      dataTableEventHandler={(obj) => {
                        handleRowClick(obj);
                      }}
                      dataTableColumn={fileColumn}
                    />
                  );
                }
              })
            : ''}

          <Input formType={table} dataTableColumn={tableHead} />
        </div>
      </div>
      {/* <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>
        <iframe
          style={{ width: 100 + '%', height: 100 + '%' }}
          src={AP_file_url + filepath}
        ></iframe>
      </Modal> */}

      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={upload}
      >
        <div
          onClick={() => {
            setUpload(false);
          }}
          className="modal-close icon-close"
        ></div>

        <div className="two-col-layout">
          <Select
            className="mb20"
            name="documentCategory"
            label="Document category"
            value={addDocumentKeys.documentCategory}
            options={category}
            onChange={(e) =>
              setAddDocumentKeys({
                ...addDocumentKeys,
                fileType: category[e.target.selectedIndex - 1].fileType,
                docComments: category[e.target.selectedIndex - 1].comments,
                [e.target.name]: e.target.value,
              })
            }
          />

          <TextInput
            label="File Type"
            name="label"
            disabled={true}
            value={addDocumentKeys.fileType}
            onChange={(e) =>
              setAddDocumentKeys({
                ...addDocumentKeys,
                [e.target.name]: e.target.value,
              })
            }
          />

          <TextAreaInput
            label="Comments"
            className="mb20"
            // disabled={true}
            name="docComments"
            // style={{ width: 105 + '%' }}
            value={addDocumentKeys.docComments || ''}
            onChange={(e) =>
              setAddDocumentKeys({
                ...addDocumentKeys,
                [e.target.name]: e.target.value,
              })
            }
          />
        </div>

        <Button
          className="default fr"
          disabled={!addDocumentKeys.documentCategory && true}
          onClick={() => onSubmitHandler()}
        >
          Submit
        </Button>
      </Modal>
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
        style={{
          content: {
            minHeight: '80%',
          },
        }}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>

        <DocumentViewer
          fileURL={AP_file_url + filepath}
          fileType={'iframe'}
          iframeStyle={{ width: 100 + '%', minHeight: '500px' }}
        />
      </Modal>
      <Button
        type="file"
        id="file-input"
        ref={fileInput}
        style={{ display: 'none' }}
        onChange={(e) => onFileUpload(e)}
      />

      <DocUpload
        title={'Upload Specification Document'}
        labels={{
          headLbl: 'Drag File or Click to upload',
          formatLbl: 'Accept format: pdf,xlsx,doc',
          sizeLbl: 'Size: Max 5mb',
        }}
        versionNumber={versionNo}
        isShow={fileUpload}
        onsubmitHandler={fileVersionUpload}
        onCancel={() => setFileUpload(false)}
      />
      <ButtonCommon functionsName={functionsName} />
    </div>
  );
}

export { ItemFileUpload };
