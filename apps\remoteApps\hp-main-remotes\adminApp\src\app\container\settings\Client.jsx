/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/**
 * <AUTHOR> Sabastian
 * @email <EMAIL>
 * @create date 22-02-2022 15:40:13
 * @modify date 2024-07-23 10:25:44
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';

import {
  getClientFormDetails,
  editClientFormDetails,
  deleteClientFormDetails,
  addClientFormDetails,
  getProfileImage,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { AP_file_url } from '@hp/constants';
import { Tooltip } from 'react-tooltip';
import {
  dataTableServiceProvider,
  formValidationUtil,
  Imageupload,
  Input,
  useConfirm,
} from '@hp/components';
import { Button } from '@hp/components';
import {
  showDialog,
  resetaddClientFormDetails,
  reseteditClientFormDetails,
  resetdeleteClientFormDetails,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';
const Client = () => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { clientFormDetails, clientFormDetailsResponse } = useSelector(
    (store) => store.settings
  );
  const { profileImageDetails } = useSelector((store) => store.user);
  const subscribe = 'subscribe';
  const clientId = '';
  const [url, setUrl] = useState('');
  const [data, setData] = useState({ form: null });
  const [formDetails, setFormDetails] = useState([]);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [upload, setUpload] = useState(false);
  const [selected, setSelected] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [notify, setnotify] = useState(null);
  const [showImage, setShowImage] = useState(false);
  const [clientLogo, setClientLogo] = useState('');

  const [show, setShow] = useState(true);
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let mesg = 'Size should be 140 x 50 or below';
  let imgSize = { height: 50, width: 140, radius: 0 };
  useEffect(() => {
    if (clientFormDetails && clientFormDetails.value) {
      setFormDetails(clientFormDetails.value);
      setTableData(
        clientFormDetails?.value?.find((val) => val.uniqueKey === 'dataTable')
          ?.value
      );
    }
  }, [clientFormDetails]);

  useEffect(() => {
    if (profileImageDetails) {
      const clientImageurl =
        profileImageDetails?.value?.clientImagePropDto?.filePath;
      const clientDp = cloneDeep(AP_file_url + clientImageurl);
      setClientLogo(clientDp);
    }
  }, [profileImageDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      dispatch(resetaddClientFormDetails());
      dispatch(reseteditClientFormDetails());
      dispatch(resetdeleteClientFormDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (clientFormDetailsResponse && clientFormDetailsResponse.value) {
      funcToSetResponseMessage(
        clientFormDetailsResponse.value.type,
        clientFormDetailsResponse.value.text
      );
    }
    dispatch(getClientFormDetails(userId));
    dispatch(getProfileImage(subscribe));
  }, [clientFormDetailsResponse]);

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let clientNotExist = true;
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (
            element.uniqueKey === 'companyName' ||
            element.uniqueKey === 'email'
          ) {
            if (element.value === null || element.value.trim() === '') {
              clientNotExist = false;
              funcToSetResMessageInModal(
                'error',
                'Mandatory fields should not be empty.'
              );
            } else {
              tempArray.map((item) => {
                if (item.uniqueKey === 'dataTable') {
                  item.value.map((val) => {
                    if (val.companyName === element.value) {
                      if (selected.companyName === element.value) {
                        return;
                      } else {
                        clientNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Client already exists'
                        );
                      }
                    }
                  });
                }
              });
            }
          }
        });
    });

    //Do not change field name, ie it should be "formDetails"
    let FormDetails = {
      formDetails: tempArray.find((val) => val.uniqueKey === 'pageSubtitle')
        .formSubDetailsInternalDTOList,
    };
    const formDataTemp = new FormData();
    const FormDetailsJSON = JSON.stringify(FormDetails);
    // data?.form?.append('formDetails', FormDetailsJSON);
    formDataTemp.append('file', data?.form?.get('file') || '');
    formDataTemp.append('imageDetails', data?.form?.get('imageDetails') || '');
    formDataTemp.append('formDetails', FormDetailsJSON);
    if (clientNotExist) {
      edit
        ? dispatch(
            editClientFormDetails({
              // data.form,
              formDetails: formDataTemp,
              clientId: selected.clientId,
              userId: userId,
            })
          )
        : dispatch(
            addClientFormDetails({ userId: userId, formData: formDataTemp })
          );
      setShowModal(false);
    }

    //To remove copy of image upload
    data?.form?.set('file', '');
    data?.form?.set('imageDetails', '');
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.value = '';
          element.disableFlag = 'F';
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setSelected(obj);
    setEdit(true);
    setShowImage(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === 'datePicker') {
            entry.disableFlag = 'T';
          } else {
            entry.disableFlag = 'F';
          }

          entry.uniqueKey === 'companyName'
            ? (entry.value = obj.companyName)
            : '';
          entry.uniqueKey === 'email' ? (entry.value = obj.email) : '';
          entry.uniqueKey === 'address' ? (entry.value = obj.address) : '';
          entry.uniqueKey === 'datePicker'
            ? (entry.value = obj.inactiveOn)
            : '';
          entry.uniqueKey === 'logoReference'
            ? (entry.value = obj.logoReferId)
            : '';
          entry.uniqueKey === 'hpUrl' ? (entry.value = obj.clientUrl) : '';
          entry.uniqueKey === 'suppPortalUrl'
            ? (entry.value = obj.supplierUrl)
            : '';
          if (entry.uniqueKey === 'checkbox') {
            if (obj.active === 'Y') {
              entry.value = 'Y';
              // obj.active = 'Y';
              setShow(false);
            } else setShow(true);
          }
          entry.uniqueKey === 'clientImage'
            ? setUrl(AP_file_url + obj.clientLogoDtlsDto.filePath)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.clientId,
      'clientId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.clientId
      ? handleConfirm(obj.clientId)
      : funcToSetResponseMessage('error', 'Please Select a Client to Delete');
  };

  //Function to confirm delete
  const handleConfirm = async (clientId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteClientFormDetails({ clientId: clientId, userId: userId }));
      setShowModal(false);
      setDisable(false);
      setSelected({});
    }
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey, element) => {
    let tempArray = cloneDeep(formDetails);
    let data = formValidationUtil.isFormcontrolValidDynamic(event, element);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element, elementIndex) => {
          if (element.uniqueKey === uniqueKey) {
            entry.formSubDetailsInternalDTOList.splice(elementIndex, 1, data);
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Function to manage the checkbox field
  const handleCheckBox = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event.target.checked ? 'Y' : '';
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Function to select the date field
  const handleDate = (date, uniqueKey) => {
    //event is formatted date string
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = date;
          }
        });
    });
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleCheckBox,
    handleDate,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelected(obj);
    setDisable(true);
    setShowImage(true);

    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'T';
          entry.uniqueKey === 'companyName'
            ? (entry.value = obj.companyName)
            : '';
          entry.uniqueKey === 'email' ? (entry.value = obj.email) : '';
          entry.uniqueKey === 'address' ? (entry.value = obj.address) : '';
          entry.uniqueKey === 'datePicker'
            ? (entry.value = obj.inactiveOn)
            : '';
          entry.uniqueKey === 'logoReference'
            ? (entry.value = obj.logoReferId)
            : '';
          entry.uniqueKey === 'hpUrl' ? (entry.value = obj.clientUrl) : '';
          entry.uniqueKey === 'suppPortalUrl'
            ? (entry.value = obj.supplierUrl)
            : '';
          if (entry.uniqueKey === 'checkbox') {
            if (obj.active === '✔') {
              entry.value = 'Y';
              obj.active = 'Y';
              setShow(false);
            } else setShow(true);
          }
          entry.uniqueKey === 'clientImage'
            ? setUrl(AP_file_url + obj.clientLogoDtlsDto.filePath)
            : '';
        });
    });

    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.clientId,
      'clientId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  const handleFilterData = (filterData) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) =>
      element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
    );
    setFormDetails(tempArray);
  };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data?.map((element, index) => {
          if (element.uniqueKey === 'pageSubtitle') {
            while (showModal) {
              let len = Math.ceil(
                element.formSubDetailsInternalDTOList.length / 2
              );
              return (
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                  key={index}
                >
                  <div
                    onClick={() => {
                      setEdit(false);
                      handleCancel();
                      setSelected({});
                      setConditionalRowStyles([]);
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div className="boxed" style={{ margin: 0 }}>
                    <Input key={index} formType={element} />
                    <div className="two-col-layout">
                      <div className="col">
                        {formControlsBinding(
                          element.formSubDetailsInternalDTOList.slice(0, len)
                        )}
                      </div>
                      <div className="col">
                        {formControlsBinding(
                          element.formSubDetailsInternalDTOList.slice(len)
                        )}
                      </div>
                    </div>{' '}
                    {showButtons && (
                      <div>
                        <div style={{ marginBottom: 10 + 'px' }}></div>

                        <Button
                          onClick={() => handleSave()}
                          className="default mr20"
                        >
                          Save
                        </Button>

                        <Button
                          onClick={() => handleCancel()}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-bar',
                        'type-' + notify.type,
                      ].join(' ')}
                      style={{
                        position: 'sticky !important',
                        width: 90 + '%',
                        bottom: 0 + 'px',
                      }}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val, idx) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : val.selector === 'active'
                      ? '5%'
                      : val.selector === 'address'
                      ? '30%'
                      : '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? (row) => (
                          <div
                            className="icon-edit-button"
                            onClick={() => handleEdit(row)}
                            key={idx}
                          ></div>
                        )
                      : val.selector === 'deleteIcon'
                      ? (row) => (
                          <div
                            className="icon-2-trash"
                            onClick={() => handleDelete(row)}
                            key={idx}
                          ></div>
                        )
                      : val.selector === 'active'
                      ? (row) => (
                          <span>{row.active === 'Y' ? '✔' : row.active}</span>
                        ) // ✅ Show "✔" instead of "Y" here
                      : '',
                };
              });

            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                // dataTableData={element.value} // Use original value, modify only in cell rendering
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else if (element.uniqueKey === 'search') {
            return;
            // return (
            //   <div
            //     style={{ display: 'inline-block', width: '200px' }}
            //     key={index}
            //   >
            //     <Input
            //       key={index}
            //       formType={element}
            //       isEditable="notShowing"
            //       tableListData={tableData}
            //       getFilteredData={(filterData) => handleFilterData(filterData)}
            //     />
            //   </div>
            // );
          } else if (element.uniqueKey === 'add_button') {
            return (
              <div
                style={{ float: 'right', marginBottom: 10 + 'px' }}
                key={index}
              >
                <Button
                  onClick={() => {
                    handleCancel();
                    setConditionalRowStyles([]);
                    setShowModal(true);
                    setShowButtons(true);
                    setEdit(false);
                    setShowImage(false);
                    setShow(false);
                  }}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-add-button "> </i>Add
                </Button>
              </div>
            );
          } else if (element.uniqueKey === 'upload_button') {
            return (
              showButtons && (
                <div
                  style={{ float: 'left', marginTop: 10 + 'px' }}
                  key={index}
                >
                  <a className="my-anchor-element">
                    <Button
                      className="small mb8 outline add-button-custom flex-row vam"
                      onClick={() => setUpload(!upload)}
                    >
                      <i className="icon-add-button "> </i>Upload Client Logo
                      <Tooltip
                        anchorSelect=".my-anchor-element"
                        place="Right Top"
                        style={{
                          width: '220px',
                          height: '70px',
                          marginLeft: '175px',
                          backgroundColor: 'rgb(132 198 83)',
                        }}
                      >
                        <b>
                          Accepted Formats: JPG, PNG, JPEG <br></br>
                          <br></br>Size should be 140 x 50 or below<br></br>
                          <br></br>Memory Size Max: 20KB
                        </b>
                      </Tooltip>
                    </Button>
                  </a>
                </div>
              )
            );
          } else if (element.uniqueKey === 'clientImage') {
            return (
              showImage && (
                <div className="header-client-logo-wrap mb20" key={index}>
                  {!url.includes('null') ? (
                    <img
                      style={{ height: '40px', width: 'auto' }}
                      // src={url ? url : sharedAsset.Login.companyLogo}
                      src={url ? url : ''}
                    />
                  ) : (
                    ''
                  )}

                  {/* <img
                    src={sharedAsset.userAsset.userLogo}
                    alt="Use Props to your content"
                  /> */}
                </div>
              )
            );
          } else if (element.uniqueKey === 'datePicker') {
            return show ? (
              <Input
                key={index}
                formType={element}
                disabledFlag={disable}
                value={'Y'}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            ) : (
              ''
            );
          } else {
            return (
              <Input
                key={index}
                formType={element}
                disabledFlag={disable}
                value={'Y'}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            );
          }
        })
      : null;
  };

  return (
    <>
      <div className="">
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
      {upload && (
        <Imageupload
          onCancel={() => setUpload(false)}
          imgSize={imgSize}
          labels={{
            headLbl: 'Drag Images or Click to upload',
            formatLbl: 'Accept format: JPG,PNG,JPEG',
            sizeLbl: 'Size: Max 20kb',
          }}
          errorMesg={mesg}
          isShow={upload}
          clientId={selected.clientId}
          imageDetailsDTO={(formData) => setData({ form: formData })}
        />
      )}
    </>
  );
};
export { Client };
