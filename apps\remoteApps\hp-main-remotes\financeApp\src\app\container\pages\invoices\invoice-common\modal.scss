@import '@hp/styles/variables.scss';
@import '@hp/styles/reset.scss';

.modal-close {
  top: 10px;
  right: 10px;
  font-size: 21px;
  cursor: pointer;
  color: $text_color;
  position: absolute;

  &:hover {
    color: $primary_color;
  }
}
.modal-close-sticky {
  position: sticky;
  top: 0px;
  background-color: white;
  z-index: 10;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: flex-end;
}
.modal-close.rt {
  top: -30px;
  right: -30px;
}
.modal-close-fixed {
  position: sticky;
  top: -30px;
  right: -30px;
  z-index: 1000;
  height: 0;
  width: 100%; /* Take full width */
  display: flex;
  justify-content: flex-end; /* Push content to right */
  padding: 10px 10px 0 0; /* Add spacing */
  box-sizing: border-box;
}
.Modal {
  top: 40px;
  left: 50%;
  width: 100%;
  bottom: 40px;
  outline: none;
  padding: 40px;
  overflow: auto;
  max-width: 1000px;
  background: #fff;
  border-radius: 4px;
  position: absolute;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}
.Modal-compareBox {
  top: 40px;
  left: 50%;
  width: 100%;
  bottom: 40px;
  outline: none;
  padding: 40px;
  overflow: auto;
  max-width: 1600px;
  background: #fff;
  border-radius: 4px;
  position: absolute;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}

.ModalOverlay {
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 9999;
  position: fixed;
  background-color: rgba(black, 0.75);
}

.ItemListModal {
  top: 40px;
  left: 50%;
  min-height: 40%;
  max-height: 75%;
  width: 100%;
  // bottom: 40px;
  outline: none;
  padding: 40px;
  overflow: auto;
  max-width: 720px;
  position: absolute;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}

.RejectModal {
  left: 50%;
  top: 150px;
  width: 100%;
  bottom: 150px;
  outline: none;
  padding: 40px;
  overflow: auto;
  max-width: 720px;
  position: absolute;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}

.ModalPoMatrix {
  top: 40px;
  left: 50%;
  width: 100%;
  bottom: 40px;
  outline: none;
  padding: 40px;
  overflow: auto;
  max-width: 850px;
  position: absolute;
  border-radius: 4px;
  background: #fff;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}

.ModelFullScreen {
  top: 1px;
  left: 50%;
  width: 100%;
  bottom: 40px;
  outline: none;
  padding: 20px 40px 40px 40px;
  overflow: auto;
  border-radius: 4px;
  position: absolute;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
  max-width: 90%;
  border: 0px solid transparent;
  overflow-y: hidden;
  overflow-x: hidden;
}
.modal-closeTop-1 {
  top: 5px;
  right: 10px;
  font-size: 20px;
  cursor: pointer;
  color: $text_color;
  position: absolute;

  &:hover {
    color: $primary_color;
  }
}

.model-small {
  top: 25%;
  left: 50%;
  width: 40%;
  height: 35%;
  bottom: 40px;
  outline: none;
  padding: 40px;
  max-width: 1000px;
  background: #fff;
  border-radius: 4px;
  position: absolute;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}
.Modal-content {
  position: relative;
  top: 40px;
  height: 90%;
  width: 62%;
  left: 30%;
  transform: translateX(-50%);
  border: 1px solid rgb(149, 50, 50);
  background: #e6e6e6;
  overflow: auto;
  border-radius: 4px;
  outline: none;
  padding: 40px 40px 20px 40px;

  display: flex;
  flex-flow: column;
  justify-content: flex-start;

  .content-button {
    margin-top: auto;
  }
}
