import React, { useEffect, useState } from 'react';
import { Row } from 'antd';

import { CustomDescriptions } from '@hp/components';

function InvPageHeader(props) {
  const { invDetails } = props;

  const [headerItems, setHeaderItems] = useState([]);

  useEffect(() => {
    if (invDetails?.formDetailsDtoList?.length) {
      let filteredList = invDetails?.formDetailsDtoList.filter(
        (data) => data?.otherClasses === 'showInHeaderCard'
      );
      let headerItemList = filteredList.map((data, index) => {
        return {
          key: index,
          label: data?.displayName,
          children:
            typeof data?.value == 'object'
              ? typeof data?.value?.value == 'string'
                ? data?.value?.value
                : ''
              : data?.value,
        };
      });
      setHeaderItems(headerItemList);
    }
  }, [invDetails]);

  /* Make this menu sticky on scroll */
  useEffect(() => {
    const header = document.getElementById('Invoice-Page-Header');
    const scrollCallBack = window.addEventListener('scroll', () => {
      if (window.scrollY > 60) {
        header.style.position = 'fixed'; // Ensure it's fixed
        header.style.top = '111px'; // Move it down slightly
        header.style.width = 'calc(100% - 287px)';
        header.style.zIndex = '1000'; // Ensure it's above other elements
        // header.style.backgroundColor = "pink"; // Prevent content behind showing through
      } else {
        header.style.position = 'static'; // Reset when scrolling back up
        header.style.width = '100%';
      }
    });
    return () => {
      window.removeEventListener('scroll', scrollCallBack);
    };
  }, []);

  return (
    <div id="Invoice-Page-Header">
      <Row className="inv-page-header">
        <CustomDescriptions items={headerItems} />
      </Row>
    </div>
  );
}

export { InvPageHeader };
