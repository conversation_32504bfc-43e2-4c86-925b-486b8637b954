import { Descriptions, Row, Col } from 'antd';
import { useEffect } from 'react';

import './ecr_eco_common.scss';
function ECRECOHeader({ listValues, noOfFields }) {
  // const history = useHistory();
  // const maxStringLength = noOfFields == 3 ? 75 : 25;
  // const [titleHover, setTitleHover] = useState(false);

  /* Make this menu sticky on scroll */
  useEffect(() => {
    const header = document.getElementById('ecr-eco-header');
    const scrollCallBack = window.addEventListener('scroll', () => {
      if (window.scrollY > 25) {
        header.style.top = '114px';
      } else {
        header.style.top = '80px';
      }
    });
    return () => {
      window.removeEventListener('scroll', scrollCallBack);
    };
  }, []);

  // let dimHeader = false;
  const ListItems = listValues.map((item, index) => {
    return {
      key: String(index + 1),
      label: (
        <span
          style={{
            fontWeight: '700',
            fontSize: '10px',
            color: '#000', // Set desired label color
            marginTop: '5px',
            opacity: '0.4',
          }}
        >
          {item.label}
        </span>
      ),
      children: (
        <div
          className="ecr-eco-header-item"
          // style={{
          //   whiteSpace: 'nowrap',
          //   overflow: 'hidden',
          //   textOverflow: 'ellipsis',
          //   width: '100%',
          //   fontWeight: '400',
          //   fontSize: '14px',
          //   color: 'rgb(0, 0, 0)',
          // }}
          // className={!item.value ? 'disable-ecr-eco-div' : ''}
          dangerouslySetInnerHTML={{
            __html: item.value,
            // item.value?.length > maxStringLength && !titleHover
            //   ? item.value.slice(0, maxStringLength) + '...'
            //   : item.value,
          }}
          // onMouseEnter={() =>
          //   item.value?.length > maxStringLength ? setTitleHover(true) : ''
          // }
          // onMouseLeave={() => (titleHover ? setTitleHover(false) : '')}
        />
      ),
    };
  });

  // const headerRedirect = () => {
  //   history.push({
  //     pathname: ``,
  //   });
  // };

  return (
    <Row
      id="ecr-eco-header"
      className="ecr-eco-header"
      style={
        {
          // top: window.scrollY > 55 ? '50px' : '80px',
          // backgroundColor: dimHeader ? '#F0F0F0' : '#fff',
        }
      }
    >
      <Col
      // className={`ecr-eco-header-height-control ${
      //   titleHover ? 'long' : 'short'
      // }`}
      >
        <Descriptions
          size="middle"
          layout="horizontal"
          column={noOfFields}
          // column={{
          //   xs: 4,
          //   sm: 4,
          //   md: 4,
          //   lg: 4,
          //   xl: 4,
          //   xxl: 4,
          // }}
          items={ListItems}
          // Custom CSS class for Descriptions component
          className="custom-descriptions"
          style={{
            fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
          }}
        />
      </Col>
    </Row>
  );
}

export default ECRECOHeader;
