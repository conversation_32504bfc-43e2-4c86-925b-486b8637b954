import { DocumentViewer } from '@hp/components';
import React from 'react';
import { Scrollbars } from 'react-custom-scrollbars';

const ApEmailOutboxBody = (props) => {
  return (
    <div className="email-body">
      {props.subject ? (
        <div className="email-body-header">
          <h2>
            <span>Sub:</span> {props.subject}
          </h2>
        </div>
      ) : null}
      <div className="email-compose-header">
        <div className="compose-header-content">
          <div className="title">From :</div>
          {props.fromName.trim() === '' ? (
            <p>
              <b>{props.fromEmail}</b>
            </p>
          ) : (
            <p>
              <b>{props.fromName}</b> ({props.fromEmail})
            </p>
          )}
        </div>
        <div className="compose-header-content">
          <div className="title">To :</div>
          {props.toName.trim() === '' ? (
            <p>
              <b>{props.toEmail}</b>
            </p>
          ) : (
            <p>
              <b>{props.toName}</b> ({props.toEmail})
            </p>
          )}
        </div>
      </div>
      <Scrollbars className="email-content">
        <DocumentViewer
          fileURL={props.emailBody}
          fileType={'iframe'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
        />
      </Scrollbars>
    </div>
  );
};

export { ApEmailOutboxBody };
