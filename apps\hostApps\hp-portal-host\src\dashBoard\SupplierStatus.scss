@import '@hp/styles/variables.scss';

.onboarding-container {
  max-width: $max_width;
}

.onboarding-checklist {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  li {
    display: flex;
    a {
      width: 100%;
      display: flex;
      padding: 16px;
      transition: 500ms;
      border-radius: 4px;
      align-items: center;
      background-color: #fff;
      color: rgba($text_color_rgb, 1);
      border: 1px solid #c5c5d3;
      filter: drop-shadow(0px 6px 16px rgba(24, 39, 75, 0.12))
        drop-shadow(0px 8px 16px rgba(24, 39, 75, 0.04));
      &:hover {
        transition: 500ms;
        border-color: $primary_color;
      }
    }
    .profile-status-url {
      font-weight: 300;
      font-size: 18px;
      display: block;
      margin-bottom: 4px;
    }
    p {
      font-size: 14px;
      color: rgba($text_color_rgb, 0.75);
    }

    .icon-error,
    .icon-new-checkbox {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      align-items: center;
      justify-content: center;
      display: flex;
      color: #fff;
      font-size: 13px;
      margin-right: 16px;
      flex: 0 0 auto;
    }
    .icon-new-checkbox {
      background: #7dc34b;
    }
    .icon-error {
      background: $error_color;
    }
  }
}
