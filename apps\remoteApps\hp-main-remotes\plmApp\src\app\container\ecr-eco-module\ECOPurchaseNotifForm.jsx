/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-key */
import { Input, useConfirm } from '@hp/components';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { saveECOPurchaseNotificationFormDetails } from '@hp/mainstore';
import './ecr_eco_common.scss';
const ECOPurchaseNotifForm = ({ ecoId, setSaveButtonFunction, ...props }) => {
  const [ecoPurchaseForm, setEcoPurchaseForm] = useState([]);

  const dispatch = useDispatch();
  const { ecoPurchaseNotifForm } = useSelector((store) => store.pdm);
  const { confirm } = useConfirm();

  const handleSaveButtonFunction = async () => {
    const isConfirmed = await confirm('Are you sure you want to continue?');
    if (isConfirmed) {
      dispatch(
        saveECOPurchaseNotificationFormDetails({
          ecoId: ecoId,
          form: ecoPurchaseForm,
        })
      );
    }
  };

  useEffect(() => {
    setSaveButtonFunction(handleSaveButtonFunction);
  }, [setSaveButtonFunction]);

  useEffect(() => {
    if (ecoPurchaseNotifForm?.value) {
      setEcoPurchaseForm(ecoPurchaseNotifForm.value);
    }
  }, [ecoPurchaseNotifForm]);

  const handleOnChange = (event, uniqueKey, element) => {
    let tempForm = [...ecoPurchaseForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'changeInformation') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event?.target?.value ?? event;
          }
        });
      }
    });
    setEcoPurchaseForm(tempForm);
  };

  const handleDateChange = (event, uniqueKey, element) => {
    let tempForm = [...ecoPurchaseForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'changeInformation') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
          }
        });
      }
    });
    setEcoPurchaseForm(tempForm);
  };

  const handleAdvancedSelect = (event, uniqueKey, element) => {
    let tempForm = [...ecoPurchaseForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'changeInformation') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
          }
        });
      }
    });
    setEcoPurchaseForm(tempForm);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleDateChange,
    handleAdvancedSelect,
  };

  const handleFormData = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.type === 'PageTitle') {
            return (
              <div>
                <Input key={index} formType={element} />
                <div className="three-col-layout">
                  {element.formSubDetailsInternalDTOList.map(
                    (entry, subIndex) => {
                      return (
                        <Input
                          key={subIndex}
                          formType={entry}
                          isEditable="notShowing"
                          disabledFlag={true}
                          // disabledFlag={entry.uniqueKey !== 'descOfChange'}
                          onChangeHandler={(element, event) => {
                            onChangeHandlingFunctions[element.onChangeFunction](
                              event,
                              element.uniqueKey,
                              element
                            );
                          }}
                        />
                      );
                    }
                  )}
                </div>
              </div>
            );
          }
        })
      : '';
  };

  return (
    <div className="card mb20">
      {ecoPurchaseForm?.length ? handleFormData(ecoPurchaseForm) : ''}
    </div>
  );
};

export { ECOPurchaseNotifForm };
