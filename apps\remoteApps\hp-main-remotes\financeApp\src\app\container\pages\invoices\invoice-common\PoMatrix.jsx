/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-no-useless-fragment */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:12:13
 * @modify date 2024-04-30 15:52:45
 * @desc [description]
 */
import React, { useCallback, useEffect, useState } from 'react';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import { PoDataTable } from '../invoice-sub/PO-DataTable';
import Scrollbars from 'react-custom-scrollbars';
import { Input } from '@hp/components';
import { getPoMatrixs } from '@hp/mainstore';
import { Tooltip } from 'antd';
const PoMatrix = (props) => {
  const modalToOpen = props.isModalopen ? props.isModalopen : false;
  const dispatch = useDispatch();
  const [getpoMatrix, setPoMatrix] = useState();
  const [balanceAmt, setBalanceAmt] = useState();
  const [currency, setCurrency] = useState();
  const [subTotalBalanceAmt, setSubTotalBalanceAmt] = useState();
  const [taxAmtBalance, setTaxAmtBalance] = useState();

  const { combinedInvSupplier, poMatrixClassName } = props;
  useEffect(() => {
    if (modalToOpen) {
      if (props.poID && props.invID !== undefined) {
        let poId = props.poID;
        let invId = props.invID;
        getPoMatrix(poId, invId);
      } else if (props.invID === null) {
        let poId = props.poID;
        getPoMatrix(poId, null);
      }
    }
  }, [modalToOpen]);

  const getPoMatrix = async (poId, invId) => {
    dispatch(getPoMatrixs({ poId: poId, invId: invId }));
  };

  const { poMatrix } = useSelector((store) => store.accpay);

  useEffect(() => {
    if (poMatrix && poMatrix.value) {
      setPoMatrix(poMatrix.value.formDetailsDtoList);
      setBalanceAmt(poMatrix.value.balanceAmount);
      setSubTotalBalanceAmt(poMatrix.value.subTotalAmount);
      setTaxAmtBalance(poMatrix.value.taxAmount);
      setCurrency(poMatrix.value.currency);
    }
  }, [poMatrix]);

  const DisplayTitle = (row, key) => {
    return (
      <Tooltip classNames={{ root: 'ant-tooltip-container' }} title={row[key]}>
        <div
          className="display-title"
          style={{
            overflow: 'hidden',
            whiteSpace: 'nowrap',
          }}
        >
          {row[key]}
        </div>
      </Tooltip>
    );
  };

  const formSingleControlsBinding = (data) => {
    return (
      <div>
        <div className="">
          <Input
            formType={data}
            style={{ fontFamily: 'Roboto' }}
            dataTableColumn={
              data.formTableListDetailsDtoList &&
              Array.isArray(data.formTableListDetailsDtoList) &&
              data.formTableListDetailsDtoList.length
                ? data.formTableListDetailsDtoList.map((value) => {
                    return {
                      width: value.displayWidth ? value.displayWidth : '',
                      name: value.displayName ? value.displayName : '',
                      selector: value.selector ? value.selector : '',
                      cell:
                        value.selector === 'refreshIcon'
                          ? function displayCell(row) {
                              return <div className="icon-refresh"></div>;
                            }
                          : (row) => DisplayTitle(row, value.selector),
                    };
                  })
                : []
            }
          />
        </div>
      </div>
    );
  };

  const PoItems = useCallback(() => {
    return (
      <div className="inv-div">
        <div className="force-full-width  ">
          <div className="single-table-wrap ">
            {getpoMatrix && getpoMatrix.length
              ? getpoMatrix?.map((element, index) => {
                  if (element.uniqueKey === 'itemList')
                    return (
                      <PoDataTable
                        poData={[element]}
                        disableTableActions={true}
                      />
                    );
                })
              : null}
          </div>
        </div>
      </div>
    );
  }, [getpoMatrix]);

  return (
    <>
      {combinedInvSupplier ? (
        <div
          className={[
            'boxed ',
            poMatrixClassName ? poMatrixClassName : '',
          ].join('')}
        >
          <h2 className="page-title"> Balance Details</h2>
          <p className="mb20 ">Balance available in PO : {balanceAmt}</p>

          <Scrollbars
            autoHeight
            autoHeightMax={929}
            autoHeightMin={929}
            className="mb20"
          >
            <div className="form-and-image " style={{ marginRight: '15px' }}>
              {/* <div className="compare-layout-mask mb16"> */}

              {getpoMatrix && getpoMatrix.length
                ? getpoMatrix.map((element, index) => {
                    if (element.uniqueKey !== 'itemList')
                      return (
                        <>
                          {element.displayName === 'poMatrixListing' ? (
                            <div key={index}>
                              <h2 className="page-title">
                                Previous Invoice History{' '}
                              </h2>
                              {formSingleControlsBinding(element)}
                            </div>
                          ) : (
                            <div>
                              <h2 className="page-title">
                                PO Balance Details:{' '}
                              </h2>
                              {formSingleControlsBinding(element)}
                            </div>
                          )}
                        </>
                      );
                  })
                : null}
            </div>
          </Scrollbars>
          {props.hideTable ? (
            ''
          ) : (
            <div style={{ marginTop: '40px' }}>
              <PoItems />
            </div>
          )}
        </div>
      ) : (
        <Modal
          className="ModalPoMatrix"
          overlayClassName="ModalOverlay"
          ariaHideApp={false}
          isOpen={props.isModalopen}
          style={{
            content: {
              padding: '0px',
            },
          }}
        >
          <div className="modal-close-sticky">
            <span
              onClick={props.onClick}
              className="modal-close icon-close"
            ></span>
          </div>

          <div
            className="ai-based-status-match"
            style={{ padding: '0px 40px' }}
          >
            {getpoMatrix && getpoMatrix.length
              ? getpoMatrix.map((element, index) => {
                  if (element.uniqueKey !== 'itemList')
                    return (
                      <>
                        {element.displayName === 'matrixItemListing' ? (
                          <div>
                            <div className="mb20" />
                            <h2 className="page-title">PO Item Balance </h2>
                            {formSingleControlsBinding(element)}
                            <div className="mb20" />

                            <h2 className="page-title"> PO Balance </h2>
                            <div
                              className="four-col-layout"
                              style={{
                                fontSize: '12px',
                              }}
                            >
                              <div>
                                <span
                                  style={{
                                    fontWeight: '600',
                                  }}
                                >
                                  Currency:
                                </span>
                                <span> {currency}</span>
                              </div>

                              <div>
                                <span
                                  style={{
                                    fontWeight: '600',
                                  }}
                                >
                                  Sub Total:
                                </span>
                                <span> {subTotalBalanceAmt}</span>
                              </div>
                              <div>
                                <span
                                  style={{
                                    fontWeight: '600',
                                  }}
                                >
                                  Tax Amount :
                                </span>
                                <span> {taxAmtBalance}</span>
                              </div>
                              <div>
                                <span
                                  style={{
                                    fontWeight: '600',
                                  }}
                                >
                                  Total Amount :
                                </span>
                                <span> {balanceAmt}</span>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div key={index}>
                            <h2 className="page-title">
                              Previous Invoice History{' '}
                            </h2>
                            {formSingleControlsBinding(element)}
                          </div>
                        )}
                      </>
                    );
                })
              : null}
            <PoItems />
          </div>
        </Modal>
      )}
    </>
  );
};
export { PoMatrix };
