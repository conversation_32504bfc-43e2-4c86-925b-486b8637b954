import { Button } from '@hp/components';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const UnmatchedExpandableTable = (props) => {
  const poId = props?.data?.po_id;
  const invId = props?.invId;
  const KeyValue = props.keyValue;
  const mainMenu = props.mainMenu ? props.mainMenu : '';
  const subMenu = props.subMenu ? props.subMenu : '';
  let navigate = useNavigate();

  const linkOnClick = () => {
    navigate(
      `/${mainMenu}/${subMenu}/APListing/INV,unmatched/unmatched/${KeyValue}/${invId}`,
      { linkClicked: true, poId: poId, invId: invId }
    );
  };
  return (
    <div
      className="flex-row mb16"
      style={{ paddingTop: '16px', paddingLeft: '50px' }}
    >
      <Button
        className="mr16 small secondary outline "
        onClick={() => linkOnClick()}
      >
        Link
      </Button>
    </div>
  );
};
export { UnmatchedExpandableTable };
