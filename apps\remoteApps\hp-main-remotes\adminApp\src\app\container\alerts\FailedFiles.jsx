/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 27-05-2021 15:28:32
 * @modify date 2022-10-27 16:15:58
 * @desc [description]
 */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';

import { CheckBoxInput } from '@hp/components';
import { ButtonCommon, dataTableServiceProvider, Input } from '@hp/components';
import Modal from 'react-modal';
import { AP_file_url } from '@hp/constants';
import {
  getFailedFilesFormDetails,
  showDialog,
  getFailedFileListByConnKey,
  getFailedFilesTableList,
  reprocessFiles,
  resetgetFailedFilesFormDetails,
  resetgetFailedFilesTableList,
  resetgetFailedFileListByConnKey,
  resetreprocessFiles,
} from '@hp/mainstore';
import { Tooltip } from 'antd';
import { globalutils } from '@hp/components';

const FailedFiles = () => {
  const dispatch = useDispatch();
  const {
    failedFilesFormDetails,
    failedFilesTableData,
    failedFilesListForSubTable,
    failedFilesReprocessed,
  } = useSelector((store) => store.admin);
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  const [formDetails, setFormDetails] = useState([]);
  const [clientId, setClientId] = useState(null);
  const [inOutValue, setInOutValue] = useState(null);
  const [moduleId, setModuleId] = useState(null);
  const [stackTrace, setStackTrace] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  // const [checkBoxFlag, setCheckBoxFlag] = useState(false); //dummy state to trigger rerender
  const [subTableForFileDetails, setSubTableForFilesDetails] = useState(false);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [fileIdList, setFileIdList] = useState([]);
  // const [conditionalRowStylesSubDatatable, setConditionalStylesSubDatatable] =
  //   useState([]);

  useEffect(() => {
    dispatch(getFailedFilesFormDetails());

    return () => {
      dispatch(resetgetFailedFilesFormDetails());
      dispatch(resetgetFailedFilesTableList());
      dispatch(resetgetFailedFileListByConnKey());
      dispatch(resetreprocessFiles());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (failedFilesFormDetails && failedFilesFormDetails.value) {
      setFormDetails(failedFilesFormDetails.value);
    }
  }, [failedFilesFormDetails]);

  useEffect(() => {
    if (failedFilesTableData?.value) {
      let tempArray = [...formDetails];
      tempArray.map((element) => {
        if (element.uniqueKey === 'datatable') {
          element.value = failedFilesTableData.value;
        }
        return element;
      });
      setFormDetails(tempArray);
    }
  }, [failedFilesTableData]);

  useEffect(() => {
    if (failedFilesListForSubTable?.value?.length > 0) {
      setSubTableForFilesDetails(true);
      let tempArray = [...formDetails];
      tempArray.map((element) => {
        if (element.uniqueKey === 'subDatatable') {
          element.value = failedFilesListForSubTable.value;
        }
        return element;
      });
      setFormDetails(tempArray);
    } else setSubTableForFilesDetails(false);
    setFileIdList([]);
    // else handleClearSubTableValues(formDetails);
  }, [failedFilesListForSubTable]);

  useEffect(() => {
    if (failedFilesReprocessed?.value) {
      // handleClearSubTableValues(formDetails);
      dispatch(
        getFailedFilesTableList({
          clientId: clientId,
          inOutValue: inOutValue,
          moduleId: moduleId,
        })
      );
      if (selectedRow) {
        dispatch(
          getFailedFileListByConnKey({
            userId: userId,
            moduleCode: 'AP',
            rowData: selectedRow,
          })
        );
        setSelectedRow(null);
      }
      funcToSetResponseMessage(
        failedFilesReprocessed.success,
        failedFilesReprocessed.value
      );
    }
  }, [failedFilesReprocessed]);

  useEffect(() => {
    if (clientId && inOutValue && moduleId) {
      dispatch(
        getFailedFilesTableList({
          clientId: clientId,
          inOutValue: inOutValue,
          moduleId: moduleId,
        })
      );
      setFileIdList([]);
      // handleClearSubTableValues(formDetails);
    }
  }, [clientId, inOutValue, moduleId]);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  // function handleClearSubTableFlags(data) {
  //   let tempArray = [...data];
  //   tempArray.map((element) => {
  //     if (element.uniqueKey === 'subDatatable') {
  //       element.value.map((item) => (item.value = false));
  //     }
  //     return element;
  //   });
  //   setFormDetails(tempArray);
  // }

  const handleRowClick = (row) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
    setSelectedRow(row);
    dispatch(
      getFailedFileListByConnKey({
        userId: userId,
        moduleCode: 'AP',
        rowData: row,
      })
    );
  };

  // const handleSubDatatableRowClick = (row) => {
  //   setFileName(row.fileAccessURL);
  //   const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
  //     row.serialNo,
  //     'serialNo'
  //   );
  //   setConditionalStylesSubDatatable(styleAttribute);
  //   setShowPreview(true);
  // };

  const handleCombo = (e, uniqueKey) => {
    const tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      if (element.uniqueKey === 'div') {
        element.formSubDetailsInternalDTOList.map((subElement) => {
          if (subElement.uniqueKey === uniqueKey) {
            if (subElement.type === 'AdvancedSelect') {
              subElement.value = e ?? null;
            } else subElement.value = e.target.value;
            if (subElement.uniqueKey === 'clientName') {
              setClientId(e ?? null);
            } else if (subElement.uniqueKey === 'in-out') {
              setInOutValue(e ?? null);
            } else if (subElement.uniqueKey === 'moduleName') {
              setModuleId(e ?? null);
            }
          }
        });
      }
    });
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleCombo,
  };

  const handleReprocessClick = () => {
    dispatch(reprocessFiles(fileIdList));
    setFileIdList([]);
    setConditionalStyles([]);
  };

  const functionsName = {
    handleReprocessClick,
  };

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'datatable') {
            let columns = element.formSubDetailsInternalDTOList.map((cols) => {
              return {
                width:
                  cols.selector === 'serialNo'
                    ? '6%'
                    : cols.width
                    ? cols.width
                    : '',
                name: cols.displayName,
                cell: function displayTitle(row) {
                  return (
                    <Tooltip
                      classNames={{ root: 'ant-tooltip-container' }}
                      title={row[cols.selector]}
                    >
                      <div className="display-title custom-overflow">
                        {row[cols.selector]}
                      </div>
                    </Tooltip>
                  );
                },
              };
            });
            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={handleRowClick}
                conditionalRowStyles={conditionalRowStyles}
                dataTableColumn={columns}
                //   onChangeHandler = {(onChangeFunction, e, uniqueKey) => onChangeHandlingFunctions[onChangeFunction](e, uniqueKey)}
              />
            );
          } else if (element.uniqueKey === 'div') {
            return (
              <div className={element.className}>
                {element.formSubDetailsInternalDTOList.map(
                  (subElement, index) => {
                    return (
                      <Input
                        indexKey={index}
                        formType={subElement}
                        isEditable="notShowing"
                        onChangeHandler={(element, event) =>
                          onChangeHandlingFunctions[element.onChangeFunction](
                            event,
                            element.uniqueKey
                          )
                        }
                      />
                    );
                  }
                )}
              </div>
            );
          } else if (element.uniqueKey === 'title')
            return <Input indexKey={index} formType={element} />;
          else if (
            element.uniqueKey === 'subDatatable' &&
            subTableForFileDetails
          ) {
            let columns = element.formSubDetailsInternalDTOList.map((cols) => {
              return {
                selector: cols.selector,
                width:
                  cols.selector === 'reason'
                    ? '70%'
                    : cols.selector === 'fileProcessId'
                    ? '10%'
                    : '5%',
                name: cols.displayName,
                cell:
                  cols.selector === 'checkboxInput'
                    ? function displayCell(rows) {
                        return (
                          // <CheckBoxInput
                          //   checked={rows.value}
                          //   onChange={(e) => {
                          //     const elementIndex = fileIdList.indexOf(
                          //       rows.fileProcessId
                          //     );
                          //     if (elementIndex === -1) {
                          //       setFileIdList([
                          //         ...fileIdList,
                          //         rows.fileProcessId,
                          //       ]);
                          //     } else {
                          //       const tempArray = fileIdList.filter(
                          //         (_, index) => index !== elementIndex
                          //       );
                          //       setFileIdList(tempArray);
                          //     }
                          //     rows.value = e.target.checked;
                          //     setCheckBoxFlag(!checkBoxFlag);
                          //   }}
                          // />
                          <CheckBoxInput
                            checked={fileIdList.includes(rows.fileProcessId)}
                            onChange={(e) => {
                              const isChecked = e.target.checked;

                              if (isChecked) {
                                setFileIdList([
                                  ...fileIdList,
                                  rows.fileProcessId,
                                ]);
                              } else {
                                setFileIdList(
                                  fileIdList.filter(
                                    (id) => id !== rows.fileProcessId
                                  )
                                );
                              }

                              // setCheckBoxFlag((prev) => !prev); // Optional toggle
                            }}
                          />
                        );
                      }
                    : cols.selector === 'fileAccessURL'
                    ? function displayCell(row) {
                        return (
                          <Tooltip
                            classNames={{ root: 'ant-tooltip-container' }}
                            title="Download"
                          >
                            <a
                              download
                              href={`${AP_file_url}${row.fileAccessURL}`}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <span
                                className="icon icon-cloud-download"
                                style={{ color: 'black', fontSize: '20px' }}
                              />
                            </a>
                          </Tooltip>
                        );
                      }
                    : cols.selector === 'stackTrace'
                    ? function displayCell(row) {
                        return row?.stackTrace ? (
                          <Tooltip
                            classNames={{ root: 'ant-tooltip-container' }}
                            title="Show Stack Trace"
                          >
                            <span
                              className="icon icon-eye"
                              style={{ fontSize: '15px' }}
                              onClick={() => {
                                setStackTrace(row.stackTrace);
                                setShowPreview(true);
                              }}
                            />
                          </Tooltip>
                        ) : (
                          ''
                        );
                      }
                    : function displayTitle(row) {
                        return (
                          <Tooltip
                            classNames={{ root: 'ant-tooltip-container' }}
                            title={row[cols.selector]}
                          >
                            <div className="display-title custom-overflow">
                              {row[cols.selector]}
                            </div>
                          </Tooltip>
                        );
                      },
              };
            });

            return (
              <Input
                indexKey={index}
                formType={element}
                // dataTableEventHandler={handleSubDatatableRowClick}
                dataTableColumn={columns}
                // conditionalRowStyles={conditionalRowStylesSubDatatable}
              />
            );
          }
        })
      : null;
  };

  return (
    <>
      <Modal
        className="Modal-for-status"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={showPreview && stackTrace ? true : false}
      >
        <div
          onClick={() => {
            setShowPreview(false);
            setStackTrace(null);
          }}
          className="modal-close icon-close mb16"
        ></div>

        <div className="col padding-20" style={{ height: 100 + '%' }}>
          <textarea
            cols="20"
            wrap="hard"
            disabled
            style={{
              margin: '20px 20px 20px 0px',
              width: '100%',
              height: '-webkit-fill-available',
              overflowY: 'auto',
            }}
          >
            {stackTrace}
          </textarea>
          {/* <DocumentViewer
            fileURL={fileName}
            fileType={'iframe'}
            zoom={'#zoom=50'}
            iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
          /> */}
        </div>
      </Modal>

      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}

      {subTableForFileDetails === true ? (
        <ButtonCommon functionsName={functionsName} />
      ) : null}
    </>
  );
};

export { FailedFiles };
