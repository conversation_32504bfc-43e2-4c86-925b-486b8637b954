/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useEffect, useRef } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import OktaSignIn from '@okta/okta-signin-widget';
import logo from '@hp/assets';
import config from './config';
import { AP_history } from '../ApBrowserHistory/history';

import { useDispatch } from 'react-redux';
import { login } from '@hp/mainstore';

const OktaLogin = () => {
  const { oktaAuth } = useOktaAuth();
  const widgetRef = useRef();
  const dispatch = useDispatch();

  useEffect(() => {
    if (!widgetRef.current) {
      return false;
    }
    const { oktaBaseUrl, clientId, redirectUri, scopes } = config.oidc;
    const widget = new OktaSignIn({
      baseUrl: oktaBaseUrl,
      clientId,
      redirectUri,
      logo,
      authParams: {
        issuer: config.oidc.issuer,
        scopes,
      },
    });

    widget?.renderEl(
      { el: widgetRef.current },
      (res) => {
        oktaAuth?.handleLoginRedirect(res?.tokens);
        let AP_historyCopy =
          AP_history?.location?.state === undefined
            ? null
            : AP_history?.location?.state?.from?.pathname;
        dispatch(
          login({
            username: '',
            password: '',
            AP_historyCopy: '/home',
            loginType: 'okta',
          })
        );
      },
      (err) => {
        throw err;
      }
    );

    const isCorsError = (err) => err.name === 'AuthApiError' && !err.statusCode;
    widget?.on('afterError', (_context, error) => {
      if (isCorsError(error)) {
        console.log(error);
      }
    });

    return () => widget?.remove();
  }, [oktaAuth]);

  return (
    <div>
      <div ref={widgetRef} />
    </div>
  );
};

export default OktaLogin;
