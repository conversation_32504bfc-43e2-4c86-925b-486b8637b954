/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
import {
  Button<PERSON>ommon,
  CommonSpinner,
  dataTableServiceProvider,
  formValidationUtil,
  Input,
  ProcessFlow,
  TraceEvents,
  useConfirm,
  CustomSpinner,
  NavBlocker,
} from '@hp/components';
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './ecr_eco_common.scss';
import {
  pdmConstants,
  viewOrEditECODetailsFormDetails,
  getECOSavedDocumentsDetails,
  showDialog,
  updateECODetailsFormDetails,
  saveECODetailsFormDetails,
  getECRListingForECO,
  getECOProcessFlowDetails,
  getECRECOTraceEventsDetails,
  viewOrEditECRDetailsFormDetails,
  getECODetailsFormFromECR,
  deleteECOAttachment,
  getECRSavedDocumentsDetails,
  getECRECOTraceEventsHistory,
  saveECOAttachments,
  getECODetailsFormDetails,
  getECRECODocumentCategories,
  resetsaveECODetailsFormDetails,
  resetECRECOTraceEventsDetails,
  resetsaveECOAttachments,
  resetupdateECODetailsFormDetails,
  resetdeleteECOAttachment,
  resetviewOrEditECRDetailsFormDetails,
  resetgetECOProcessFlowDetails,
  resetgetECRListingForECO,
} from '@hp/mainstore';
import { ecoFlowCreateData } from './ecr_eco_process_flow_data';
import { Tabs } from 'antd';
import ECRFromECRPopup from './ECRFromECRPopup';
import ECRECOHeader from './ECRECOHeader';
import { ECRECOTree } from './ECRECOTree';
import { getClientFormattedCurrentDate } from '@hp/components';
import ECRViewInECO from './ECRViewInECO';

import AffectedItems from './AffectedItems';
import { cloneDeep } from 'lodash';
import { useAppRouterDom } from '@hp/utils';
import ECRECOPartNoSection from './ECRECOPartNoSection';
import ECRECOApprovers from './ECRECOApprovers';
import { globalutils } from '@hp/components';
const ECODetailsPage = () => {
  const { domParameters, navigate, location } = useAppRouterDom();
  const [isDataChanged, setIsDataChanged] = useState(false);
  const [currentTab, setCurrentTab] = useState('1');
  const [ecoForm, setEcoForm] = useState([]);
  const [processFlowData, setProcessFlowData] = useState(ecoFlowCreateData);
  const [selectedPartNumbers, setSelectedPartNumbers] = useState([]);
  const [filteredPartNumber, setFilteredPartNumber] = useState(null);
  const { operation, ecoId, ecrId } = location.state || {};
  const [listValues, setListValues] = useState([
    { label: 'ECO #', value: null },
    { label: 'ECR #', value: null },
    { label: 'Title', value: null },
    { label: 'Requestor', value: null },
    { label: 'Originator', value: null },
  ]);
  const [fileDataList, setFileDataList] = useState(null);
  const [treeDataList, setTreeDataList] = useState(null);
  // const [affectedItems, setAffectedItems] = useState([]);
  // const [selectedPartNo, setSelectedPartNo] = useState(null);
  const [traceEvents, setTraceEvents] = useState([]);
  const [showButtons, setShowButtons] = useState(true);
  const [redirectTo, setRedirectTo] = useState(null);
  const [ecrListDetails, setEcrListDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [selectedECRId, setSelectedECRId] = useState(null);
  const [isECRViewDisabled, setIsECRViewDisabled] = useState(true);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [showApproverModal, setShowApproverModal] = useState(false);

  const state = location?.state || null;
  const isDraft = state?.operation === 'edit';

  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const {
    ecrEcoDocCategories,
    ecoDetailsForm,
    ecoDetailsFormRes,
    ecoDetailsSaveRes,
    ecoAttachmentDeleteRes,
    ecoSavedDocuments,
    ecoProcessFlowRes,
    ecrEcoTraceEvents,
    // ecrEcoPartNoFiltered,
    // ecoAffectedItemsRes,
    ecrListingForEco,
  } = useSelector((store) => store.pdm);
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;

  const dateToday = getClientFormattedCurrentDate();
  const ecrFromEcoPopupRef = useRef(null);
  const partNoRef = useRef(null);
  const documentsRef = useRef(null);

  useEffect(() => {
    if (isDraft) {
      if (ecoId) {
        dispatch(
          viewOrEditECODetailsFormDetails({
            ecoId: ecoId,
            userId: userId,
            clientId: clientId,
            method: 'Edit',
          })
        );
        dispatch(getECOSavedDocumentsDetails(ecoId));
        dispatch(
          getECOProcessFlowDetails({ ecoId: ecoId, clientId: clientId })
        );
        dispatch(
          getECRECOTraceEventsDetails({ tableName: 'ECO', tableId: ecoId })
        );
      }
      if (ecrId) {
        dispatch(
          viewOrEditECRDetailsFormDetails({
            ecrId: ecrId,
            userId: userId,
            clientId: clientId,
            method: 'View',
          })
        );
        dispatch(getECRSavedDocumentsDetails(ecrId));
      }
    }
    //  else if (state?.operation === 'create') {
    else {
      //   if (ecrId) {
      //     dispatch(pdmAction.getECODetailsFormFromECR(ecrId, userId, clientId));
      //     dispatch(
      //       pdmAction.viewOrEditECRDetailsFormDetails(
      //         ecrId,
      //         userId,
      //         clientId,
      //         'View'
      //       )
      //     );
      //     dispatch(pdmAction.getECRSavedDocumentsDetails(ecrId));
      //   }
      //   dispatch(pdmAction.getECRECODocumentCategories(userId));
      // } else {
      dispatch(getECODetailsFormDetails(userId));
      dispatch(getECRECODocumentCategories(userId));
    }
    return () => {
      dispatch(resetsaveECODetailsFormDetails());
      dispatch(resetsaveECOAttachments());
      dispatch(resetupdateECODetailsFormDetails());
      dispatch(resetdeleteECOAttachment());
      dispatch(resetviewOrEditECRDetailsFormDetails());
      dispatch(resetgetECOProcessFlowDetails());
      dispatch(resetECRECOTraceEventsDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (ecoDetailsForm?.value) {
      let tempForm = cloneDeep(ecoDetailsForm.value);
      tempForm.map((item) => {
        if (item.uniqueKey === 'createEco') {
          handleHeaderValues(item?.formSubDetailsInternalDTOList ?? []);
          if (!isDraft) {
            item.formSubDetailsInternalDTOList.map((entry) =>
              entry.uniqueKey === 'dateRequested'
                ? (entry.value = dateToday)
                : ''
            );
          }
        } else if (item.uniqueKey === 'partNoTable') {
          if (item?.value?.length) {
            let tempPartNoList = item.value.map((entry) => entry.value);
            setSelectedPartNumbers(tempPartNoList);
            tempForm.map((element) =>
              element.uniqueKey === 'affectedItems'
                ? element.formSubDetailsInternalDTOList.map((entry) => {
                    if (entry.uniqueKey === 'partNumberSelect')
                      entry.comboBoxOptions = item.value.map((entry) => ({
                        commonId: entry.value,
                        commonName: entry.partNo,
                      }));
                    return entry;
                  })
                : ''
            );
            item.value = tempPartNoList;
          } else setSelectedPartNumbers([]);
        }
      });

      setEcoForm(tempForm);
      setIsDataChanged(false);
      setIsLoading(false);
    }
  }, [ecoDetailsForm]);

  useEffect(() => {
    if (ecrListingForEco?.value) {
      setEcrListDetails(ecrListingForEco.value);
      setIsLoading(false);
      setShowModal(true);
      dispatch(resetgetECRListingForECO());
    }
  }, [ecrListingForEco]);

  useEffect(() => {
    if (ecoDetailsFormRes?.value) {
      setIsDataChanged(false);
      funcToSetResponseMessage(
        ecoDetailsFormRes.value?.type,
        ecoDetailsFormRes.value?.text
      );
      if (redirectTo) {
        const timeout = setTimeout(() => {
          const currentPath = window.location.pathname;
          const newPath = currentPath.replace(
            '/eco/create',
            `/eco/${redirectTo}`
          );
          navigate(newPath);
        }, 5000);
        return () => {
          clearTimeout(timeout);
        };
      } else if (ecoId) {
        dispatch(
          viewOrEditECODetailsFormDetails({
            ecoId: ecoId,
            userId: userId,
            clientId: clientId,
            method: 'Edit',
          })
        );
        dispatch(getECOSavedDocumentsDetails(ecoId));
        dispatch(
          getECOProcessFlowDetails({ ecoId: ecoId, clientId: clientId })
        );
        dispatch(
          getECRECOTraceEventsDetails({ tableName: 'ECO', tableId: ecoId })
        );
      }
    }
  }, [ecoDetailsFormRes]);

  useEffect(() => {
    if (ecoAttachmentDeleteRes?.value) {
      funcToSetResponseMessage(
        ecoAttachmentDeleteRes.value?.type,
        ecoAttachmentDeleteRes.value?.text
      );
    }
  }, [ecoAttachmentDeleteRes]);

  useEffect(() => {
    if (ecoDetailsSaveRes?.value?.ecrIdOrecoId) {
      let tempFileDataList = new FormData();
      tempFileDataList.append('file', null);
      if (fileDataList) tempFileDataList = fileDataList;
      dispatch(
        saveECOAttachments({
          ecoId: ecoDetailsSaveRes.value.ecrIdOrecoId,
          userId: userId,
          clientId: clientId,
          payload: tempFileDataList,
        })
      );
    }
  }, [ecoDetailsSaveRes]);

  useEffect(() => {
    if (ecrEcoDocCategories?.value) {
      const transformedData =
        ecrEcoDocCategories?.value.map((doc) => ({
          id: doc.documentsId,
          text: doc.documentName,
          parent: 0,
          hasChild: true,
          className: 'x',
          creationDate: null,
          curRevNum: null,
          detail: null,
          droppable: true,
          extension: null,
          fileName: null,
          filePath: null,
          fileTypeId: null,
          firstChild: true,
          itemCode: null,
          orderId: null,
          orginatedBy: null,
          partNumber: null,
          qty: 5,
          sibilingNext: true,
          uomId: 1,
          uploadedAt: null,
          uploadedBy: null,
          documentNumber: null,
          expriryDate: null,
        })) ?? [];
      setTreeDataList(transformedData);
    }
  }, [ecrEcoDocCategories]);

  useEffect(() => {
    if (ecoSavedDocuments?.value) {
      setTreeDataList(ecoSavedDocuments.value);
    }
  }, [ecoSavedDocuments]);

  // useEffect(() => {
  //   if (ecrEcoPartNoFiltered?.value) {
  //     let tempForm = [...ecoForm];
  //     tempForm.map((item) => {
  //       if (item.uniqueKey === 'createEco') {
  //         item.formSubDetailsInternalDTOList.map((entry) =>
  //           entry.uniqueKey === 'partNumber'
  //             ? (entry.comboBoxOptions = ecrEcoPartNoFiltered?.value)
  //             : ''
  //         );
  //       }
  //     });
  //     setEcoForm(tempForm);
  //   }
  // }, [ecrEcoPartNoFiltered]);

  // useEffect(() => {
  //   if (ecoAffectedItemsRes?.value) {
  //     setAffectedItems(ecoAffectedItemsRes?.value);
  //   }
  // }, [ecoAffectedItemsRes]);

  useEffect(() => {
    if (ecoProcessFlowRes?.value) {
      setProcessFlowData({
        nodes: ecoProcessFlowRes.value?.pdmProfileWorkflowRenderingDtos,
        edges: ecoProcessFlowRes.value?.pdmProfileWorkflowEdgeDtos,
      });
    }
  }, [ecoProcessFlowRes]);

  useEffect(() => {
    if (ecrEcoTraceEvents?.value) {
      setTraceEvents(ecrEcoTraceEvents.value);
    }
  }, [ecrEcoTraceEvents]);

  // useEffect(() => {
  //   if (searchString?.length > 0) {
  //     const timeout = setTimeout(() => {
  //       dispatch(pdmAction.getECRECOPartNoDetails(searchString));
  //     }, 500);
  //     return () => {
  //       clearTimeout(timeout);
  //     };
  //   }
  // }, [searchString]);

  function handleListApprovedECR() {
    if (!isDraft) {
      dispatch(getECRListingForECO(userId));
      setIsLoading(true);
    }
  }

  async function handleSubmit() {
    let validateForm = formValidationUtil.validateForm(ecoForm);
    if (validateForm.validSuccess) {
      const isConfirmed = await confirm('Are you sure you want to continue?');
      if (isConfirmed) {
        if (isDraft) {
          if (ecoId)
            dispatch(
              updateECODetailsFormDetails({
                ecoId: ecoId,
                userId: userId,
                clientId: clientId,
                status: 'pending_review',
                payload: ecoForm,
              })
            );
        } else
          dispatch(
            saveECODetailsFormDetails({
              userId: userId,
              clientId: clientId,
              ecrId: selectedECRId ?? ecrId,
              status: 'pending_review',
              payload: ecoForm,
            })
          );
        setRedirectTo('pending_review');
        setShowButtons(false);
      }
    } else {
      setEcoForm([...validateForm.formList]);
      if (currentTab !== '1')
        funcToSetResponseMessage('error', 'Please Enter Mandatory Fields');
    }
  }

  function isBlank(str) {
    return !str || /^\s*$/.test(str);
  }

  function handleDraft() {
    if (isDraft && !isDataChanged) {
      funcToSetResponseMessage('info', 'No Changes Detected');
      return;
    }
    let validateForm = { validSuccess: true, formList: null };
    validateForm.formList = cloneDeep(ecoForm);
    validateForm.formList.map((item) =>
      item.uniqueKey === 'createEco'
        ? item.formSubDetailsInternalDTOList.map((entry) =>
            entry.uniqueKey === 'changeAnalyst'
              ? isBlank(entry.value)
                ? ((entry.errorFlag = true),
                  (entry.valid = false),
                  (validateForm.validSuccess = false),
                  (entry.errorMessage = 'Enter Mandatory Fields'))
                : ''
              : ''
          )
        : ''
    );
    if (!validateForm.validSuccess) {
      funcToSetResponseMessage(
        'info',
        'Please Select Change Analyst to Save as Draft'
      );
      return;
    }

    if (isDraft) {
      if (ecoId)
        dispatch(
          updateECODetailsFormDetails({
            ecoId: ecoId,
            userId: userId,
            clientId: clientId,
            status: 'draft',
            payload: ecoForm,
          })
        );
    } else {
      dispatch(
        saveECODetailsFormDetails({
          userId: userId,
          clientId: clientId,
          ecrId: selectedECRId ?? ecrId,
          status: 'draft',
          payload: ecoForm,
        })
      );
      setRedirectTo('draft');
      setShowButtons(false);
    }
  }

  async function handleClear() {
    if (isDataChanged) {
      const isConfirmed = await confirm(
        'Are you sure you want to clear all the fields?'
      );
      if (isConfirmed) {
        handleClearConfirm();
        setIsDataChanged(false);
        setSelectedECRId(null);
      }
    } else handleClearConfirm();

    function handleClearConfirm() {
      let tempForm = [...ecoForm];
      tempForm.map((item) => {
        if (item.uniqueKey === 'createEco') {
          item.formSubDetailsInternalDTOList.map((entry) => {
            if (entry.uniqueKey !== 'dateRequested') {
              entry.value = null;
            }
          });
        } else if (item.uniqueKey === 'partNoTable') item.value = [];
        else if (item.uniqueKey === 'affectedItems') {
          item.formSubDetailsInternalDTOList.map((entry) => {
            if (entry.uniqueKey === 'partNumberSelect')
              entry.comboBoxOptions = [];
            return entry;
          });
        }
      });
      setSelectedPartNumbers([]);
      setEcoForm(tempForm);
      handlePartNoChange([], []);
      if (partNoRef.current) {
        partNoRef.current.handleClearPartNo();
      }
      if (documentsRef.current) {
        documentsRef.current.handleClearDocuments();
      }
      setFileDataList(null);
    }
  }

  async function handleCancel() {
    if (isDataChanged) {
      const isConfirmed = await confirm(
        'Changes made are not saved. Do you want to continue?'
      );
      if (isConfirmed) {
        navigate(-1);
      }
    } else {
      navigate(-1);
    }
  }

  const functionsName = {
    handleListApprovedECR,
    handleSubmit,
    handleDraft,
    handleClear,
    handleCancel,
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  const handleOnChange = (event, uniqueKey, element) => {
    setIsDataChanged(true);
    let tempForm = [...ecoForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'createEco') {
        item.formSubDetailsInternalDTOList.map((entry, elementIndex) => {
          if (entry.uniqueKey === uniqueKey) {
            let data = formValidationUtil.validateInputMaxLengthDynamic(
              event,
              element
            );
            item.formSubDetailsInternalDTOList.splice(elementIndex, 1, data);
          }
        });
      }
    });
    setEcoForm(tempForm);
  };

  const handleAdvancedSelect = (event, uniqueKey, element) => {
    setIsDataChanged(true);
    let tempForm = [...ecoForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'createEco') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
          }
        });
      } else if (item.uniqueKey === 'affectedItems') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
            setFilteredPartNumber(event);
          }
        });
      }
    });
    setEcoForm(tempForm);
  };

  const handleOnChangeMain = (event, uniqueKey, element) => {
    setIsDataChanged(true);
    let tempArray = [...ecoForm];
    tempArray.map((item) => {
      if (item.uniqueKey === uniqueKey) {
        item.value = event?.target?.value ?? event;
      }
    });
    setEcoForm(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleAdvancedSelect,
    handleOnChangeMain,
  };

  function handleUpdatedFileList(list) {
    const formData = new FormData();
    list.forEach((entry) => {
      if (entry.file)
        formData.append(
          `files[${entry.parentName}][${entry.index}]`,
          entry.file,
          entry.fileName
        );
    });
    setFileDataList(formData);
  }

  // const handlePartNoSearch = (filter, element) => {
  //   setSearchString(filter.trim());
  // };

  function handleRowClicked(row) {
    setSelectedECRId(row.ecrId);
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.ecrId,
      'ecrId'
    );
    setConditionalRowStyles(styleAttribute);
  }

  const handleFormDetails = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'approvedEcr') {
            return (
              <div className="inv-div">
                <Input
                  dataTablePersistHead
                  key={index}
                  formType={element}
                  dataTableEventHandler={(obj) => {
                    handleRowClicked(obj);
                  }}
                  conditionalRowStyles={conditionalRowStyles}
                  customTableStyles={{
                    table: {
                      style: {
                        contain: 'inline-size',
                      },
                    },
                  }}
                />
              </div>
            );
          }
          // else if (element.uniqueKey === 'partNumber')
          //   return (
          //     <Input
          //       key={index}
          //       formType={element}
          //       isEditable="notShowing"
          //       onChangeHandler={(element, event) => {
          //         onChangeHandlingFunctions[element.onChangeFunction](
          //           event,
          //           element.uniqueKey,
          //           element
          //         );
          //       }}
          //       onSearchHandler={(element, event) =>
          //         handlePartNoSearch(event, element)
          //       }
          //       getLabel
          //     />
          //   );
          else if (element.uniqueKey !== 'partNumber')
            return (
              <Input
                key={index}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
                minDate={
                  element.uniqueKey === 'completionDate' ? new Date() : ''
                }
              />
            );
        })
      : '';
  };

  const handleToggleIcon = (event) => {
    event.stopPropagation();
    setIsECRViewDisabled(!isECRViewDisabled);
  };

  const handlePartNoChange = (selectedPartNumbers, partNoOptions) => {
    setSelectedPartNumbers(selectedPartNumbers);
    setEcoForm((prev) =>
      prev.map((item) => {
        if (item.uniqueKey === 'partNoTable') item.value = selectedPartNumbers;
        else if (item.uniqueKey === 'affectedItems') {
          item.formSubDetailsInternalDTOList.map((entry) => {
            if (entry.uniqueKey === 'partNumberSelect')
              entry.comboBoxOptions = partNoOptions;
            return entry;
          });
        }
        return item;
      })
    );
    setIsDataChanged(true);
  };

  const handleTabs = (item) => {
    if (item.type === 'div') {
      if (item.uniqueKey === 'processFlow') {
        return (
          <div
            className="boxed mb20"
            style={{ maxWidth: '600px', maxHeight: '600px' }}
          >
            <h3 className="page-sub-title">Process Flow</h3>
            <div className="ecr-process-flow">
              <ProcessFlow dataFlowResponse={processFlowData} />
            </div>
          </div>
        );
      } else
        return item.uniqueKey === 'createEco' ? (
          <>
            <div className="card mb20">
              {/* <div
              style={{
                maxWidth: '370px',
              }}
            >
              <Input
                formType={
                  ecoForm?.find(
                    (entry) => entry.uniqueKey === 'changeOrder'
                  ) ?? { type: null }
                }
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey,
                    element
                  );
                }}
              />
            </div>
            <div className="page-sub-title"></div> */}
              <div className="three-col-layout">
                {handleFormDetails(item.formSubDetailsInternalDTOList)}
              </div>
            </div>
            <div className="card mb20">
              <ECRECOPartNoSection
                ref={partNoRef}
                parameter={isDraft ? 'draftEco' : 'createEco'}
                formData={ecoDetailsForm?.value ?? []}
                partNoChanged={(selectedPartNumbers, partNoOptions) =>
                  handlePartNoChange(selectedPartNumbers, partNoOptions)
                }
              />
            </div>
          </>
        ) : item.uniqueKey === 'relatedDocs' ? (
          <div className="mb20">
            <div className="ecr-eco-docs-container">
              {ecrId || selectedECRId ? (
                <div className="ecr-eco-view-icon">
                  <span
                    className={
                      isECRViewDisabled
                        ? 'icon icon-circle-up'
                        : 'icon icon-circle-down'
                    }
                    onClick={handleToggleIcon}
                  ></span>
                </div>
              ) : (
                ''
              )}
              <div
                className={
                  isECRViewDisabled
                    ? 'ecr-eco-section-show'
                    : 'ecr-eco-section-hide'
                }
              >
                {!isECRViewDisabled ? (
                  <div className="ecr-eco-minimized-title">
                    <div className="page-sub-title">New Documents</div>
                  </div>
                ) : (
                  ''
                )}
                <ECRECOTree
                  ref={documentsRef}
                  treeDataList={treeDataList}
                  parameter={isDraft ? 'draft' : 'newPrePr'}
                  customTitle={ecrId || selectedECRId ? 'New Documents' : null}
                  updatedFileList={(list) => {
                    handleUpdatedFileList(list);
                    setIsDataChanged(true);
                  }}
                  deleteAttachmentFile={(fileId) => {
                    dispatch(
                      deleteECOAttachment({
                        fileId: fileId,
                        userId: userId,
                        ecoId: ecoId,
                      })
                    );
                  }}
                />
              </div>
            </div>
            {ecrId || selectedECRId ? (
              <div className="ecr-eco-docs-container">
                <div className="ecr-eco-view-icon">
                  <span
                    className={
                      isECRViewDisabled
                        ? 'icon icon-circle-down '
                        : 'icon icon-circle-up'
                    }
                    onClick={handleToggleIcon}
                  ></span>
                </div>
                <div
                  className={
                    isECRViewDisabled
                      ? 'ecr-eco-section-hide'
                      : 'ecr-eco-section-show'
                  }
                >
                  {isECRViewDisabled ? (
                    <div className="ecr-eco-minimized-title">
                      <div className="page-sub-title">Related Documents</div>
                    </div>
                  ) : (
                    ''
                  )}
                  <ECRViewInECO />
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
        ) : item.uniqueKey === 'affectedItems' ? (
          <div className="card mb20">
            {handleFormDetails(item.formSubDetailsInternalDTOList)}
            <AffectedItems
              selectedPartNumbers={selectedPartNumbers}
              filteredPartNumber={filteredPartNumber}
            />
          </div>
        ) : (
          ''
        );
    }
  };

  function handleHeaderValues(formData) {
    let tempListValues = [...listValues];
    formData.forEach((item) => {
      switch (item.uniqueKey) {
        case 'ecoNo':
          tempListValues[0].value = item.value;
          break;
        case 'ecrNo':
          tempListValues[1].value = item.value;
          break;
        case 'title':
          tempListValues[2].value = item.value;
          break;
        case 'requestedBy':
          tempListValues[3].value = item.comboBoxOptions.find(
            (entry) => entry.commonId === item.value
          )?.commonName;
          break;
        case 'originator':
          tempListValues[4].value = item.comboBoxOptions.find(
            (entry) => entry.commonId === item.value
          )?.commonName;
          break;
      }
    });
    setListValues(tempListValues);
  }

  const handleCreateEcoFromEcr = () => {
    if (selectedECRId) {
      dispatch(
        getECODetailsFormFromECR({
          ecrId: selectedECRId,
          userId: userId,
          clientId: clientId,
        })
      );
      dispatch(
        viewOrEditECRDetailsFormDetails({
          ecrId: selectedECRId,
          userId: userId,
          clientId: clientId,
          method: 'View',
        })
      );
      dispatch(getECRSavedDocumentsDetails(selectedECRId));
      setShowModal(false);
      setIsLoading(true);
      setConditionalRowStyles([]);
    } else {
      if (ecrFromEcoPopupRef.current) {
        ecrFromEcoPopupRef.current.funcToSetResMessageInModal(
          'error',
          'Please Select an ECR'
        );
      }
    }
  };

  return (
    <>
      <NavBlocker
        isDataChanged={isDataChanged}
        isNavBlocked={(flag) => {
          // You can use this callback if needed
        }}
      />
      <ECRFromECRPopup
        ref={ecrFromEcoPopupRef}
        handleCreateEcoFromEcr={handleCreateEcoFromEcr}
        handleFormDetails={() => handleFormDetails(ecrListDetails)}
        showModal={showModal}
        setShowModal={(flag) => {
          setShowModal(flag);
          setSelectedECRId(null);
          setConditionalRowStyles([]);
        }}
      />
      {isDraft && ecoForm?.length ? (
        <>
          <div className="pull-right view-switcher">
            <div
              className="service-request-launcher fr"
              onClick={() => {
                setShowApproverModal(true);
              }}
            >
              <i className="icon-user-check mr8"></i>
              <label className="label">Approvers</label>
            </div>
          </div>
          <ECRECOApprovers
            userId={userId}
            ecoId={ecoId}
            modalOpen={showApproverModal}
            handleModalClose={(flag) => setShowApproverModal(flag)}
            approvalFormData={ecoForm.find(
              (entry) => entry.uniqueKey === 'approvalForm'
            )}
            handleApprovalChangeSave={() =>
              dispatch(
                viewOrEditECODetailsFormDetails({
                  ecoId: ecoId,
                  userId: userId,
                  clientId: clientId,
                  method: 'Edit',
                })
              )
            }
          />
        </>
      ) : (
        ''
      )}
      <ECRECOHeader
        listValues={isDraft ? listValues : listValues.slice(2)}
        noOfFields={isDraft ? 5 : 3}
      />
      <div className="ecr-eco-main-container ">
        {ecoForm?.length ? (
          <Tabs
            activeKey={currentTab}
            tabBarStyle={{
              fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
            }}
            items={ecoForm.map((item, index) => {
              if (item.type === 'div' && item.uniqueKey !== 'approvalForm') {
                if (item.uniqueKey === 'affectedItems')
                  return {
                    key: String(index + 1),
                    label: item?.displayName,
                    children: handleTabs(item),
                    destroyInactiveTabPane: true,
                  };
                else
                  return {
                    key: String(index + 1),
                    label: item?.displayName,
                    children: handleTabs(item),
                  };
              }
            })}
            onChange={(tabIndex) => {
              if (currentTab === '1')
                handleHeaderValues(
                  ecoForm.find((item) => item.uniqueKey === 'createEco')
                    ?.formSubDetailsInternalDTOList ?? []
                );
              setCurrentTab(tabIndex);
            }}
          />
        ) : (
          ''
        )}
      </div>

      {isDraft && traceEvents?.length ? (
        <div>
          <div className=" page-sub-title">Events</div>
          <div className="boxed mb20">
            <Tabs
              activeKey={'1'}
              items={[
                {
                  key: '1',
                  label: 'WorkFlow Events',
                  children: (
                    <TraceEvents
                      externalHistoryFunction
                      externalHistoryValue={'pdm'}
                      key="traceEventsEditor"
                      enableComments={false}
                      data={traceEvents}
                      userId={userId}
                      onEnterKeyPress={() => null}
                      sendButton={() => null}
                      handleHistoryClick={(eventId, editFunction) =>
                        dispatch(
                          getECRECOTraceEventsHistory({
                            editFunction: editFunction,
                            eventId: eventId,
                            userId: userId,
                          })
                        )
                      }
                    />
                  ),
                },
              ]}
            />
          </div>
        </div>
      ) : (
        ''
      )}
      <CommonSpinner visible={isLoading} />
      {showButtons ? <ButtonCommon functionsName={functionsName} /> : ''}
    </>
  );
};

export { ECODetailsPage };
