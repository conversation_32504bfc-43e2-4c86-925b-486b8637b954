/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable jsx-a11y/heading-has-content */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable @nx/enforce-module-boundaries */

import React, { useEffect, useState } from 'react';
import './finance_list.scss';
import { showDialog } from '@hp/mainstore';
import DataTable from 'react-data-table-component';
import { useDispatch, useSelector } from 'react-redux';
import { FinListingSubList } from '../finance-sub/FinListingSubList';
import { Ellipsis } from 'react-awesome-spinners';
import { Reconciliation } from './Reconciliation';
import ChangeSession from './ChangeSession';
import { Tabs, Tooltip } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import { useAppRouterDom } from '@hp/utils';
import {
  Button,
  TextInput,
  ButtonCommon,
  globalutils,
  TraceEvents,
  ServiceResponseComponent,
  useConfirm,
  dataTableServiceProvider,
  ServiceRequestLauncher,
} from '@hp/components';

import {
  getSessionChangeForm,
  getSessionDetailsAuthorize,
  getSessionDetailsTraceEvents,
  getStatmentSourceMasterForm,
  getStatmentOutwardMasterForm,
  getStatmentSourceMasterCount,
  getStatmentOutwardMasterCount,
  getPaymentOutwardMasterCount,
  getPaymentSourceMasterForm,
  getPaymentSourceMasterCount,
  getPaymentOutwardMasterForm,
  getSerReqLists,
  getServiceReqTypesCombo,
  rescheduledTranscationPayments,
  rescheduledTranscationStatements,
  financeResetStateField,
} from '@hp/mainstore';

const FinListing = () => {
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();
  const { buttons } = useSelector((state) => state.buttons || null);

  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user?.userId);
  const clientId = user?.clientId;
  const Parameter = domParameters?.parameters || '';
  const subMenu = domParameters?.submenu || '';
  const {
    paymentSourceMasterForm,
    paymentSourceMasterCount,
    paymentOutwardMasterCount,
    paymentOutwardMasterForm,
    statmentOutwardMasterCount,
    statmentOutwardMasterForm,
    statmentSourceMasterCount,
    statmentSourceMasterForm,
    updateSessionRes,
    traceEventRes,
    masterFormRes,
    rescheduledTranscationPaymentsRes,
    rescheduledTranscationStatementsRes,
    finListigMasterTableCount,
    finListLoading,
  } = useSelector((store) => store.finance);
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);
  const { serReqList, serviceReqTypes } = useSelector((store) => store.accpay);
  const [TableListinglength, setTableListlength] = useState(0);
  const [parameterValues, setParameterValues] = useState('');
  const [TableList, setTableList] = useState([]);
  const [TableHead, setTableHead] = useState([]);
  const [onRowClicked, setOnRowClick] = useState(false);
  const [dataOnRowClick, setDataOnRowClick] = useState(null);
  const [refreshToggle, setRefreshToggle] = useState(true);
  // const [subTableLength, setSubTableLength] = useState(0);
  const [allRowSelected, setAllRowSelected] = useState(false);
  const [serviceResponseModal, setServiceResponseModal] = useState(false);
  const [sendRequest, setSendRequest] = useState();
  const [serReqIdName, setSerReqIdName] = useState('');
  const [serReqModuleCode, setSerReqModuleCode] = useState('');
  const [showSerReq, setShowSerReq] = useState(false);
  const [headerName, setHeader] = useState('');

  const initialRowStyles = [
    {
      when: (row) => row?.dueFlag === 'Y' || row?.dueFlag === 'YO',
      style: (row) => ({
        borderStyle: 'solid',
        borderWidth: '0 0 0 4px',
        position: 'absolute',
        borderColor: row?.dueFlag === 'Y' ? 'red' : 'orange',
      }),
    },
  ];

  const [conditionalRowStyles, setConditionalStyles] =
    useState(initialRowStyles);
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [tableData, setTableData] = useState([]);
  const [data, setData] = useState({ form: null });
  const [showChangeSession, setShowChangeSession] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [masterFormData, setMasterFormData] = useState({});
  const [detailsForm, setDetailsForm] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [traceEvents, setTraceEvents] = useState([]);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [oldStatus, setOldStatus] = useState('');
  const [tempButtonData, setTempButtonData] = useState([]);

  const [columnTobeSorted, setColumnTobeSorted] = useState({
    sortingVar: '',
    ascOrDesc: 'asc',
  });
  const [offset, setOffset] = useState(1);
  const [pageLimit, setPageLimit] = useState(10);
  const [search, setSearch] = useState('');

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (buttons !== null) {
      setTempButtonData(buttons);
    }
  }, [buttons]);

  const { confirm } = useConfirm();

  useEffect(() => {
    if (Parameter) {
      if (Parameter.indexOf(',') > -1) {
        const Value = Parameter.split(',');
        setSerReqIdName(Value[0] === 'bankOut' ? 'PFBankOut' : 'PFBankIn');
        setSerReqModuleCode(
          Value[0] === 'bankOut' ? 'PF_BANK_OUT' : 'PF_BANK_IN'
        );
        setOldStatus(Value[1]);
        setParameterValues(Value);
        setOnRowClick(false);
        setDataOnRowClick(null);
        setSelectedRows([]);
        dispatch(
          financeResetStateField({
            fieldNames: [
              'sessionChangeTimeSlotList',
              'traceEventRes',
              'updateSessionRes',
              'paymentsTraceEventRes',
              'rescheduledTranscationPaymentsRes',
              'rescheduledTranscationStatementsRes',
              'paymentSourceMasterForm',
              'paymentOutwardMasterForm',
              'statmentOutwardMasterForm',
              'statmentSourceMasterForm',
              'paymentsTraceEventRes',
              'statmentSourceDetailsForm',
              'paymentOutwardDetailsForm',
              'statmentOutwardDetailsForm',
              'paymentSourceDetailsForm',
            ],
          })
        );
      }
    }

    return resetTableState;
  }, [Parameter]);

  const resetTableState = () => {
    setPageLimit(10);
    setOffset(1);
    setColumnTobeSorted({
      sortingVar: '',
      ascOrDesc: 'asc',
    });
    setTableHead([]);
    setTableData([]);
    setSearchInput('');
    setSearch('');
    setIsSearchActive(false);
    setMasterFormData({});
  };

  useEffect(() => {
    if (serviceReqTypes && serviceReqTypes.value) {
      const Options = serviceReqTypes.value.map((value) => {
        return {
          value: value.serReqId,
          display: value.serReqType,
        };
      });
      setSendRequest(Options);
    }
  }, [serviceReqTypes]);

  //---------------------Payment Source --------------------

  const validStatuses = [
    'for-authorize',
    'authorized',
    'for-xmsn',
    'transmitted',
    'ack-rcvd',
    'rescheduled',
    'erp-rcvd',
    'ap-rcvd',
    'in-process',
    'fail-file',
    'fail-xmsn',
    'rejected',
  ];
  const paymentsOutwardsStatuses = [
    'for-authorize',
    'authorized',
    'for-xmsn',
    'transmitted',
    'ack-rcvd',
    'rescheduled',
  ];
  const statementsSourceStatuses = ['bank'];

  const alignedSelectors = [
    'amount',
    'availableBalance',
    'creditAmt',
    'debitAmt',
    'avalBalance',
  ];

  useEffect(() => {
    if (parameterValues) {
      masterTableApiHandler();
      if (
        (parameterValues[0] === 'bankOut' &&
          paymentsOutwardsStatuses.includes(parameterValues[1])) ||
        (parameterValues[0] === 'bankIn' &&
          statementsSourceStatuses.includes(parameterValues[1]))
      )
        setShowSerReq(true);
      else setShowSerReq(false);
    }
  }, [parameterValues]);

  useEffect(() => {
    let filterableStr = filterString?.filterStr;
    if (filterableStr == null || filterableStr === '') {
      setTableData(TableList);
    }
    globalSearch(TableList);
  }, [filterString.filterStr]);

  useEffect(() => {
    if (TableList) {
      setFilterString({ filterStr: '' });
      setTableData(TableList);
    }
    if (Array.isArray(TableList) && TableList.length > 0) {
      setIsSearchActive(true);
    }
  }, [TableList]);

  useEffect(() => {
    if (updateSessionRes?.value) {
      let outwatsMasterFormObj = {
        clientId: clientId,
        userId: userId,
        status: parameterValues[1],
        limit: pageLimit,
        offset: offset,
        searchVariable: search,
        formDetailsDto: masterFormData,
        ...columnTobeSorted,
      };
      dispatch(getPaymentOutwardMasterForm(outwatsMasterFormObj));
      funcToSetResponseMessage('success', updateSessionRes.value);
      setOnRowClick(false);
      setDataOnRowClick(null);
    }
  }, [updateSessionRes]);

  useEffect(() => {
    if (
      rescheduledTranscationPaymentsRes &&
      rescheduledTranscationPaymentsRes.value
    ) {
      funcToSetResponseMessage(
        'success',
        rescheduledTranscationPaymentsRes.value
      );

      if (parameterValues?.[0] === 'bankOut') {
        dispatch(
          getPaymentOutwardMasterForm({
            clientId,
            userId,
            status: parameterValues[1],
            limit: pageLimit,
            offset: offset,
            searchVariable: search,
            formDetailsDto: masterFormData,
            ...columnTobeSorted,
          })
        );
      }
      setOnRowClick(false);
      setDataOnRowClick(null);
      setSelectedRows([]);
      setTraceEvents([]);
      setConditionalStyles(initialRowStyles);
      setTempButtonData((prevButtons) =>
        prevButtons.map((button) =>
          button.parameter === 'reschedule' || button.parameter === 'authorize'
            ? { ...button, disable: true }
            : button
        )
      );
    }
  }, [rescheduledTranscationPaymentsRes]);

  useEffect(() => {
    if (
      rescheduledTranscationStatementsRes &&
      rescheduledTranscationStatementsRes.value
    ) {
      funcToSetResponseMessage(
        'success',
        rescheduledTranscationStatementsRes.value
      );

      if (parameterValues?.[0] === 'bankIn') {
        dispatch(
          getStatmentOutwardMasterForm({
            clientId,
            userId,
            status: parameterValues[1],
            limit: pageLimit,
            offset: offset,
            searchVariable: search,
            formDetailsDto: masterFormData,
            ...columnTobeSorted,
          })
        );
      }
      setOnRowClick(false);
      setDataOnRowClick(null);
      setSelectedRows([]);
      setTraceEvents([]);
      setConditionalStyles(initialRowStyles);
      setTempButtonData((prevButtons) =>
        prevButtons.map((button) =>
          button.parameter === 'reschedule'
            ? { ...button, disable: true }
            : button
        )
      );
    }
  }, [rescheduledTranscationStatementsRes]);

  useEffect(() => {
    if (traceEventRes?.value) {
      setTraceEvents(traceEventRes.value);
    } else setTraceEvents([]);
  }, [traceEventRes]);

  useEffect(() => {
    if (!finListigMasterTableCount?.value) return;
    setTableListlength(finListigMasterTableCount?.value);
  }, [finListigMasterTableCount]);

  const formatColumn = (item, withTooltip = false) => ({
    ...item,
    name: item.displayName,
    displayName: item.displayName,
    width: item.displayWidth,
    style: alignedSelectors.includes(item.selector)
      ? {
          textAlign: 'right',
          justifyContent: 'flex-end',
          paddingRight: '16px',
        }
      : {},
    // optionally, header style as well:
    headerStyle: alignedSelectors.includes(item.selector)
      ? {
          width: '100%',
        }
      : {},
    sortable: true,
    cell: (row) => {
      const content = row[item.selector];
      const isDue = row?.dueFlag === 'Y' || row?.dueFlag === 'YO';
      const textRef = React.useRef(null);
      const [isOverflowed, setIsOverflowed] = React.useState(false);

      React.useEffect(() => {
        if (textRef.current) {
          const { scrollWidth, clientWidth } = textRef.current;
          setIsOverflowed(scrollWidth > clientWidth);
        }
      }, [content]);

      if (item.selector === 'availableBalance' && row?.receivedTime) {
        return (
          <Tooltip
            classNames={{
              root: 'ant-tooltip-container',
              popup: 'finList-custom-tooltip',
            }}
            title={row.receivedTime}
          >
            <p
              onClick={() => callBackFunc(row)}
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                margin: 0,
              }}
            >
              {content}
            </p>
          </Tooltip>
        );
      }

      // this tooltip only if content is overflowing
      //If want to add tooltip for every column remove the ref condition
      const CellContent = (
        <div
          className={
            item.selector === 'serialNo' && isDue ? 'serialNoDuePadding' : ''
          }
          ref={textRef}
          onClick={() => callBackFunc(row)}
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            cursor: 'pointer',
            width: alignedSelectors.includes(item.selector) ? '100%' : '',
            textAlign: alignedSelectors.includes(item.selector) ? 'end' : '',
          }}
        >
          {content}
        </div>
      );

      return isOverflowed && withTooltip ? (
        <Tooltip
          overlayInnerStyle={{
            backgroundColor: '#000',
            color: '#fff',
            fontSize: '12px',
            padding: '5px 10px',
            borderRadius: '4px',
          }}
          title={content}
        >
          {CellContent}
        </Tooltip>
      ) : (
        CellContent
      );
    },
  });

  useEffect(() => {
    if (parameterValues) {
      if (
        parameterValues[1] === 'erp' ||
        parameterValues[1] === 'sap' ||
        parameterValues[1] === 'ap-rcvd'
      ) {
        if (paymentSourceMasterForm && paymentSourceMasterForm.value) {
          paymentSourceMasterForm.value.map((items) => {
            if (items.uniqueKey === 'pymt_master_table') {
              setMasterFormData(items);
              const modifiedForm = items.formSubDetailsInternalDTOList.map(
                (item) => {
                  return {
                    ...item,
                    name: item.displayName,
                    displayName: item.displayName,
                    width: item.displayWidth,
                    // right: [
                    //   'availableBalance',
                    //   'amount',
                    //   'creditAmt',
                    //   'debitAmt',
                    // ].includes(item.selector),
                    // center: ['currency'].includes(item.selector),

                    sortable: item.selector === 'serialNo' ? false : true,
                    style: alignedSelectors.includes(item.selector)
                      ? {
                          textAlign: 'right',
                          justifyContent: 'flex-end',
                          paddingRight: '16px',
                        }
                      : {},
                    // optionally, header style as well:
                    headerStyle: alignedSelectors.includes(item.selector)
                      ? {
                          width: '100%',
                        }
                      : {},
                    cell: (value) => {
                      const content = value[item?.selector];
                      return (
                        <p
                          title={content}
                          style={{
                            margin: 0,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            width: alignedSelectors.includes(item.selector)
                              ? '100%'
                              : '',
                            textAlign: alignedSelectors.includes(item.selector)
                              ? 'end'
                              : '',
                          }}
                          onClick={() => callBackFunc(value)}
                        >
                          {content}
                        </p>
                      );
                    },
                  };
                }
              );
              setTableHead(modifiedForm);
              setTableList(items.value);
            }
            if (items.uniqueKey === 'pf_payment_erpdetails') {
              setDetailsForm(items);
            }
          });
        }
      }
    }
  }, [paymentSourceMasterForm]);

  const titleNames = {
    erp: 'Source / ERP',
    sap: 'Source / SAP',
    'ap-rcvd': 'Source / Accounts Payables',
    bank: 'Source / Bank',
    'for-authorize': 'Outwards / Authorize',
    authorized: 'Outwards / Authorized',
    'for-xmsn': 'Outwards / For Transmission',
    transmitted: 'Outwards / Transmitted',
    'ack-rcvd': 'Outwards / Acknowledged',
    rescheduled: 'Outwards / Rescheduled',
    'in-process': 'Outwards / In Process',
    'fail-file': 'Failures / File Generation',
    'fail-xmsn': 'Failures / Transmission',
    rejected: 'Failures / Rejection',
  };
  useEffect(() => {
    if (parameterValues) {
      if (validStatuses.includes(parameterValues[1])) {
        if (parameterValues[0] === 'bankOut') {
          if (paymentOutwardMasterForm && paymentOutwardMasterForm.value) {
            paymentOutwardMasterForm.value.map((items) => {
              if (items.uniqueKey === 'outwardsMaster') {
                setMasterFormData(items);
                const modifiedForm = items.formSubDetailsInternalDTOList.map(
                  (item) => formatColumn(item, true)
                );

                setTableHead(modifiedForm);
                setTableList(items.value);
              }
              if (items.uniqueKey === 'outwardsDetails') {
                setDetailsForm(items);
              }
            });
          }
        } else if (parameterValues[0] === 'bankIn') {
          if (statmentOutwardMasterForm && statmentOutwardMasterForm.value) {
            statmentOutwardMasterForm.value.map((items) => {
              if (items.uniqueKey === 'stat_out_master_tab') {
                setMasterFormData(items);
                let modifiedForm = items.formSubDetailsInternalDTOList.map(
                  (item) => {
                    return {
                      ...item,
                      name: item.displayName,
                      displayName: item.displayName,
                      width: item.displayWidth,
                      // right: [
                      //   'availableBalance',
                      //   'amount',
                      //   'creditAmt',
                      //   'debitAmt',
                      // ].includes(item.selector),
                      // center: ['currency'].includes(item.selector),
                      style: alignedSelectors.includes(item.selector)
                        ? {
                            textAlign: 'right',
                            justifyContent: 'flex-end',
                            paddingRight: '16px',
                          }
                        : {},
                      // optionally, header style as well:
                      headerStyle: alignedSelectors.includes(item.selector)
                        ? {
                            width: '100%',
                          }
                        : {},
                      sortable: item.selector === 'serialNo' ? false : true,
                      cell: (value) => {
                        const content = value[item?.selector];
                        return (
                          <p
                            title={content}
                            style={{
                              margin: 0,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              width: alignedSelectors.includes(item.selector)
                                ? '100%'
                                : '',
                              textAlign: alignedSelectors.includes(
                                item.selector
                              )
                                ? 'end'
                                : '',
                            }}
                            onClick={() => callBackFunc(value)}
                          >
                            {content}
                          </p>
                        );
                      },
                    };
                  }
                );
                if (parameterValues[1] === 'in-process') {
                  modifiedForm = modifiedForm.filter(
                    (form) => form.selector !== 'fileName'
                  );
                }
                setTableHead(modifiedForm);
                setTableList(items.value);
              }
              if (items.uniqueKey === 'stat_out_details_tab') {
                setDetailsForm(items);
              }
            });
          }
        }
      } else if (parameterValues[1] === 'bank') {
        if (statmentSourceMasterForm && statmentSourceMasterForm.value) {
          statmentSourceMasterForm.value.map((items) => {
            if (items.uniqueKey === 'stat_source_master_tab') {
              setMasterFormData(items);
              const modifiedForm = items.formSubDetailsInternalDTOList.map(
                (item) => {
                  return {
                    ...item,
                    name: item.displayName,
                    displayName: item.displayName,
                    // right: [
                    //   'availableBalance',
                    //   'amount',
                    //   'creditAmt',
                    //   'debitAmt',
                    // ].includes(item.selector),
                    // center: ['currency'].includes(item.selector),
                    width: item.displayWidth,
                    sortable: item.selector === 'serialNo' ? false : true,
                    style: alignedSelectors.includes(item.selector)
                      ? {
                          textAlign: 'right',
                          justifyContent: 'flex-end',
                          paddingRight: '16px',
                        }
                      : {},
                    // optionally, header style as well:
                    headerStyle: alignedSelectors.includes(item.selector)
                      ? {
                          width: '100%',
                        }
                      : {},
                    cell: (value) => {
                      const content = value[item?.selector];
                      return (
                        <p
                          title={content}
                          style={{
                            margin: 0,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            width: alignedSelectors.includes(item.selector)
                              ? '100%'
                              : '',
                            textAlign: alignedSelectors.includes(item.selector)
                              ? 'end'
                              : '',
                          }}
                          onClick={() => callBackFunc(value)}
                        >
                          {content}
                        </p>
                      );
                    },
                  };
                }
              );
              setTableHead(modifiedForm);
              setTableList(items.value);
            }
            if (items.uniqueKey === 'stat_source_details_tab') {
              setDetailsForm(items);
            }
          });
        }
      }
    }
  }, [
    paymentOutwardMasterForm,
    statmentOutwardMasterForm,
    statmentSourceMasterForm,
  ]);

  useEffect(() => {
    if (parameterValues) {
      setHeader(titleNames[parameterValues[1]] || '');
      setConditionalStyles(initialRowStyles);
    }
  }, [parameterValues]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  function getServReqListByType(primaryId) {
    if (primaryId)
      dispatch(
        getSerReqLists({
          idName: serReqIdName,
          id: primaryId,
          moduleCode: serReqModuleCode,
          userId: userId,
          reqType: 'subscribe',
        })
      );
  }

  const callBackFunc = (data) => {
    let tableName = '';
    let tableId = '';
    if (subMenuName === 'Payments') {
      if (
        innerMenuName === 'Acc. Payables' ||
        innerMenuName === 'ERP' ||
        innerMenuName === 'SAP'
      ) {
        tableName = 'PYMT_IN';
        tableId = 'inTxnId';
      } else {
        tableName = 'PYMT_OUT';
        tableId = 'bankOutTxnId';
      }
    } else if (subMenuName === 'Statements') {
      if (innerMenuName === 'Bank') {
        tableName = 'STMT_IN';
        tableId = 'bankInTxnMasterBeanId';
      } else {
        tableName = 'STMT_OUT';
        tableId = 'internalOutTransactionId';
      }
    }
    setTempButtonData((prev) => {
      return prev.map((button) => {
        if (
          button.parameter === 'reschedule' ||
          button.parameter === 'authorize'
        ) {
          return {
            ...button,
            disable: false,
          };
        }
        return button;
      });
    });
    setOnRowClick(true);
    setDataOnRowClick(data);
    if (showSerReq) {
      getServReqListByType(data[tableId]);

      dispatch(getServiceReqTypesCombo(serReqModuleCode));
    }
    dispatch(
      getSessionDetailsTraceEvents({ tableName, tableId: data[tableId] } ?? {})
    );

    setTimeout(() => {
      //  detail table container id
      const targetElement = document.getElementById('finSubList-container');

      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);

    let styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      data.serialNo,
      'serialNo'
    )[0];

    if (data?.dueFlag === 'Y' || data?.dueFlag === 'YO')
      styleAttribute = {
        ...styleAttribute,
        style: {
          ...styleAttribute.style,
          borderStyle: 'solid',
          borderWidth: '0 0 0 4px',
          position: 'absolute',
          borderColor: data?.dueFlag === 'Y' ? 'red' : 'orange',
        },
      };
    setConditionalStyles([...initialRowStyles, styleAttribute]);
    if (dataOnRowClick?.bankOutTxnId === data?.bankOutTxnId) return;
    setSelectedRows([]);
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setTableData(filteredData);
    }
  };

  const authorize = async () => {
    const isConfirmed = await confirm('Are you sure you want to Proceed?');
    if (!isConfirmed) return;
    dispatch(
      getSessionDetailsAuthorize({
        bankOutTxnId: dataOnRowClick?.bankOutTxnId ?? null,
        status: 'authorized',
        userId,
      })
    );
  };

  const handleReschedule = async () => {
    const isConfirmed = await confirm('Are you sure you want to Proceed?');
    if (!isConfirmed) return;
    if (subMenu === 'fin-statements') {
      dispatch(
        rescheduledTranscationStatements({
          intOutTxnId: dataOnRowClick?.internalOutTransactionId ?? null,
          userId: userId,
          status: 'for-xmsn',
          oldStatus,
        })
      );
    } else {
      dispatch(
        rescheduledTranscationPayments({
          bankOutTxnId: dataOnRowClick?.bankOutTxnId ?? null,
          userId: userId,
          status: 'authorized',
          oldStatus,
        })
      );
    }
  };
  // const customStyles = {
  //   headCells: {
  //     style: {
  //       '&:nth-child(4)': {
  //         // Amount column (4th position)
  //         textAlign: 'right',
  //         justifyContent: 'flex-end',
  //         paddingRight: '16px',
  //       },
  //       '&:nth-child(5)': {
  //         // Currency column (5th position)
  //         textAlign: 'right',
  //         justifyContent: 'flex-end',
  //         paddingRight: '16px',
  //       },
  //     },
  //   },
  //   cells: {
  //     style: {
  //       '&:nth-child(4)': {
  //         // Amount column cells
  //         textAlign: 'right',
  //         justifyContent: 'flex-end',
  //         paddingRight: '16px',
  //       },
  //       '&:nth-child(5)': {
  //         // Currency column cells
  //         textAlign: 'right',
  //         justifyContent: 'flex-end',
  //         paddingRight: '16px',
  //       },
  //     },
  //   },
  //   rows: {
  //     style: {
  //       '&:hover': {
  //         cursor: 'pointer',
  //       },
  //     },
  //   },
  // };
  const functionsName = {
    authorize,
    handleReschedule,
  };

  // Handle Page Change
  const handlePageChange = (newOffset) => {
    setOffset(newOffset);
    masterTableApiHandler({ offset: newOffset });
  };

  // Handle Rows per Page Change
  const handlePerRowsChange = (newLimit, page) => {
    setPageLimit(newLimit);
    masterTableApiHandler({ limit: newLimit });
  };

  const sortHandler = (data, direction) => {
    if (!data?.selector) return;
    setColumnTobeSorted({ sortingVar: data.selector, ascOrDesc: direction });
    masterTableApiHandler({ sortingVar: data.selector, ascOrDesc: direction });
  };
  const overlayStyles = {
    position: 'absolute',
    top: '55px',
    left: 0,
    width: '100%',
    height: 'calc(100% - 110px)',
    background: 'rgba(255, 255, 255, 0.6)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '20px',
    fontWeight: 'bold',
  };
  const refreshHandler = () => {
    setOnRowClick(false);
    setConditionalStyles(initialRowStyles);
    setRefreshToggle(!refreshToggle);
    setTempButtonData((prevButtons) =>
      prevButtons.map((button) =>
        button.parameter === 'reschedule' || button.parameter === 'authorize'
          ? { ...button, disable: true }
          : button
      )
    );
  };

  const sessionCheckHandler = (row, subTableLength = 0) => {
    if (!row) return;

    // setSubTableLength(subTableLength);

    setSelectedRows((prev = []) => {
      return prev.some((data) => data.serialNo === row.serialNo)
        ? prev.filter((data) => data.serialNo !== row.serialNo)
        : [...prev, row];
    });
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    if (value.trim() === '') {
      setSearch('');
      masterTableApiHandler({ searchVariable: '' });
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && isSearchActive && searchInput !== '') {
      handleSearch();
    }
  };

  const handleSearch = () => {
    if (isSearchActive && searchInput !== '') {
      if (searchInput.trim() === '') {
        setSearch('');
        masterTableApiHandler({ searchVariable: '' });
      } else {
        setSearch(searchInput);
        masterTableApiHandler({ searchVariable: searchInput });

        // clear details list
        setConditionalStyles([]);
        dispatch(
          financeResetStateField({
            fieldNames: [
              'statmentSourceDetailsForm',
              'paymentOutwardDetailsForm',
              'statmentOutwardDetailsForm',
              'paymentSourceDetailsForm',
            ],
          })
        );
        setDataOnRowClick(null);
      }
    } else {
      return;
    }
  };

  const masterTableApiHandler = (parameter = {}) => {
    let formObj = {
      clientId: clientId,
      userId: userId,
      status: parameterValues[1],
      source: parameterValues[1],
      limit: pageLimit,
      offset: offset,
      formDetailsDto: masterFormData,
      searchVariable: search,
      ...columnTobeSorted,
      ...parameter,
    };
    let countObj = {
      clientId: clientId,
      userId: userId,
      status: parameterValues[1],
      source: parameterValues[1],
      searchVariable: search,
      formDetailsDto: masterFormData,
      ...parameter,
    };
    if (
      parameterValues[1] === 'erp' ||
      parameterValues[1] === 'sap' ||
      parameterValues[1] === 'ap-rcvd'
    ) {
      dispatch(getPaymentSourceMasterCount(countObj));
      dispatch(getPaymentSourceMasterForm(formObj));
    } else if (
      parameterValues?.[0] === 'bankOut' &&
      validStatuses.includes(parameterValues[1])
    ) {
      dispatch(getPaymentOutwardMasterCount(countObj));
      dispatch(getPaymentOutwardMasterForm(formObj));
    } else if (
      parameterValues?.[0] === 'bankIn' &&
      validStatuses.includes(parameterValues[1])
    ) {
      dispatch(getStatmentOutwardMasterCount(countObj));
      dispatch(getStatmentOutwardMasterForm(formObj));
    } else if (parameterValues[1] === 'bank') {
      dispatch(getStatmentSourceMasterCount(countObj));
      dispatch(getStatmentSourceMasterForm(formObj));
    }
  };

  return (
    <div className="fin_list_container">
      <div className="page-title mb20 group">
        {headerName}
        <span className="refresh-button">
          <span
            className="icon-refresh"
            onClick={() => {
              refreshHandler();
            }}
          ></span>
        </span>
      </div>
      <div className="page-sub-title mb16 group">
        {parameterValues &&
        parameterValues.length &&
        parameterValues[0] === 'bankOut'
          ? 'Payment File'
          : 'Bank File'}
      </div>

      <div className="fin_list_search_div">
        <TextInput
          placeholder="Search"
          className="fin_search_input"
          value={searchInput}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={!isSearchActive}
          label={false}
        />

        <Button className="small default button" onClick={handleSearch}>
          Search
        </Button>
      </div>

      <div
        className="mb40 styledDatatable"
        style={{ position: 'relative', width: '100%', minHeight: '70px' }}
      >
        <DataTable
          persistTableHead
          dense={false}
          striped={true}
          noHeader={true}
          pagination={true}
          sortServer={true}
          data={tableData}
          columns={TableHead}
          highlightOnHover={true}
          customStyles={{
            rows: {
              style: {
                '&:hover': {
                  cursor: 'pointer',
                },
              },
            },
          }}
          progressPending={!TableHead.length && finListLoading}
          // paginationResetDefaultPage={resetTable}
          paginationTotalRows={TableListinglength}
          conditionalRowStyles={conditionalRowStyles}
          onSort={sortHandler}
          onRowClicked={callBackFunc}
          onChangePage={handlePageChange}
          onChangeRowsPerPage={handlePerRowsChange}
          paginationPerPage={pageLimit}
          paginationDefaultPage={offset}
          key={pageLimit}
          paginationServer
          progressComponent={<Ellipsis color={globalutils.spinnerColor()} />}
        />

        {TableHead.length && finListLoading ? (
          <div style={overlayStyles}>
            <Ellipsis color={globalutils.spinnerColor()} />
          </div>
        ) : (
          ''
        )}
      </div>

      {onRowClicked === true &&
      parameterValues[1] === 'pymt-bank' &&
      dataOnRowClick ? (
        <Reconciliation
          tableName={parameterValues}
          tableData={dataOnRowClick}
          parameter={Parameter}
        />
      ) : onRowClicked === true && dataOnRowClick ? (
        <>
          {showSerReq ? (
            <>
              <div className="view-switcher">
                <ServiceRequestLauncher
                  onClick={() => {
                    getServReqListByType(
                      dataOnRowClick[
                        parameterValues[0] === 'bankOut'
                          ? 'bankOutTxnId'
                          : 'bankInTxnMasterBeanId'
                      ]
                    );
                    setServiceResponseModal(true);
                  }}
                  count={serReqList?.value?.length}
                />
              </div>
              <ServiceResponseComponent
                clicktype={serReqModuleCode}
                idName={serReqIdName}
                txnId={
                  dataOnRowClick[
                    parameterValues[0] === 'bankOut'
                      ? 'bankOutTxnId'
                      : 'bankInTxnMasterBeanId'
                  ]
                }
                comboOptions={sendRequest}
                onClick={() => setServiceResponseModal(false)}
                isModalopen={serviceResponseModal}
              />
            </>
          ) : (
            ''
          )}
          {dataOnRowClick ? (
            <FinListingSubList
              tableName={parameterValues}
              tableData={dataOnRowClick}
              parameter={Parameter}
              detailsForm={detailsForm}
              popupWindow={(val) => setData(val)}
              handleSelectedRows={sessionCheckHandler}
              selectAllRows={setSelectedRows}
              allRowSelected={setAllRowSelected}
              subMenuName={subMenuName}
              innerMenuName={innerMenuName}
            />
          ) : (
            ''
          )}
          {(parameterValues[1] === 'for-authorize' ||
            parameterValues[1] === 'authorized') &&
          selectedRows?.length ? (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                position: 'relative',
                bottom: '20px',
              }}
            >
              <Button
                onClick={() => {
                  dispatch(
                    getSessionChangeForm({
                      userId: userId,
                      clientId: clientId,
                      source: dataOnRowClick?.bankAccountId ?? null,
                      bankOutTxnId: dataOnRowClick?.bankOutTxnId ?? null,
                      bankAccountId: dataOnRowClick?.bankAccountId ?? null,
                    })
                  );
                  setShowChangeSession(true);
                }}
                className="small mb8 outline mt10"
              >
                Change Session
              </Button>
            </div>
          ) : (
            ''
          )}
        </>
      ) : (
        ''
      )}
      {showChangeSession ? (
        <ChangeSession
          outwardsMaster={dataOnRowClick ?? null}
          selectedOutwardsDetailsList={selectedRows}
          showChangeSessionModal={showChangeSession}
          handleChangeSessionModal={(flag) => setShowChangeSession(flag)}
          // isSelectAll={subTableLength === selectedRows?.length}
          isSelectAll={allRowSelected}
        />
      ) : (
        ''
      )}
      {traceEvents?.length ? (
        <Tabs className="boxed mb20" style={{ marginRight: '0px' }}>
          <TabPane tab={'WorkFlow Events'} key={0}>
            <TraceEvents
              key="traceEventsEditor"
              data={traceEvents}
              userId={userId}
            />
          </TabPane>
        </Tabs>
      ) : (
        ''
      )}
      <ButtonCommon
        tempButtonData={tempButtonData}
        functionsName={functionsName}
      />
    </div>
  );
};

export { FinListing };
