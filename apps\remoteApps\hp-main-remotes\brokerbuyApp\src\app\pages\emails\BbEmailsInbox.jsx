/* eslint-disable array-callback-return */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-lone-blocks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */

import React, { useState, useEffect } from 'react';
import {
  TraceEvents,
  CommonSpinner,
  DocumentViewer,
  getClientFormattedCurrentDate,
} from '@hp/components';

import { useDispatch, useSelector } from 'react-redux';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { Scrollbars } from 'react-custom-scrollbars';
import { EmailListing } from '../_email-ui/EmailListing';
import { EmailSearch } from '../_email-ui/EmailSearch';
import { EmailBody } from '../_email-ui/EmailBody';
import { EmailEmpty } from '../_email-ui/EmailEmpty';
import { EmailNewCompose } from '../_email-ui/EmailNewCompose';
import { EmailReply } from '../_email-ui/EmailReply';
import { EmailPagination } from '../_email-ui/EmailPagination';
import '../_email-ui/EmailUI.scss';
import { AP_USER } from '@hp/constants';
import Modal from 'react-modal';
import cloneDeep from 'lodash.clonedeep';
import { ApEmailOutboxBody } from '../_ap-emails/ApEmailOutboxBody';
import { accPayConstants, emailConstants } from '@hp/mainstore';
import { useAppRouterDom } from '@hp/utils';
import {
  showDialog,
  fetchEmailDetailsByDocId,
  getApApproverUserList,
  getEmailCategoryList,
  getCount,
  emailDetailsByDocId,
  setEmailPrimaryId,
  traceEvent,
  apEmailInboxs,
} from '@hp/mainstore';
import { AP_ID } from '@hp/constants';
import { globalutils } from '@hp/components';

const ApEmailsInbox = (props) => {
  const { domParameters } = useAppRouterDom();
  const menuData = domParameters?.menuData || '';
  // const submenu = domParameters?.submenu || '';
  const Parameter = domParameters?.parameters || '';

  const dispatch = useDispatch();
  const {
    apEmailInbox,
    apEmailInboxFail,
    emailInboxDetails,
    apProcessorUserList,
    apEmailCategoryList,
    emailLoading,
    emailCategoryAddRemoveSuccess,
  } = useSelector((store) => store.email);
  const { innerMenuName } = useSelector((store) => store.menu);
  const { countforAPDasboard, accPaytraceEvents } = useSelector(
    (store) => store.accpay
  );
  let user = globalutils.getDataFromStorage('all');

  const userId = user.userId;
  const clientId = user.clientId;
  const table_name = Parameter || '';
  let tableName = [];
  if (table_name.indexOf(',') > -1) {
    tableName = table_name.split(',');
  }
  const [TableListing, setTableList] = useState();
  const [filteredData, setFilteredData] = useState();
  // const [tableListLength, setTableListLength] = useState(0);
  const [state, setState] = useState();
  const [isLoading, setLoading] = useState(false);
  const [isModal, setIsModal] = React.useState(false);
  const [filePath, setfilePath] = React.useState('');
  const [newCompose, setNewCompose] = useState(false);
  const [emailReply, setEmailReply] = useState(false);
  const [assignedUser, setAssignedUserName] = useState('');
  const [isProcessedFlag, setIsProcessed] = useState(false);
  const [processTabClick, setProcessTabClick] = useState('UnProcessed');
  const [currentDate, setCurrentDate] = useState();
  const [limit, setLimit] = useState(0);
  const [offset, setOffset] = useState(50);
  const [activeFirst, setActiveFirst] = useState(true);
  const [activeLast, setActiveLast] = useState(true);
  const [processedCount, setProcessedCount] = useState('');
  const [unProcessedCount, setUnProcessedCount] = useState('');
  const [traceEvents, setTraceEvents] = useState();
  const [addCommentsValue, setAddCommentsValue] = useState('');
  const onAddCommentsChangeHandler = (event) => {
    setAddCommentsValue(event.target.value);
  };

  const funcToSetResponseMessage = (showPopup, type, resMessage) => {
    let canClose = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
      })
    );
  };

  useEffect(() => {
    setCurrentDate(getClientFormattedCurrentDate());

    return () => {
      dispatch(
        fetchEmailDetailsByDocId({
          docId: '',
          emailId: '',
          type: 'unsubscribe',
        })
      );
    };
  }, []);

  useEffect(() => {
    setLoading(emailLoading);
  }, [emailLoading]);

  useEffect(() => {
    if (emailCategoryAddRemoveSuccess && emailCategoryAddRemoveSuccess.value) {
      window.location.reload();
    }
    let data = null;
    dispatch({
      type: emailConstants.AP_EMAIL_CATEGORY_ADD_REMOVE_SUCCESS,
      data,
    });
  }, [emailCategoryAddRemoveSuccess]);

  useEffect(() => {
    if (apEmailInbox) {
      setActiveLast(true);
      setTableList(apEmailInbox.value);
      setFilteredData(apEmailInbox.value);
      // setTableListLength(apEmailInbox.length);
      if (apEmailInbox.value.length < offset) {
        setActiveLast(false);
      }
      setState('');
    } else if (apEmailInboxFail) {
      apEmailInboxFail.response && apEmailInboxFail.response.data
        ? funcToSetResponseMessage(
            true,
            'error',
            apEmailInboxFail.response.data
          )
        : null;
    }
    return () => {
      setTableList('');
    };
  }, [apEmailInbox, apEmailInboxFail]);

  useEffect(() => {
    setLoading(false);
    setState('');
    if (emailInboxDetails !== undefined && emailInboxDetails != null) {
      setState(emailInboxDetails);
    }
    return () => {
      setState('');
    };
  }, [emailInboxDetails]);

  useEffect(() => {
    setFilteredData([]);
    apEmailInboxData();
    setNewCompose(false);
    dispatch(getApApproverUserList());
    dispatch(getEmailCategoryList());

    dispatch(getCount({ clientId: clientId, userId: userId, APId: AP_ID }));
  }, [props]);

  useEffect(() => {
    return () => {
      dispatch(
        apEmailInbox({
          userId,
          clientId,

          tableName: tableName[0],
          category: tableName[1],
          status: tableName[2],
          process: processTabClick,
          limit,
          offset,
          data: null,
          type: 'unsubscribe',
          searchData: null,
        })
      );
    };
  }, []);
  useEffect(() => {
    apEmailInboxData();
  }, [processTabClick, offset]);

  useEffect(() => {
    if (limit >= 0) {
      apEmailInboxData();
      if (limit === 0) {
        setActiveFirst(false);
      } else {
        setActiveFirst(true);
      }
    }
  }, [limit]);

  useEffect(() => {
    if (
      countforAPDasboard &&
      countforAPDasboard.value.AP_EMAIL &&
      countforAPDasboard.value.AP_EMAIL.length > 0
    ) {
      setProcessedCount('');
      setUnProcessedCount('');
      countforAPDasboard.value.AP_EMAIL.filter(
        (e) => e.status && Parameter[1] && e.status === Parameter[1]
      ).map((a) => {
        if (a) {
          a.emailProcessedCount
            ? setProcessedCount(['(', a.emailProcessedCount, ')'].join(''))
            : null;
          a.emailUnProcessedCount
            ? setUnProcessedCount(['(', a.emailUnProcessedCount, ')'].join(''))
            : null;
        }
      });
    }
  }, [countforAPDasboard]);

  useEffect(() => {
    if (accPaytraceEvents) {
      setTraceEvents(accPaytraceEvents.value);
      let data = null;
      dispatch({
        type: accPayConstants.ACCPAY_TRACE_EVENTS_SUCCESS,
        data,
      });
    }
  }, [accPaytraceEvents]);

  const apEmailInboxData = () => {
    dispatch(
      apEmailInboxs({
        userId,
        clientId,

        tableName: tableName[0],
        category: tableName[1],
        status: tableName[2],
        process: processTabClick,
        limit: limit,
        offset,
        data: null,
        type: 'subscribe',
      })
    );
  };
  const onclickFunc = (data, id) => {
    setAssignedUserName('');
    let userName = '';
    let categoryName = '';
    let userId = data.apUserId ? data.apUserId : '';
    let docId = data.documentId ? data.documentId : '';
    let emailId = data.emailId ? data.emailId : '';
    let category = data.category ? data.category : '';
    {
      data.processFlag === 'Y' ? setIsProcessed(true) : setIsProcessed(false);
    }
    dispatch(
      emailDetailsByDocId({ docId: docId, emailId: emailId, type: 'subscribe' })
    );

    dispatch(traceEvent({ tableName: 'AP_EMAIL', tableId: emailId }));
    let newTableListing = cloneDeep(filteredData);
    newTableListing.map(function (data, idx) {
      newTableListing[idx].isSelected = false;
      if (idx === id) {
        newTableListing[idx].isSelected = true;
        newTableListing[idx].readFlag = 'Y';
      }
    });
    if (apProcessorUserList) {
      apProcessorUserList.map(function (value) {
        if (userId) {
          if (value.userId === userId) {
            setAssignedUserName(value.userName);
            userName = value.userName;
            return;
          }
        }
      });
    }
    if (apEmailCategoryList) {
      apEmailCategoryList.value.map(function (value) {
        if (category) {
          if (value.emailCategoryCode === category) {
            categoryName = value.emailCategoryName;
            return;
          }
        }
      });
    }
    dispatch(
      setEmailPrimaryId({
        email_id: emailId,
        user_id: userId,
        user_name: userName,
        category_name: categoryName,
        category_code: category,
      })
    );
    setFilteredData(newTableListing);
    setNewCompose(false);
    setState('');
    setLoading(true);
    setEmailReply(false);
  };

  const viewFrame = (path) => {
    setfilePath(path);
    setIsModal(true);
  };

  const setProcessTabClickFunc = (value) => {
    setProcessTabClick(value);
    setLimit(0);
    setOffset(50);
    setActiveFirst(false);
    setActiveLast(true);
  };

  const pageableFunc = (iconName) => {
    if (limit >= 0) {
      iconName === 'first'
        ? setLimit(limit - limit)
        : iconName === 'prev'
        ? setLimit(limit - 1)
        : iconName === 'next'
        ? setLimit(limit + 1)
        : iconName === 'last'
        ? setLimit(limit + 1)
        : null;
    }
  };
  return (
    <>
      <CommonSpinner visible={isLoading} />
      <div className="email-ui flex-row mb40">
        <div className="left-controls">
          {/* New Message Button */}
          <div className="new-message-wrap">
            <button
              className="new-message"
              onClick={() => setNewCompose(!newCompose)}
            >
              New Message
            </button>
          </div>
          {/* Message Count */}
          <div className="message-count">
            {innerMenuName ? innerMenuName : ''}{' '}
          </div>

          {/* Search Wrap */}
          <EmailSearch
            tableList={TableListing}
            processFlag={processTabClick}
            menuParameters={tableName}
            filteredData={(data) => setFilteredData(data)}
            limit={limit}
            offset={offset}
            menu_data={menuData}
            parameter={table_name}
          />

          {/* Tabs */}
          <Tabs className="email-tabs">
            <TabList>
              <Tab onClick={() => setProcessTabClickFunc('UnProcessed')}>
                {['Unprocessed', unProcessedCount].join(' ')}
              </Tab>
              <Tab onClick={() => setProcessTabClickFunc('Processed')}>
                {['Processed', processedCount].join(' ')}
              </Tab>
            </TabList>
            <TabPanel>
              <div className="email-list">
                <Scrollbars className="scroll-wrap">
                  {filteredData
                    ? filteredData.map(function (data, idx) {
                        switch (data.processFlag) {
                          case 'N': {
                            return (
                              <EmailListing
                                key={idx}
                                isEmailRead={
                                  data.readFlag === 'Y' ? false : true
                                }
                                subject={data.subject}
                                email={data.fromAddress}
                                name=""
                                isSelected={
                                  data.hasOwnProperty('isSelected')
                                    ? data.isSelected
                                    : false
                                }
                                date={data.rcvdDate}
                                time={data.rcvdTime}
                                onClick={() => onclickFunc(data, idx)}
                                // org="Pinnacle India"
                                // label="2 Invoices"
                              />
                            );
                          }
                        }
                      })
                    : null}
                </Scrollbars>
                <EmailPagination
                  activeFirst={activeFirst}
                  activeLast={activeLast}
                  currentPage={limit + 1}
                  totalPage={''}
                  pageableIconClick={(iconName) => pageableFunc(iconName)}
                  pagePerRowClick={(value) => setOffset(Number(value))}
                />
              </div>
            </TabPanel>
            <TabPanel>
              <div className="email-list">
                <Scrollbars class="scroll-wrap">
                  {filteredData
                    ? filteredData.map(function (data, idx) {
                        switch (data.processFlag) {
                          case 'Y': {
                            return (
                              <EmailListing
                                isEmailRead={
                                  data.readFlag === 'Y' ? false : true
                                }
                                subject={data.subject}
                                email={data.fromAddress}
                                name=""
                                isSelected={
                                  data.hasOwnProperty('isSelected')
                                    ? data.isSelected
                                    : false
                                }
                                date={data.rcvdDate}
                                time={data.rcvdTime}
                                onClick={() => onclickFunc(data, idx)}
                                // org="Pinnacle India"
                                // label="2 Invoices"
                              />
                            );
                          }
                        }
                      })
                    : null}
                </Scrollbars>
                <EmailPagination
                  activeFirst={activeFirst}
                  activeLast={activeLast}
                  currentPage={limit + 1}
                  totalPage={''}
                  pageableIconClick={(iconName) => pageableFunc(iconName)}
                  pagePerRowClick={(value) => setOffset(Number(value))}
                />
              </div>
            </TabPanel>
          </Tabs>
        </div>
        <div className="right-controls">
          {newCompose ? (
            <EmailNewCompose />
          ) : state ? (
            Parameter[1] === 'outbox' ? (
              <ApEmailOutboxBody
                subject={state ? state.subject : ''}
                fromName=""
                fromEmail={state ? state.fromAddress : ''}
                toName=""
                toEmail={
                  state && state.toAddressList && state.toAddressList.length
                    ? state.toAddressList.map((resData) => {
                        return resData;
                      })
                    : ''
                }
                emailBody={state && state.htmlPart ? state.htmlPart : ''}
              />
            ) : (
              <>
                <EmailBody
                  subject={state ? state.subject : ''}
                  isProcessed={isProcessedFlag}
                  name={assignedUser ? assignedUser : ''}
                  fromName=""
                  fromEmail={state ? state.fromAddress : ''}
                  toName=""
                  toEmail={
                    state && state.toAddressList && state.toAddressList.length
                      ? state.toAddressList.map((resData) => {
                          return resData;
                        })
                      : ''
                  }
                  attachments={
                    state &&
                    state.emailAttachmentDetailsDtoList &&
                    state.emailAttachmentDetailsDtoList.length
                      ? state.emailAttachmentDetailsDtoList.map((resdata) => {
                          return {
                            fileName: resdata.fileName,
                            filePath: resdata.attachementPath,
                            isVerified: resdata.verified,
                            isZip: resdata.zip,
                            isDuplicate: resdata.duplicate,
                            subData:
                              resdata.emailAttachmentDetailsDtoSub &&
                              resdata.emailAttachmentDetailsDtoSub.length
                                ? resdata.emailAttachmentDetailsDtoSub
                                : '',
                          };
                        })
                      : []
                  }
                  emailBody={state && state.htmlPart ? state.htmlPart : ''}
                  onFileClick={(filePath) => viewFrame(filePath)}
                  replyEmail={() => setEmailReply(true)}
                  menu_data={menuData}
                />
                {emailReply ? (
                  <EmailReply
                    subject={state ? 'RE: ' + state.subject : ''}
                    // fromName="Sudhir Raj"
                    fromEmail={
                      state && state.toAddressList && state.toAddressList.length
                        ? state.toAddressList.map((resData) => {
                            return resData;
                          })
                        : ''
                    }
                    toName=""
                    toEmail={state ? state.fromAddress : ''}
                    date={currentDate ? currentDate : ''}
                    onDeleteClick={() => setEmailReply(false)}
                  />
                ) : null}
              </>
            )
          ) : (
            <EmailEmpty />
          )}
        </div>
      </div>
      <div className="boxed mb40">
        <TraceEvents
          key="traceEventsEditor"
          onAddCommentsChangeHandler={onAddCommentsChangeHandler}
          addCommentsValue={addCommentsValue}
          enableComments={true}
          disabled={true}
          data={traceEvents}
        />
      </div>
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>
        <DocumentViewer
          fileURL={filePath}
          fileType={'iframe'}
          zoom={'#zoom=50'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
        />
      </Modal>
    </>
  );
};

export { ApEmailsInbox };
