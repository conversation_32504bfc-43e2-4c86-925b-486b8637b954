// Missing Fields Drawer Styles
.missing-fields-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.missing-fields-drawer {
  position: fixed;
  top: 0;
  right: 0;
  width: 480px;
  height: 100vh;
  background: #ffffff;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    width: 100%;
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .header-content {
    display: flex;
    align-items: center;
    gap: 12px;

    .header-icon {
      color: #faad14;
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .close-button {
    border: none;
    box-shadow: none;
    color: #8c8c8c;
    
    &:hover {
      color: #262626;
      background: #f5f5f5;
    }
  }
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.no-missing-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;

  .success-message {
    .success-icon {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      background: #f6ffed;
      color: #52c41a;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-weight: bold;
      margin: 0 auto 16px;
    }

    h4 {
      color: #262626;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    p {
      color: #8c8c8c;
      font-size: 14px;
      margin: 0;
    }
  }
}

.missing-fields-list {
  .summary {
    margin-bottom: 24px;
    padding: 16px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;

    .summary-text {
      margin: 0;
      color: #d46b08;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.supplier-card {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .supplier-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;

    .supplier-name {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .missing-count {
      background: #fff2e8;
      color: #d46b08;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .missing-fields-grid {
    padding: 16px 20px;
    display: grid;
    gap: 12px;
  }

  .missing-field-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .field-name {
      color: #595959;
      font-size: 14px;
      flex: 1;
    }

    .edit-link {
      color: #1890ff;
      text-decoration: none;
      font-size: 13px;
      font-weight: 500;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        color: #40a9ff;
        background: #f0f9ff;
        text-decoration: none;
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

.drawer-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;

  .close-footer-button {
    width: 100%;
    height: 40px;
    border: 1px solid #d9d9d9;
    background: #ffffff;
    color: #595959;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }
}

// Scrollbar styling
.drawer-content::-webkit-scrollbar {
  width: 6px;
}

.drawer-content::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.drawer-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}
