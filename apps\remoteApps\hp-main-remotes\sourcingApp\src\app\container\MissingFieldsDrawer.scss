// Missing Fields Drawer Styles
.missing-fields-drawer {
  .ant-drawer-header {
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }

  .ant-drawer-body {
    padding: 24px;
  }

  @media (max-width: 768px) {
    .ant-drawer {
      width: 100% !important;
    }
  }
}

// Missing Fields Button in page title
.page-title {
  .ant-btn-link {
    &:hover {
      background: rgba(24, 144, 255, 0.06);
      border-radius: 4px;
    }

    &:focus {
      background: rgba(24, 144, 255, 0.06);
      border-radius: 4px;
    }
  }
}

// Custom styles for missing fields content
.missing-fields-content {
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .ant-list-item {
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }

  .ant-btn-link {
    padding: 4px 8px;
    height: auto;
    font-size: 12px;
  }
}
