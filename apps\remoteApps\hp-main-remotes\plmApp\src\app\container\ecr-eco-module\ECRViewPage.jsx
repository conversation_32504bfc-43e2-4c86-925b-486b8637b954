/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CommonSpinner,
  formValidationUtil,
  Input,
  ProcessFlow,
  TraceEvents,
  useConfirm,
} from '@hp/components';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  viewOrEditECRDetailsFormDetails,
  getECRSavedDocumentsDetails,
  getECRProcessFlowDetails,
  getECRECOTraceEventsDetails,
  getECRECOTraceEventsHistory,
  updateECRStatusListDetails,
  approveECRStatus,
  resetviewOrEditECRDetailsFormDetails,
  resetgetECRSavedDocumentsDetails,
  resetgetECRProcessFlowDetails,
  resetgetECRECOTraceEventsDetails,
  showDialog,
} from '@hp/mainstore';
import './ecr_eco_common.scss';
import { Tabs } from 'antd';
import { ECRECOTree } from './ECRECOTree';
import { useAppRouterDom } from '@hp/utils';
import ECRECOHeader from './ECRECOHeader';
import ECRECOPartNoSection from './ECRECOPartNoSection';
import AffectedItems from './AffectedItems';
import { RejectModal } from '../../container/RejectModal';
import ECRECOApprovers from './ECRECOApprovers';
import { globalutils } from '@hp/components';

const ECRViewPage = (props) => {
  const { navigate, location } = useAppRouterDom();
  const [currentTab, setCurrentTab] = useState('1');
  const [ecrForm, setEcrForm] = useState([]);
  const [treeDataList, setTreeDataList] = useState([]);
  const [traceEvents, setTraceEvents] = useState([]);
  const [processFlowData, setProcessFlowData] = useState(null);
  const [buttonCommonData, setButtonCommonData] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const [selectedPartNumbers, setSelectedPartNumbers] = useState(null);
  const [filteredPartNumber, setFilteredPartNumber] = useState(null);
  const [errorFlag, setErrorFlag] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [showApproverModal, setShowApproverModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [listValues, setListValues] = useState([
    { label: 'ECR #', value: null },
    { label: 'Title', value: null },
    { label: 'Requestor', value: null },
    { label: 'Originator', value: null },
  ]);
  const state = location?.state || null;
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const {
    ecrDetailsForm,
    ecrSavedDocuments,
    ecrProcessFlowRes,
    ecrEcoTraceEvents,
    ecrChangeStatusRes,
    loading,
  } = useSelector((store) => store.pdm);
  const buttonData = useSelector((state) => state.buttons.buttons || null);
  // const parameter = domParameters?.parameters || '';
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;
  const ecrId = state?.ecrId ?? null;
  const selectedEcrData = state?.row ?? null;
  const isApproval = state?.isApproval ?? null;
  const authorised = state?.auth ?? null;

  useEffect(() => {
    dispatch(
      viewOrEditECRDetailsFormDetails({
        ecrId: ecrId,
        userId: userId,
        clientId: clientId,
        method: 'View',
      })
    );
    dispatch(getECRSavedDocumentsDetails(ecrId));
    dispatch(getECRProcessFlowDetails({ ecrId: ecrId, clientId: clientId }));
    dispatch(getECRECOTraceEventsDetails({ tableName: 'ECR', tableId: ecrId }));
    if (isApproval && !authorised) {
      funcToSetResponseMessage(
        'error',
        'You are not authorised to approve/reject this ECR.'
      );
    }
    return () => {
      dispatch(resetviewOrEditECRDetailsFormDetails());
      dispatch(resetgetECRSavedDocumentsDetails());
      dispatch(resetgetECRProcessFlowDetails());
      dispatch(resetgetECRECOTraceEventsDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
      setEcrForm([]);
    };
  }, []);

  useEffect(() => {
    if (buttonData && buttonData !== undefined) {
      let tempButtonData = [];
      if (isApproval)
        tempButtonData = buttonData.map((item) => ({
          ...item,
          disable: isApproval && !authorised,
        }));
      // else if (parameter === 'draft') return;
      // else
      //   tempButtonData = [
      //     {
      //       ...buttonData?.find((btn) => btn.funcPath === 'handleReject'),
      //       disable: isApproval && !authorised,
      //     },
      //   ];
      tempButtonData.push({
        buttonId: null,
        menuId: null,
        label: 'Close',
        type: 'outline mr20 fr button mb20',
        funcPath: 'handleClose',
        orderId: 2,
        accessFlag: 'Y',
        parameter: '',
        labelEn: 'Close',
        disable: false,
      });
      setButtonCommonData(tempButtonData);
    }
  }, [buttonData]);

  useEffect(() => {
    if (ecrDetailsForm?.value) {
      let tempForm = [...ecrDetailsForm.value];
      let tempSelectedPartNumbers = [];
      let tempPartNoOptions = [];
      ecrDetailsForm?.value.forEach((item) => {
        if (item.uniqueKey === 'ecrView') {
          handleHeaderValues(item?.formSubDetailsInternalDTOList ?? []);
        } else if (item.uniqueKey === 'partNoTable') {
          if (item?.value?.length) {
            item?.value?.forEach((entry) => {
              tempSelectedPartNumbers.push(entry.value);
              tempPartNoOptions.push({
                commonId: entry.value,
                commonName: entry.partNo,
              });
            });
            setSelectedPartNumbers(tempSelectedPartNumbers);
          }
        }
      });
      const updatedTempForm = tempForm.map((item) => {
        if (item.uniqueKey === 'affectedItems') {
          const updatedFormSubDetails = item.formSubDetailsInternalDTOList.map(
            (entry) => {
              if (entry.uniqueKey === 'partNumberSelect') {
                // Create a new object with updated `comboBoxOptions`
                return {
                  ...entry,
                  comboBoxOptions: tempPartNoOptions,
                };
              }
              return entry;
            }
          );
          return {
            ...item,
            formSubDetailsInternalDTOList: updatedFormSubDetails,
          };
        }
        return item;
      });
      setEcrForm(updatedTempForm);
    }
  }, [ecrDetailsForm]);

  useEffect(() => {
    if (ecrSavedDocuments?.value) {
      setTreeDataList(ecrSavedDocuments.value);
    }
  }, [ecrSavedDocuments]);

  useEffect(() => {
    if (ecrProcessFlowRes?.value) {
      setProcessFlowData({
        nodes: ecrProcessFlowRes.value?.pdmProfileWorkflowRenderingDtos,
        edges: ecrProcessFlowRes.value?.pdmProfileWorkflowEdgeDtos,
      });
    }
  }, [ecrProcessFlowRes]);

  useEffect(() => {
    if (ecrEcoTraceEvents?.value) {
      setTraceEvents(ecrEcoTraceEvents.value);
    }
  }, [ecrEcoTraceEvents]);

  useEffect(() => {
    if (ecrChangeStatusRes?.value) {
      funcToSetResponseMessage(
        ecrChangeStatusRes.value?.type,
        ecrChangeStatusRes.value?.text
      );
      navigate(-1);
    }
  }, [ecrChangeStatusRes]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  function handleHeaderValues(formData) {
    let tempListValues = [...listValues];
    formData.forEach((item) => {
      switch (item.uniqueKey) {
        case 'ecrNo':
          tempListValues[0].value = item.value;
          break;
        case 'title':
          tempListValues[1].value = item.value;
          break;
        case 'requestedBy':
          tempListValues[2].value = item.value;
          break;
        case 'originator':
          tempListValues[3].value = item.value;
          break;
        // case 'partNum':
        //   setSelectedPartNo(item.value);
        //   break;
      }
    });
    setListValues(tempListValues);
  }

  const handleAdvancedSelect = (event, uniqueKey, element) => {
    let tempForm = [...ecrForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'affectedItems') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
            setFilteredPartNumber(event);
          }
        });
      }
    });
    setEcrForm(tempForm);
  };

  const onChangeHandlingFunctions = {
    handleAdvancedSelect,
  };

  const handleTabs = (item) => {
    if (item.uniqueKey === 'processFlow') {
      return (
        <div
          className="boxed mb20"
          style={{ maxWidth: '600px', maxHeight: '600px' }}
        >
          <h3 className="page-sub-title">Process Flow</h3>
          <div className="ecr-process-flow">
            {processFlowData ? (
              <ProcessFlow dataFlowResponse={processFlowData} />
            ) : (
              ''
            )}
          </div>
        </div>
      );
    } else if (item.uniqueKey === 'ecrView') {
      return (
        <>
          <div className="card mb20">
            <Input formType={item} />
          </div>
          {selectedPartNumbers?.length ? (
            <div className="card mb20">
              <ECRECOPartNoSection
                parameter={'view'}
                formData={ecrDetailsForm?.value ?? []}
              />
            </div>
          ) : (
            ''
          )}
        </>
      );
    } else if (item.uniqueKey === 'attachments') {
      return (
        <div className="ecr-eco-docs-container mb20">
          <ECRECOTree treeDataList={treeDataList} disabled />
        </div>
      );
    } else if (item.uniqueKey === 'affectedItems') {
      return selectedPartNumbers?.length ? (
        <div className="card mb20">
          <Input
            formType={item.formSubDetailsInternalDTOList[0]}
            isEditable="notShowing"
            onChangeHandler={(element, event) => {
              onChangeHandlingFunctions[element.onChangeFunction](
                event,
                element.uniqueKey,
                element
              );
            }}
          />
          <AffectedItems
            selectedPartNumbers={selectedPartNumbers}
            filteredPartNumber={filteredPartNumber}
          />
        </div>
      ) : (
        ''
      );
    }
  };

  function handleReject() {
    setShowPopup(true);
  }

  async function handleClose() {
    navigate(-1);
  }

  async function handleApprove() {
    const isConfirmed = await confirm('Are you sure you want to continue?');
    if (isConfirmed) {
      if (selectedEcrData?.ecrId)
        dispatch(
          approveECRStatus({
            ecrId: selectedEcrData.ecrId,
            status: 'approved',
            userId: userId,
            clientId: clientId,
          })
        );
      else funcToSetResponseMessage('error', 'Please Select at least one ECR');
    }
  }

  async function moveForApprovals() {
    const isConfirmed = await confirm('Are you sure you want to continue?');
    if (isConfirmed) {
      if (selectedEcrData)
        dispatch(
          updateECRStatusListDetails({
            status: 'for-approval',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: [selectedEcrData],
          })
        );
      else funcToSetResponseMessage('error', 'Please Select at least one ECR');
    }
  }

  const functionsName = {
    handleReject,
    handleClose,
    handleApprove,
    moveForApprovals,
  };

  return (
    <>
      {ecrForm?.length ? (
        <>
          <div className="pull-right view-switcher">
            <div
              className="service-request-launcher fr"
              onClick={() => {
                setShowApproverModal(true);
              }}
            >
              <i className="icon-user-check mr8"></i>
              <label className="label">Approvers</label>
            </div>
          </div>
          <ECRECOApprovers
            isViewOnly
            userId={userId}
            ecrId={ecrId}
            modalOpen={showApproverModal}
            handleModalClose={(flag) => setShowApproverModal(flag)}
            approvalFormData={ecrForm.find(
              (entry) => entry.uniqueKey === 'approvalForm'
            )}
          />
        </>
      ) : (
        ''
      )}
      <ECRECOHeader listValues={listValues} noOfFields={4} />
      <div className="ecr-eco-main-container">
        {showPopup ? (
          <RejectModal
            rejectClick={() => {
              if (rejectReason?.trim().length > 0) {
                dispatch(
                  updateECRStatusListDetails({
                    status: 'rejected',
                    userId: userId,
                    clientId: clientId,
                    flag: 'N',
                    payload: [
                      { ...selectedEcrData, rejectionReason: rejectReason },
                    ],
                  })
                );
                setErrorFlag(false);
                setShowPopup(false);
                setRejectReason('');
              } else setErrorFlag(true);
              setErrorMessage(null);
            }}
            isModalOpen={showPopup}
            isModalClose={() => {
              setShowPopup(false);
              setErrorFlag(false);
              setRejectReason('');
              setErrorMessage(null);
            }}
            onRejectCommentsChangeHandler={(e) => {
              let isValid = formValidationUtil.validateInputMaxLengthStatic(
                e,
                500
              );
              setErrorFlag(isValid.errorFlag);
              setErrorMessage(isValid.errorMessage);
              if (!isValid.errorFlag) {
                setRejectReason(e.target.value);
              }
            }}
            rejectComments={rejectReason}
            errorFlag={errorFlag}
            errorMessage={errorMessage}
          />
        ) : (
          ''
        )}
        {ecrForm?.length ? (
          <Tabs
            tabBarStyle={{
              fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
            }}
            activeKey={currentTab}
            items={ecrForm.map((item, index) => {
              if (
                !(
                  item.uniqueKey === 'partNoTable' ||
                  item.uniqueKey === 'approvalForm'
                )
              )
                return {
                  key: String(index + 1),
                  label: item?.displayName,
                  children: handleTabs(item),
                };
            })}
            onChange={(tabIndex) => setCurrentTab(tabIndex)}
          />
        ) : (
          ''
        )}
        {traceEvents?.length ? (
          <div>
            <div className=" page-sub-title">Events</div>
            <div className="boxed mb20">
              <Tabs
                activeKey={'1'}
                tabBarStyle={{
                  fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
                }}
                items={[
                  {
                    key: '1',
                    label: 'WorkFlow Events',
                    children: (
                      <TraceEvents
                        showRejectReason
                        externalHistoryFunction
                        externalHistoryValue={'pdm'}
                        key="traceEventsEditor"
                        enableComments={false}
                        data={traceEvents}
                        userId={userId}
                        onEnterKeyPress={() => null}
                        sendButton={() => null}
                        handleHistoryClick={(eventId, editFunction) =>
                          dispatch(
                            getECRECOTraceEventsHistory({
                              editFunction: editFunction,
                              eventId: eventId,
                              userId: userId,
                            })
                          )
                        }
                      />
                    ),
                  },
                ]}
              />
            </div>
          </div>
        ) : (
          ''
        )}
      </div>

      <CommonSpinner visible={loading} />
      {selectedEcrData ? (
        <ButtonCommon
          tempButtonData={buttonCommonData}
          functionsName={functionsName}
        />
      ) : (
        ''
      )}
    </>
  );
};

export { ECRViewPage };
