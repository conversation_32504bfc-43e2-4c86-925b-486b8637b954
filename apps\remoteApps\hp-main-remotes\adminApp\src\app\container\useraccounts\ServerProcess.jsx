/* eslint-disable react/jsx-key */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 04-10-2022 10:40:13
 * @modify date 10-10-2022 18:05:20
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import { showDialog } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';

import { settingsConstants } from '@hp/mainstore';
import { Button } from '@hp/components';
import { Input } from '@hp/components';

const ServerProcess = () => {
  const dispatch = useDispatch();
  const { ServerProcessDetails, ServerProcessDetailsResponse } = useSelector(
    (store) => store.settings
  );

  const [formDetails, setFormDetails] = useState([]);

  // Clear Notification on exit
  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch({
        type: settingsConstants.LOGOUT_USER_SESSION_SUCCESS,
        removeNotification,
      });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    ServerProcessDetails && ServerProcessDetails.value
      ? setFormDetails(ServerProcessDetails.value)
      : '';
  }, [ServerProcessDetails]);

  useEffect(() => {
    if (
      ServerProcessDetailsResponse &&
      ServerProcessDetailsResponse.value &&
      ServerProcessDetailsResponse.value !== undefined
    ) {
      funcToSetResponseMessage(
        ServerProcessDetailsResponse.value.type,
        ServerProcessDetailsResponse.value.text
      );
    }
    getServerProcessDetails();
  }, [ServerProcessDetailsResponse]);

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    dispatch(
      showDialog({
        showPopup: false,
        type: type,
        responseMessage: resMessage,
        canClose: true,
        autoHide: true,
      })
    );
  };

  // Fetch User Session Form
  const getServerProcessDetails = () => {
    dispatch(getServerProcessDetails());
  };

  const handleLogout = () => {
    // let newObj = {
    //   userIdList: [2],
    //   logoutType: 'BATCH_USERID',
    // };
    // dispatch(settingsAction.logoutUserSession(newObj));
  };

  const handleOnSearch = (event, uniqueKey) => {
    // console.log('hi', event.target.value, uniqueKey);
  };
  const handleClientSelect = (event, uniqueKey) => {
    // console.log('hi', event.target.value, uniqueKey);
  };
  const handleUserSelect = (event, uniqueKey) => {
    // console.log('hi', event.target.value, uniqueKey);
  };

  const onChangeHandlingFunctions = {
    handleOnSearch,
    handleClientSelect,
    handleUserSelect,
  };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val) => {
                return {
                  selector: val.selector,
                  width: val.width,
                  name: val.displayName,
                  cell:
                    val.selector === 'logoutButton'
                      ? function displayCell(row) {
                          return (
                            <Button
                              onClick={handleLogout(row)}
                              className="default button"
                            >
                              Logout
                            </Button>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input key={index} formType={element} dataTableColumn={columns} />
            );
          } else if (element.uniqueKey === 'search') {
            return (
              <div className="three-col-layout">
                <div style={{ width: 200 }}>
                  <Input
                    key={index}
                    formType={element}
                    isEditable="notShowing"
                    onChangeHandler={(element, event) => {
                      onChangeHandlingFunctions[element.onChangeFunction](
                        event,
                        element.uniqueKey
                      );
                    }}
                  />
                </div>
                <div style={{ width: 300 }}>
                  {data.map(
                    (element) =>
                      element.uniqueKey === 'clientName' && (
                        <Input
                          formType={element}
                          isEditable="notShowing"
                          onChangeHandler={(element, event) => {
                            onChangeHandlingFunctions[element.onChangeFunction](
                              event,
                              element.uniqueKey
                            );
                          }}
                        />
                      )
                  )}
                </div>
                <div style={{ width: 300 }}>
                  {data.map(
                    (element, index) =>
                      element.uniqueKey === 'userName' && (
                        <Input
                          key={index}
                          formType={element}
                          isEditable="notShowing"
                          onChangeHandler={(element, event) => {
                            onChangeHandlingFunctions[element.onChangeFunction](
                              event,
                              element.uniqueKey
                            );
                          }}
                        />
                      )
                  )}
                </div>
              </div>
            );
          } else if (element.type === 'PageTitle') {
            return (
              <Input key={index} formType={element} isEditable="notShowing" />
            );
          }
        })
      : null;
  };

  return (
    <div className="">
      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}
    </div>
  );
};
export { ServerProcess };
