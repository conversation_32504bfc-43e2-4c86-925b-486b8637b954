/* eslint-disable no-unused-vars */
/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable no-dupe-keys */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 23-07-2021 13:49:31
 * @modify date 12-11-2021 13:43:42
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';

import { Scrollbars } from 'react-custom-scrollbars';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { AP_USER } from '@hp/constants';
import {
  getMenuTree,
  deleteButtonConfirm,
  getButtonsForMenu,
  deleteButtonFromMenu,
  saveButtonToMenu,
  editButton,
  moveButtonUpAndDown,
  updateButtonsSettings,
  resetupdateButtonsSettings,
  resetgetButtonsForMenu,
  resetsaveButtonToMenu,
  reseteditButton,
  resetdeleteButton,
  resetmoveButtonUpAndDown,
  resetdeleteButtonConfirm,
  showDialog,
} from '@hp/mainstore';
import { useAppRouterDom } from '@hp/utils';
import {
  AlertModalPopup,
  ButtonCommon,
  useConfirm,
} from '@hp/components/common';
import {
  CheckBoxInput,
  RadioInput,
  Select,
  TextInput,
} from '@hp/components/ui';
import { Button } from 'antd';
import { globalutils } from '@hp/components'

const ButtonCreation = () => {
  const { domParameters } = useAppRouterDom();
  // import { useAppRouterDom } from '@hp/utils';
  const urlParam = domParameters?.parameters || '';
  let module = urlParam;

  if (module && module.indexOf(',') > -1) {
    let moduleList = urlParam.split(',');
    module = moduleList[1];
  }

  const [isDisable, setDisable] = useState(false);
  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  const {
    menuButtondata,
    saveButton,
    editButtonNotification,
    deleteButtonNotification,
    deleteButtonConfirmNotif,
    moveUpAndDown,
    updateButtonsResponse,
  } = useSelector((store) => store.admin);

  const { menuTree } = useSelector((store) => store.portalAdmin);
  //combo box for button types
  const buttonType = [
    {
      buttonTypeId: 1,
      buttonType: 'default mr20 fl button',
    },
    {
      buttonTypeId: 2,
      buttonType: 'white mr20 fl button',
    },
    {
      buttonTypeId: 3,
      buttonType: 'secondary mr20 fl button',
    },
    {
      buttonTypeId: 4,
      buttonType: 'outline mr20 fl button',
    },
    {
      buttonTypeId: 5,
      buttonType: 'error mr20 fl button',
    },
    {
      buttonTypeId: 6,
      buttonType: 'secondary-error mr20 fl button',
    },
    {
      buttonTypeId: 7,
      buttonType: 'default mr20 fr button',
    },
    {
      buttonTypeId: 8,
      buttonType: 'white mr20 fr button',
    },
    {
      buttonTypeId: 9,
      buttonType: 'secondary mr20 fr button',
    },
    {
      buttonTypeId: 10,
      buttonType: 'outline mr20 fr button',
    },
    {
      buttonTypeId: 11,
      buttonType: 'error mr20 fr button',
    },
    {
      buttonTypeId: 12,
      buttonType: 'secondary-error mr20 fr button',
    },
    {
      buttonTypeId: 13,
      buttonType: 'small default mr20 fl button',
    },
    {
      buttonTypeId: 14,
      buttonType: 'small white mr20 fl button',
    },
    {
      buttonTypeId: 15,
      buttonType: 'small secondary mr20 fl button',
    },
    {
      buttonTypeId: 16,
      buttonType: 'small outline mr20 fl button',
    },
    {
      buttonTypeId: 17,
      buttonType: 'small error mr20 fl button',
    },
    {
      buttonTypeId: 18,
      buttonType: 'small secondary-error mr20 fl button',
    },
    {
      buttonTypeId: 19,
      buttonType: 'small default mr20 fr button',
    },
    {
      buttonTypeId: 20,
      buttonType: 'small white mr20 fr button',
    },
    {
      buttonTypeId: 21,
      buttonType: 'small secondary mr20 fr button',
    },
    {
      buttonTypeId: 22,
      buttonType: 'small outline mr20 fr button',
    },
    {
      buttonTypeId: 23,
      buttonType: 'small error mr20 fr button',
    },
    {
      buttonTypeId: 24,
      buttonType: 'small secondary-error mr20 fr button',
    },
  ];

  let rowID = 0;
  let user = globalutils.getDataFromStorage('all');
  const userId = user.userId;

  //this state is to set buttons details to Button dto
  const [state, setState] = useState({
    buttonId: '',
    funcPath: '',
    label: '',
    menuId: '',
    orderId: '',
    parameter: '',
    type: '',
    accessFlag: '',
    labelEn: '',
    disable: '',
  });
  const [saveOrEdit, setSaveOrEdit] = useState('');
  const [buttonsEnable, setButtonsEnable] = useState(false);
  const [menus, setMenus] = useState([]);
  const [toggleState, setToggleState] = useState([]);
  const [fieldDisabled, setFieldDisabled] = useState(true);
  const [buttonState, setButtonState] = useState([]);
  const [buttonData, setButtonData] = useState([]);
  const [buttonDataId, setButtonDataId] = useState();
  const [rowClickedID, setRowClickedID] = useState(0);
  const [buttonAlertModal, setButtonAlertModal] = useState(false);
  const [buttonAlertMessage, setButtonAlertMessage] = useState('');

  useEffect(() => {
    dispatch(getMenuTree(module));
    return () => {
      dispatch(resetupdateButtonsSettings());
      dispatch(resetgetButtonsForMenu());
      dispatch(resetsaveButtonToMenu());
      dispatch(reseteditButton());
      dispatch(resetdeleteButton());
      dispatch(resetmoveButtonUpAndDown());
      dispatch(resetdeleteButtonConfirm());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (menuTree && menuTree.value) {
      setMenus([menuTree.value]);
    }
  }, [menuTree]);

  useEffect(() => {
    if (
      moveUpAndDown &&
      moveUpAndDown.value &&
      moveUpAndDown.value !== undefined
    ) {
      if (moveUpAndDown.value.buttonAndLanguageDtoList) {
        setButtonState(moveUpAndDown.value.buttonAndLanguageDtoList);
        setButtonData(moveUpAndDown.value.buttonAndLanguageDtoList);
      }
      funcToSetResponseMessage(
        moveUpAndDown.value.type,
        moveUpAndDown.value.text
      );
    }
  }, [moveUpAndDown]);

  useEffect(() => {
    if (saveButton && saveButton.value && saveButton.value !== undefined) {
      if (saveButton.value.buttonAndLanguageDtoList) {
        setButtonState(saveButton.value.buttonAndLanguageDtoList);
        setButtonData(saveButton.value.buttonAndLanguageDtoList);
      }
      funcToSetResponseMessage(saveButton.value.type, saveButton.value.text);
    }
  }, [saveButton]);

  useEffect(() => {
    if (
      editButtonNotification &&
      editButtonNotification.value &&
      editButtonNotification.value !== undefined
    ) {
      if (editButtonNotification.value.buttonAndLanguageDtoList) {
        setButtonState(editButtonNotification.value.buttonAndLanguageDtoList);
        setButtonData(editButtonNotification.value.buttonAndLanguageDtoList);
      }
      funcToSetResponseMessage(
        editButtonNotification.value.type,
        editButtonNotification.value.text
      );
    }
  }, [editButtonNotification]);

  useEffect(() => {
    if (
      deleteButtonNotification &&
      deleteButtonNotification.value &&
      deleteButtonNotification.value !== undefined
    ) {
      if (deleteButtonNotification.value.context) {
        setButtonAlertModal(true);
        setButtonAlertMessage(deleteButtonNotification.value.text);
      } else {
        if (deleteButtonNotification.value.buttonAndLanguageDtoList) {
          setButtonState(
            deleteButtonNotification.value.buttonAndLanguageDtoList
          );
          setButtonData(
            deleteButtonNotification.value.buttonAndLanguageDtoList
          );
        } else setButtonState([]);
        funcToSetResponseMessage(
          deleteButtonNotification.value.type,
          deleteButtonNotification.value.text
        );
      }
    }
  }, [deleteButtonNotification]);

  useEffect(() => {
    if (
      deleteButtonConfirmNotif &&
      deleteButtonConfirmNotif.value &&
      deleteButtonConfirmNotif.value !== undefined
    ) {
      if (deleteButtonConfirmNotif.value.buttonAndLanguageDtoList) {
        setButtonState(deleteButtonConfirmNotif.value.buttonAndLanguageDtoList);
        setButtonData(deleteButtonConfirmNotif.value.buttonAndLanguageDtoList);
      } else setButtonState([]);
      funcToSetResponseMessage(
        deleteButtonConfirmNotif.value.type,
        deleteButtonConfirmNotif.value.text
      );
    }
  }, [deleteButtonConfirmNotif]);

  useEffect(() => {
    if (
      updateButtonsResponse &&
      updateButtonsResponse.value &&
      updateButtonsResponse.value !== undefined
    ) {
      funcToSetResponseMessage(
        updateButtonsResponse.value.type,
        updateButtonsResponse.value.text
      );
    }
  }, [updateButtonsResponse]);

  useEffect(() => {
    if (menuButtondata && menuButtondata.value && menuButtondata.value.length) {
      setButtonState(menuButtondata.value);

      setButtonData(menuButtondata.value);
    } else {
      setButtonState([]);
    }
  }, [menuButtondata]);

  const handleProceed = () => {
    buttonDataId > 0 &&
      dispatch(deleteButtonConfirm({ buttonId: buttonDataId, module, userId }));
    setState({
      label: '',
      parameter: '',
      funcPath: '',
      type: '',
      disable: '',
      menuId: state.menuId,
      disable: '',
    });
    setButtonAlertModal(false);
    dispatch(resetdeleteButton());
  };

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    dispatch(
      showDialog({
        showPopup,
        type: type,
        responseMessage: resMessage,
        canClose,
      })
    );
  };

  //on clicking a particular menu in menutree we ll get the buttons.
  const rowClicked = (event, id, menu) => {
    event.stopPropagation();
    setRowClickedID(id);
    toggleState[id] = toggleState[id] ? false : true;
    setToggleState(cloneDeep(toggleState));
    setButtonsEnable(true);
    let menuId = menu.menuId;
    setState({
      buttonId: '',
      label: '',
      parameter: '',
      funcPath: '',
      type: '',
      menuId: menuId,
      orderId: '',
      disable: '',
    });
    setButtonDataId('');
    if (menuId > 0) {
      dispatch(getButtonsForMenu({ menuid: menuId, module }));
    }
  };

  //handling the fields
  const handleOnChange = (event) => {
    let button = cloneDeep(state);
    button[event.target.name] = event.target.value;
    setState(button);
  };

  const addButtonToMenu = () => {
    setFieldDisabled(false);
    setSaveOrEdit('save');
    setState({
      buttonId: '',
      label: '',
      parameter: '',
      funcPath: '',
      type: '',
      disable: false,
      menuId: state.menuId,
    });
    setButtonDataId('');
  };

  const deleteButton = async (buttonId) => {
    const isConfirmed = await confirm(' Are you sure you want to delete? ');
    if (isConfirmed) {
      if (buttonId > 0) {
        dispatch(deleteButtonFromMenu({ buttonId, module, userId }));
        setState({
          label: '',
          parameter: '',
          funcPath: '',
          type: '',
          disable: '',
          menuId: state.menuId,
        });
      }
    }
  };

  // const editButton = async (buttonId) => {

  //       setState({
  //         label: '',
  //         parameter: '',
  //         funcPath: '',
  //         type: '',
  //         disable: '',
  //         menuId: state.menuId,
  //       });
  //     }

  // };

  const handleSave = () => {
    if (state.label === '') {
      funcToSetResponseMessage('error', 'Please add button label.');
    } else if (saveOrEdit === 'save') {
      dispatch(saveButtonToMenu({ state, module, userId }));
      setState({
        buttonId: '',
        label: '',
        parameter: '',
        funcPath: '',
        type: '',
        disable: '',
        menuId: state.menuId,
      });
      setButtonDataId('');
    } else if (saveOrEdit === 'edit') {
      dispatch(editButton({ state, module, userId }));
      setFieldDisabled(true);
    }
  };

  const moveMenuUpOrDown = (moveUpOrDown) => {
    if (moveUpOrDown) {
      dispatch(
        moveButtonUpAndDown({
          orderId: state.orderId,
          moveUpOrDown,
          menuId: state.menuId,
          module,
          userId,
        })
      );
    }
  };

  const updateSettings = () => {
    dispatch(updateButtonsSettings({ menuId: state.menuId, module, userId }));
  };

  const nullChecker = (stateData) => {
    if (stateData === null) {
      return '';
    } else {
      return stateData;
    }
  };

  const handleCancel = () => {
    if (state.buttonId === '') {
      setState({
        label: '',
        parameter: '',
        funcPath: '',
        type: '',
        disable: '',
      });
      setButtonDataId('');
    } else {
      dispatch(getButtonsForMenu({ menuid: state.menuId, module: module }));
      let textData = buttonData.filter(
        (data) => data.buttonId === buttonDataId
      );
      setState({
        label: nullChecker(textData[0]?.label),
        parameter: nullChecker(textData[0]?.parameter),
        funcPath: nullChecker(textData[0]?.funcPath),
        type: nullChecker(textData[0]?.type),
        disable: nullChecker(textData[0]?.disable),
      });
    }
    setFieldDisabled(true);
  };

  const handleDisabledCheckBox = (event) => {
    //setIsCheckeddisabled(!isCheckeddisabled);
    let flagValue = cloneDeep(state);

    // flagValue.disable = !flagValue.disable;
    flagValue.disable = event.target.checked;
    setState(flagValue);
    // let button = cloneDeep(state);
    // button[event.target.name] = event.target.value;
    // setState(button);
  };

  const functionsName = {
    handleSave,
    handleCancel,
    updateSettings,
  };

  const RowPrint = (props) => {
    return props.data && props.data.length
      ? props.data.map((data, index) => {
          rowID++;

          let currentRowID = rowID;
          let newParent = props.parent + '[' + index + '].menuList';
          let currentToggleState = toggleState[currentRowID] ? 'open' : 'close';
          let hasChildren =
            data.childMenu && data.childMenu.length
              ? 'has-children'
              : 'no-children';

          return (
            <div
              onClick={(e) => rowClicked(e, currentRowID, data)}
              key={index}
              id={'row-' + currentRowID}
              className={[
                'tr',
                currentRowID === rowClickedID ? 'active-tr' : '',
                currentToggleState,
              ].join(' ')}
            >
              <div className="flex-row">
                <div className={['td', 'menu', hasChildren].join(' ')}>
                  {data.menuName}
                </div>
              </div>
              {data.childMenu && data.childMenu.length ? (
                <RowPrint data={data.childMenu} parent={newParent} />
              ) : null}
            </div>
          );
        })
      : null;
  };

  return (
    <>
      <div className="page-title">Buttons Management</div>
      <div className="two-col-layout mb20">
        <div className="col">
          <div className="card mb20">
            <Scrollbars
              // This will activate auto-height
              autoHeight
              autoHeightMin={100}
              autoHeightMax={830}
            >
              <div className="access-control-table">
                <div className="thead">
                  <div className="tr flex-row"></div>
                </div>
                <div className="tbody">
                  {menus && menus.length ? (
                    <RowPrint data={menus} parent={''} />
                  ) : (
                    ''
                  )}
                </div>
              </div>
            </Scrollbars>
          </div>
        </div>
        <div className="col">
          <div className="card">
            <div className="page-sub-title flex-row">
              Action Items / Buttons
            </div>
            {buttonState && buttonState.length > 0 ? (
              <div className="card-3 mb20">
                <div className="page-sub-title">Buttons</div>

                <div className="access-control-table">
                  <div className="tbody">
                    {buttonState.map((data, idx) => {
                      return (
                        <label
                          className="flex-row button-row-tr"
                          key={idx}
                          onClick={() => {
                            setState({
                              buttonId: data.buttonId,
                              label: nullChecker(data.label),
                              parameter: nullChecker(data.parameter),
                              funcPath: nullChecker(data.funcPath),
                              type: nullChecker(data.type),
                              menuId: data.menuId,
                              orderId: data.orderId,
                              disable: data.disable,
                            });
                            setButtonDataId(data.buttonId);
                          }}
                        >
                          <div className="left-controls">
                            <div className="td">
                              <RadioInput className="mr8" name="buttonName" />
                            </div>
                            <div className="td">
                              <Button className={data.type}>
                                {data.label}
                              </Button>
                            </div>
                          </div>
                          <div className="right-controls">
                            <div className="td mr20">
                              <a>
                                <i
                                  className="icon-edit-button"
                                  onClick={() => {
                                    setFieldDisabled(false);
                                    setSaveOrEdit('edit');
                                  }}
                                  //onClick={() => editButton(data.buttonId)}
                                ></i>
                              </a>
                            </div>
                            <div className="td">
                              <a>
                                <i
                                  className="icon icon-delete"
                                  onClick={() => deleteButton(data.buttonId)}
                                ></i>
                              </a>
                            </div>
                          </div>
                        </label>
                      );
                    })}
                  </div>
                </div>
              </div>
            ) : null}
            <div className="card-3 mb20">
              <div className="two-col-layout">
                <div className="col mb20">
                  <TextInput
                    label="Button Label"
                    name="label"
                    disabled={fieldDisabled}
                    value={state ? state.label : ''}
                    onChange={handleOnChange}
                  />
                </div>
                <div className="col mb20">
                  <TextInput
                    label="Function Path"
                    name="funcPath"
                    disabled={fieldDisabled}
                    value={state ? state.funcPath : ''}
                    onChange={handleOnChange}
                  />
                </div>
              </div>
              <div className="two-col-layout">
                <div className="col  ">
                  <Select
                    label="Type"
                    className="mb20"
                    name="type"
                    options={
                      buttonType
                        ? buttonType.map((value) => {
                            return {
                              value: value.buttonType,
                              display: value.buttonType,
                            };
                          })
                        : []
                    }
                    value={state ? state.type : ''}
                    disabled={fieldDisabled}
                    onChange={handleOnChange}
                  />
                  <CheckBoxInput
                    checked={state.disable === true ? true : ''}
                    label="Disable"
                    onChange={handleDisabledCheckBox}
                  />
                </div>
                <div className="col">
                  <TextInput
                    label="Parameter"
                    className="mb20"
                    name="parameter"
                    disabled={fieldDisabled}
                    value={state ? state.parameter : ''}
                    onChange={handleOnChange}
                  />
                </div>
              </div>
            </div>
            <div>
              <button
                style={{ cursor: 'pointer' }}
                className="move-up-down-button mr24"
                onClick={() => addButtonToMenu()}
              >
                <span className="icon-add-button "></span> Add Button
              </button>
              {buttonState && buttonState.length > 0 && (
                <span>
                  <button
                    style={{ cursor: 'pointer' }}
                    className="move-up-down-button mr24"
                    onClick={() => moveMenuUpOrDown('moveUp')}
                  >
                    <span className="icon-up-arrow"></span>
                    Move Up
                  </button>
                  <button
                    style={{ cursor: 'pointer' }}
                    className="move-up-down-button"
                    onClick={() => moveMenuUpOrDown('moveDown')}
                  >
                    <span className="icon-down-arrow"></span>
                    Move Down
                  </button>
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
      {buttonAlertMessage && (
        <AlertModalPopup
          isAlertOpen={buttonAlertModal}
          setAlertClose={() => setButtonAlertModal(false)}
          message={buttonAlertMessage}
          handleProceed={handleProceed}
          handleCancel={() => setButtonAlertModal(false)}
        />
      )}
      {buttonsEnable === true ? (
        <ButtonCommon functionsName={functionsName} />
      ) : null}
    </>
  );
};
export { ButtonCreation };
