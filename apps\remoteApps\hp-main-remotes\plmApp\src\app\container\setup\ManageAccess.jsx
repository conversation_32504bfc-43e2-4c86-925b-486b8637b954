/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import { Button, Input, Treeview } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import NodeSearchTable from '../item/NodeSearchTable';
import { useEffect, useState } from 'react';
import {
  getPDMItemTree,
  getPLMAccessPrivilegeForm,
  treeView,
} from '@hp/mainstore';
import { globalutils } from '@hp/utils';

const ManageAccess = () => {
  const userId = globalutils.getDataFromStorage('userId');
  const dispatch = useDispatch();
  // const [action, setAction] = useState('');
  // const [node, setNode] = useState('');
  const [accessPrivilegeForm, setAccessPrivilegeForm] = useState(null);

  const { innerMenuName, subMenuName } = useSelector((store) => store.menu);

  const { pdmItemTreeList, plmAccessPrivilegeForm } = useSelector(
    (store) => store.pdm
  );

  useEffect(() => {
    dispatch(getPDMItemTree());
    dispatch(getPLMAccessPrivilegeForm(userId));
  }, []);

  useEffect(() => {
    if (plmAccessPrivilegeForm?.value) {
      setAccessPrivilegeForm(plmAccessPrivilegeForm.value);
    }
  }, [plmAccessPrivilegeForm]);

  useEffect(() => {
    if (pdmItemTreeList?.value?.length) {
      dispatch(treeView(pdmItemTreeList.value));
    }
  }, [pdmItemTreeList]);

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'userItemAccess') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val, idx) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'deleteIcon'
                      ? '4%'
                      : val.selector === 'serialNo'
                      ? '5%'
                      : '',
                  name: val.displayName,
                };
              });
            return (
              <Input
                key={index}
                dataTablePersistHead={true}
                indexKey={index}
                formType={element}
                dataTableColumn={columns}
              />
            );
          } else if (element.uniqueKey === 'addUser') {
            return (
              <Button
                key={index}
                className="small mb8 outline add-button-custom flex-row vam"
              >
                <i className="icon-add-button"></i>Add User
              </Button>
            );
          }
          return null; // Added default return
        })
      : null;
  };

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      <div
        className="two-col-layout"
        style={{
          alignItems: 'stretch',
        }}
      >
        <div
          className="card"
          style={{
            flex: 1,
            minHeight: '70vh',
            overflow: 'auto',
          }}
        >
          <Treeview
            form={(action, node) => {
              // setAction(action);
              // setNode(node);
            }}
            // parameter={parameters}
            handleScrollToTop={handleScrollToTop}
          />
        </div>
        <div
          className="card"
          style={{
            flex: 2,
            minHeight: '70vh',
            overflow: 'auto',
          }}
        >
          Description
          <div>
            {accessPrivilegeForm && accessPrivilegeForm.length
              ? formControlsBinding(accessPrivilegeForm)
              : ''}
          </div>
        </div>
      </div>
    </>
  );
};

export { ManageAccess };
