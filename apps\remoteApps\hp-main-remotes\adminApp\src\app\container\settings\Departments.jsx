/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-unused-vars */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 23-03-2022 15:40:13
 * @modify date 2024-08-01 15:18:03
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
// import {
//   Input,
//   Button,
//   useConfirm,
//   dataTableServiceProvider,
// } from '@hp/components';
import { AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { settingsConstants } from '@hp/mainstore';
import { dataTableServiceProvider, Input, useConfirm } from '@hp/components';
import { Button } from '@hp/components';
import {
  addDepartmentFormDetails,
  deleteDepartmentFormDetails,
  editDepartmentFormDetails,
  getDepartmentFormDetail,
  deptFormDetailsResponseClear,
} from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import { globalutils } from '@hp/components';
const Departments = () => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { deptFormDetails, deptFormDetailsResponse } = useSelector(
    (store) => store.settings
  );

  let user = globalutils.getDataFromStorage('all');
  const clientIdVal = user.clientId;
  const [clientId, setClientId] = useState(clientIdVal);
  const [entityId, setEntityId] = useState(0);
  const [formDetails, setFormDetails] = useState([]);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [selected, setSelected] = useState({});
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [notify, setnotify] = useState(null);
  const [tableData, setTableData] = useState([]);

  // Fetch Department details form
  const getDepartmentFormDetailsFn = (clientId, entityId) => {
    dispatch(
      getDepartmentFormDetail({ clientId: clientId, entityId: entityId })
    );
  };

  useEffect(() => {
    if (deptFormDetails && deptFormDetails.value) {
      setFormDetails(deptFormDetails.value);
      setTableData(
        deptFormDetails.value.find((val) => val.uniqueKey === 'dataTable').value
      );
    }
  }, [deptFormDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch(deptFormDetailsResponseClear(removeNotification));
      // dispatch({
      //   type: settingsConstants.EDIT_DEPARTMENT_FORM_SUCCESS,
      //   removeNotification,
      // });
      // dispatch({
      //   type: settingsConstants.DELETE_DEPARTMENT_FORM_SUCCESS,
      //   removeNotification,
      // });
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (
      deptFormDetailsResponse &&
      deptFormDetailsResponse.value &&
      deptFormDetailsResponse.value !== undefined
    ) {
      funcToSetResponseMessage(
        deptFormDetailsResponse.value.type,
        deptFormDetailsResponse.value.text
      );
    }
    getDepartmentFormDetailsFn(clientId, entityId);
  }, [deptFormDetailsResponse]);

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let deptNotExist = true;
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'name' || element.uniqueKey === 'code') {
            if (element.value === null || element.value.trim() === '') {
              deptNotExist = false;
              element.uniqueKey === 'name'
                ? funcToSetResMessageInModal(
                    'error',
                    'Please Enter Department Name'
                  )
                : funcToSetResMessageInModal(
                    'error',
                    'Please Enter Department Code'
                  );
            } else {
              tempArray.map((item) => {
                if (item.uniqueKey === 'dataTable') {
                  item.value.map((val) => {
                    if (val.deptName === element.value) {
                      if (selected.deptName === element.value) {
                        return;
                      } else {
                        deptNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Department Name already exists'
                        );
                      }
                    }
                    if (val.deptCode === element.value) {
                      if (selected.deptCode === element.value) {
                        return;
                      } else {
                        deptNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Department Code already exists'
                        );
                        return;
                      }
                    }
                  });
                }
              });
            }
          }
        });
    });
    if (deptNotExist) {
      edit
        ? dispatch(
            editDepartmentFormDetails({
              formDetails: formDetails,
              deptId: selected.deptId,
            })
          )
        : dispatch(addDepartmentFormDetails(formDetails));
      setShowModal(false);
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.disableFlag = 'F';
          element.value = '';
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setEdit(true);
    setSelected(obj);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          entry.uniqueKey === 'name' ? (entry.value = obj.deptName) : '';
          entry.uniqueKey === 'code' ? (entry.value = obj.deptCode) : '';
          entry.uniqueKey === 'description' ? (entry.value = obj.deptDesc) : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.deptId,
      'deptId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.deptId
      ? handleConfirm(obj.deptId)
      : funcToSetResponseMessage(
          'error',
          'Please Select a Department to Delete'
        );
  };

  //Function to confirm delete
  const handleConfirm = async (deptId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteDepartmentFormDetails(deptId));
      setDisable(false);
      setShowModal(false);
      setSelected({});
    }
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event.target.value;
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Select a value in the select combo-box
  const handleSelect = (event, uniqueKey) => {
    if (uniqueKey === 'clientSelect') {
      getDepartmentFormDetailsFn(event.target.value, 0);
      setClientId(event.target.value);
    } else {
      getDepartmentFormDetailsFn(clientId, event.target.value);
      setEntityId(event.target.value);
    }
    setDisable(false);
    setConditionalRowStyles([]);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleSelect,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelected(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'T';
          entry.uniqueKey === 'name' ? (entry.value = obj.deptName) : '';
          entry.uniqueKey === 'code' ? (entry.value = obj.deptCode) : '';
          entry.uniqueKey === 'description' ? (entry.value = obj.deptDesc) : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.deptId,
      'deptId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  // const handleFilterData = (filterData) => {
  //   let tempArray = cloneDeep(formDetails);
  //   tempArray.map((element) =>
  //     element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
  //   );
  //   setFormDetails(tempArray);
  // };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'pageSubtitle') {
            while (showModal) {
              return (
                <Modal
                  key={index}
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                >
                  <div
                    onClick={() => {
                      handleCancel();
                      setEdit(false);
                      setSelected({});
                      setConditionalRowStyles([]);
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div className="boxed" style={{ margin: 0 }}>
                    <Input key={index} formType={element} />
                    <div>
                      {formControlsBinding(
                        element.formSubDetailsInternalDTOList
                      )}
                    </div>
                    {showButtons && (
                      <div>
                        <Button
                          onClick={() => handleSave()}
                          className="default mr20"
                        >
                          Save
                        </Button>
                        <Button
                          onClick={() => handleCancel()}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-bar',
                        'type-' + notify.type,
                      ].join(' ')}
                      style={{ position: 'sticky !important', width: 90 + '%' }}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'add_button') {
            return (
              <div style={{ float: 'right', marginBottom: 10 + 'px' }}>
                <Button
                  onClick={() => {
                    handleCancel();
                    setSelected({});
                    setConditionalRowStyles([]);
                    setEdit(false);
                    setShowModal(true);
                    setShowButtons(true);
                  }}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-add-button "> </i>Add
                </Button>
              </div>
            );
          } else if (element.type === 'Select') {
            if (element.uniqueKey === 'entitySelect') {
              return (
                <div className="three-col-layout">
                  {/* <div style={{ width: '200px', marginTop: '25px' }}>
                    <Input
                      formType={data.find(
                        (item) => item.uniqueKey === 'search'
                      )}
                      isEditable="notShowing"
                      tableListData={tableData}
                      getFilteredData={(filterData) =>
                        handleFilterData(filterData)
                      }
                    />
                  </div> */}
                  {clientIdVal === 1 && (
                    <div className="col">
                      {data.map((entry, index) => {
                        return (
                          entry.uniqueKey === 'clientSelect' && (
                            <Input
                              key={index}
                              isEditable={'notShowing'}
                              formType={entry}
                              onChangeHandler={(element, event) => {
                                onChangeHandlingFunctions[
                                  element.onChangeFunction
                                ](event, element.uniqueKey);
                              }}
                            />
                          )
                        );
                      })}
                    </div>
                  )}
                  <div className="col">
                    <Input
                      key={index}
                      formType={element}
                      isEditable={'notShowing'}
                      onChangeHandler={(element, event) => {
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey
                        );
                      }}
                    />
                  </div>
                </div>
              );
            }
          } else if (element.uniqueKey === 'search') {
            return;
          } else if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else {
            return (
              <Input
                key={index}
                disabledFlag={disable}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey
                  );
                }}
              />
            );
          }
        })
      : null;
  };

  return (
    <div className="">
      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}
    </div>
  );
};
export { Departments };
