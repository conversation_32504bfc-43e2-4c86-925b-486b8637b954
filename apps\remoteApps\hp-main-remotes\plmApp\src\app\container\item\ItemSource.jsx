/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useEffect, useState } from 'react';
import {
  showDialog,
  setSubRowColumn,
  pdmDeleteSupplier,
  getPrimaryFlagValidation,
  setPrimarySupplierFlag,
  getItemSource,
  getPLMItemSourceTotalPOIssuedList,
  pdmConstants,
} from '@hp/mainstore';
import { useSelector, useDispatch } from 'react-redux';
import {
  Input,
  CheckBoxInput,
  Button,
  useConfirm,
  globalutils,
} from '@hp/components';
import Modal from 'react-modal';
import SupplierSearch from './SupplierSearch';

import ItemSourceEditForm from './ItemSourceEditForm';
import { Tooltip, Button as AntdButton } from 'antd';

function ItemSource(props) {
  const { itemId } = props;
  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  const [sourceForm, setSourceForm] = useState();
  const [coustomColumn, setCoustomColumn] = useState(false);
  const [supplierSearchForm, setSupplierSearchForm] = useState([]);
  const [selectedItemSupplier, setSelectedItemSupplier] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [keys, setKeys] = useState('');
  const [isModal, setIsModal] = useState(false);
  const [open, setOpen] = useState(false);
  const [modalData, setModalData] = useState(null);

  const {
    itemSource,
    primaryFlagForm,
    primaryFlagRes,
    addVendorRes,
    deleteVendorRes,
    plmItemSourceTotalPOIssuedList,
  } = useSelector((store) => store.pdm);

  const userId = globalutils.getDataFromStorage('userId');

  useEffect(() => {
    if (primaryFlagForm && primaryFlagForm.value) {
      primaryConfirm(primaryFlagForm.value);
    }
    return () => {
      dispatch({
        type: pdmConstants.PRIMARY_FLAG_SET_FORM_SUCCESS,
        payload: null,
      });
    };
  }, [primaryFlagForm]);

  useEffect(() => {
    if (primaryFlagRes && primaryFlagRes.value) {
      funcToSetResponseMessage('success', primaryFlagRes.value);
      dispatch(
        getItemSource({
          itemId: itemId,
          obj: { supplierName: '' },
        })
      );
    }
    return () => {
      dispatch({
        type: pdmConstants.SET_PRIMARY_FLAG_SUCCESS,
        payload: null,
      });
    };
  }, [primaryFlagRes]);

  const handleUpdate = (updatedData) => {
    setIsModal(false);
  };

  useEffect(() => {
    dispatch(
      getItemSource({
        itemId: itemId,
        obj: { supplierName: '' },
      })
    );
  }, [itemId]);

  useEffect(() => {
    if (addVendorRes && addVendorRes.value) {
      funcToSetResponseMessage('success', addVendorRes.value);
      dispatch(
        getItemSource({
          itemId: itemId,
          obj: { supplierName: '' },
        })
      );
    }
    setIsModal(false);
  }, [addVendorRes]);

  useEffect(() => {
    if (deleteVendorRes && deleteVendorRes.value) {
      funcToSetResponseMessage('success', deleteVendorRes.value);
      dispatch(
        getItemSource({
          itemId: itemId,
          obj: { supplierName: '' },
        })
      );
    }
    return () =>
      dispatch({
        type: pdmConstants.DELETE_SUPPLIER_SUCCESS,
        payload: null,
      });
  }, [deleteVendorRes]);

  const primaryConfirm = async (msg) => {
    const isConfirmed = await confirm(msg);
    if (isConfirmed) {
      dispatch(setPrimarySupplierFlag(...keys));
    }
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  useEffect(() => {
    if (!coustomColumn && sourceForm) {
      let dataTableSubColumn = sourceForm.formSubDetailsInternalDTOList.filter(
        (list) => list.type === 'ExpandableSubDataTable'
      );
      dataTableSubColumn =
        dataTableSubColumn[0]?.formSubDetailsInternalDTOList.map(
          (value, index) => {
            return {
              width: value.displayWidth ? value.displayWidth : '',
              name: value.displayName ? value.displayName : '',
              selector: value.selector ? value.selector : '',
              center: ['count', 'delete', 'secondary', 'primary'].includes(
                value.selector
              ),
              cell:
                value.selector === 'primary'
                  ? (row) => {
                      return (
                        <CheckBoxInput
                          checked={row.primaryFlag ? true : false}
                          disabled={row.secondaryFlag ? true : false}
                          onChange={() => {
                            setKeys([row.itemSourceId, row.itemId, 'primary']);
                            dispatch(
                              getPrimaryFlagValidation({
                                itemSourceId: row.itemSourceId,
                                itemId: row.itemId,
                                action: 'primary',
                              })
                            );
                          }}
                        />
                      );
                    }
                  : value.selector === 'secondary'
                  ? (row) => {
                      return (
                        <CheckBoxInput
                          checked={row.secondaryFlag ? true : false}
                          disabled={row.primaryFlag ? true : false}
                          onChange={() => {
                            setKeys([
                              row.itemSourceId,
                              row.itemId,
                              'secondary',
                            ]);
                            dispatch(
                              getPrimaryFlagValidation({
                                itemSourceId: row.itemSourceId,
                                itemId: row.itemId,
                                action: 'secondary',
                              })
                            );
                          }}
                        />
                      );
                    }
                  : value.selector === 'delete'
                  ? (row) => {
                      if (row.primaryFlag === 'Y' || row.secondaryFlag === 'Y')
                        return (
                          <div
                            className="icon-2-trash"
                            // disabled={true}
                            onClick={() =>
                              funcToSetResponseMessage('info', 'Not possible')
                            }
                          ></div>
                        );
                      else
                        return (
                          <div
                            className="icon-2-trash"
                            onClick={() =>
                              supplierDeleteHadler(row.itemSourceId, row.itemId)
                            }
                          ></div>
                        );
                    }
                  : value.selector === 'edit'
                  ? (row) => {
                      if (row.primaryFlag === 'Y' || row.secondaryFlag === 'Y')
                        return (
                          <div
                            className="icon-edit-button"
                            disabled={true}
                            onClick={() =>
                              funcToSetResponseMessage('info', 'Not possible')
                            }
                          ></div>
                        );
                      else
                        return (
                          <div
                            className="icon-edit-button"
                            onClick={() =>
                              supplierEditHandler(row.itemSourceId)
                            }
                          ></div>
                        );
                    }
                  : (row) => DisplayTitle(row, value.selector),
            };
          }
        );

      dispatch(setSubRowColumn(dataTableSubColumn));
      setCoustomColumn(true);
    }
  }, [sourceForm]);

  const DisplayTitle = (row, key) => {
    return (
      <Tooltip classNames={{ root: 'ant-tooltip-container' }} title={row[key]}>
        {key === 'count' ? (
          <AntdButton
            color="default"
            variant="outlined"
            className="plm-item-source-count-box"
            onClick={() => handleShowCountDetails(row.itemId)}
          >
            <div className="custom-overflow" key={key}>
              {row[key]}
            </div>
          </AntdButton>
        ) : (
          <div className="custom-overflow" key={key}>
            {row[key]}
          </div>
        )}
      </Tooltip>
    );
  };

  function handleShowCountDetails(itemId) {
    dispatch(
      getPLMItemSourceTotalPOIssuedList({ userId: userId, itemId: itemId })
    );
    setOpen(true);
  }

  useEffect(() => {
    if (plmItemSourceTotalPOIssuedList?.value) {
      setModalData(plmItemSourceTotalPOIssuedList?.value[0] ?? null);
    }
  }, [plmItemSourceTotalPOIssuedList]);

  useEffect(() => {
    let newForm = [];

    if (!itemSource?.value?.length) return;
    itemSource.value.forEach((form) => {
      if (form.uniqueKey === 'itemSourceList') {
        let tempSourceForm = {
          ...form,
          value: Array.isArray(form?.value)
            ? form?.value
            : form?.value
            ? [form.value]
            : [],
        };
        setSourceForm(tempSourceForm);
      } else newForm.push(form);
    });

    setSupplierSearchForm(newForm);
  }, [itemSource]);

  // delete function for supplier
  const supplierDeleteHadler = async (itemSourceId, itemId) => {
    const isConfirmed = await confirm('Confirm Delete');
    if (isConfirmed) {
      dispatch(
        pdmDeleteSupplier({
          itemSourceId: itemSourceId,
          itemId: itemId,
        })
      );
    }
  };
  const supplierEditHandler = (itemSourceId) => {
    setIsEditMode(true);
    setIsModal(true);

    const foundItem = itemSource.value
      .find((f) => f.uniqueKey === 'itemSourceList')
      ?.value?.commonExpandedRowList?.find(
        (item) => item.itemSourceId === itemSourceId
      );
    setSelectedItemSupplier(foundItem ?? null);
  };

  const handleCancel = () => {
    setIsModal(false);
    setIsEditMode(false);
    setSelectedItemSupplier(null);
  };

  return (
    <div>
      {supplierSearchForm.length ? (
        <Button
          className={
            'small mb20 outline add-button-custom flex-row vam button fr'
          }
          onClick={() => setIsModal(true)}
        >
          <i className="icon-add-button "> </i>
          Add Supplier
        </Button>
      ) : (
        ''
      )}
      <Input
        formType={sourceForm}
        dataTablePagination={false}
        dataTablePersistHead
      />
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={open}
        style={{
          content: {
            height: 'min-content',
            maxHeight: ' -webkit-fill-available',
            overflowY: 'hidden',
          },
        }}
      >
        <div
          style={{
            position: 'sticky',
            top: '0px',
            marginLeft: '-40px',
            zIndex: '999',
            width: 'calc(100% + 40px)',
            height: '40px',
            background: '#fff',
            transform: 'translate(40px, -40px)',
          }}
        >
          <div
            style={{
              fontSize: '16px',
              lineHeight: '16px',
              fontWeight: '500',
              color: 'rgb(32, 32, 42)',
              letterSpacing: '-0.02em',
              borderBottom: '1px solid rgb(232, 232, 233)',
              paddingTop: '20px',
              width: 'calc(100% - 40px)',
            }}
          >
            PO Issued
          </div>
          <span
            className="modal-close icon-close"
            onClick={() => setOpen(false)}
            style={{ fontSize: 20 + 'px' }}
          ></span>
        </div>
        {modalData ? (
          <div
            style={{
              position: 'relative',
              top: '-20px',
            }}
          >
            <Input
              formType={modalData}
              dataTablePagination={false}
              dataTablePersistHead
              overrideColDataFromReducer
            />
          </div>
        ) : (
          ''
        )}
        <div
          style={{
            position: 'sticky',
            bottom: '0px',
            zIndex: '999',
            height: '60px',
            background: '#fff',
            transform: 'translateY(40px)',
          }}
        >
          <Button className="outline fr" onClick={() => setOpen(false)}>
            Close
          </Button>
        </div>
      </Modal>
      <Modal
        className="ModalPoMatrix"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
        style={{
          content: {
            height: 'min-content',
            maxHeight: ' -webkit-fill-available',
          },
        }}
      >
        <h1 className="mb16">Supplier Item Details</h1>
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>

        {isEditMode ? (
          <ItemSourceEditForm
            itemSupplier={selectedItemSupplier}
            onUpdate={handleUpdate}
            onCancel={handleCancel}
          />
        ) : (
          <SupplierSearch Searchform={supplierSearchForm} itemId={itemId} />
        )}
      </Modal>
    </div>
  );
}

export { ItemSource };
