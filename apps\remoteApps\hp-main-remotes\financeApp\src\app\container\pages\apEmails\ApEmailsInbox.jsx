/* eslint-disable no-prototype-builtins */
/* eslint-disable array-callback-return */
/* eslint-disable no-lone-blocks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { Scrollbars } from 'react-custom-scrollbars';
import { EmailListing } from '@hp/components';
import { EmailSearch } from '@hp/components';
import { EmailBody } from '@hp/components';
import { EmailEmpty } from '@hp/components';
import { EmailNewCompose } from '@hp/components';
import { EmailPagination } from '@hp/components';
import '@hp/styles/EmailUI.scss';
import '@hp/styles/MultiEmail.scss';
import { AP_USER, AP_emailSubmitId } from '@hp/constants';
import Modal from 'react-modal';
import cloneDeep from 'lodash.clonedeep';
import { Popconfirm } from 'antd';
import { ApEmailOutboxBody } from './ApEmailOutboxBody';
import { EmailDraftSend } from '@hp/components';
import { useAppRouterDom } from '@hp/utils';
import {
  Button,
  getClientFormattedCurrentDate,
  MultiEmailInput,
  RichTextEditor,
  TraceEvents,
  CustomSpinner,
} from '@hp/components';
import { ButtonCommon, CommonSpinner, DocumentViewer } from '@hp/components';

import {
  apEmailUserSearchCount,
  apSendEmail,
  addAndRemoveCategory,
  apEmailDraft,
  showDialog,
  getCount,
  traceEvent,
  addUserComment,
  emailConstants,
  accPayConstants,
  apProcessorUserUpdateClear,
  apEmailInboxs,
  apEmailDelete,
  financeResetStateField,
} from '@hp/mainstore';

import { setEmailPrimaryId } from '@hp/mainstore';
import {
  getApApproverUserList,
  fetchEmailDetailsByDocId,
  getEmailCategoryList,
  emailCategoryAddRemoveSuccessClear,
} from '@hp/mainstore';
import { AP_ID } from '@hp/constants';
import { useLocation } from 'react-router-dom';
import { globalutils } from '@hp/components';

const ApEmailsInbox = (props) => {
  let history = useLocation().pathname;
  const childModalref = useRef();
  let isDraftPage = false;
  const { domParameters, location } = useAppRouterDom();
  let Parameter = [];
  if (domParameters?.parameters) {
    domParameters.parameters.indexOf(',') > -1
      ? (Parameter = domParameters.parameters.split(','))
      : [];

    isDraftPage = Parameter.includes('draft');
  }
  const menuData = location.pathname.split('/')[1] || '';

  const dispatch = useDispatch();
  const {
    inboxData,
    apEmailInboxFail,
    emailInboxDetails,
    apProcessorUserList,
    apEmailCategoryList,
    emailLoading,
    emailCategoryAddRemoveSuccess,
    apProcessorUserUpdate,
    emailSetProcessedFlag,
    emailSendResponse,
    apEmailUserSearchCount,
  } = useSelector((store) => store.email);
  const { innerMenuName } = useSelector((store) => store.menu);
  const { countforAPDasboard, accPaytraceEvents } = useSelector(
    (store) => store.accpay
  );

  let user = globalutils.getDataFromStorage('all');

  const userId = user.userId;
  const clientId = user.clientId;
  const table_name = domParameters?.parameters || '';
  let tableName = [];
  if (table_name.indexOf(',') > -1) {
    tableName = table_name.split(',');
  }

  const [searchString, setSearchString] = useState('');
  const [TableListing, setTableList] = useState();
  const [filteredData, setFilteredData] = useState();
  //  const [tableListLength, setTableListLength] = useState(0);

  const [state, setState] = useState();
  const [isLoading, setLoading] = useState(false);
  const [isModal, setIsModal] = React.useState(false);
  const [filePath, setfilePath] = React.useState('');
  const [newCompose, setNewCompose] = useState(false);
  const [emailReply, setEmailReply] = useState(false);
  const [emailReplyAll, setEmailReplyAll] = useState(false);
  const [forwardEmail, setForwardEmail] = useState(false);
  const [isProcessTabClick, setIsProcessTabClick] = useState(false);

  const [replayType, setReplayType] = useState('');

  const [assignedUser, setAssignedUserName] = useState('');
  const [isProcessedFlag, setIsProcessed] = useState(false);
  const [processTabClick, setProcessTabClick] = useState('UnProcessed');
  const [currentDate, setCurrentDate] = useState();
  const [limit, setLimit] = useState(50);
  const [offset, setOffset] = useState(0);
  const [activeFirst, setActiveFirst] = useState(false);
  const [activeLast, setActiveLast] = useState(false);
  const [processedCount, setProcessedCount] = useState(0);
  const [unProcessedCount, setUnProcessedCount] = useState(0);
  const [traceEvents, setTraceEvents] = useState();
  const [addCommentsValue, setAddCommentsValue] = useState('');
  const [unprocessedPageCount, setUnprocessedPageCount] = useState(0);
  const [processedPageCount, setProcessedPageCount] = useState(0);
  const [isEmailVisible, setIsEmailVisible] = useState(false);
  const [ccOnClick, setCcOnClick] = useState(false);
  const [bccOnClick, setBccOnClick] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [emailPreview, setEmailPreview] = useState();
  const [draftListClicked, setDraftListClicked] = useState(false);
  const [emailIds, setEmailIds] = useState('');
  const [emailSubject, setEmailSubject] = useState();
  const [isSearchMode, setIsSearchMode] = useState(false);

  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [searchedEmailCount, setSearchedEmailCount] = useState(null);
  const [userEmailSearchObject, setUserEmailSearchObject] = useState(null);
  //  const [isProcessTabClick, setIsProcessTabClick] = useState(false);

  let bodyEmpty = false;
  let subjectEmpty = false;
  let toEmpty = false;

  const [buttonFlag, setButtonFlag] = useState();
  const onAddCommentsChangeHandler = (event) => {
    setAddCommentsValue(event.target.value);
  };
  const [buttonEnable, setButtonEnable] = useState(false);
  //function for showing notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    setCurrentDate(getClientFormattedCurrentDate());
    return () => {
      dispatch(
        fetchEmailDetailsByDocId({
          docId: '',
          emailId: '',
          type: 'unsubscribe',
        })
      );
    };
  }, [history]);

  useEffect(() => {
    setAddCommentsValue('');
  }, [emailIds]); // added from v2

  useEffect(() => {
    if (isDraftPage) {
      setEmailPreview('');
    } else {
      setDraftListClicked(false);
    }
  }, [isDraftPage, history]);

  useEffect(() => {
    setLoading(emailLoading);
  }, [emailLoading]);

  useEffect(() => {
    if (emailCategoryAddRemoveSuccess && emailCategoryAddRemoveSuccess.value) {
      funcToSetResponseMessage('success', emailCategoryAddRemoveSuccess.value);
      render();
    }
    let data = null;
    return () => dispatch(emailCategoryAddRemoveSuccessClear(null));
  }, [emailCategoryAddRemoveSuccess, history]);

  const buttonData = useSelector((state) => state.buttons.buttons || null);

  // const setDefaultButtonFlag = (flag) => {
  //   if (buttonData && buttonData !== undefined) {
  //     buttonData.map((btn) => {
  //       if (btn.label === 'Process') {
  //         flag ? (btn.disable = true) : (btn.disable = false);
  //       }
  //     });
  //     setButtonFlag(buttonData);
  //   }
  // };

  useEffect(() => {
    if (
      emailSetProcessedFlag &&
      emailSetProcessedFlag.value.processedSuccessfully
    ) {
      funcToSetResponseMessage('success', emailSetProcessedFlag.value?.message);
      // setDefaultButtonFlag(emailSetProcessedFlag?.value?.processedSuccessfully);
    } else
      funcToSetResponseMessage('error', emailSetProcessedFlag?.value?.message);
  }, [emailSetProcessedFlag]);

  useEffect(() => {
    if (emailReply || emailReplyAll || forwardEmail) {
      const divElements = document.getElementsByClassName('quill ');

      // for (let i = 0; i < divElements.length; i++) {
      //   const divElement = divElements[i];

      //   divElement.style.height = 300 + 'px';
      // }
      setIsEmailVisible(true);
    }
    return () => setIsEmailVisible(false);
  }, [emailReply, emailReplyAll, forwardEmail]);

  useEffect(() => {
    if (apProcessorUserUpdate) {
      funcToSetResponseMessage('success', apProcessorUserUpdate);
      render();
    }
    let data = null;
    return () => dispatch(apProcessorUserUpdateClear(null));
  }, [apProcessorUserUpdate]);

  useEffect(() => {
    if (inboxData) {
      setButtonEnable(false);
      setTableList(inboxData.value);
      setFilteredData(inboxData.value);

      let tempCountMain = countforAPDasboard?.value?.AP_EMAIL?.find(
        (item) => item?.status === Parameter[1]
      );
      let tempUnprocessedPageCount = Math.ceil(
        tempCountMain?.emailUnProcessedCount / limit
      );
      let tempProcessedPageCount = Math.ceil(
        tempCountMain?.emailProcessedCount / limit
      );

      if (searchedEmailCount > 0) {
        tempUnprocessedPageCount = Math.ceil(searchedEmailCount);
        tempProcessedPageCount = Math.ceil(searchedEmailCount);
      }

      if (currentTab === 0) {
        setActiveLast(offset + 1 < tempUnprocessedPageCount ? true : false);
      } else if (currentTab === 1) {
        setActiveLast(offset + 1 < tempProcessedPageCount ? true : false);
      }
      setState('');
    } else if (apEmailInboxFail) {
      apEmailInboxFail.response && apEmailInboxFail.response.data
        ? funcToSetResponseMessage('error', apEmailInboxFail.response.data)
        : null;
    }
    return () => {
      setTableList('');
    };
  }, [inboxData, apEmailInboxFail, currentTab, history]);
  useEffect(() => {
    // Fetch data or perform actions based on the updated processTabClick state
    apEmailInboxData(limit, null, processTabClick, offset);
  }, [processTabClick, history]);

  useEffect(() => {
    setLoading(false);
    setState('');
    if (emailInboxDetails !== undefined && emailInboxDetails != null) {
      setState(emailInboxDetails.value);

      setEmailPreview({
        ...emailPreview,
        subject: emailInboxDetails?.value?.subject,
        toAddressList: [emailInboxDetails?.value?.fromAddress],
        bccList: emailInboxDetails?.value?.bccList,
        ccList: emailInboxDetails?.value?.ccList,
        content: emailInboxDetails?.value?.htmlPart,
      });

      setEmailSubject(emailInboxDetails?.value?.subject);
      setButtonEnable(true);
    }
    return () => {
      setState('');
      setEmailPreview('');
    };
  }, [emailInboxDetails, history]);

  useEffect(() => {
    render();
    setLimit(50);
  }, [props, currentTab, processTabClick, history]);

  const render = () => {
    setFilteredData([]);
    apEmailInboxData(50, null, 'UnProcessed');
    setNewCompose(false);
    dispatch(getApApproverUserList());
    dispatch(getEmailCategoryList());
    dispatch(getCount({ clientId: clientId, userId: userId, APId: AP_ID }));
  };

  useEffect(() => {
    return () => {
      dispatch(
        apEmailInboxs({
          userId,
          clientId,
          tableName: tableName[0],
          category: tableName[1],
          status: tableName[2],
          process: processTabClick,
          limit,
          offset,
          data: null,
          type: 'unsubscribe',
          searchData: null,
        })
      );
      dispatch(
        apSendEmail({
          content: emailPreview?.content,
          subject: emailPreview?.subject,
          toAddress: emailPreview?.toAddressList,
          emailTempId: AP_emailSubmitId,
          invId: null,
          ccAddress: emailPreview?.ccList,
          bccAddress: emailPreview?.bccList,
          userId: userId,
          type: 'unsubscribe',
          file:
            emailInboxDetails?.value?.emailAttachmentDetailsDtoList?.length &&
            forwardEmail
              ? emailInboxDetails?.value?.emailAttachmentDetailsDtoList
              : [],
        })
      );
      setIsEmailVisible(false);
      setBccOnClick(false);
      setCcOnClick(false);
      setOpen(false);
    };
  }, [
    dispatch,
    emailPreview,
    userId,
    clientId,
    processTabClick,
    limit,
    AP_emailSubmitId,
    emailInboxDetails,
    forwardEmail,
    history,
  ]);

  useEffect(() => {
    if (userEmailSearchObject === null) {
      apEmailInboxData(limit);
    }
  }, [userEmailSearchObject]);

  useEffect(() => {
    setOffset(0);
  }, [isProcessTabClick]);

  useEffect(() => {
    setOffset(0);
    setLimit(50);
    setProcessTabClick('UnProcessed');
    setCurrentTab(0);
    setTraceEvents(null);
    dispatch({
      type: accPayConstants.ACCPAY_TRACE_EVENTS_SUCCESS,
      payload: null,
    });
    dispatch(financeResetStateField({ fieldName: 'pdfFetch' }));
  }, [Parameter?.[1]]);

  useEffect(() => {
    if (limit) {
      apEmailInboxData(limit);
      if (unProcessedCount > 0 && processedCount > 0) {
        setUnprocessedPageCount(Math.ceil(unProcessedCount / limit));
        setProcessedPageCount(Math.ceil(processedCount / limit));
      }
    }
  }, [limit]);

  useEffect(() => {
    if (offset >= 0) {
      apEmailInboxData(limit);
      if (offset === 0) {
        setActiveFirst(false);
      } else {
        setActiveFirst(true);
      }
    }
  }, [offset]);

  useEffect(() => {
    if (apEmailUserSearchCount) {
      setOffset(0);
      setSearchedEmailCount(apEmailUserSearchCount.value / limit);
      setUnprocessedPageCount(Math.ceil(apEmailUserSearchCount.value / limit));
      setProcessedPageCount(Math.ceil(apEmailUserSearchCount.value / limit));
    }
  }, [apEmailUserSearchCount, limit]);

  useEffect(() => {
    if (
      countforAPDasboard &&
      countforAPDasboard.value.AP_EMAIL &&
      countforAPDasboard.value.AP_EMAIL.length > 0
    ) {
      setProcessedCount(0);
      setUnProcessedCount(0);
      setProcessedPageCount(0);
      setUnprocessedPageCount(0);
      setSearchedEmailCount(0);
      countforAPDasboard.value.AP_EMAIL.filter(
        (e) => e.status && Parameter[1] && e.status === Parameter[1]
      ).map((a) => {
        if (a) {
          a.emailProcessedCount
            ? (setProcessedCount(a.emailProcessedCount),
              setProcessedPageCount(Math.ceil(a.emailProcessedCount / limit)))
            : null;
          a.emailUnProcessedCount
            ? (setUnProcessedCount(a.emailUnProcessedCount),
              setUnprocessedPageCount(
                Math.ceil(a.emailUnProcessedCount / limit)
              ))
            : null;
        }
      });
    }
  }, [countforAPDasboard, processTabClick]);

  useEffect(() => {
    if (emailSendResponse) {
      funcToSetResponseMessage('success', 'Email has been sent successfully');
      setIsEmailVisible(false);

      setOpen(false);
    }
  }, [emailSendResponse]);

  useEffect(() => {
    if (accPaytraceEvents) {
      setTraceEvents(accPaytraceEvents.value);
      let data = null;
      dispatch({
        type: accPayConstants.ACCPAY_TRACE_EVENTS_SUCCESS,
        data,
      });
    }
  }, [accPaytraceEvents]);

  const apEmailInboxData = (
    currentLimit,
    text = null,
    status,
    isSearch = false
  ) => {
    const payload = {
      userId: userEmailSearchObject?.userId || userId,
      clientId: userEmailSearchObject?.clientId || clientId,
      tableName: userEmailSearchObject?.tableName || tableName[0],
      category: userEmailSearchObject?.category || tableName[1],
      status: userEmailSearchObject?.status || tableName[2],
      process: processTabClick,
      limit: currentLimit,
      offset,
      data: userEmailSearchObject?.data || null,
      type: userEmailSearchObject?.type || 'subscribe',
      searchData:
        userEmailSearchObject?.searchData ||
        text?.toLowerCase().trim() ||
        searchString.toLowerCase().trim(),
    };
    userEmailSearchObject && userEmailSearchObject != null
      ? dispatch(
          apEmailInboxs({
            userId: userEmailSearchObject.userId,
            clientId: userEmailSearchObject.clientId,
            tableName: userEmailSearchObject.tableName,
            category: userEmailSearchObject.category,
            status: userEmailSearchObject.status,
            process: processTabClick,
            limit: currentLimit,
            offset,
            data: userEmailSearchObject.data,
            type: userEmailSearchObject.type,
            searchData: userEmailSearchObject.searchData,
          })
        )
      : dispatch(
          apEmailInboxs({
            userId,
            clientId,
            tableName: tableName[0],
            category: tableName[1],
            status: tableName[2],
            process: processTabClick,
            limit: currentLimit,
            offset,
            data: null,
            type: 'subscribe',
            searchData: text ?? searchString.toLowerCase().trim(),
          })
        );

    let obj = {
      category: tableName[1],
      status: tableName[2],
      clientId: clientId,
      limit: currentLimit,
      offset: offset,
      searchVariable: searchString.toLowerCase().trim(),
    };
    // dispatch(emailAction.apEmailSearchTotalCount(userId, processTabClick, obj));
  };
  const saveDraft = () => {
    dispatch(
      apEmailDraft(
        emailPreview?.content,
        emailPreview?.subject,
        emailPreview?.toAddressList,
        AP_emailSubmitId,
        '',
        emailPreview?.ccList,
        emailPreview?.bccList,
        userId
      )
    );
    setOpen(false);
    setIsEmailVisible(false);
  };
  const replyEmail = () => {
    setEmailReply(true);
    setReplayType('Re : ');
  };
  const replyEmailAll = () => {
    setEmailReplyAll(true);
    setReplayType('Re : ');
  };
  const ForwardEmail = () => {
    setForwardEmail(true);
    setReplayType('Frwd : ');
  };

  const handleOk = () => {
    setConfirmLoading(true);

    setTimeout(() => {
      setConfirmLoading(false);
      sendMail(true);
      setOpen(false);
    }, 500);
  };

  const handleCancel = () => {
    bodyEmpty = false;
    subjectEmpty = false;
    setOpen(false);
  };

  const sendMail = (isOk) => {
    if (
      emailPreview?.content === '' ||
      emailPreview?.content === '<div><br></div>' ||
      emailPreview?.content === undefined
    ) {
      bodyEmpty = false;
    } else if (
      emailPreview?.content !== '' &&
      emailPreview?.content !== '<div><br></div>' &&
      emailPreview?.content !== undefined
    ) {
      bodyEmpty = true;
    }
    if (
      emailPreview?.subject === '' ||
      emailPreview?.subject === null ||
      emailPreview?.subject === undefined
    ) {
      subjectEmpty = false;
    } else if (
      emailPreview?.subject !== '' &&
      emailPreview?.subject !== null &&
      emailPreview?.subject !== undefined
    ) {
      subjectEmpty = true;
    }

    if (
      emailPreview?.toAddressList === '' ||
      emailPreview?.toAddressList?.length === 0 ||
      emailPreview?.toAddressList === undefined ||
      emailPreview?.toAddressList[0] === null
    ) {
      funcToSetResponseMessage('error', 'Please enter to address');
      toEmpty = false;
    } else if (
      emailPreview?.toAddressList !== '' &&
      emailPreview?.toAddressList.length !== 0 &&
      emailPreview?.toAddressList !== undefined
      // emailPreview?.toAddressList[0] === null
    ) {
      toEmpty = true;
    }
    if (!subjectEmpty || !bodyEmpty) {
      setOpen(true);
    } else {
      setOpen(false);
    }

    if (!toEmpty) {
      setOpen(false);
    } else {
      setOpen(true);
    }

    if (toEmpty && bodyEmpty && subjectEmpty) {
      dispatch(
        apSendEmail({
          content: emailPreview?.content,
          subject: emailPreview?.subject,
          toAddress: emailPreview?.toAddressList,
          emailTempId: AP_emailSubmitId,
          invId: null,
          ccAddress: emailPreview?.ccList,
          bccAddress: emailPreview?.bccList,
          userId: userId,
          type: 'subscribe',
          file:
            emailInboxDetails?.value?.emailAttachmentDetailsDtoList?.length &&
            forwardEmail
              ? emailInboxDetails?.value?.emailAttachmentDetailsDtoList
              : [],
        })
      );

      setOpen(false);
      setIsEmailVisible(false);
    }

    if (isOk && toEmpty) {
      dispatch(
        apSendEmail({
          content: emailPreview?.content,
          subject: emailPreview?.subject,
          toAddress: emailPreview?.toAddressList,
          emailTempId: AP_emailSubmitId,
          invId: null,
          ccAddress: emailPreview?.ccList,
          bccAddress: emailPreview?.bccList,
          userId: userId,
          type: 'subscribe',
          file:
            emailInboxDetails?.value?.emailAttachmentDetailsDtoList?.length &&
            forwardEmail
              ? emailInboxDetails?.value?.emailAttachmentDetailsDtoList
              : [],
        })
      );

      setOpen(false);
      setIsEmailVisible(false);
    }
    setEmailReply(false);
    setEmailReplyAll(false);
    setForwardEmail(false);
  };

  const closeMail = () => {
    dispatch(apEmailDraft(emailPreview));
  };
  const onclickFunc = (data, id) => {
    setAssignedUserName('');
    let userName = '';
    let categoryName = '';
    let userId = data.apUserId ? data.apUserId : '';
    let docId = data.documentId ? data.documentId : '';
    let emailId = data.emailId ? data.emailId : '';
    let category = data.category ? data.category : '';
    setEmailIds(emailId);
    {
      data.processFlag === 'Y' ? setIsProcessed(true) : setIsProcessed(false);
    }
    dispatch(fetchEmailDetailsByDocId({ docId, emailId, type: 'subscribe' }));
    dispatch(traceEvent({ tableName: 'AP_EMAIL', tableId: emailId }));
    let newTableListing = cloneDeep(filteredData);
    newTableListing.map(function (data, idx) {
      newTableListing[idx].isSelected = false;
      if (idx === id) {
        newTableListing[idx].isSelected = true;
        newTableListing[idx].readFlag = 'Y';
      }
    });
    if (apProcessorUserList) {
      apProcessorUserList?.map(function (value) {
        if (userId) {
          if (value.userId === userId) {
            setAssignedUserName(value.userName);
            userName = value.userName;
            return;
          }
        }
      });
    }
    if (apEmailCategoryList) {
      apEmailCategoryList?.value.map(function (value) {
        if (category) {
          if (value.emailCategoryCode === category) {
            categoryName = value.emailCategoryName;
            return;
          }
        }
      });
    }
    dispatch(
      setEmailPrimaryId({
        email_id: emailId,
        user_id: userId,
        user_name: userName,
        category_name: categoryName,
        category_code: category,
      })
    );
    if (tableName[1] === 'draft') {
      setDraftListClicked(true);
    } else {
      setDraftListClicked(false);
    }

    setFilteredData(newTableListing);
    setNewCompose(false);
    setState('');
    setLoading(true);
    setEmailReply(false);
    setEmailReplyAll(false);
    setForwardEmail(false);
    setButtonEnable(true);
  };

  const viewFrame = (path) => {
    setfilePath(path);
    setIsModal(true);
  };
  const newWindow = (path) => {
    window.open(path, 'blank');
  };

  const setProcessTabClickFunc = (value, limit) => {
    setProcessTabClick(value);
    setLimit(limit);
    setActiveFirst(false);
    apEmailInboxData(50, null, value);
  };

  const pageableFunc = (iconName, processType) => {
    iconName === 'first'
      ? setOffset(0)
      : iconName === 'prev' && offset > 0
      ? setOffset(offset - 1)
      : iconName === 'next'
      ? processType === 'unprocessed' && offset < unprocessedPageCount - 1
        ? setOffset(offset + 1)
        : processType === 'processed' && offset < processedPageCount - 1
        ? setOffset(offset + 1)
        : ''
      : iconName === 'last'
      ? processType === 'unprocessed'
        ? setOffset(unprocessedPageCount - 1)
        : setOffset(processedPageCount - 1)
      : null;
  };
  const emailAssignTo = () => {
    childModalref.current.openModal();
  };
  const emailChangeCategory = () => {
    childModalref.current.openCategoryModal();
  };
  const emailOnChangeTo = (_emails, removeDuplicates) => {
    const changedArray = removeDuplicates(_emails);
    setEmailPreview({ ...emailPreview, toAddressList: changedArray });
  };

  const emailOnChangeCc = (_emails, removeDuplicates) => {
    const changedArray = removeDuplicates(_emails);
    setEmailPreview({ ...emailPreview, ccList: changedArray });
  };

  const emailOnChangeBcc = (_emails, removeDuplicates) => {
    const changedArray = removeDuplicates(_emails);
    setEmailPreview({ ...emailPreview, bccList: changedArray });
  };

  const onChangeHandler = (event, key) => {
    if (emailPreview !== undefined) {
      let updatedState = { ...emailPreview };
      switch (event.target.name) {
        case 'subject':
          updatedState.subject = event.target.value;
          setReplayType('');
          break;
        case 'toAddress':
          updatedState.toAddress = event.target.value;
          break;

        default:
          break;
      }
      setEmailPreview(updatedState);
    }
  };

  const onRichTextChangeHandler = (event, key, emailTemplate) => {
    setEmailPreview({ ...emailPreview, content: event });
  };
  const handleClick = () => {
    setEmailPreview({ ...emailPreview, subject: emailSubject });

    setIsEmailVisible(false);
    setBccOnClick(false);
    setCcOnClick(false);
    setOpen(false);

    setEmailReply(false);
    setEmailReplyAll(false);
    setForwardEmail(false);
  };

  const emailOnProcess = () => {
    childModalref.current.onProcessedClick();
  };
  const functionsName = {
    emailAssignTo,
    emailChangeCategory,
    emailOnProcess,
  };

  const onEnterKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      commentSendButton();
    }
  };
  const commentSendButton = () => {
    if (addCommentsValue && addCommentsValue.replace(/\s/g, '').length) {
      const obj = { userComment: addCommentsValue, emailId: emailIds };
      dispatch(addUserComment(obj));
    }
  };

  return (
    <>
      <CommonSpinner visible={isLoading} />
      <div className="email-ui-main-container">
        <div className="three-col-layout db-header-card flex-row ">
          <div
            className="two-col-layout"
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <div
              className="message-count"
              style={{
                padding: '5px 0 0 5px',
                fontSize: '18px',
                whiteSpace: 'nowrap',
                position: 'relative',
                zIndex: 1,
              }}
            >
              {innerMenuName ? innerMenuName : ''}
            </div>
            <div
              style={{
                position: 'absolute',
                left: '100px',
                marginTop: '5px',
              }}
            >
              {/* added marginTop from v2: '5px', */}

              <EmailSearch
                isManualSearch
                tableList={TableListing}
                processFlag={processTabClick}
                menuParameters={tableName}
                filteredData={() => null}
                limit={limit}
                offset={offset}
                menu_data={menuData}
                parameter={table_name}
                searchText={(text) => {
                  if (text === '') {
                    apEmailInboxData(limit, '');
                  }
                  setSearchString(text);
                }}
                handleSearch={() => apEmailInboxData(limit)}
                setUserEmailSearchObject={setUserEmailSearchObject}
              />
            </div>
          </div>

          {/* New Message Button */}
          <button
            className="newMessage-button "
            onClick={() => {
              setNewCompose(true);
              setButtonEnable(false);
              setTraceEvents(null);
            }}
          >
            <i className="icon-pencil mr8" style={{ fontSize: '12px' }}>
              {' '}
            </i>{' '}
            New Message
          </button>
        </div>
      </div>
      <div className="email-ui flex-row mb40">
        <div className="left-controls ">
          {/* New Message Button */}
          {/* <div className="new-message-wrap ">
            <button
              className="small default  button fr"
              onClick={() => {
                setNewCompose(!newCompose);
                setButtonEnable(false);
              }}
            >
              New Message
            </button>
          </div> */}
          {/* Tabs */}
          <Tabs
            className="email-tabs"
            onSelect={(tabIndex) => setCurrentTab(tabIndex)}
            selectedIndex={currentTab}
          >
            {tableName[1] === 'recv' ||
            tableName[1] === 'oth' ||
            tableName[1] === 'inq' ||
            tableName[1] === 'inv' ||
            tableName[1] === 'int' ? (
              <TabList>
                <Tab onClick={() => setProcessTabClickFunc('UnProcessed', 50)}>
                  {['Unprocessed', ['(', unProcessedCount, ')'].join('')].join(
                    ' '
                  )}
                </Tab>
                <Tab onClick={() => setProcessTabClickFunc('Processed', 50)}>
                  {['Processed', ['(', processedCount, ')'].join('')].join(' ')}
                </Tab>
              </TabList>
            ) : (
              ''
            )}

            <TabPanel>
              <div className="email-list">
                <Scrollbars className="scroll-wrap">
                  {filteredData
                    ? filteredData.map(function (data, idx) {
                        if (data.processFlag === 'N') {
                          return (
                            <EmailListing
                              key={idx}
                              isEmailRead={data.readFlag === 'Y' ? false : true}
                              subject={data.subject}
                              email={data.fromAddress}
                              name={data?.fromName || ''}
                              isSelected={
                                data.hasOwnProperty('isSelected')
                                  ? data.isSelected
                                  : false
                              }
                              date={data.rcvdDate}
                              time={data.rcvdTime}
                              onClick={() => onclickFunc(data, idx)}
                              attachmentCount={data.attachmentCount}
                              invCount={data?.invCount}
                              pageSelected={tableName ? tableName[1] : ''}

                              // org="Pinnacle India"
                              // label="2 Invoices"
                            />
                          );
                        } else {
                          return (
                            <EmailListing
                              key={idx}
                              isEmailRead={data.readFlag === 'Y' ? false : true}
                              subject={data.subject}
                              email={data.fromAddress}
                              name={data?.fromName || ''}
                              isSelected={
                                data.hasOwnProperty('isSelected')
                                  ? data.isSelected
                                  : false
                              }
                              date={data.rcvdDate}
                              time={data.rcvdTime}
                              onClick={() => onclickFunc(data, idx)}
                              attachmentCount={data.attachmentCount}
                              invCount={data?.invCount}
                              pageSelected={tableName ? tableName[1] : ''}
                              // org="Pinnacle India"
                              // label="2 Invoices"
                            />
                          );
                        }
                        // switch (data.processFlag) {
                        //   case 'N': {
                        //     return (
                        //       <EmailListing
                        //         key={idx}
                        //         isEmailRead={
                        //           data.readFlag === 'Y' ? false : true
                        //         }
                        //         subject={data.subject}
                        //         email={data.fromAddress}
                        //         name={data?.fromName || ''}
                        //         isSelected={
                        //           data.hasOwnProperty('isSelected')
                        //             ? data.isSelected
                        //             : false
                        //         }
                        //         date={data.rcvdDate}
                        //         time={data.rcvdTime}
                        //         onClick={() => onclickFunc(data, idx)}
                        //         attachmentCount={data.attachmentCount}
                        //         // org="Pinnacle India"
                        //         // label="2 Invoices"
                        //       />
                        //     );
                        //   }
                        //   default: {
                        //     <EmailListing
                        //       key={idx}
                        //       isEmailRead={data.readFlag === 'Y' ? false : true}
                        //       subject={data.subject}
                        //       email={data.fromAddress}
                        //       name={data?.fromName || ''}
                        //       isSelected={
                        //         data.hasOwnP roperty('isSelected')
                        //           ? data.isSelected
                        //           : false
                        //       }
                        //       date={data.rcvdDate}
                        //       time={data.rcvdTime}
                        //       onClick={() => onclickFunc(data, idx)}
                        //       attachmentCount={data.attachmentCount}
                        //       // org="Pinnacle India"
                        //       // label="2 Invoices"
                        //     />;
                        //   }
                        // }
                      })
                    : null}
                </Scrollbars>
                <EmailPagination
                  activeFirst={activeFirst}
                  activeLast={activeLast}
                  currentPage={offset + 1}
                  currentLimit={limit}
                  totalPage={unprocessedPageCount}
                  pageableIconClick={(iconName, processType) =>
                    pageableFunc(iconName, processType)
                  }
                  pagePerRowClick={(value) =>
                    setProcessTabClickFunc(processTabClick, value)
                  }
                  processType={'unprocessed'}
                />
              </div>
            </TabPanel>
            <TabPanel>
              <div className="email-list">
                <Scrollbars className="scroll-wrap">
                  {filteredData
                    ? filteredData.map(function (data, idx) {
                        switch (data.processFlag) {
                          case 'Y': {
                            return (
                              <EmailListing
                                isEmailRead={
                                  data.readFlag === 'Y' ? false : true
                                }
                                subject={data.subject}
                                email={data.fromAddress}
                                name={data?.fromName || ''}
                                isSelected={
                                  data.hasOwnProperty('isSelected')
                                    ? data.isSelected
                                    : false
                                }
                                date={data.rcvdDate}
                                time={data.rcvdTime}
                                attachmentCount={data.attachmentCount}
                                invCount={data?.invCount}
                                onClick={() => onclickFunc(data, idx)}
                                // org="Pinnacle India"
                                // label="2 Invoices"
                                key={idx}
                                pageSelected={tableName ? tableName[1] : ''}
                              />
                            );
                          }
                        }
                      })
                    : null}
                </Scrollbars>
                <EmailPagination
                  activeFirst={activeFirst}
                  activeLast={activeLast}
                  currentPage={offset + 1}
                  currentLimit={limit}
                  totalPage={processedPageCount}
                  pageableIconClick={(iconName, processType) =>
                    pageableFunc(iconName, processType)
                  }
                  pagePerRowClick={(value) =>
                    setProcessTabClickFunc(processTabClick, value)
                  }
                  processType={'processed'}
                />
              </div>
            </TabPanel>
          </Tabs>
        </div>
        {isDraftPage && draftListClicked ? (
          <div className="right-controls">
            <EmailDraftSend
              subject={state ? state.subject : ''}
              isProcessed={isProcessedFlag}
              name={assignedUser ? assignedUser : ''}
              fromName=""
              fromEmail={state ? state.fromAddress : ''}
              toName=""
              toEmail={
                state && state.toAddressList && state.toAddressList.length
                  ? state.toAddressList.map((resData) => {
                      return resData;
                    })
                  : ''
              }
              attachments={
                state &&
                state.emailAttachmentDetailsDtoList &&
                state.emailAttachmentDetailsDtoList.length
                  ? state.emailAttachmentDetailsDtoList.map((resdata) => {
                      return {
                        attachmentRouteURL: resdata.attachmentRouteURL,
                        attachmentId: resdata.attachmentId,
                        emailInDetailsId: resdata.emailInDetailsId,
                        attachmentRouteURLList: resdata.attachmentRouteURLList,
                        fileName: resdata.fileName,
                        filePath: resdata.attachementPath,
                        isVerified: resdata.verified,
                        isZip: resdata.zip,
                        isDuplicate: resdata.duplicate,
                        subData:
                          resdata.emailAttachmentDetailsDtoSub &&
                          resdata.emailAttachmentDetailsDtoSub.length
                            ? resdata.emailAttachmentDetailsDtoSub
                            : '',
                      };
                    })
                  : []
              }
              emailBody={state && state.htmlPart ? state.htmlPart : ''}
              menu_data={menuData}
              ref={childModalref}
              parameter={Parameter}
            />
          </div>
        ) : (
          <div className="right-controls">
            {newCompose ? (
              <EmailNewCompose onCancel={() => setNewCompose(false)} />
            ) : state ? (
              Parameter[1] === 'outbox' ? (
                <ApEmailOutboxBody
                  subject={state ? state.subject : ''}
                  fromName=""
                  fromEmail={state ? state.fromAddress : ''}
                  toName=""
                  toEmail={
                    state && state.toAddressList && state.toAddressList.length
                      ? state.toAddressList.map((resData) => {
                          return resData;
                        })
                      : ''
                  }
                  emailBody={state && state.htmlPart ? state.htmlPart : ''}
                />
              ) : (
                <>
                  <EmailBody
                    subject={state ? state.subject : ''}
                    isProcessed={isProcessedFlag}
                    name={assignedUser ? assignedUser : ''}
                    fromName=""
                    fromEmail={state ? state.fromAddress : ''}
                    toName=""
                    toEmail={
                      state && state.toAddressList && state.toAddressList.length
                        ? state.toAddressList.map((resData) => {
                            return resData;
                          })
                        : ''
                    }
                    attachments={
                      state &&
                      state.emailAttachmentDetailsDtoList &&
                      state.emailAttachmentDetailsDtoList.length
                        ? state.emailAttachmentDetailsDtoList.map((resdata) => {
                            return {
                              attachmentRouteURL: resdata.attachmentRouteURL,
                              attachmentId: resdata.attachmentId,
                              emailInDetailsId: resdata.emailInDetailsId,
                              attachmentRouteURLList:
                                resdata.attachmentRouteURLList,
                              fileName: resdata.fileName,
                              filePath: resdata.attachementPath,
                              isVerified: resdata.verified,
                              isZip: resdata.zip,
                              isDuplicate: resdata.duplicate,
                              subData:
                                resdata.emailAttachmentDetailsDtoSub &&
                                resdata.emailAttachmentDetailsDtoSub.length
                                  ? resdata.emailAttachmentDetailsDtoSub
                                  : '',
                            };
                          })
                        : []
                    }
                    emailBody={state && state.htmlPart ? state.htmlPart : ''}
                    onFileClick={(filePath) => viewFrame(filePath)}
                    openNewTab={(filePath) => newWindow(filePath)}
                    replyEmail={() => replyEmail()}
                    replyEmailAll={() => replyEmailAll()}
                    forwardEmail={() => ForwardEmail()}
                    menu_data={menuData}
                    ref={childModalref}
                    parameter={Parameter}
                  />
                  <div
                    style={{ marginBottom: 30 + 'px' }}
                    className={`email-moving-div ${
                      isEmailVisible
                        ? 'email-moving-div-active'
                        : 'email-moving-div-inactive'
                    }`}
                  >
                    <div
                      className="email-new-compose"
                      style={{
                        bottom: 10 + 'px',
                        right: 10 + 'px',
                      }}
                    >
                      {/* <h2>Email Preview</h2> */}

                      <div className="new-compose-body">
                        <div
                          style={{
                            maxHeight: '390px',
                            overflowY: 'scroll',
                            paddingRight: '10px',
                            marginTop: '20px',
                          }}
                        >
                          <div
                            onClick={handleClick}
                            className="modal-close icon-close"
                          ></div>
                          <div className="to-compose-input-custom-for-multiEmail">
                            <div className="compose-input-custom-for-multiEmail">
                              <p className="prefix">To :</p>

                              <MultiEmailInput
                                emailList={
                                  emailPreview?.toAddressList &&
                                  emailPreview?.toAddressList.length
                                    ? emailPreview?.toAddressList
                                    : []
                                }
                                emailOnchange={emailOnChangeTo}
                              />
                            </div>

                            {/* <input
                          type="email"
                          autoComplete="false"
                          multiple={true}
                          value={
                            emailPreview?.toAddressList &&
                            emailPreview?.toAddressList !== null
                              ? emailPreview.toAddressList.join(",")
                              : ""
                          }
                          name="toAddress"
                          // disabled={!emailPreviewProp.emailAction}
                          onChange={(event) => onChangeHandler(event)}
                        /> */}
                            <div>
                              <div className="button-cc-bcc-div">
                                {!ccOnClick ? (
                                  <button
                                    onClick={() => setCcOnClick(!ccOnClick)}
                                  >
                                    Cc
                                  </button>
                                ) : (
                                  ''
                                )}
                                {!bccOnClick ? (
                                  <button
                                    onClick={() => setBccOnClick(!bccOnClick)}
                                  >
                                    Bcc
                                  </button>
                                ) : (
                                  ''
                                )}
                              </div>
                            </div>
                          </div>
                          {ccOnClick ? (
                            <div className="compose-input-custom-for-multiEmail">
                              <p className="prefix mb8">Cc :</p>
                              <MultiEmailInput
                                emailList={
                                  emailPreview?.ccList &&
                                  emailPreview?.ccList !== null
                                    ? emailPreview?.ccList
                                    : []
                                }
                                emailOnchange={emailOnChangeCc}
                                // disabled={previewItem.isEmailPreviewEnable}
                              />
                            </div>
                          ) : null}

                          {bccOnClick ? (
                            <div className="compose-input-custom-for-multiEmail ">
                              <p className="prefix mb8">Bcc :</p>
                              <div className="multiemail-input-container">
                                <MultiEmailInput
                                  emailOnchange={emailOnChangeBcc}
                                  emailList={
                                    emailPreview?.bccList &&
                                    emailPreview?.bccList !== null
                                      ? emailPreview?.bccList
                                      : []
                                  }
                                />
                              </div>
                            </div>
                          ) : null}

                          <div className="compose-input mt8">
                            <p className="prefix">Subject :</p>
                            <input
                              style={{ fontWeight: 'bold' }}
                              type="text"
                              name="subject"
                              autoComplete="false"
                              value={
                                emailPreview
                                  ? replayType + emailPreview?.subject
                                  : ''
                              }
                              // disabled={previewItem.isEmailPreviewEnable}
                              onChange={(event) => onChangeHandler(event)}
                            />
                          </div>
                          <div
                            className="compose-content"
                            style={{ overflow: 'hidden' }}
                          >
                            {' '}
                            <div
                              style={{
                                maxHeight: '270px',
                                overflowY: 'scroll',
                              }}
                            >
                              <RichTextEditor
                                name="content"
                                onChange={(event) =>
                                  onRichTextChangeHandler(event, 'richText')
                                }
                                // disabled={previewItem.isEmailPreviewEnable}
                                value={
                                  emailPreview && emailPreview?.content
                                    ? emailPreview?.content
                                    : ''
                                }
                              />{' '}
                            </div>
                          </div>
                        </div>

                        <div
                          className="compose-button mb4"
                          style={{
                            // width: '88.5%',
                            // position: 'absolute',
                            // bottom: 0,
                            background: 'white',
                            padding: '10px 0px 10px 0px',
                          }}
                        >
                          <div className="compose-send-save">
                            <Popconfirm
                              className="popup-confirm-button"
                              title="Title"
                              description="Send this message without a subject or text in the body?"
                              open={open}
                              onConfirm={handleOk}
                              okButtonProps={{ loading: confirmLoading }}
                              onCancel={handleCancel}
                            >
                              <Button
                                className="small default mr20"
                                onClick={() => sendMail()}
                              >
                                Send
                              </Button>
                              {/*    //    <Button
                            //   // disabled={!previewItem.saveDraftBtnn}
                            //   className="small default mr20"
                            //   onClick={() => sendMail()}
                            // >
                            //   Send
                            // </Button> */}
                            </Popconfirm>

                            <Button
                              // disabled={!previewItem.saveDraftBtnn}
                              className="small outline "
                              onClick={() => saveDraft()}
                            >
                              Save Draft
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* {emailReply || emailReplyAll || forwardEmail ? (
                  <EmailReply
                    subject={state ? 'RE: ' + state.subject : ''}
                    // fromName="Sudhir Raj"
                    fromEmail={
                      state && state.toAddressList && state.toAddressList.length
                        ? state.toAddressList.map((resData) => {
                            return resData;
                          })
                        : ''
                    }
                    toName=""
                    toEmail={state ? state.fromAddress : ''}
                    date={currentDate ? currentDate : ''}
                    onDeleteClick={() => setEmailReply(false)}
                  />
                ) : null} */}
                </>
              )
            ) : (
              <EmailEmpty />
            )}
          </div>
        )}
      </div>
      {buttonEnable && (
        <div className="boxed mb40">
          <TraceEvents
            key="traceEventsEditor"
            onAddCommentsChangeHandler={onAddCommentsChangeHandler}
            addCommentsValue={addCommentsValue}
            enableComments={buttonEnable}
            disabled={true}
            data={traceEvents}
            userId={userId}
            onEnterKeyPress={onEnterKeyPress}
            sendButton={commentSendButton}
          />
        </div>
      )}
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
        style={{
          content: {
            minHeight: '97%',
            minWidth: '75%',
            top: '2%',
          },
        }}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>
        <DocumentViewer
          fileURL={filePath}
          fileType={'iframe'}
          iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
          zoom={'#zoom=100'}
        />
      </Modal>
      {buttonEnable ? (
        <ButtonCommon
          tempButtonData={buttonFlag}
          functionsName={functionsName}
        />
      ) : (
        ''
      )}
    </>
  );
};

export { ApEmailsInbox };
