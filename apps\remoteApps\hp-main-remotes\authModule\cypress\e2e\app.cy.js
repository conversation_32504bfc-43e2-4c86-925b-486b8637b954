describe('Login Page Tests', () => {
  beforeEach(() => {
    cy.visit('https://dev3.highpointsuite.com/');
  });

  it('should load the login page', () => {
    cy.contains('h1', 'Sign In', { timeout: 10000 }).should('be.visible');
    cy.get('label').contains('Username').should('be.visible');
    cy.get('label').contains('Password').should('be.visible');
  });

  it('should allow a user to enter credentials and submit the form', () => {
    cy.get('#username', { timeout: 10000 }).should('be.visible').type('issac');
    cy.get('#password').type('Pinnacle12$');
    cy.get('button[type="submit"]').click();
  });
});
