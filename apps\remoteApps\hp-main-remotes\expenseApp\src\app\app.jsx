import { Route, Routes } from 'react-router-dom';
import { ExpenseReportDetails } from './container';
import { useEffect } from 'react';
import { utils } from '@hp/utils';

export function App() {
  useEffect(() => {
    async function decryptAndSetUser() {
      await utils.userdetailsDecrypt();
    }

    decryptAndSetUser();
  }, []);
  return (
    <Routes>
      <Route
        exact
        path="/expenses-report/:functionPath/:params"
        element={<ExpenseReportDetails />}
      />
      {/* <Route
        exact
        path="/expenses-report/:functionPath/:params/:reportNumber"
        element={<ExpenseCompareComponent/>}
      /> */}
    </Routes>
  );
}
export default App;
