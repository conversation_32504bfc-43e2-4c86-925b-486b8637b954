/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import '../ApReportStyle.scss';
import { useDispatch, useSelector } from 'react-redux';
import { getAPReports, accPayConstants } from '@hp/mainstore';
import { useAppRouterDom } from '@hp/utils';
import {
  CommonSpinner,
  globalutils,
  DateRangeInput,
  Button,
} from '@hp/components';
import { ReportDataList, ReportGraph } from '../components';
import { Badge } from 'antd';

const ReturnedInvoice = () => {
  const { apReportDTO, loading } = useSelector((state) => state.accpay);
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();

  const urlParam = domParameters?.parameters || '';
  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user.userId);
  const clientId = user.clientId;
  const [reportData, setReportData] = useState();
  const [graphData, setGraphData] = useState();
  const [periodCovered, setPeriodCovered] = useState();
  const [_IsLoading, setLoading] = useState(true);
  const [toSearchDate, setToSearchDate] = useState();
  const [fromSearchDate, setFromSearchDate] = useState();

  let endDate = new Date();
  let startDate = new Date();
  startDate.setDate(endDate.getDate() - 30);

  // Function to format the date as "MM-DD-YYYY"
  const formatDateMMDDYYYY = (date) => {
    let month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
    let day = date.getDate().toString().padStart(2, '0');
    let year = date.getFullYear();
    return `${month}-${day}-${year}`;
  };

  let formattedEndDate = formatDateMMDDYYYY(endDate);
  let formattedStartDate = formatDateMMDDYYYY(startDate);

  const [dateChange, setDateChange] = useState({
    fromDate: formattedStartDate,
    toDate: formattedEndDate,
  });
  useEffect(() => {
    dispatch(
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'subscribe',
        fromDate: dateChange.fromDate,
        toDate: dateChange.toDate,
      })
    );
  }, [
    clientId,
    dateChange.fromDate,
    dateChange.toDate,
    dispatch,
    fromSearchDate,
    toSearchDate,
    urlParam,
    userId,
  ]);

  useEffect(() => {
    return () => {
      dispatch({
        type: accPayConstants.AP_REPORT_FETCHED_SUCCESS,
        apReportDTO: null,
      });
    };
  }, []);

  useEffect(() => {
    if (apReportDTO != null && apReportDTO.value) {
      setReportData(apReportDTO.value.listView);
      setGraphData(apReportDTO.value.graphDetails);
      setPeriodCovered(apReportDTO.value.periodCovered);
    }
  }, [apReportDTO]);

  const onDateChange = (e) => {
    const startDate = globalutils.formatDate(e.fromDate, 'mm-dd-yyyy');
    const endDate = globalutils.formatDate(e.toDate, 'mm-dd-yyyy');
    setFromSearchDate(startDate.replaceAll('/', '-'));
    setToSearchDate(endDate.replaceAll('/', '-'));
    setDateChange(e);
  };
  const searchHandler = () => {
    dispatch(
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'subscribe',
        fromDate: fromSearchDate,
        toDate: toSearchDate,
      })
    );
  };
  return (
    <>
      <div className="page-sub-title">Returned Invoices Report</div>
      <CommonSpinner visible={loading} />
      {apReportDTO ? (
        <>
          {periodCovered ? (
            <Badge.Ribbon text={periodCovered}> </Badge.Ribbon>
          ) : (
            ''
          )}
          <div className="header-nav card  mb20">
            <div className="three-col-layout mb20">
              <div>
                <DateRangeInput
                  className="mr20"
                  style={{ width: 100 + '%' }}
                  label="Duration"
                  formType={{
                    value: dateChange,
                  }}
                  onChange={(e) => onDateChange(e)}
                />
              </div>
              <div>
                {' '}
                <Button
                  className=" mr20 mt20 small default "
                  style={{ width: '10%' }}
                  onClick={searchHandler}
                >
                  Search
                </Button>
              </div>
              <div></div>
            </div>
          </div>
          <div className="report-body-main">
            <div className="report-data-body card  mr20">
              <ReportDataList data={reportData} />
            </div>
            <div className="report-graph-body ">
              <ReportGraph data={graphData} isLoading={() => setLoading()} />
            </div>
          </div>
        </>
      ) : (
        ''
      )}
    </>
  );
};
export { ReturnedInvoice };
