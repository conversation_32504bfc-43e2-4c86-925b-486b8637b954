/* eslint-disable array-callback-return */
/* eslint-disable no-prototype-builtins */
import { globalutils } from '@hp/components';
import { passApListingData } from '@hp/mainstore';

var entities = [];
var coordsReqBody = [];

var annotationConfigs = {
  data: '',
  defaultAnnotations: [],
  entity: {},
  config: {
    disableOCR: true,
    readonly: true,
    hideAnnotatingTooltips: true,
    hideAnnotatingEntityVisualizations: true,
    hideAnnotateableBoxes: true,
    shouldUpdateDefaultAnnotations: true,
  },
  initialScale: 2,
  isZoomed: false,
};

function extractionLabels(primaryArray, secondaryArray) {
  if (primaryArray && primaryArray.length) {
    primaryArray.map((controls, i) => {
      if (
        controls[Object.keys(controls)]?.invForm &&
        controls[Object.keys(controls)]?.invForm.length
      ) {
        let assignObject = controls[Object.keys(controls)]?.invForm[0];
        entities.push({
          id: i,
          name: assignObject?.displayName,
          color: '#3b73b9',
          entityType: 'AREA',
          uniquekey: assignObject?.uniqueKey,
        });
      }
    });
    secondaryArray.map((controls, secIdx) => {
      if (
        controls[Object.keys(controls)]?.invForm &&
        controls[Object.keys(controls)]?.invForm.length
      ) {
        let secAssign = controls[Object.keys(controls)]?.invForm[0];
        entities.push({
          id: secIdx,
          name: secAssign?.displayName,
          color: '#7bb216',
          entityType: 'AREA',
          uniquekey: secAssign?.uniqueKey,
        });
      }
    });
    entities = entities.map((e, i) => ({ ...e, id: i }));
  }
}

function mapDisplayNameToTextBox(annoArray) {
  if (annoArray && annoArray.length) {
    var lastObj = annoArray[annoArray.length - 1];

    // Check if 'text' property already exists in 'areaAnnotation'
    if (!lastObj?.areaAnnotation?.hasOwnProperty('text')) {
      // If 'text' property does not exist, add it with the entity's 'name'
      lastObj.areaAnnotation.text = lastObj?.entity?.name;
    }

    // Return the updated array
    return annoArray;
  }

  // Get the last object in the array
}

function extractedValuesMapping(response) {
  let defaultAnnotations = [];
  if (response?.boundingBoxList.length) {
    response?.boundingBoxList.map((coordsElement, index) => {
      defaultAnnotations.push({
        id: index,
        page: response.currentPage,
        areaAnnotation: {
          boundingBox: {
            left: coordsElement.left,
            top: coordsElement.top,
            width: coordsElement.width,
            height: coordsElement.height,
          },
          pdfInformation: {
            width: response.pageWidth,
            height: response.pageHeight,
            scale: 1,
          },
          text: coordsElement.text,
        },
        entity: {
          id: index,
          name: coordsElement.text,
          color: '#b39ddb',
          entityType: 'AREA',
        },
      });
    });

    return defaultAnnotations;
  }
}

function findNextOrPrev(data, objectkeys, key, type) {
  let searchMap = [];
  const dataMap = new Map();
  if (type === 'arrayData') {
    searchMap = data;
  } else {
    searchMap = data?.value?.invTableDataDtoList;
  }
  for (const obj of searchMap) {
    const key = `${objectkeys.currentInvId}_${objectkeys.currentPOId}`;
    if (!dataMap.has(key)) {
      dataMap.set(key, []);
    }
    dataMap.get(key).push(obj);
  }

  const objects = dataMap.get(
    `${objectkeys.currentInvId}_${objectkeys.currentPOId}`
  );

  if (!objects) return null;

  const foundIndex = objects?.findIndex((obj) => {
    if (objectkeys.currentInvId && objectkeys.currentPOId) {
      return (
        obj.inv_id === objectkeys.currentInvId &&
        obj.po_id === objectkeys.currentPOId
      );
    }
    if (objectkeys.currentInvId && objectkeys.currentPOId === undefined) {
      return obj.inv_id === objectkeys.currentInvId;
    }
    if (objectkeys.currentPOId && objectkeys.currentInvId === undefined) {
      return obj.po_id === objectkeys.currentPOId;
    }
    if (objectkeys.currentPPId) {
      return obj.pp_id === objectkeys.currentPPId;
    }
  });
  if (foundIndex !== -1) {
    let newIndex;

    if (key === 'next') {
      newIndex = foundIndex < objects.length - 1 ? foundIndex + 1 : 0;
    } else if (key === 'prev') {
      newIndex = foundIndex > 0 ? foundIndex - 1 : objects.length - 1;
    }

    return objects[newIndex];
  }

  return null;
}

function previousNext(
  actionKey,
  dispatch,
  apCachedData,
  apListingFilteredData
) {
  let currentApList;
  if (
    apListingFilteredData &&
    apListingFilteredData !== '' &&
    apListingFilteredData !== null &&
    apListingFilteredData.length > 0
  ) {
    apCachedData.value.invTableDataDtoList = apListingFilteredData;
    currentApList = apCachedData;
  } else {
    currentApList = apCachedData;
  }

  const currentURLKeys =
    globalutils.getURLParams('parmsLevelOne').parmsLevelOne;

  const currentKey = currentURLKeys.split(',')[1];
  const currentUrl = window.location.href;
  const urlSegments = currentUrl.split('/');

  const actions = {
    'non-po': () => {
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelThree').parmsLevelThree
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    'part-match': () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    matched: () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    unmatched: () => {
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    'on-hold': () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    rejected: () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    returned: () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    'wait-inv': () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      //urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    'cost-inv': () => {
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelThree').parmsLevelThree
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    credit_note: () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    RF: () => {
      const currentPOId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );
      const currentInvId = Number(
        globalutils.getURLParams('parmsLevelFive').parmsLevelFive
      );
      const foundObject = findNextOrPrev(
        currentApList,
        { currentPOId, currentInvId },
        actionKey
      );
      urlSegments[urlSegments.length - 2] = foundObject?.po_id;
      urlSegments[urlSegments.length - 1] = foundObject?.inv_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    'for-xmsn': () => {
      const currentPPId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );

      const newList = currentApList?.value?.ppTableDataDtoList;

      const foundObject = findNextOrPrev(
        newList,
        { currentPPId },
        actionKey,
        'arrayData'
      );
      urlSegments[urlSegments.length - 1] = foundObject?.pp_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
    'for-approval': () => {
      const currentPPId = Number(
        globalutils.getURLParams('parmsLevelFour').parmsLevelFour
      );

      const newList = currentApList?.value?.ppTableDataDtoList;
      const foundObject = findNextOrPrev(
        newList,
        { currentPPId },
        actionKey,
        'arrayData'
      );
      urlSegments[urlSegments.length - 1] = foundObject?.pp_id;
      const url = urlSegments.join('/');
      dispatch(passApListingData(foundObject));
      window.location.href = url;
    },
  };
  if (actions[currentKey]) {
    actions[currentKey]();
  }
}

export const invoiceService = {
  entities,
  coordsReqBody,
  extractionLabels,
  mapDisplayNameToTextBox,
  annotationConfigs,
  extractedValuesMapping,
  previousNext,
};
