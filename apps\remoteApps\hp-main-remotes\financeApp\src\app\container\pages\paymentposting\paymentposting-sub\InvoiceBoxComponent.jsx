import {
  DatePickerInput,
  ImageSwitcher,
  Select,
  TextAreaInput,
  TextInput,
} from '@hp/components';
import React from 'react';
import DataTable from 'react-data-table-component';

const InvoiceBoxComponent = (props) => {
  const [isDisabled, setisDisabled] = React.useState(true);
  const invoiceColumns = [
    { name: 'Item', selector: 'serial_no', sortable: true },
    { name: 'Description', selector: 'description', sortable: true },
    { name: 'Quantity', selector: 'quantity', sortable: true },
    { name: 'Unit', selector: 'unit', sortable: true },
    { name: 'Unit Price', selector: 'unit_price', sortable: true },
    { name: 'Amount', selector: 'amount', sortable: true },
    { name: 'Tax', selector: 'tax', sortable: true },
    { name: 'Tax Amount', selector: 'tax_amount', sortable: true },
  ];
  setisDisabled(true);
  return (
    <div className="boxed">
      <h1 className="page-title">Invoice Details</h1>
      <div className="form-and-image">
        <div className="compare-layout-mask">
          <TextInput
            label="Invoice No"
            className="mb20"
            disabled={isDisabled}
            value={''}
          />
          <DatePickerInput
            className="mb20"
            label="Date of Invoice"
            disabled={isDisabled}
            value={''}
          />
          <TextInput
            label="PO No"
            className="mb20"
            disabled={isDisabled}
            value={''}
          />
          <DatePickerInput
            className="mb20"
            label="Revision Date"
            disabled={isDisabled}
            value={''}
          />
          <TextAreaInput
            label="Supplier"
            className="mb20"
            disabled={isDisabled}
            value={''}
          ></TextAreaInput>

          <TextAreaInput
            label="Ship To"
            className="mb20"
            disabled={isDisabled}
            value={''}
          ></TextAreaInput>

          <TextAreaInput
            label="Ship Via"
            className="mb20"
            value={''}
            disabled={isDisabled}
          ></TextAreaInput>

          <DatePickerInput
            className="mb20"
            label="Shipped Date"
            disabled={isDisabled}
            value={''}
          />

          <DatePickerInput
            className="mb20"
            label="Due Date"
            disabled={isDisabled}
            value={''}
          />
          <TextInput
            label="Payment Terms"
            className="mb20"
            disabled={isDisabled}
            value={''}
          />
          <TextInput
            label="Tax Code"
            className="mb20"
            disabled={isDisabled}
            value={''}
          />
          <DatePickerInput
            className="mb20"
            label="Baseline Date"
            disabled={isDisabled}
            value={''}
          />

          <div className="flex-row align-end mb40">
            <Select
              label="Total Amount"
              className="mr20"
              style={{ width: 96 + 'px' }}
              disabled={isDisabled}
            />
            <TextInput
              label=""
              className="flex-shrink"
              value={''}
              disabled={isDisabled}
            />
          </div>
          <ImageSwitcher
            className={'is-' + props.view + ' mb20'}
            onClick={props.imageClickHandler}
            label="View Invoice"
          />
        </div>
        <div
          className={['richContentContainer', props.imgHandle[0]].join(' ')}
          onClick={props.imageClickHandler}
        ></div>
        <div className="force-full-width">
          <h2 className="page-sub-title mb20">Item List</h2>
          <div className="mb24 styledDatatable">
            <DataTable
              highlightOnHover={true}
              noHeader={true}
              striped={true}
              dense={true}
              className="mb24"
              columns={invoiceColumns}
              data={[]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export { InvoiceBoxComponent };
