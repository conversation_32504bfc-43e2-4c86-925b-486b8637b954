import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';

const ExpandableItems = (props) => {
  const [tableColumns, setTableColumns] = useState([]);
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (props !== undefined) {
      setTableColumns(props.columns);
      setTableData(props.innerTableData);
    }
  }, [props]);

  return (
    <div className="two-col-layout-20 expandable-items-custom-styling force-max-width styledDatatable">
      <DataTable
        highlightOnHover={true}
        noHeader={true}
        striped={true}
        dense={true}
        columns={tableColumns}
        data={tableData}
      />
    </div>
  );
};
export default ExpandableItems;
