/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */

import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { getViewRolesListing, showDialog } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { useAppRouterDom } from '@hp/utils';
import { ButtonCommon, dataTableServiceProvider } from '@hp/components';
import { TextInput } from '@hp/components';
import { useLocation } from 'react-router-dom';

const CreateRoles = (props) => {
  const { domParameters, location, navigate } = useAppRouterDom();
  const { pathname } = useLocation();
  const segments = pathname.split('/').filter(Boolean);
  const menuData = domParameters?.menuData || segments[0] || '';
  const submenu = domParameters?.submenu || segments[1] || '';
  const parameters = domParameters.parameters;

  const alertMessage =
    location && location.state && location.state.message
      ? location.state.message
      : null;
  const roleIdList =
    location && location.state && location.state.roleIdList
      ? location.state.roleIdList
      : null;
  let module = '';
  if (parameters && parameters.indexOf(',') > -1) {
    let moduleList = parameters.split(',');
    module = moduleList[1];
  }

  const dispatch = useDispatch();
  const { viewroleslist } = useSelector((store) => store.admin);
  const [TableListing, setTableList] = useState();
  const [searchBox, setSearchBox] = useState();
  const [roleIdValue, setRoleId] = useState(0);
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });

  useEffect(() => {
    getParameter();
    alertMessage && funcToSetResponseMessage('error', alertMessage);
    return () => {
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (viewroleslist && viewroleslist.value !== undefined) {
      if (roleIdList && roleIdList.length > 0) {
        let affectedRolesList = [];
        roleIdList.map((roleId) => {
          affectedRolesList.push(
            viewroleslist.value.filter((role) => {
              return roleId === role.roleId;
            })[0]
          );
        });
        setTableList(affectedRolesList);
        setSearchBox(affectedRolesList);
        return;
      }
      setTableList(viewroleslist.value);
      setSearchBox(viewroleslist.value);
    }
  }, [viewroleslist]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    dispatch(
      showDialog({
        showPopup,
        type: type,
        responseMessage: resMessage,
        canClose,
      })
    );
  };

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const getParameter = () => {
    dispatch(getViewRolesListing(module));
  };

  const DataTableEventHandler = (e) => {
    let roleId = e.roleId;
    navigate(`/${menuData}/${submenu}/EditAndSaveRoles/${roleId}/${module}`, {
      state: 'edit',
    });
  };

  //on clicking these buttons it will navigate to EditandSaveRoles component where menus will be displayed for creating and updating roles
  const handleCreate = () => {
    navigate(`/${menuData}/${submenu}/EditAndSaveRoles/0/${module}`, {
      state: 'create',
    });
  };

  //on clicking these buttons it will navigate to EditandSaveRoles component where menus will be displayed,
  //and it will have a copy of a source roles and it will enable you to edit.Hence new role will be created
  const handleCreateAs = () => {
    let roleId = roleIdValue;
    roleId === 0
      ? funcToSetResponseMessage('error', 'Please select a role to continue')
      : navigate(
          `/${menuData}/${submenu}/EditAndSaveRoles/${roleId}/${module}`,
          { state: 'createAs' }
        );
  };

  const getRoleId = (e) => {
    let roleId = e.roleId;
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      e.roleId,
      'roleId'
    );
    setConditionalStyles(styleAttribute);
    setRoleId(roleId);
  };

  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const functionsName = {
    handleCreate,
    handleCreateAs,
  };

  const columns = [
    {
      cell: function displayCell(row) {
        return (
          <div className="role-align">
            <a onClick={() => DataTableEventHandler(row)}>
              <i className="icon-edit-button"></i>
            </a>
          </div>
        );
      },
      width: 50 + 'px',
    },
    {
      name: 'Role Name',
      selector: 'roleName',
      sortable: true,
    },
    {
      name: 'Description',
      selector: 'description',
      sortable: true,
    },
  ];

  return (
    <>
      <div className="page-title">Create Roles</div>
      <div className="two-col-layout mb20">
        <div className="three-col-layout">
          <TextInput
            placeholder="Search"
            style={{ width: 105 + '%' }}
            value={filterString.filterStr}
            onChange={(e) => handleChange(e)}
          />
        </div>
      </div>
      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          theme={'solarized'}
          onRowClicked={(e) => getRoleId(e)}
          conditionalRowStyles={conditionalRowStyles}
        />
      </div>
      <div className="mb20"></div>
      <ButtonCommon functionsName={functionsName} />
    </>
  );
};

export { CreateRoles };
