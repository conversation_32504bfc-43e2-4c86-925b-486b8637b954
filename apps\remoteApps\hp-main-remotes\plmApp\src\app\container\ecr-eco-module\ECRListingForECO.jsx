/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
import { Button, dataTableServiceProvider, Input } from '@hp/components';
import './ecr_eco_common.scss';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { alertActions, pdmAction } from '@hp/mainstore';
import { pdmConstants } from '@hp/mainstore';
import { useAppRouterDom } from '@hp/utils';
import { globalutils } from '@hp/components';
const ECRListingForECO = (props) => {
  const { domParameters, navigate } = useAppRouterDom();
  const dispatch = useDispatch();
  const { ecrListingForEco } = useSelector((store) => store.pdm);

  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';

  const [formDetails, setFormDetails] = useState(null);
  const [selectedECRId, setSelectedECRId] = useState(null);
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);

  useEffect(() => {
    dispatch(pdmAction.getECRListingForECO(userId));
    return () => {
      let removeNotification = null;
      dispatch({
        type: pdmConstants.GET_ECR_LISTING_FOR_ECO_SUCCESS,
        removeNotification,
      });
      dispatch(alertActions.showDialog(false, '', '', true, true));
    };
  }, []);

  useEffect(() => {
    if (ecrListingForEco?.value) {
      setFormDetails(ecrListingForEco.value);
    }
  }, [ecrListingForEco]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      alertActions.showDialog(showPopup, type, resMessage, canClose, autoHide)
    );
  };

  function handleRowClicked(row) {
    setSelectedECRId(row.ecrId);
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      row.ecrId,
      'ecrId'
    );
    setConditionalRowStyles(styleAttribute);
  }

  const handleFormDetails = (data) => {
    return data.map((element, index) => {
      if (element.uniqueKey === 'approvedEcr') {
        return (
          <div className="inv-div">
            <Input
              dataTablePersistHead
              key={index}
              formType={element}
              dataTableEventHandler={(obj) => {
                handleRowClicked(obj);
              }}
              conditionalRowStyles={conditionalRowStyles}
            />
          </div>
        );
      }
    });
  };

  function handleCreateNewECO() {
    navigate({
      pathname: `/${menuData}/${submenu}/eco/create/new`,
    });
  }

  function handleCreateECOFromECR() {
    if (selectedECRId)
      navigate({
        pathname: `/${menuData}/${submenu}/eco/create/${selectedECRId}`,
        state: {
          operation: 'create',
          ecrId: selectedECRId,
        },
      });
    else funcToSetResponseMessage('error', 'Please Select an ECR');
  }
  return (
    <>
      {formDetails?.length ? handleFormDetails(formDetails) : ''}

      <div id="btn-bottom-white-bar" className="group fixed-button-bar">
        <Button
          onClick={() => handleCreateECOFromECR()}
          className="default mr20 fl button mb20"
        >
          Create ECO From ECR
        </Button>
        <Button
          onClick={() => handleCreateNewECO()}
          className="default mr20 fl button mb20"
        >
          Create ECO
        </Button>
      </div>
    </>
  );
};

export { ECRListingForECO };
