// This file can be replaced during build by using the `fileReplacements` array.
// When building for production, this file is replaced with `environment.prod.ts`.

import { AP_API_URL } from '@hp/constants';

// export const environment = {
//   production: false,
//   OktaClientID: "0oaa06cqzhQ1E5OZZ697",
//   OktaIssuer: "https://trial-4286632.okta.com",
//   oktaBaseURL: "https://trial-4286632.okta.com",
//   oktaRedirectUri: "http://localhost:3000/api/okta/oidc/login_redirect",
//   oktaINteractionCode: false,
// };

export const environment = {
  production: true,
  OktaClientID: '0oaz5s2zm7jXZkxmF357',
  OktaIssuer: 'https://luminar.okta.com',
  oktaBaseURL: 'https://luminar.okta.com',
  oktaRedirectUri: `${AP_API_URL}/okta/oidc/login_redirect`,
  oktaINteractionCode: false,
};
