/* eslint-disable react-hooks/exhaustive-deps */
import { Input } from '@hp/components';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ECRECOTree } from './ECRECOTree';
import { pdmConstants } from '@hp/mainstore';

const ECRViewInECO = () => {
  const dispatch = useDispatch();
  const { ecrDetailsForm, ecrSavedDocuments } = useSelector(
    (store) => store.pdm
  );

  const [ecrFormView, setEcrFormView] = useState(null);
  const [treeDataList, setTreeDataList] = useState(null);

  useEffect(() => {
    return () => {
      let removeNotification = null;
      dispatch({
        type: pdmConstants.VIEW_OR_EDIT_ECR_DETAILS_FORM_SUCCESS,
        removeNotification,
      });
      dispatch({
        type: pdmConstants.GET_ECR_SAVED_DOCUMENTS_SUCCESS,
        removeNotification,
      });
    };
  }, []);

  useEffect(() => {
    if (ecrDetailsForm?.value) {
      setEcrFormView(
        {
          ...ecrDetailsForm.value.find(
            (entry) => entry.uniqueKey === 'ecrView'
          ),
          displayName: 'Related ECR Summary',
        } ?? null
      );
    }
  }, [ecrDetailsForm]);

  useEffect(() => {
    if (ecrSavedDocuments?.value) {
      setTreeDataList(ecrSavedDocuments.value);
    }
  }, [ecrSavedDocuments]);

  return (
    <div>
      <div className="card">
        <div className="page-sub-title" style={{ marginRight: '20px' }}>
          {'Related Documents'}
        </div>
        <div
        // className="eco-related-docs-container"
        >
          {treeDataList ? (
            <ECRECOTree treeDataList={treeDataList} disabled />
          ) : (
            ''
          )}
          {/* <div className="eco-related-docs-item-left">
            {treeDataList ? (
              <ECRECOTree treeDataList={treeDataList} disabled />
            ) : (
              ''
            )}
          </div> */}
          {/* <div className="card eco-related-docs-item-right">
            {ecrFormView ? <Input formType={ecrFormView} /> : ''}
          </div> */}
          <div className="card">
            {ecrFormView ? <Input formType={ecrFormView} /> : ''}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ECRViewInECO;
