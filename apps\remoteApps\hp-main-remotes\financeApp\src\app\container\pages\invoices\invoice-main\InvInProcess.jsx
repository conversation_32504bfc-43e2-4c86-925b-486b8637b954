/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> <PERSON><PERSON>
 * @email <EMAIL>
 * @create date 25-10-2024 09:39:10
 * @modify date 25-10-2024 18:31:18
 * @desc [description]
 */
import React, { useState, useEffect } from 'react';
import { PageWrap, CommonSpinner } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { InvInProcessTable } from './InvInProcessTable';
import { useAppRouterDom } from '@hp/utils';
import { getInvInProcess } from '@hp/mainstore';
import '@hp/styles/ExpandedTable.scss';
import { globalutils } from '@hp/components';
const InvInProcess = (props) => {
  const dispatch = useDispatch();
  let user = globalutils.getDataFromStorage('all');
  let userId = user.userId;
  let clientId = user.clientId;
  const { domParameters, navigate, location } = useAppRouterDom();
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';
  // const parameter = domParameters?.parameters || '';
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);
  const { invInProcessResponse } = useSelector((state) => state.accpay);
  const { dynamicListLimitAndOffset } = useSelector((store) => store.util);
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [formDetails, setFormDetails] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    dispatch(
      getInvInProcess({
        clientId: clientId,
        userId: userId,
        limit: limit,
        offset: offset,
        type: 'subscribe',
      })
    );
  }, [clientId, userId, limit, offset, dispatch]);

  useEffect(() => {
    if (dynamicListLimitAndOffset) {
      setLimit(dynamicListLimitAndOffset?.limit);
      setOffset(dynamicListLimitAndOffset.offset);
    }
  }, [dynamicListLimitAndOffset]);

  useEffect(() => {
    if (invInProcessResponse && invInProcessResponse?.value) {
      setFormDetails(invInProcessResponse.value);
      setTableData(
        invInProcessResponse.value.find(
          (val) => val.uniqueKey === 'invInProcess'
        )?.value ?? []
      );
      setIsLoading(false);
    } else setFormDetails([]);
  }, [invInProcessResponse]);

  //function for setting notification
  // const funcToSetResponseMessage = (type, resMessage) => {
  //   let showPopup = true;
  //   let canClose = true;
  //   let autoHide = true;
  //   dispatch(
  //     alertActions.showDialog(showPopup, type, resMessage, canClose, autoHide)
  //   );
  // };

  const handleRowClicked = (row, viewType) => {
    const basePath = location.pathname.split('/Invoice')[0];

    const dynamicPath = `${basePath}/InProcessView/${row.serialNo}`;
    // navigate({ pathname: dynamicPath,, {
    //   state: {
    //     filePath: row.fileViewUrl,
    //     tableData: row.commonExpandedRowList,
    //   },
    // }});
    navigate({
      pathname: dynamicPath,
      state: {
        filePath: row.fileViewUrl,
        tableData: row.commonExpandedRowList,
      },
    });
  };

  function formControlsBinding(data) {
    return data.map((element, index) => {
      if (element.uniqueKey === 'invInProcess') {
        return (
          <div key={index}>
            <InvInProcessTable
              formDetails={element}
              handleRowClicked={(row) => handleRowClicked(row, 'view')}
              handleViewTable={(row) => handleRowClicked(row, 'edit')}
              key={index}
            />
          </div>
        );
      }
    });
  }

  return (
    <>
      <div
        className="page-title"
        style={{
          display: 'flex',
          marginBottom: '16px',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <h1>
          {subMenuName} / {innerMenuName}
        </h1>
      </div>
      <CommonSpinner visible={isLoading} />
      {formDetails?.length && !isLoading
        ? formControlsBinding(formDetails)
        : ''}
    </>
  );
};

export { InvInProcess };
