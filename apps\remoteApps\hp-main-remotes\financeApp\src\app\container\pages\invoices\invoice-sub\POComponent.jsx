/* eslint-disable array-callback-return */
/* eslint-disable no-lone-blocks */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @create date 27-05-2021 10:42:42
 * @modify date 19-05-2022 14:26:36
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { accPayConstants } from '@hp/mainstore';
import { AP_poIdConstant, AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import { CommonSpinner, TextInput } from '@hp/components';
import {
  getServiceReqTypesComboforPOs,
  getAccapaySerReqListsPO,
} from '@hp/mainstore';
import { getPODetailsForAccPay } from '@hp/mainstore';
import { globalutils } from '@hp/components';
const POComponent = (props) => {
  const dispatch = useDispatch();

  const poId = props.poId ? props.poId : null;
  const [isLoading, setisLoading] = useState(true);

  const [poNumberString, setPoNumberString] = useState('');
  const [poFormDetails, setPOFormDetails] = React.useState({
    allDataArray: {
      amountConverted: '',
      clientId: '',
      connKeyId: '',
      currencyId: '',
      description: '',
      eventTime: '',
      filePath: '',
      formDetailsDtoList: [],
      grn_id: '',
      inv_id: '',
      poItemsDtoList: [],
      poNumber: '',
      po_id: '',
      releaseNumber: '',
      status: '',
      supplierId: '',
      userId: '',
    },
  });

  const { accpayPoDetails, serviceReqTypesforPO, serReqListPO } = useSelector(
    (store) => store.accpay
  );
  const [servReqPoLength, setServReqPoLength] = useState();
  let user = globalutils.getDataFromStorage('all');
  const userId = user?.userId;

  useEffect(() => {
    if (serReqListPO && serReqListPO.value) {
      setServReqPoLength(serReqListPO.value.length);
    }
  }, [serReqListPO]);

  const [sendRequest, setSendRequest] = useState();

  const [filterString, setFilterString] = useState({
    filterStr: '',
  });

  const [Filter, setFilterData] = useState({ FilteredData: [] });

  useEffect(() => {
    if (poFormDetails && poFormDetails.allDataArray) {
      let tempArray = poFormDetails.allDataArray;
      tempArray.poAndReleaseNo
        ? setPoNumberString(tempArray.poAndReleaseNo)
        : tempArray.poNumber
        ? setPoNumberString(tempArray.poNumber)
        : null;
    }
  }, [poFormDetails]);

  useEffect(() => {
    getPoInvResponse();
    return () => {
      let details = null;
      let poListOnSearchPo = null;
      setPOFormDetails([]);
      dispatch({
        type: accPayConstants.ACCPAY_PO_DETAILS_SUCCESS,
        details,
      });
      dispatch({
        type: accPayConstants.SEARCH_PO_SUCCESS,
        poListOnSearchPo,
      });
    };
  }, []);

  useEffect(() => {
    poId ? getPoInvResponse() : null;
  }, [poId]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setFilterData({
          FilteredData: poFormDetails.allDataArray.poItemsDtoList,
        })
      : null;
    globalSearch(poFormDetails.allDataArray.poItemsDtoList);
  }, [filterString.filterStr]);

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setFilterData({ FilteredData: filteredData });
    }
  };

  const getPoInvResponse = async () => {
    if (poId !== null && poId !== undefined) {
      dispatch(
        getPODetailsForAccPay({ key: AP_poIdConstant, id: poId, userId })
      );
      dispatch(getServiceReqTypesComboforPOs('PO'));
      dispatch(
        getAccapaySerReqListsPO({
          idName: 'PO',
          id: poId,
          moduleCode: 'AP',
          userId: userId,
        })
      );
    }
  };
  //ItemList Search
  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  useEffect(() => {
    if (accpayPoDetails && accpayPoDetails !== 'cleared') {
      accpayPoDetails.value
        ? setPOFormDetails({ allDataArray: accpayPoDetails.value })
        : '';
      setFilterData({ FilteredData: accpayPoDetails.value.poItemsDtoList });
    }
    setisLoading(false);
  }, [accpayPoDetails]);

  // let sendRequest = [{ value: "Other request", display: "Other request" }];
  useEffect(() => {
    if (serviceReqTypesforPO && serviceReqTypesforPO.value) {
      const Options = serviceReqTypesforPO.value.map((value) => {
        return {
          value: value.serReqId,
          display: value.serReqType,
        };
      });
      setSendRequest(Options);
    }
  }, [serviceReqTypesforPO]);

  return (
    <div className="boxed">
      {/* <h1 className="page-title">PO# {poNumberString}</h1> */}
      <div className="form-and-image">
        <div className="force-full-width">
          <h2 className="page-sub-title mb20">Item List</h2>
          <TextInput
            placeholder="Search"
            // label={false}
            style={{ width: 40 + '%' }}
            value={filterString.filterStr}
            onChange={(e) => handleChange(e)}
          />
          <div className="mb20"></div>
          {poFormDetails.allDataArray.formDetailsDtoList &&
          poFormDetails.allDataArray.formDetailsDtoList.length
            ? poFormDetails.allDataArray.formDetailsDtoList.map(function (
                data,
                idx
              ) {
                const DisplayTitle = (row, key) => {
                  return (
                    <div
                      className="display-title custom-overflow"
                      title={row[key]}
                    >
                      {row[key]}
                    </div>
                  );
                };
                switch (data.type) {
                  case 'DataTable': {
                    {
                      return Array.isArray(
                        poFormDetails.allDataArray.poItemsDtoList
                      ) ? (
                        <div className="mb24 styledDatatable">
                          <DataTable
                            key={idx}
                            highlightOnHover={true}
                            noHeader={true}
                            striped={true}
                            dense={true}
                            // disabled={disabledFlag}
                            pagination={true}
                            paginationDefaultPage={1}
                            paginationResetDefaultPage={true}
                            paginationTotalRows={
                              poFormDetails.allDataArray.poItemsDtoList.length
                            }
                            paginationPerPage={10}
                            columns={
                              data.formSubDetailsInternalDTOList &&
                              data.formSubDetailsInternalDTOList.length
                                ? data.formSubDetailsInternalDTOList.map(
                                    (value) => {
                                      return {
                                        name: value.displayName
                                          ? value.displayName
                                          : '',
                                        selector: value.selector
                                          ? value.selector
                                          : '',
                                        width: value.displayWidth
                                          ? value.displayWidth
                                          : '',
                                        cell: (row) =>
                                          DisplayTitle(row, value.selector),
                                      };
                                    }
                                  )
                                : null
                            }
                            data={Filter.FilteredData}
                          />
                        </div>
                      ) : null;
                    }
                  }
                }
              })
            : null}
        </div>
      </div>
      <CommonSpinner visible={isLoading} />
    </div>
  );
};
export { POComponent };
