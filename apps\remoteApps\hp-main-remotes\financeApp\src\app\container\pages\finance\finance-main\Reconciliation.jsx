/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { INTERNAL_SERVER_ERROR_ALERT, AP_USER } from '@hp/constants';
import { financeConstants } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { Ellipsis } from 'react-awesome-spinners';
import { globalutils } from '@hp/components';
import {
  getReconciliationStatement,
  getReconciliationPayments,
} from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';

const Reconciliation = (props) => {
  const table_Data = props.tableData ? props.tableData : '';
  const txnId = props.tableData ? props.tableData.txnId : '';
  const [view] = React.useState('view-1');
  const [statementList, setStatementList] = useState([]);
  const [paymentList, setPaymentList] = useState([]);
  const [isLoading, setLoading] = useState(true);
  let user = globalutils.getDataFromStorage('all');
  const clientId = parseInt(user?.clientId);
  const userId = parseInt(user?.userId);
  const dispatch = useDispatch();
  const {
    reconciliationStatementList,
    reconciliationStatementListError,
    reconciliationPaymentList,
    reconciliationPaymentListError,
  } = useSelector((store) => store.finance);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: '',
      })
    );
  };

  useEffect(() => {
    dispatch(
      getReconciliationStatement({
        tableName: 'bankIn',
        userId: userId,
        clientId: clientId,
        txnId: txnId,
      })
    );
    dispatch(
      getReconciliationPayments({
        tableName: 'bankOut',
        userId: userId,
        clientId: clientId,
        txnId: txnId,
      })
    );
  }, [table_Data]);

  useEffect(() => {
    let error = null;
    if (reconciliationStatementList && reconciliationStatementList.value) {
      setLoading(false);
      setStatementList(
        reconciliationStatementList.value.bankTxnDetailsTableDataDTOList
      );
    }
    if (reconciliationStatementListError) {
      setLoading(false);
      funcToSetResponseMessage('error', INTERNAL_SERVER_ERROR_ALERT);
      dispatch({
        type: financeConstants.RECONCILIATION_STATEMENT_LIST_ERROR,
        error,
      });
    }
  }, [reconciliationStatementList, reconciliationStatementListError]);

  useEffect(() => {
    let error = null;
    if (reconciliationPaymentList && reconciliationPaymentList.value) {
      setLoading(false);
      setPaymentList(
        reconciliationPaymentList.value.bankTxnDetailsTableDataDTOList
      );
    }
    if (reconciliationPaymentListError) {
      setLoading(false);
      funcToSetResponseMessage('error', INTERNAL_SERVER_ERROR_ALERT);
      dispatch({
        type: financeConstants.RECONCILIATION_PAYMENT_LIST_ERROR,
        error,
      });
    }
  }, [reconciliationPaymentList, reconciliationPaymentListError]);

  const statementInfo = [
    { name: 'Txn. #', selector: 'txnRefNumber' },
    { name: 'Payment Ref. #', selector: 'pymtRefNo' },
    { name: 'Txn Date', selector: 'txnDate' },
    { name: 'Account Number', selector: 'accountNumber' },
    { name: 'Currency', selector: 'currency' },
    { name: 'Amount', selector: 'amountConverted' },
  ];
  const paymentInfo = [
    { name: 'Txn. #', selector: 'refNumber' },
    { name: 'Due Date', selector: 'dueDate' },
    { name: 'Account Number', selector: 'accountNumber' },
    { name: 'Currency', selector: 'currency' },
    { name: 'Amount', selector: 'amountConverted' },
  ];

  return (
    <div className="invoice-compare">
      <div className={'mb20 viewHolder ' + view}>
        <div className="boxed mb24 styledDatatable">
          <h1 className="page-title">Statements</h1>
          <DataTable
            persistTableHead
            highlightOnHover={true}
            noHeader={true}
            striped={true}
            customStyles={{
              rows: {
                style: {
                  '&:hover': {
                    cursor: 'pointer',
                  },
                },
              },
            }}
            columns={statementInfo}
            data={statementList}
            progressPending={isLoading}
            progressComponent={<Ellipsis color={globalutils.spinnerColor()} />}
          />
        </div>
        <div className="boxed mb24 styledDatatable">
          <h1 className="page-title">Payments</h1>
          <DataTable
            persistTableHead
            highlightOnHover={false}
            noHeader={true}
            striped={true}
            columns={paymentInfo}
            data={paymentList}
            progressPending={isLoading}
            progressComponent={<Ellipsis color={globalutils.spinnerColor()} />}
          />
        </div>
      </div>
    </div>
  );
};

export { Reconciliation };
