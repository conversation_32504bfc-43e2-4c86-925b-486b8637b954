/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-11-17 12:54:50
 * @modify date 2024-07-29 15:46:01
 * @desc [description]
 */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { dataTableServiceProvider, Input } from '@hp/components';
import { Button } from '@hp/components';
import {
  getAllEmailForStatusCheck,
  getAllEmailForStatusCheckByClientID,
  getAllEmailForStatusCheckByClientIDAndInOut,
  getAllEmailForStatusCheckByInOut,
  getEmailStatusForSingleConnection,
  getFtpForAllConnections,
  showDialog,
} from '@hp/mainstore';

function Email() {
  const dispatch = useDispatch();

  const [selectionfield, setSelectionfield] = useState('');
  const [datatable, setDatatable] = useState('');
  const [clientId, setClientId] = useState('');
  const [inOut, setInOut] = useState('');
  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const { emailRes } = useSelector((store) => store.admin);

  const { innerMenuName } = useSelector((store) => store.menu);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  //initial api call ....

  useEffect(() => {
    dispatch(getAllEmailForStatusCheck());
  }, []);

  // reducer value handler

  useEffect(() => {
    if (emailRes && emailRes.value) {
      if (!selectionfield) {
        setSelectionfield(emailRes.value[0]);
        setDatatable(emailRes.value[1]);
        dispatch(getFtpForAllConnections(emailRes.value[1].value));
      } else {
        setDatatable(emailRes.value[1]);
      }
    }
  }, [emailRes]);

  const SingleConnectionHandler = (SingleData) => {
    dispatch(getEmailStatusForSingleConnection(SingleData));
  };

  // setting field values .........
  const selectionHandler = (element, event) => {
    const updatedValue = event.target.value;

    const updatedFormList = selectionfield.formSubDetailsInternalDTOList.map(
      (item) => {
        if (item.formSubDetailsId === element.formSubDetailsId) {
          return {
            ...item,
            value: updatedValue,
          };
        }
        return item;
      }
    );

    setSelectionfield({
      ...selectionfield,
      formSubDetailsInternalDTOList: updatedFormList,
    });

    if (element.displayName === 'Select Client') {
      setClientId(updatedValue);
    } else {
      setInOut(updatedValue);
    }
  };

  // search....../

  const searchHandler = () => {
    if (clientId && inOut) {
      dispatch(
        getAllEmailForStatusCheckByClientIDAndInOut({
          ClientId: clientId,
          InOut: inOut,
        })
      );
    } else if (clientId) {
      dispatch(getAllEmailForStatusCheckByClientID(clientId));
    } else if (inOut) {
      dispatch(getAllEmailForStatusCheckByInOut(inOut));
    } else {
      funcToSetResponseMessage('info', 'select one field');
    }
  };

  const clearHandler = () => {
    const Clearall = selectionfield.formSubDetailsInternalDTOList.map(
      (item) => {
        return { ...item, value: null };
      }
    );
    setSelectionfield({
      ...selectionfield,
      formSubDetailsInternalDTOList: Clearall,
    });
    setClientId('');
    setInOut('');
  };

  const onRowClick = (data) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      data.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
  };

  return (
    <>
      <div className="page-title">{innerMenuName} </div>
      {selectionfield && selectionfield.formSubDetailsInternalDTOList.length ? (
        <div className={selectionfield.className}>
          {' '}
          {selectionfield.formSubDetailsInternalDTOList.map((item) => {
            return (
              <Input
                indexKey={item.formSubDetailsId}
                formType={item}
                onChangeHandler={(element, event) =>
                  selectionHandler(element, event)
                }
                isEditable={'notShowing'}
              />
            );
          })}
          <div>
            <Button
              className="small default mr4 "
              style={{ marginTop: '24px' }}
              onClick={clearHandler}
            >
              {' '}
              Clear{' '}
            </Button>{' '}
            <Button
              className="small default"
              style={{ marginTop: '24px' }}
              onClick={searchHandler}
            >
              {' '}
              Search{' '}
            </Button>{' '}
          </div>{' '}
        </div>
      ) : (
        ''
      )}
      {datatable ? (
        <div className={datatable.className}>
          <Input
            formType={datatable}
            conditionalRowStyles={conditionalRowStyles}
            dataTableColumn={
              datatable.formSubDetailsInternalDTOList &&
              Array.isArray(datatable.formSubDetailsInternalDTOList) &&
              datatable.formSubDetailsInternalDTOList.length
                ? datatable.formSubDetailsInternalDTOList.map((value) => {
                    return {
                      width:
                        value.selector === 'serialNo'
                          ? '6%'
                          : value.width
                          ? value.width
                          : '',
                      name: value.displayName ? value.displayName : '',
                      selector: value.selector ? value.selector : '',
                      cell:
                        value.selector === 'refreshIcon'
                          ? function displayCell(row) {
                              return (
                                <div
                                  className="icon-refresh"
                                  onClick={() => {
                                    SingleConnectionHandler(row);
                                    onRowClick(row);
                                  }}
                                ></div>
                              );
                            }
                          : '',
                    };
                  })
                : []
            }
          />
        </div>
      ) : (
        ''
      )}
    </>
  );
}

export default Email;
