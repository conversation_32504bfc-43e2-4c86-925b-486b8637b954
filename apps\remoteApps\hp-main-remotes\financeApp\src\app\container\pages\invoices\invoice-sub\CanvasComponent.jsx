import React, { useRef, useEffect, useState } from 'react';

const CanvasComponent = () => {
  const canvasRef = useRef(null);
  const [nodes, setNodes] = useState([]);
  const [connections, setConnections] = useState([]);

  useEffect(() => {
    const generateRow = (startId, startY) => {
      const rowNodes = [];
      const rowConnections = [];

      for (let i = 0; i < 5; i++) {
        const id = startId + i;
        const x = 50 + i * 100;
        const y = startY;
        rowNodes.push({ id, x, y });

        if (i < 4) {
          rowConnections.push({ from: id, to: id + 1 });
        }
      }

      return { nodes: rowNodes, connections: rowConnections };
    };

    const row1 = generateRow(1, 50);
    const row2 = generateRow(8, 150);

    setNodes([...row1.nodes, ...row2.nodes]);
    setConnections([...row1.connections, ...row2.connections]);

    const fourthNodeFirstRow = row1.nodes.find((node) => node.id === 4);
    const ninthNodeSecondRow = row2.nodes.find((node) => node.id === 9);
    setConnections((connections) => [
      ...connections,
      { from: fourthNodeFirstRow.id, to: ninthNodeSecondRow.id, dashed: true },
    ]);
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    nodes.forEach((node) => {
      ctx.beginPath();
      ctx.arc(node.x, node.y, 20, 0, 2 * Math.PI);
      ctx.fillStyle = 'lightblue';
      ctx.fill();
      ctx.stroke();

      ctx.fillStyle = 'black';
      ctx.fillText(node.id, node.x - 5, node.y + 5);
    });

    connections.forEach((connection) => {
      const fromNode = nodes.find((node) => node.id === connection.from);
      const toNode = nodes.find((node) => node.id === connection.to);

      ctx.beginPath();

      const angle = Math.atan2(toNode.y - fromNode.y, toNode.x - fromNode.x);
      const offsetX = 20 * Math.cos(angle);
      const offsetY = 20 * Math.sin(angle);

      ctx.moveTo(fromNode.x + offsetX, fromNode.y + offsetY);

      if (connection.dashed) {
        ctx.setLineDash([5, 5]);
      }

      ctx.lineTo(toNode.x - offsetX, toNode.y - offsetY);
      ctx.stroke();

      if (connection.dashed) {
        ctx.setLineDash([]);
      }
    });
  }, [nodes, connections]);

  return (
    <canvas
      ref={canvasRef}
      width={600}
      height={300}
      style={{ border: '1px solid black' }}
    />
  );
};

export default CanvasComponent;
