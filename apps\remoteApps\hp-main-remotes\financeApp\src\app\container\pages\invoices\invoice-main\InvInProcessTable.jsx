/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
import Pagination from '@hp/components';
import React, { useState, useEffect, useCallback } from 'react';

export const InvInProcessTable = ({
  formDetails,
  newPrList,
  handleViewTable,
  handleDelete,
  handleRowClicked,
}) => {
  const [toggle, setToggle] = useState([]);
  const [paginationRows, setPaginationRows] = useState([]);
  const [attributes, setAttributes] = useState({
    currentPage: 1,
    dataPerPage: 10,
  });

  useEffect(() => {
    if (formDetails?.value) {
      const { currentPage, dataPerPage } = attributes;
      const indexOfLastData = currentPage * dataPerPage;
      const indexOfFirstData = indexOfLastData - dataPerPage;
      setPaginationRows(
        formDetails.value.slice(indexOfFirstData, indexOfLastData)
      );
    }
  }, [attributes.currentPage, formDetails]);

  const Header = useCallback(
    ({ headerCol }) => (
      <div
        className="header-single"
        style={{ width: headerCol?.displayWidth || '10%' }}
      >
        {headerCol.displayName || ''}
      </div>
    ),
    []
  );

  const toggleRow = (event, id) => {
    event.stopPropagation();
    setToggle((prevToggle) =>
      prevToggle.includes(id) ? prevToggle.filter((item) => item !== id) : [id]
    );
  };

  const Rows = useCallback(
    ({ rows, header, rowIndex }) => (
      <div
        className={`table-row ${toggle.includes(rowIndex) ? 'open' : 'close'}`}
      >
        <div
          className="table-row-flex white"
          onClick={() => handleRowClicked(rows)}
        >
          {header.map((item) => {
            if (item.selector === 'viewTable') {
              return (
                <div
                  className="row-single"
                  style={{ width: '5%', textAlign: 'right' }}
                >
                  <span
                    className="icon-eye"
                    onClick={() => handleViewTable(rows)}
                  />
                </div>
              );
            }
            return (
              <div
                className="row-single"
                style={{ width: item.displayWidth || '10%' }}
              >
                {rows[item.selector]}
              </div>
            );
          })}
          {rows.commonExpandedRowList.length > 0 && (
            <div
              className="row-single has-open-button"
              style={{
                width: '4%',
                textAlign: 'center',
                position: 'absolute',
                right: '20px',
              }}
            >
              <div
                className="openButton"
                onClick={(e) => toggleRow(e, rowIndex)}
              >
                <i className="icon-caret" />
              </div>
            </div>
          )}
        </div>
        {toggle.includes(rowIndex) && (
          <div className="table-row-content">
            <div className="table-sub-row">
              {rows.commonExpandedRowList.map((item, index) => (
                <ExpandedRowContent item={item} index={index} key={index} />
              ))}
            </div>
          </div>
        )}
      </div>
    ),
    [toggle]
  );

  const ExpandedRowContent = ({ item, index }) => (
    <div
      className="single-list"
      style={{ margin: '0px 50px 8px', width: 'calc(100% - 100px)' }}
    >
      <InputControl label="#" value={index + 1} width="10%" />
      <InputControl label="Received At #" value={item.rcvd_at} width="10%" />
      <InputControl label="File Name" value={item.file_name} width="10%" />
    </div>
  );

  const InputControl = ({ label, value, width }) => (
    <div className="alt-input-control" style={{ width }}>
      <input disabled type="text" value={value} />
      <label className="label">{label}</label>
    </div>
  );

  const paginate = (pageNumber) =>
    setAttributes((prev) => ({ ...prev, currentPage: pageNumber }));

  return (
    <div>
      {formDetails && (
        <div className="table-div">
          <div className="table-header">
            {formDetails.formSubDetailsInternalDTOList?.map(
              (headerColumn, index) => (
                <Header headerCol={headerColumn} key={index} />
              )
            )}
          </div>
          <div className="table-body mb20">
            {paginationRows.length > 0 ? (
              paginationRows.map((row, index) => (
                <Rows
                  rows={row}
                  header={formDetails.formSubDetailsInternalDTOList}
                  rowIndex={index}
                  key={index}
                />
              ))
            ) : (
              <NoRecords />
            )}
          </div>
          {formDetails?.value?.length > 10 && (
            <Pagination
              dataPerPage={attributes.dataPerPage}
              totalPosts={formDetails.value.length}
              paginate={paginate}
            />
          )}
        </div>
      )}
    </div>
  );
};

const NoRecords = () => (
  <div className="table-row close">
    <div className="table-row-flex white">
      <div
        className="table-sub-row"
        style={{ width: '100%', textAlign: 'center' }}
      >
        <p style={{ paddingTop: '10px' }}>There are no records to display</p>
      </div>
    </div>
  </div>
);
