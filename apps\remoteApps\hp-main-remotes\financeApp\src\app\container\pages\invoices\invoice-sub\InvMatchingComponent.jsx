/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @create date 13-07-2021 10:13:03
 * @modify date 09-05-2024 11:44:40
 * @desc [description]
 */
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';

import '@hp/styles/InvoiceCompare.scss';
import { InvDatatable } from './inv-table/INV-DataTable.jsx';
import { cloneDeep } from 'lodash';
import { ItemListModal } from '../invoice-common/ItemListModal.jsx';
import { useUnmount } from 'react-use';
import { accPayConstants } from '@hp/mainstore';
import { formValidationUtil, globalutils, Input } from '@hp/components';
import {
  saveNewInvDetails,
  passINVEditedDetails,
  getDueDateCalculated,
  getEntityChangeRes,
  getSupplierFilteredByClientWithCode,
} from '@hp/mainstore';
import { inputTextDescription } from '@hp/mainstore';
import { passInvEventTime } from '@hp/mainstore';
const InvMatchingComponent = (props) => {
  const {
    tableName,
    disabled,
    addCommentsValue,
    onSaveClick,
    onSubmittedClick,
    onSaveDisable,
    onSubmittedClickDisable,
    notificationBar,
    onSubmitvalidationSuccess,
    setisLoading,
    invItemsOnAdd,
    passValueToParent,
    setDisToolTip,
    setDisabled,
    onFocusHandler,
    threeColLayout,
  } = props;

  const dispatch = useDispatch();
  const {
    accpayINVDetails,
    accpayEntityChange,
    serReqList,
    serviceReqTypes,
    multiselectFilterData,
    multiselectFilterWithCodeClientData,
    dueDateFromBaselineDate,
  } = useSelector((store) => store.accpay);

  const { inputDescription } = useSelector((store) => store.util);

  const invId = parseInt(props.invId);
  const [sendRequest, setSendRequest] = useState();
  const [poOrInvResponse, setPoOrInvResponse] = useState([]);

  const [servReqInvLength, setServReqInvLength] = useState();

  const [disabledFlag, setDisabledFlag] = useState(true);
  const [classToDisabled, setClassToDisabled] =
    React.useState('is-not-editing');
  const [baselineDate, setBaselineDate] = React.useState();
  const [paymentTermsId, setPaymentTermsId] = useState();
  const [multiselectFilterValue, setMultiselectFilterValue] =
    React.useState('');
  const [itemListMessage, setItemListMessage] = React.useState('');
  const [itemClickModal, setItemClickModal] = React.useState();
  const [filePath, setFilePath] = React.useState('');
  const docRef = useRef(null);
  let user = globalutils.getDataFromStorage('all');
  const clientId = user.clientId;
  const [dueDateList, setDueDateList] = React.useState({});
  const [invDate, setInvoiceDate] = React.useState({});

  useUnmount(() => {
    setPoOrInvResponse([]);
    let removeNotification = null;
    dispatch({
      type: accPayConstants.ACCPAY_ENTITY_CHANGE_RESPONSE_SUCCESS,
      removeNotification,
    });
  });

  useEffect(() => {
    if (poOrInvResponse) {
      let formDetails = cloneDeep(poOrInvResponse);

      formDetails?.formDetailsDtoList?.map(function (data, index) {
        if (data.uniqueKey === 'baselineDate') {
          if (
            dueDateList &&
            dueDateList.dueDateFromBaselineDateList &&
            dueDateList.dueDateFromBaselineDateList.baselineDate !== undefined
          ) {
            formDetails.formDetailsDtoList[index].value =
              dueDateList.dueDateFromBaselineDateList.baselineDate;
          }
        }
        if (data.uniqueKey === 'dueDate') {
          if (
            dueDateList &&
            dueDateList.dueDateFromBaselineDateList &&
            dueDateList.dueDateFromBaselineDateList.dueDate !== undefined
          ) {
            formDetails.formDetailsDtoList[index].value =
              dueDateList.dueDateFromBaselineDateList.dueDate;
          }
        }
        return null;
      });
      setPoOrInvResponse({ ...formDetails });
    }
  }, [dueDateList]);

  useEffect(() => {
    if (inputDescription && inputDescription !== undefined) {
      let formDetails = cloneDeep(poOrInvResponse);

      if (formDetails && formDetails.formDetailsDtoList) {
        formDetails.formDetailsDtoList.map((item) => {
          if (item.uniqueKey === 'totalcharges') {
            if (item.formSubDetailsInternalDTOList[0]) {
              if (inputDescription.length === 1) {
                if (
                  inputDescription[0].taxType.id === '' &&
                  inputDescription[0].taxType.value
                ) {
                  item.formSubDetailsInternalDTOList[0].value = null;
                } else {
                  item.formSubDetailsInternalDTOList[0].value =
                    inputDescription;
                }
              } else {
                item.formSubDetailsInternalDTOList[0].value = inputDescription;
              }
            }
          }
        });
      }
      setPoOrInvResponse(formDetails);
    }
  }, [inputDescription]);

  useEffect(() => {
    if (
      multiselectFilterWithCodeClientData?.value &&
      poOrInvResponse?.formDetailsDtoList
    ) {
      let tempForm = cloneDeep(poOrInvResponse);
      tempForm?.formDetailsDtoList.map((entry) => {
        if (entry.uniqueKey === 'supplier') {
          entry.comboBoxOptions =
            multiselectFilterWithCodeClientData?.value.map((item) => {
              return {
                commonId: item.supplierId,
                commonName: item.supplierName,
              };
            }) ?? [];
        }
      });
      setPoOrInvResponse(tempForm);
    }
  }, [multiselectFilterWithCodeClientData]);

  useEffect(() => {
    if (poOrInvResponse) {
      if (passValueToParent) passValueToParent(poOrInvResponse);
    }
  }, [poOrInvResponse]);

  useEffect(() => {
    if (invItemsOnAdd) {
      var tempArray = cloneDeep(poOrInvResponse);
      /**
       * <AUTHOR> sherlin ( modified Aiswarya RB )
       * @email <EMAIL>
       * @desc [added for line items update ]
       */

      tempArray?.formDetailsDtoList?.map((item) => {
        item.type === 'DataTable' ? (item.value = invItemsOnAdd) : null;
      });

      tempArray.invItemsDtoList = invItemsOnAdd;
      setPoOrInvResponse(tempArray);
    }
  }, [invItemsOnAdd]);

  useEffect(() => {
    if (accpayEntityChange?.value) {
      let tempForm = { ...poOrInvResponse };
      tempForm?.formDetailsDtoList?.map((item, index) => {
        if (item.uniqueKey === 'itemList') {
          let itemList = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'itemList'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, itemList);
        } else if (item.uniqueKey === 'location') {
          let location = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'location'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, location);
        }
      });
      setPoOrInvResponse(tempForm);
    }
  }, [accpayEntityChange]);

  useEffect(() => {
    setDisabledFlag(disabled);
    if (disabled) {
      setClassToDisabled('is-not-editing');
    } else setClassToDisabled('is-editing');
  }, [disabled]);

  useEffect(() => {
    if (serReqList && serReqList.value) {
      setServReqInvLength(serReqList.value.length);
    }
  }, [serReqList]);

  useEffect(() => {
    if (onSubmittedClick) {
      if (tableName[1] === 'unmatched') {
        onSubmitvalidationSuccess(true);
        return;
      }
      checkValidation();
    }
    if (onSaveClick) {
      let invForms = cloneDeep(poOrInvResponse);

      invForms.userComments = addCommentsValue ? addCommentsValue : '';

      if (invForms) {
        var newArray = invForms?.formDetailsDtoList?.filter(function (el) {
          return el.uniqueKey && el.uniqueKey === 'itemList';
        });
        newArray && newArray.length > 0
          ? (invForms.invItemsDtoList = newArray[0].value)
          : '';
      }
      if (tableName[1] === 'cost-inv' && !invId) {
        dispatch(
          saveNewInvDetails({
            clientId: globalutils.getDataFromStorage('clientId'),
            userId: globalutils.getDataFromStorage('userId'),
            detailsData: invForms,
            status: 'cost-inv',
          })
        );
      } else {
        dispatch(
          passINVEditedDetails({
            clientId: globalutils.getDataFromStorage('clientId'),
            userId: globalutils.getDataFromStorage('userId'),
            detailsData: invForms,
          })
        );
      }

      dispatch(inputTextDescription([]));
    }
  }, [onSubmittedClick, onSaveClick]);

  useEffect(() => {
    if (
      tableName &&
      (tableName[0] === 'PP' || tableName[0] === 'INV') &&
      accpayINVDetails &&
      accpayINVDetails.value.formDetailsDtoList &&
      accpayINVDetails.value.formDetailsDtoList.length
    ) {
      setFilePath(accpayINVDetails.value.filePath);
      props.filePath(accpayINVDetails.value.filePath);

      // Create a new array without modifying the original
      const filteredFormDetails =
        accpayINVDetails.value.formDetailsDtoList.filter(
          (data) => data.uniqueKey !== 'approvalForm'
        );

      // Create a new object to hold the updated response
      const updatedResponse = {
        ...accpayINVDetails.value,
        formDetailsDtoList: filteredFormDetails,
      };

      setPoOrInvResponse(updatedResponse);
    } else {
      null;
    }
  }, [accpayINVDetails]);

  useEffect(() => {
    if (accpayINVDetails) {
      dispatch(passInvEventTime(accpayINVDetails.value.eventTime));
      let formDetails = cloneDeep(accpayINVDetails.value);
      formDetails && formDetails.formDetailsDtoList
        ? formDetails.formDetailsDtoList.map(function (data) {
            if (data.uniqueKey === 'baselineDate') {
              data.value ? setBaselineDate(data.value) : setBaselineDate('');
            }
            if (data.uniqueKey === 'pymtTerms') {
              data.value
                ? setPaymentTermsId(data.value)
                : setPaymentTermsId(null);
            }
          })
        : null;
    }
  }, [accpayINVDetails]);

  useEffect(() => {
    if (serviceReqTypes && serviceReqTypes.value) {
      const Options = serviceReqTypes.value.map((value) => {
        return {
          value: value.serReqId,
          display: value.serReqType,
        };
      });
      setSendRequest(Options);
    }
  }, [serviceReqTypes]);

  useEffect(() => {
    dueDateFromBaselineDate && dueDateFromBaselineDate !== undefined
      ? setDueDateList({
          dueDateFromBaselineDateList: dueDateFromBaselineDate.value,
        })
      : '';
  }, [dueDateFromBaselineDate]);

  //function to calculate due date on baselinedate and paymentterms.
  const getDueDateFromBaselineDate = (uniqueName, targetValue) => {
    let formDetails = cloneDeep(poOrInvResponse);
    let invId = poOrInvResponse.inv_id;
    let clientId = poOrInvResponse.clientId;

    if (uniqueName === 'invDate') {
      var invoiceDate = targetValue;
      invoiceDate
        ? dispatch(
            getDueDateCalculated({
              invId,
              paymentTermsId,
              clientId,
              invoiceDate,
              baselineDate,
            })
          )
        : '';
    }
    if (uniqueName === 'pymtTerms') {
      let paymentTermsID = targetValue;
      paymentTermsID
        ? dispatch(
            getDueDateCalculated({
              invId,
              paymentTermsId: paymentTermsID,
              clientId,
              invoiceDate: invDate,
              baselineDate: baselineDate,
            })
          )
        : '';
    }
  };

  const handleINVOnChange = (event, uniqueKey) => {
    let formDetails = cloneDeep(poOrInvResponse);
    formDetails?.formDetailsDtoList?.map((data) => {
      if (data.uniqueKey && data.uniqueKey === uniqueKey) {
        data.value = event.target.value;
        data.errorFlag = false;
      }
      return null;
    });

    setPoOrInvResponse(formDetails);
  };

  const handleDateChange = (event, uniqueKey) => {
    let formDetails = cloneDeep(poOrInvResponse);
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = event)
        : null;
    });
    setPoOrInvResponse(formDetails);
    getDueDateFromBaselineDate(uniqueKey, event);
    setInvoiceDate(event);
  };

  //if paymentterms, handle combobox to calculate duedate by calling func "getDueDateFromBaselineDate".
  const handleComboBoxValue = (event, uniqueKey) => {
    let targetValue = parseInt(event.target.value);
    let formDetails = cloneDeep(poOrInvResponse);
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = targetValue)
        : null;
    });
    setPoOrInvResponse(formDetails);
  };

  useEffect(() => {
    let option = [];
    option =
      multiselectFilterData &&
      multiselectFilterData.value &&
      multiselectFilterData.value.length
        ? multiselectFilterData.value.map((item) => {
            const container = {};
            container.commonId = item.supplierId;
            container.commonName = item.supplierName;
            return container;
          })
        : '';

    poOrInvResponse?.formDetailsDtoList &&
      poOrInvResponse?.formDetailsDtoList?.length &&
      poOrInvResponse?.formDetailsDtoList?.map((formControl) => {
        if (formControl && formControl.uniqueKey === 'supplier') {
          formControl.comboBoxOptions = option ? option : [];
        }
      });
  }, [multiselectFilterData, multiselectFilterValue]);

  const handleMultiselectComboBoxValue = (event, uniqueKey) => {
    let formDetails = cloneDeep(poOrInvResponse);
    multiselectFilter(event, uniqueKey);
    if (!event.id || !event.value) return;
    formDetails.formDetailsDtoList.filter(function (data) {
      data.uniqueKey && data.uniqueKey === uniqueKey
        ? (data.value = event)
        : null;
    });
    setPoOrInvResponse(formDetails);
  };

  const handleAdvancedSelect = (event, uniqueKey) => {
    let formDetails = cloneDeep(poOrInvResponse);
    formDetails.formDetailsDtoList.filter(function (data) {
      data?.uniqueKey === uniqueKey ? (data.value = event) : '';
    });
    setPoOrInvResponse(formDetails);
    if (uniqueKey === 'pymtTerms') {
      getDueDateFromBaselineDate(uniqueKey, event?.id ?? event);
      setPaymentTermsId(event?.id ?? event);
    } else if (event?.id && uniqueKey === 'invEntity') {
      let payload = formDetails?.formDetailsDtoList?.filter(
        (item) =>
          item.uniqueKey === 'itemList' ||
          item.uniqueKey === 'totalcharges' ||
          item.uniqueKey === 'location'
      );
      dispatch(
        getEntityChangeRes({ clientId, entityId: event.id, payload, invId })
      );
    }
  };

  const onChangeHandlingFunctions = {
    handleINVOnChange,
    handleDateChange,
    handleComboBoxValue,
    handleMultiselectComboBoxValue,
    handleAdvancedSelect,
  };

  const handleAdvancedSelectSearch = (filter, element) => {
    if (element?.type === 'AdvancedSelect') {
      if (element?.uniqueKey === 'supplier') {
        dispatch(
          getSupplierFilteredByClientWithCode({
            filterField: filter,
            clientId,
            type: filter ? 'subscribe' : 'unSubscribe',
          })
        );
      }
    }
  };

  const multiselectFilter = (filter, uniqueKey) => {
    if (uniqueKey && uniqueKey === 'supplier') {
      if (filter?.target?.value) {
        setMultiselectFilterValue(filter?.target?.value);
      }
    }
  };

  useEffect(() => {
    if (multiselectFilterValue) {
      dispatch(
        getSupplierFilteredByClientWithCode({
          filterField: multiselectFilterValue,
          clientId,
          type: 'subscribe',
        })
      );
    }
  }, [multiselectFilterValue]);

  const checkValidation = () => {
    let formDetails = cloneDeep(poOrInvResponse);
    formDetails.userComments = addCommentsValue ? addCommentsValue : '';
    const iterableData = formDetails.formDetailsDtoList;
    let isValid = formValidationUtil.validateForm(iterableData);
    if (isValid.validSuccess) {
      onSubmitvalidationSuccess(true);
    } else {
      setisLoading(false),
        notificationBar(
          'error',
          'Process failed! Mandatory fields missing. ' + itemListMessage
        );
      setItemListMessage('Data missing in item list!');
    }
    setPoOrInvResponse({
      ...formDetails,
      formDetailsDtoList: isValid.formList,
    });
    onSaveDisable(false);
    onSubmittedClickDisable(false);
  };

  const getForms = () => {
    return poOrInvResponse?.formDetailsDtoList &&
      poOrInvResponse?.formDetailsDtoList.length
      ? poOrInvResponse.formDetailsDtoList.map((invControl, poindex) => {
          if (invControl.type !== 'DataTable') {
            if (invControl.uniqueKey !== 'creditNote') {
              return threeColLayout ? (
                <Input
                  getLabel
                  useIdasValue
                  formType={invControl}
                  isEditable="notShowing"
                  onChangeHandler={(element, event) =>
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey
                    )
                  }
                  onSearchHandler={(element, event) =>
                    handleAdvancedSelectSearch(event, element)
                  }
                  onFocusHandler={onFocusHandler}
                  multiselectFilter={multiselectFilter}
                  disabledFlag={disabled}
                />
              ) : (
                <div className="single" key={poindex}>
                  <div className="td label">
                    <p
                      className={
                        invControl.required === 'Y'
                          ? 'required-asterisk-field'
                          : ''
                      }
                    >
                      {invControl.displayName}
                    </p>
                  </div>

                  <div className="td po">
                    <Input
                      getLabel
                      useIdasValue
                      formType={invControl}
                      onChangeHandler={(element, event) =>
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey
                        )
                      }
                      onSearchHandler={(element, event) =>
                        handleAdvancedSelectSearch(event, element)
                      }
                      onFocusHandler={onFocusHandler}
                      multiselectFilter={multiselectFilter}
                      disabledFlag={disabled}
                    />
                  </div>
                </div>
              );
            }
          }
        })
      : '';
  };
  const onSaveItemList = (itemList) => {
    let formDetails = cloneDeep(poOrInvResponse);
    formDetails?.invItemsDtoList.push(itemList);
    formDetails.formDetailsDtoList.map((data) => {
      data && data.type && data.type === 'DataTable'
        ? data.value && data.value.length
          ? data.value.push(itemList)
          : (data.value = [itemList])
        : null;
    });
    setPoOrInvResponse(formDetails);
    props?.setPOINVCallback(formDetails);
    setItemClickModal(false);
  };

  const setValueOfDataTable = (dataTableValue) => {
    let formDetails = cloneDeep(poOrInvResponse);
    if (formDetails) {
      formDetails.invItemsDtoList = dataTableValue;
      formDetails?.formDetailsDtoList?.map((item) => {
        item.type === 'DataTable' ? (item.value = dataTableValue) : null;
      });
    }
    setPoOrInvResponse(formDetails);
  };
  const onDocPointer = () => {
    if (docRef.current) {
      docRef.current.scrollIntoView({ behavior: 'smooth' });
      setDisToolTip(true);
      setDisabled(false);
    }
  };

  return (
    <div
      className={[props.className, 'edit-only-half', classToDisabled].join(' ')}
    >
      <div className="inv-div">
        <div
          className="inv-table-view"
          style={{
            boxShadow: threeColLayout ? '0 1px 3px #c4bebe' : 'none',
          }}
        >
          <div className="inv-table-body">
            <div
              className={[
                'primary-secondary-table-wrap',
                tableName[0] === 'INV'
                  ? 'data-full-view-wrap'
                  : 'data-single-view-wrap',
              ].join(' ')}
            >
              <div
                className={` ${
                  threeColLayout ? 'three-col-layout' : 'inv-table-body-format'
                }`}
              >
                {getForms()}
              </div>
            </div>
          </div>
        </div>
        <div className="force-full-width">
          <div className="single-table-wrap">
            {tableName && (tableName[0] === 'PP' || tableName[0] === 'INV') ? (
              <InvDatatable
                disabledFlag={disabledFlag}
                status={tableName[1]}
                setItemClickModal={() => setItemClickModal(true)}
                setValueOfDataTable={setValueOfDataTable}
                invData={poOrInvResponse?.formDetailsDtoList}
                onDocPointer={onDocPointer}
                isEditable={props?.isEditable ? props?.isEditable : null}
                onSaveClick={onSaveClick}
                // invItemList={invItemList}
              />
            ) : null}
          </div>
        </div>
      </div>
      <div className="force-full-width">
        <ItemListModal
          isModalOpen={itemClickModal}
          isModalClose={() => setItemClickModal(false)}
          invId={invId}
          onSaveItemListClick={(event) => onSaveItemList(event)}
          poOrInvDto={poOrInvResponse?.formDetailsDtoList || []}

          // onDeleteClick={() => onDeleteItemList()}
          // serialErrorFlag={}
        />
      </div>
    </div>
  );
};
export { InvMatchingComponent };
