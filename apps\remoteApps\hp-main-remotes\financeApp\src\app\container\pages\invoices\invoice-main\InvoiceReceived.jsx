/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import { useEffectOnce } from 'react-use';
import { Tab, TabList, TabPanel, Tabs } from 'react-tabs';
import { AgingReport } from './AgingReport';
import { useAppRouterDom } from '@hp/utils';
import {
  Button,
  DateRangeInput,
  DocumentViewer,
  globalutils,
  Select,
  TextInput,
  CommonSpinner,
  DocUpload,
} from '@hp/components';
import {
  getInvDetailsbyCriteria,
  uploadFile,
  accPayConstants,
  showDialog,
} from '@hp/mainstore';

const InvoiceReceived = (props) => {
  const {
    invReceived,
    invReceivedByCriteria,
    uploadFileResponse,
    uploadFileError,
  } = useSelector((store) => store.accpay);
  const { domParameters } = useAppRouterDom();

  const dispatch = useDispatch();
  let user = globalutils.getDataFromStorage('all');
  const clientId = user?.clientId;
  const userId = user?.userId;
  let todaysDate = globalutils.formatDateMonth(new Date());
  const [toSearchDate, setToSearchDate] = useState();
  const [fromSearchDate, setFromSearchDate] = useState();

  const [invTableData, setinvTabledata] = useState({ TableListResponse: [] });
  const [Filter, setFilterData] = useState({ FilteredData: [] });
  const [modalIsOpen, setmodalIsOpen] = useState(false);
  const [filePath, setFilepath] = useState(null);
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [onSelectCriteria, setOnSelectCriteria] = useState('');
  const [onSearchCriteria, setOnSearchCriteria] = useState(null);
  const [comboOptions, setComboOptions] = useState([]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const fromDate = domParameters?.from || null;
  const toDate = domParameters?.to || null;
  const statusVal = domParameters?.status || null;
  const tabIndexVal = domParameters?.tabIndex || null;
  const payload = props?.location?.state ? props.location.state : null;
  const startDateFormat = globalutils.formatDate(fromDate, 'mm-dd-yyyy');
  const endDateFormat = globalutils.formatDate(toDate, 'mm-dd-yyyy');
  const newStartDate = globalutils.formatDateMonth(startDateFormat);
  const newtoDate = globalutils.formatDateMonth(endDateFormat);
  const [dateChange, setDateChange] = useState({
    fromDate: fromDate ? newStartDate : todaysDate,
    toDate: toDate ? newtoDate : todaysDate,
  });
  const [currentTab, setCurrentTab] = useState(0);

  const [attachPopup, setAttachPopup] = useState(false);
  const [isLoading, setLoading] = useState(false);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };
  useEffectOnce(() => {
    /**
     * <AUTHOR> Paul J
     * @email <EMAIL>
     * @description: Component didmount for checking the condition based on the URL .
     */

    if (fromDate && toDate) {
      dispatch(
        getInvDetailsbyCriteria({
          criteria: onSelectCriteria,
          conditionData: onSearchCriteria,
          clientId: clientId,
          fromSearchDate: fromDate,
          toSearchDate: toDate,
        })
      );
      setFromSearchDate(fromDate);
      setToSearchDate(toDate);
      setFilterString({ ...filterString, filterStr: 'Matched' });
    } else if (statusVal && tabIndexVal) {
      setCurrentTab(parseInt(tabIndexVal));
    }
  }, []);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setFilterData({ FilteredData: invTableData.TableListResponse })
      : null;
    globalSearch(invTableData.TableListResponse);
  }, [filterString.filterStr]);

  useEffect(() => {
    if (invReceived) {
      const tempArray = invReceived.value;
      var result = tempArray.map(function (el, i) {
        var obj = Object.assign({}, el);
        obj.fileicon = (
          <span
            key={i}
            className="icon-attach"
            onClick={() => rowIconClick(i, tempArray)}
          ></span>
        );
        return obj;
      });

      setinvTabledata({ TableListResponse: result });
      setFilterData({ FilteredData: result });
    }
  }, [invReceived]);

  useEffect(() => {
    if (uploadFileResponse && uploadFileResponse.value) {
      funcToSetResponseMessage('sucess', uploadFileResponse.value);
      setLoading(false);
    }
    return () => {
      dispatch({
        type: accPayConstants.UPLOAD_FILE_SUCCESS,
        payload: null,
      });
    };
  }, [uploadFileResponse]);

  useEffect(() => {
    if (uploadFileError) {
      setLoading(false);
    }
  }, [uploadFileError]);

  useEffect(() => {
    if (invReceivedByCriteria !== undefined) {
      const tempArray = invReceivedByCriteria?.value;
      var result = tempArray?.map(function (el, i) {
        var obj = Object.assign({}, el);
        obj.fileicon = (
          <span
            key={i}
            className="icon-attach"
            onClick={() => rowIconClick(i, tempArray)}
          ></span>
        );
        return obj;
      });

      setinvTabledata({ TableListResponse: result });
      setFilterData({ FilteredData: result });
      //setLoading(false);
    }
    return () => {
      dispatch({
        type: accPayConstants.ACCPAY_INV_RECEIVED_CRITERIA,
        invReceivedByCriteria: undefined,
      });
    };
  }, [invReceivedByCriteria]);

  useEffect(() => {
    setComboOptions([
      {
        display: '',
        value: '',
      },
      {
        display: 'Invoice Number',
        value: 'inv_number',
      },
      {
        display: 'Supplier Name',
        value: 'supplier',
      },
      {
        display: 'Purchase Number',
        value: 'po_number',
      },
    ]);
  }, []);

  const rowIconClick = (index, selectedData) => {
    setFilepath(selectedData[index].fileName);
    setmodalIsOpen(true);
  };

  const onDateChange = (e) => {
    const startDate = globalutils.formatDate(e.fromDate, 'mm-dd-yyyy');
    const endDate = globalutils.formatDate(e.toDate, 'mm-dd-yyyy');
    setFromSearchDate(startDate.replaceAll('-', '/'));
    setToSearchDate(endDate.replaceAll('-', '/'));
    findDataBtwnDates(endDate.replaceAll('-', '/'));

    setDateChange(e);
  };

  const findDataBtwnDates = (toDate) => {
    // dispatch(accPayAction.getInvDetailsByDate(received, fromSearchDate,toDate, clientId));
  };

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };
  const globalSearch = (FilterData) => {
    let FilterString = filterString.filterStr;

    const containsKeyword = (val) =>
      (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
      (typeof val === 'string' &&
        val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

    const filteredData = FilterData.filter((entry) =>
      Object.values(entry).some(containsKeyword)
    );
    setFilterData({ FilteredData: filteredData });
  };

  const DisplayTitle = (row, key) => {
    return (
      <div className="display-title" title={row[key]}>
        {row[key]}
      </div>
    );
  };

  const invoicecolumns = [
    {
      name: 'Received On',
      selector: 'rcvdAt',
      width: '15%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'rcvdAt'),
    },
    {
      name: 'Invoice #',
      selector: 'invNumber',
      width: '10%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'invNumber'),
    },
    {
      name: 'Invoice Date',
      selector: 'invDate',
      width: '10%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'invDate'),
    },
    {
      name: 'PO #',
      selector: 'poNumber',
      width: '10%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'poNumber'),
    },
    {
      name: 'Supplier Name',
      selector: 'supplierName',
      width: '19%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'supplierName'),
    },
    {
      name: 'Currency',
      selector: 'currency',
      width: '10%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'currency'),
    },
    {
      name: 'Amount',
      selector: 'amountConverted',
      width: '10%',
      sortable: true,
      sortFunction: (rowA, rowB) => {
        let a = parseFloat(rowA?.amountConverted.replace(/,/g, ''));
        let b = parseFloat(rowB?.amountConverted.replace(/,/g, ''));
        if (isNaN(a) || b > a) return -1;
        else if (isNaN(b) || a > b) return 1;
        else return 0;
      },
      cell: (row) => DisplayTitle(row, 'amountConverted'),
    },
    {
      name: 'Status',
      selector: 'status',
      width: '10%',
      sortable: true,
      cell: (row) => DisplayTitle(row, 'status'),
    },
    {
      name: 'Attachments',
      selector: 'fileicon',
      width: '10%',
    },
  ];
  const closeFunction = () => {
    setmodalIsOpen(false);
  };

  const onFileUpload = (fileWithDescription) => {
    setLoading(true);
    setAttachPopup(false);
    dispatch(
      uploadFile({
        clientId,
        userId,
        file: fileWithDescription.file,
      })
    );
  };
  const handleUploadClick = () => {
    // fileInput.current.click();
    setAttachPopup(true);
  };

  const onSelectOption = (value) => {
    setOnSelectCriteria(value);
  };

  const handleCriteriaSearch = (event) => {
    setOnSearchCriteria(event.target.value);
  };

  const onClearFunction = () => {
    setOnSelectCriteria('');
    setOnSearchCriteria('');
    setFilterString({ filterStr: '' });
    setDateChange({ fromDate: todaysDate, toDate: todaysDate });
  };

  const onSearchFunction = () => {
    var fromDate = globalutils.formatDate(fromSearchDate, 'mm-dd-yyyy');
    var toDate = globalutils.formatDate(toSearchDate, 'mm-dd-yyyy');
    dispatch(
      getInvDetailsbyCriteria({
        criteria: onSelectCriteria,
        conditionData: onSearchCriteria,
        clientId,
        fromSearchDate: fromDate,
        toSearchDate: toDate,
      })
    );
  };

  return (
    <>
      <h1 className="page-title"> Invoice / Received</h1>
      <CommonSpinner visible={isLoading} />
      <Tabs
        className="mb24"
        onSelect={(tabIndex) => setCurrentTab(tabIndex)}
        selectedIndex={currentTab}
      >
        <TabList>
          <Tab>Received Invoice</Tab>
          <Tab>Aging Report</Tab>
        </TabList>
        <TabPanel>
          <div className="flex-row mb20">
            <Select
              label="Field Name"
              placeholder="Select"
              className="mr20"
              style={{ width: 100 + '%' }}
              disabled={false}
              value={onSelectCriteria}
              options={comboOptions}
              onChange={(e) => onSelectOption(e.target.value)}
            />

            <TextInput
              label="Keyword"
              placeholder="Keyword"
              className="mr20"
              style={{ width: 100 + '%' }}
              value={onSearchCriteria}
              onChange={(e) => handleCriteriaSearch(e)}
            />

            <DateRangeInput
              className="mr20"
              style={{ width: 100 + '%' }}
              label="Duration"
              formType={{
                value: dateChange,
              }}
              onChange={(e) => onDateChange(e)}
            />

            <div className="flex-row mt24">
              <Button
                className="small mb8 mr20 outline add-button-custom vam"
                onClick={onSearchFunction}
              >
                Search
              </Button>
              <Button
                className="small mb8 outline add-button-custom vam"
                onClick={onClearFunction}
              >
                Clear
              </Button>
            </div>
          </div>

          <div className="mb20"></div>
          <TextInput
            placeholder="Filter"
            style={{ width: 20 + '%' }}
            value={filterString.filterStr}
            onChange={(e) => handleChange(e)}
          />
          <div className="mb8"></div>
          <div className="mb24 styledDatatable">
            <DataTable
              persistTableHead
              highlightOnHover={true}
              noHeader={true}
              striped={true}
              columns={invoicecolumns}
              data={Filter.FilteredData}
              pagination={true}
              paginationDefaultPage={1}
              paginationPerPage={10}
              //progressPending={isLoading}
            />
          </div>
          <div id="btn-bottom-white-bar" className="group  fixed-button-bar">
            <Button
              className="mr16 default"
              onClick={handleUploadClick}
              disabled={isCalendarOpen}
            >
              Upload Invoice
            </Button>
          </div>
        </TabPanel>
        <TabPanel>
          <AgingReport statusVal={statusVal} payload={payload} />
        </TabPanel>
      </Tabs>
      {/* <input
        hidden
        type="file"
        id="file-input"
        accept=".doc,.docx,application/msword,.pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        onChange={(event) => onFileUpload(event)}
        ref={fileInput}
      /> */}
      <DocUpload
        labels={{
          headLbl: 'Drag File or Click to upload',
          formatLbl:
            'Accept format: JPG, PNG, GIF, WebP, PDF, TXT, CSV, XLS, XLSX, DOC, DOCX, ZIP',
          sizeLbl: 'Size: Max 50mb',
        }}
        isShow={attachPopup}
        onCancel={() => setAttachPopup(false)}
        onsubmitHandler={onFileUpload}
      />
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={modalIsOpen}
      >
        <div
          onClick={() => closeFunction()}
          className="modal-close icon-close"
        ></div>
        <div className="col padding-20" style={{ height: 100 + '%' }}>
          <DocumentViewer
            fileURL={filePath}
            fileType={'iframe'}
            zoom={'#zoom=100'}
            iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
          />
        </div>
      </Modal>
    </>
  );
};
export { InvoiceReceived };
