/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/**
 * <AUTHOR> R B
 * @email <EMAIL>
 * @create date 13-07-2021 10:13:03
 * @modify date 2024-05-08 15:31:16
 * @desc [description]
 */
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AP_poIdConstant } from '@hp/constants';
import { PoDataTable } from './PO-DataTable';
import { cloneDeep } from 'lodash';
import { getPODetailsForAccPay } from '@hp/mainstore';
import '@hp/styles/InvoiceCompare.scss';
import { globalutils, Input } from '@hp/components';
import '../invoice-main/invoicecompare.scss';

const POMatchingComponent = (props) => {
  const {
    tableName,
    setValueOfDataTable,
    disabled,
    invResponseData,
    disableTableActions,
    hideTable,
    hidePOTitleFromUnmatched,
  } = props;
  const dispatch = useDispatch();
  const { accpayPoDetails, serReqList, serviceReqTypes } = useSelector(
    (store) => store.accpay
  );
  const invId = parseInt(props.invId);
  const poId = parseInt(props.poId);
  const [sendRequest, setSendRequest] = useState();
  const [poResponse, setPoResponse] = useState([]);
  const [servReqInvLength, setServReqInvLength] = useState();
  const [IsServiceResponseModalopen, setServiceResponseModalopen] =
    React.useState(false);

  const poResponseMethod = useCallback(() => {
    dispatch(
      getPODetailsForAccPay({
        key: AP_poIdConstant,
        id: poId,
        userId: globalutils.getDataFromStorage('userId'),
      })
    );
  }, [dispatch, poId]);

  useEffect(() => {
    poResponseMethod();
    return () => {
      setPoResponse([]);
    };
  }, [poId, poResponseMethod]);

  useEffect(() => {
    if (serReqList && serReqList.value) {
      setServReqInvLength(serReqList.value.length);
    }
  }, [serReqList]);

  useEffect(() => {
    if (
      accpayPoDetails &&
      accpayPoDetails.value.formDetailsDtoList &&
      accpayPoDetails.value.formDetailsDtoList.length
    ) {
      let accpayArray = cloneDeep(accpayPoDetails);
      setPoResponse(accpayArray.value.formDetailsDtoList);
    }
  }, [accpayPoDetails, dispatch]);

  useEffect(() => {
    if (serviceReqTypes && serviceReqTypes.value) {
      const Options = serviceReqTypes.value.map((value) => {
        return {
          value: value.serReqId,
          display: value.serReqType,
        };
      });
      setSendRequest(Options);
    }
  }, [serviceReqTypes]);

  const getForms = () => {
    return poResponse && poResponse.length
      ? poResponse.map((poControl, poindex) => {
          if (poControl.type !== 'DataTable') {
            return (
              <div className="single" key={poindex}>
                <div className="td label">
                  <p>{poControl.displayName}</p>
                </div>
                <div className="td po">
                  <Input disabledFlag={true} formType={poControl} />
                </div>
              </div>
            );
          }
          return null;
        })
      : '';
  };
  return (
    <div
      className={[props.className, 'edit-only-half', 'is-not-editing'].join(
        ' '
      )}
    >
      {/* <ServiceResponseComponent
        clicktype={"INV"}
        invId={invId}
        comboOptions={sendRequest}
        onClick={() => setServiceResponseModalopen(false)}
        isModalopen={IsServiceResponseModalopen}
      /> */}
      <div className="inv-div">
        <div
          className="inv-table-view"
          // style={{
          //   height: 886 + "px",
          // }}
        >
          {/* <h1 className="page-title group">
            <ServiceRequestLauncher
              onClick={() => setServiceResponseModalopen(true)}
              className="fr"
              count={servReqInvLength}
            />
          </h1> */}
          {!hidePOTitleFromUnmatched ? (
            <div className="inv-table-header">
              <div className="inv-table-format">
                <div className="th label"></div>
                {tableName[0] === 'PO' || tableName[0] === 'INV' ? (
                  <div className="th po">Purchase Order</div>
                ) : (
                  ''
                )}
                {tableName[0] === 'PP' ? (
                  <div className="th invoice">Invoice</div>
                ) : (
                  ''
                )}
              </div>
            </div>
          ) : (
            ''
          )}
          <div className="inv-table-body">
            <div
              className={[
                'primary-secondary-table-wrap',
                tableName[0] === 'INV'
                  ? 'data-full-view-wrap'
                  : 'data-single-view-wrap',
              ].join(' ')}
            >
              <div className="inv-table-body-format">{getForms()}</div>
            </div>
          </div>
        </div>

        <div className="single-table-wrap force-full-width">
          {hideTable ? (
            ''
          ) : (
            <PoDataTable
              setValueOfDataTable={setValueOfDataTable}
              disabledFlag={disabled}
              poData={poResponse}
              invData={invResponseData}
              disableTableActions={disableTableActions}
            />
          )}
        </div>
      </div>
    </div>
  );
};
export { POMatchingComponent };
