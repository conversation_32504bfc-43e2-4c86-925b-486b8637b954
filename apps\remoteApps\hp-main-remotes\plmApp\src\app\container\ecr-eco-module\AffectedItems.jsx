/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
import AntTree from './AntTree';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getECRAffectedItemsDetails, pdmConstants } from '@hp/mainstore';

const AffectedItems = ({
  selectedPartNumbers,
  filteredPartNumber,
  ...props
}) => {
  const dispatch = useDispatch();
  const { ecrAffectedItemsRes } = useSelector((store) => store.pdm);

  const [affectedItemsList, setAffectedItemsList] = useState([]);
  const [filteredItemsList, setFilteredItemsList] = useState([]);

  useEffect(() => {
    if (selectedPartNumbers?.length)
      dispatch(getECRAffectedItemsDetails(selectedPartNumbers));
    else {
      setAffectedItemsList([]);
      setFilteredItemsList([]);
      let removeNotification = null;
      dispatch({
        type: pdmConstants.GET_ECR_AFFECTED_ITEMS_SUCCESS,
        removeNotification,
      });
    }
  }, [selectedPartNumbers]);

  useEffect(() => {
    if (affectedItemsList?.length) {
      if (filteredPartNumber)
        setFilteredItemsList(
          affectedItemsList.filter(
            (item) => item.partNumberId === filteredPartNumber
          )
        );
      else setFilteredItemsList(affectedItemsList);
    }
  }, [filteredPartNumber]);

  useEffect(() => {
    if (ecrAffectedItemsRes?.value) {
      setAffectedItemsList(ecrAffectedItemsRes.value);
      setFilteredItemsList(ecrAffectedItemsRes.value);
    }
  }, [ecrAffectedItemsRes]);

  return filteredItemsList?.length ? (
    filteredItemsList.map((item) => (
      <div className="card mb20">
        <div>
          <h2 className="page-sub-title mb20">{item?.partNumber}</h2>
          <AntTree treeData={item.affectedSubItemDtoList} />
        </div>
      </div>
    ))
  ) : (
    <div className="card mb20">
      <p className="ecr-eco-no-affected-items">
        Please select a <b>Part #</b> to show Affected Items
      </p>
    </div>
  );
};

export default AffectedItems;
