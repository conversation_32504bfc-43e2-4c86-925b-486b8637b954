/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useRef } from 'react';
import cloneDeep from 'lodash.clonedeep';
import { ImageExtraction, PDFExtraction } from '@hp/components';
import { AP_file_url } from '@hp/constants';
import '@hp/styles/InvoiceCompare.scss';
import { useDispatch, useSelector } from 'react-redux';
import { FetchDocFromServer, financeResetStateField } from '@hp/mainstore';

function Docviewer(props) {
  const iframeRef = useRef(null);
  const dispatch = useDispatch();
  const {
    reactDocprop,
    actionHandlers,
    fileURL,
    fileType,
    zoom,
    iframeStyle,
    title,
    fullScreen,
    setModelClose,
    pageNumber,
  } = props;
  const pdfResponse = useSelector((state) => state.finance.pdfFetch);
  const relativePath = fileURL;

  useEffect(() => {
    if (pdfResponse) {
      const blob = new Blob([pdfResponse], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      if (iframeRef.current) {
        iframeRef.current.src = url;
      }

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [pdfResponse]);
  useEffect(() => {
    return () => {
      dispatch(financeResetStateField({ fieldNames: 'pdfFetch' }));
    };
  }, []);
  useEffect(() => {
    if (fileURL && !fileURL.startsWith('blob:')) {
      dispatch(FetchDocFromServer({ serverPath: relativePath }));
    }
  }, [fileURL]);
  const FileView = useCallback(() => {
    var docURL = '';
    switch (fileType) {
      case 'iframe':
        if (fileURL && fileURL.length) {
          if (zoom) {
            docURL = AP_file_url + fileURL + zoom;
            docURL = cloneDeep(docURL);
          } else {
            docURL = AP_file_url + fileURL;
            docURL = cloneDeep(docURL);
          }
        } else {
          docURL = '';
        }

        return (
          <iframe
            title={title ? title : ''}
            style={
              iframeStyle
                ? iframeStyle
                : { width: 100 + '%', height: 100 + '%' }
            }
            src={docURL}
            className="iframe-container"
            ref={iframeRef}
            key={pageNumber ?? 1}
          ></iframe>
        );
      case 'reactDocViewer':
        if (fileURL) {
          docURL = AP_file_url + fileURL;
          // docURL = cloneDeep(docURL);

          if (
            docURL.endsWith('.png') ||
            docURL.endsWith('.jpg') ||
            docURL.endsWith('.jpeg') ||
            docURL.endsWith('.gif')
          ) {
            return <ImageExtraction />;
          } else if (docURL.endsWith('.pdf') || docURL.endsWith('.PDF')) {
            return reactDocprop ? (
              <PDFExtraction
                reactDocprop={reactDocprop}
                url={docURL}
                actionHandlers={actionHandlers}
                fullScreen={fullScreen}
                setModelClose={setModelClose}
              />
            ) : (
              ''
            );
          }
        }
        break;
      default:
        return <div></div>;
    }
  }, [fileType, reactDocprop, fileURL]);

  return <FileView />;
}

export default React.memo(Docviewer);
