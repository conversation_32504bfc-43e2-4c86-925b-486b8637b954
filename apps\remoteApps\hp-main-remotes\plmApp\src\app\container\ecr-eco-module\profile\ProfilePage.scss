@import '@hp/styles/variables.scss';
/*select*/
select {
  background-color: #fff;
}
select option {
  background-color: #fff;
}
select > * {
  background-color: #fff;
}
/*endselect*/
/*end diabled radio button*/
input.disabled-radio {
  pointer-events: none;
  opacity: 0.5;
}
/*end diabled radio button*/
/*hover sub*/

.inner-main {
  display: flex;
  justify-content: space-between;

  .left {
    width: 100%;

    .basic-information {
      //height: 8000px;
      h2 {
        font-size: 24px;
        line-height: 36px;
        color: #20202a;
        font-weight: normal;
        margin-bottom: 32px;
      }
    } /*basic information*/

    .basic-form {
      gap: 40px;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      max-width: 680px;
      margin-bottom: 82px;
      justify-content: space-between;
      > * {
        flex-basis: calc((100% - 40px) / 2);
      }
    } /*basic-form*/

    .process-control-inner {
      gap: 40px;
      width: 100%;
      max-width: 754px;
      margin-bottom: 40px;
      .p-left {
        max-width: 356px;
        width: 100%;
      }
      .p-right {
        max-width: 356px;
        width: 100%;
      }
      /*hover sub*/
      .validate-amount,
      .p-container {
        position: relative;
      }
      /*hover sub*/
      .p-container {
        padding: 16px;
        padding-bottom: 0px;
        border-radius: 8px;
        border: 1px solid #e2e2e9;
        max-width: 356px;
        margin-bottom: 40px;

        /*approval-settings*/
        &.approval-settings {
          .inner-head {
            margin-bottom: 16px;
          }
          .inner-p {
            font-size: 12px;
            line-height: 18px;
            color: #444444;
            margin-bottom: 8px;
          }
          .inv-amt {
            display: flex;
            gap: 8px;
            margin-bottom: 32px;

            /*select*/
            .select-wrap:after {
              width: 35px;
            }

            select {
              width: 82px;
              height: 40px;
              border: none;
              font-size: 14px;
              line-height: 21px;
              color: #20202a;
              padding: 0 16px 0 16px;
            }

            /*end select*/

            input[type='text'] {
              width: 234px;
              height: 40px;
              border: 1px solid #c5c5d3;
              border-radius: 4px;
              padding: 10px 16px;
            }
          }

          /*select*/
          .p-amt-upto {
            max-width: 324px;
            // width: 100%;
            min-height: 63px;
            background: #f9fafb;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 30px;
            position: relative;

            p {
              font-size: 14px;
              line-height: 150%;
              color: #1a1a1a;
              flex-shrink: 0;
            }
            svg {
              width: 12px;
              height: 8px;
              flex-shrink: 0;
            }
          }
          .inv-amt-sub {
            display: flex;
            gap: 8px;
          }
          .p-inv-amt-sub {
            margin-top: 9px;
            font-size: 15px;
          }
        }
        /*end approval-settings*/

        &.manage-languages {
          position: relative;
          .head {
            h3 {
              &:after {
                left: 310px !important;
              }
            }
          }
          .ml-p {
            font-size: 14px;
            line-height: 21px;
            color: #20202a;
            margin-bottom: 24px;
          }
          .lang-div {
            max-width: 324px;
            width: 100%;
            min-height: 40px;
            background: #f6f6f6;
            border: 1px solid #dadce0;
            box-sizing: border-box;
            border-radius: 4px;
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-right: 16px;
            padding-left: 16px;
            gap: 10px;
            flex-wrap: wrap;
            position: relative;
            padding: 8px 16px;

            .lang-more {
              width: 16px;
              height: 4px;
              margin-right: 20px;
              flex-shrink: 0;
            }
            .lang-drop {
              flex-shrink: 0;
              width: 12px;
              height: 8px;
              position: absolute;
              right: 8px;
              // padding-right: 16px;
            }
          }
        }
      } /*p-container*/
    }
    /*process-control-inner*/

    .match-criteria {
      max-width: 688px;
    } /*match criteria*/

    .match-criteria-inner {
      display: flex;
      margin-bottom: 40px;
      flex-wrap: wrap;
      max-width: 855px;
    } /*match-criteria-inner*/

    .validation {
      max-width: 752px;
      width: 100%;
      margin-bottom: 16px;
      .v-inner {
        display: flex;
        gap: 40px;
        .v-border {
          background: #ffffff;
          border: 1px solid #e2e2e9;
          width: 100%;
          border-radius: 8px;
          padding: 16px;
        } /*v-left and v-right*/
        .v-left {
          max-width: 356px;
          width: 100%;

          .inv-p {
            font-size: 11px;
            line-height: 13px;
            color: #505050;
            margin-bottom: 16px;
          }

          .list {
            width: 100%;
            min-height: 92px;
            display: flex;
            justify-content: flex-start;
            gap: 7px;
            border: 1px solid #e2e2e9;
            border-radius: 4px;
            padding: 16px;
            flex-wrap: wrap;
          } /*list*/
          /*action in validation header*/

          .po-miss {
            margin-top: 40px;
          }
        } /*v-left*/
        .v-right {
          max-width: 356px;
          width: 100%;
          .bill-to {
            margin-bottom: 40px;
          }
        } /*v-right*/
      }
    }
    /*Validations*/

    .service-request {
      max-width: 709px;
      width: 100%;
    }
    .service-request-inner {
      margin-bottom: 40px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 16px;

      .service-req-contents {
        display: flex;
        flex-wrap: wrap;
      }

      .left {
        max-width: 283px;
        width: 100%;
      }
      .right {
        max-width: 283px;
        width: 100%;
      }
    } /*Service Request*/
    .email-settings-inner {
      display: flex;
      flex-direction: column;
      margin-bottom: 30px;
      // max-width: 749px;
      width: 100%;
    } /*email settings inner*/
  } /*end Left*/

  .right {
    width: 100%;
    max-width: 200px;
    flex-shrink: 0;
  }
}

.create-approval {
  max-width: 324px;
  width: 100%;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  background: #ffffff;
  border: 1px solid #dadce0;
  .ca-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .ca-head-h3 {
      font-weight: bold;
      font-size: 14px;
      line-height: 150%;
      color: #1a1a1a;
      text-transform: capitalize;
    }
  }
  .req-appr {
    gap: 9px;
    // display: flex;
    align-items: center;
    // justify-content: space-between;
    .req-appr-p {
      font-weight: normal;
      font-size: 12px;
      line-height: 21px;
      color: #20202a;
      flex: 1;
    }
    div {
      flex: 2;
    }
    .req-appr-select {
      height: 40px;
      font-size: 12px;
      line-height: 18px;
      color: #20202a;
      font-style: normal;
      font-weight: normal;
      padding: 11px 42px 11px 16px;
    }
  }
  .ca-btns {
    display: flex;
    align-items: center;
    margin-top: 24px;
    .ca-btn {
      width: 115px;
      height: 36px;
      font-size: 14px;
      color: #3b73b9;
      font-weight: bold;
      text-align: center;
      line-height: 20px;
      padding: 8px 24px;
      border-radius: 32px;
      background: #ffffff;
      border: 1px solid #3b73b9;
    }
    .ca-btn-a {
      cursor: default;
      font-size: 14px;
      line-height: 20px;
      color: #3b73b9;
      padding: 8px 24px;
    }
  }
}

.ml-search-sec {
  z-index: 10;
  width: 100%;
  background: #fff;
  padding-bottom: 16px;
  margin-top: -16px;
  margin-bottom: 16px;
  border: 1px solid #e2e2e9;
  border-radius: 0px 0px 4px 4px;

  &.sr-position {
    position: absolute;
    width: 224px;
    top: 44px;
    left: 150px;
  }
  &.sr-top {
    top: 64px;
  }

  //position: relative;
  .ml-search-sec-h {
    position: relative;
    box-shadow: 0px 6px 14px -6px rgba(0, 0, 0, 0.12),
      0px 10px 32px -4px rgba(0, 0, 0, 0.1);
    .ml-search-sec-in {
      padding: 16px;
      width: 100%;
      font-size: 12px;
      line-height: 18px;
      color: #20202a;
      border: none;
    }
    svg {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 16px;
    }
  }

  .ml-search-sec-inner {
    min-height: 150px;
    max-height: 180px;
    overflow-y: auto;
    .ml-search-sec-inner-p {
      font-style: normal;
      font-weight: normal;
      font-size: 12px;
      line-height: 18px;
      color: #20202a;
      padding: 12px 16px;

      &:hover {
        background: #f6f7f8;
      }
    }
  }
}
.close-top-right {
  position: absolute;
  right: 5px;
  top: 5px;
  font-size: 13px;
  cursor: pointer;
}
