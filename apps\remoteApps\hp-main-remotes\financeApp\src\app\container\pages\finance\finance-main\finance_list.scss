@import '@hp/styles/variables.scss';
.Modal-popup {
  top: 40px;
  left: 50%;
  width: 100%;
  bottom: 40px;
  outline: none;
  padding: 40px;
  overflow: auto;
  height: 450px;
  max-width: 600px;
  background: #fff;
  border-radius: 4px;
  position: absolute;
  border: 1px solid #ccc;
  transform: translate(-50%, 0);
}
.buttonCommon {
  margin-top: 75px;
  padding-left: 32px;
}

.react-datepicker-popper {
  z-index: 10000 !important;
}

.fin_list_container {
  .fin_list_search_div {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }

  .fin_search_input {
    width: 200px;
  }
}

.modal-flex-container {
  display: flex;
  flex-direction: column;
  height: 100%; // full height of modal
}

.data-table-scrollable {
  flex: 1 1 auto;
  overflow-y: auto;
  padding-right: 8px; // to prevent cutoff scrollbar if needed
}

.modal-footer {
  padding-top: 12px;
  text-align: right;
  background: #fff;
  z-index: 1;
}

.serialNoDuePadding {
  margin-left: -3px;
}

.icon-check-alert-light {
  color: #ff9700;
  border-radius: 50%;
  background-color: #3c4fff;
}

.tooltip-reconcilliation-custom .ant-tooltip-inner {
  background: none;
  box-shadow: none;
  padding: 0 5px;
}

.tooltip-reconcilliation-custom .ant-tooltip-arrow::before {
  background: #ffff99;
}
