import Modal from 'react-modal';
import { Button, TextAreaInput } from '@hp/components';

const RejectModal = (props) => {
  const {
    rejectClick,
    isModalOpen,
    isModalClose,
    onRejectCommentsChangeHandler,
    rejectComments,
    errorFlag,
    errorMessage,
  } = props;
  return (
    <Modal
      className="RejectModal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      //isOpen={true}
      isOpen={isModalOpen}
      style={{
        content: {
          width: '60%',

          height: 'auto',

          top: '50%',

          left: '50%',

          right: 'auto',

          bottom: 'auto',

          marginRight: '-50%',

          transform: 'translate(-50%, -50%)',

          overflow: 'hidden',
        },
      }}
    >
      <div className="page-sub-title">
        <div style={{ fontWeight: 'bold', fontSize: '15px' }}>
          Reject Reason
        </div>
      </div>

      <div onClick={isModalClose} className="modal-close icon-close"></div>
      <div className="col">
        <TextAreaInput
          label=""
          className="mb16"
          style={{ height: 180 + 'px' }}
          onChange={onRejectCommentsChangeHandler}
          value={rejectComments}
          error={{
            flag: errorFlag,
            message: errorMessage ?? 'Enter valid reject reason!!',
          }}
        />
      </div>

      <Button onClick={rejectClick} className="small outline mr20">
        Submit
      </Button>
    </Modal>
  );
};

export { RejectModal };
