{"extends": ["plugin:cypress/recommended", "plugin:@nx/react", "../../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}, {"files": ["*.cy.{ts,js,tsx,jsx}", "cypress/**/*.{ts,js,tsx,jsx}"], "parserOptions": {"project": "apps/remoteApps/hp-main-remotes/authModule/tsconfig.*?.json"}, "rules": {}}]}