/* eslint-disable no-console */
/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

import { Input, useConfirm } from '@hp/components';
import React, { useEffect } from 'react';
import { useState, useLayoutEffect } from 'react';
import { dataTableServiceProvider } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';

import FormActions from './add-edit-brokers';
import cloneDeep from 'lodash.clonedeep';
import { Button } from '@hp/components';
import { memo } from 'react';
import { processMPNCache } from './processMPNCache';
// import 'libs/hp-styles/src/lib/_dataTable.scss';
import {
  processMpnGetForm,
  brokerLists,
  distributorProductIDSuccess,
  addEditBroker,
} from '@hp/mainstore';
import { Tooltip } from 'antd';
function DistributorBroker(props) {
  const { form, selectedTab, setSelectedTab } = props;

  const {
    brokerList,
    processAddEditForm,
    processAddEditResponse,
    distributorProductID,
  } = useSelector((store) => store.bb);
  const dispatch = useDispatch();
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [conditionalRowStylesTwo, setConditionalStylesTwo] = useState([]);
  const [brokerDistributorId, setBrokerDistributorId] = useState();
  const [addEditFormInputs, setAddEditFormInputs] = useState([]);
  const [action, setAction] = useState('');
  const [quantity, setQuantity] = useState();
  const [addForm, setAddForm] = useState(false);
  const [addBrokerEnable, setAddBrokerEnable] = useState();
  const [productDetailsId, setProductDetailsId] = useState();
  const [formData, setFormData] = useState();
  const { confirm } = useConfirm();
  useLayoutEffect(() => {
    dispatch(
      processMpnGetForm({
        reqType: 'subscribe',
        productDetailsId: productDetailsId,
      })
    );
    return () => {
      dispatch(processMpnGetForm('unSubscribe'));
    };
  }, [productDetailsId, processAddEditResponse]);

  useLayoutEffect(() => {
    if (form && form !== null && form !== undefined) {
      setFormData(form);
    }
  }, [form]);

  useEffect(() => {
    if (distributorProductID) {
      setProductDetailsId(distributorProductID);
    }
  }, [distributorProductID]);
  useLayoutEffect(() => {
    if (brokerList?.value && formData) {
      const tempArray = cloneDeep(formData);
      tempArray &&
        tempArray !== undefined &&
        tempArray.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'brokerList') {
            element.value = brokerList.value;
          }
        });
      setFormData(tempArray);
    }
  }, [brokerList]);

  const DisplayTitle = (row, key) => {
    return row.brokerFlag && key === 'completed' ? (
      <div>
        <div className="col">
          {' '}
          <div className=" icon-checked " style={{ marginLeft: '-5px' }}></div>
        </div>
        <div>
          <div title={row[key]}></div> {row[key]}
          {''}
        </div>
      </div>
    ) : (
      <Tooltip classNames={{ root: 'ant-tooltip-container' }} title={row[key]}>
        <div className="display-title custom-overflow">
          {' '}
          {row[key]}
          {''}
        </div>
      </Tooltip>
    );
  };

  useLayoutEffect(() => {
    /**
     * <AUTHOR> Shahanas
     * @email <EMAIL>
     * @description: Add Edit form response.
     */
    if (processAddEditForm && processAddEditForm.value) {
      setAddEditFormInputs(processAddEditForm.value);
    } else setAddEditFormInputs([]);
  }, [processAddEditForm]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: trigger for pageRefresh after edit and delete (broker).
     */
    if (processAddEditResponse) {
      setAddForm(false);
      dispatch(
        brokerLists({ masterID: productDetailsId, reqType: 'subscribe' })
      );
    }
    return () => {
      dispatch(
        brokerLists({ masterID: productDetailsId, reqType: 'unSubscribe' })
      );
    };
  }, [processAddEditResponse]);

  useLayoutEffect(() => {
    let mpnSelected = processMPNCache.addDistributor.mpnSelectedStyle;
    let distSelected = processMPNCache.addDistributor.distributorStyle;
    setConditionalStyles(mpnSelected);
    setConditionalStylesTwo(distSelected);
  }, [selectedTab]);

  const handleDelete = async (row) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(
        addEditBroker({
          formDetailsDto: '',
          brokerDistributorId: row.brokerDistributorId,
          action: 'delete',
          productDetailsId: productDetailsId,
          DistributorId: '',
        })
      );
    }
  };
  const addEditBrokerHandler = (row, action) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: ADD,EDIT Dialog box.
     */
    setBrokerDistributorId(row.brokerDistributorId);

    const updatedValues = [...addEditFormInputs];

    const foundIndex = updatedValues.findIndex(
      (e) => e.uniqueKey === 'page-title'
    );

    if (foundIndex !== -1) {
      const originalPageTitleObj = updatedValues[foundIndex];

      // Deep clone formSubDetailsInternalDTOList
      const updatedSubList =
        originalPageTitleObj.formSubDetailsInternalDTOList.map((el) => {
          const newEl = { ...el }; // Clone to avoid mutation

          switch (newEl.uniqueKey) {
            case 'broker':
              newEl.value = action === 'edit' ? row.vendorName : '';
              newEl.disableFlag = action === 'edit' ? 'T' : 'F';
              break;
            case 'email':
              newEl.value = action === 'edit' ? row.email : '';
              newEl.disableFlag = action === 'edit' ? 'T' : 'F';
              break;
            case 'stock':
              newEl.value = action === 'edit' ? row.stock : '';
              break;
            case 'price':
              newEl.value = action === 'edit' ? row.price : '';
              break;
            case 'contact':
              newEl.value = action === 'edit' ? row.contactName : '';
              newEl.disableFlag = action === 'edit' ? 'T' : 'F';
              break;
            case 'demand':
              newEl.value = action === 'edit' ? row.demand : quantity;
              break;
            default:
              break;
          }

          return newEl;
        });

      // Replace the original object with the updated clone
      updatedValues[foundIndex] = {
        ...originalPageTitleObj,
        formSubDetailsInternalDTOList: updatedSubList,
      };
    }

    setAction(action);
    setAddEditFormInputs(updatedValues);
    setAddForm(true);
  };

  const handleRowClick = (type, event, el) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    switch (el.uniqueKey) {
      case 'MPN-subDetails':
        var styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
          type.serialNo,
          'serialNo'
        );
        processMPNCache.addDistributor.mpnSelectedStyle = styleAttribute;
        processMPNCache.addDistributor.mpnRowId = type.productFileDetailsId;
        setQuantity(type.quantity);
        setConditionalStyles(styleAttribute);
        setProductDetailsId(type.productFileDetailsId);
        dispatch(distributorProductIDSuccess(type.productFileDetailsId));
        dispatch(
          brokerLists({
            masterID: type.productFileDetailsId,
            reqType: 'subscribe',
          })
        );
        setAddBrokerEnable(true);
        break;
      case 'MPN':
        setConditionalStyles(styleAttribute);
        // setRowData(el);
        break;
      case 'brokerList':
        var styleAttributeTwo =
          dataTableServiceProvider.conditionalDataTableRow(
            type.serialNo,
            'serialNo'
          );
        processMPNCache.addDistributor.distributorStyle = styleAttributeTwo;
        setConditionalStylesTwo(styleAttributeTwo);
        setBrokerDistributorId(type.brokerDistributorId);
        break;
      default:
        break;
    }
  };

  const handlers = {
    handleRowClick,
    addEditBrokerHandler,
  };
  return (
    <>
      <div className="two-col-layout">
        {formData &&
          formData?.formSubDetailsInternalDTOList?.map((items, i) => {
            if (items.uniqueKey !== 'brokerList') {
              return (
                <div className="col" key={i.toString()}>
                  <div className="page-sub-title">Part List</div>
                  <Input
                    formType={items}
                    isEditable="notShowing"
                    onChangeHandler={(type, event) => {
                      if (!type) {
                        //
                        return;
                      }
                      const handlerFunc = handlers[type?.onChangeFunction];
                      if (typeof handlerFunc === 'function') {
                        handlerFunc(type, event, 'search');
                      } else {
                        //
                      }
                    }}
                    dataTableEventHandler={(type, event) => {
                      if (!items || !items.onChangeFunction) {
                        //
                        return;
                      }
                      const eventHandler = handlers[items?.onChangeFunction];
                      if (typeof eventHandler === 'function') {
                        eventHandler(type, event, items);
                      } else {
                        //
                      }
                    }}
                    selectedRow={(selectedProps) => {
                      if (!items || !items.selector) {
                        //
                        return;
                      }
                      const selectedFunc = handlers[items?.selector];
                      if (typeof selectedFunc === 'function') {
                        selectedFunc(selectedProps);
                      } else {
                        //
                      }
                    }}
                    conditionalRowStyles={conditionalRowStyles}
                    dataTableColumn={
                      items?.formSubDetailsInternalDTOList?.length
                        ? items.formSubDetailsInternalDTOList.map(
                            (rowData) => ({
                              width: rowData.displayWidth || '',
                              name: rowData.displayName || '',
                              selector: rowData.selector || '',
                              cell: (row) =>
                                DisplayTitle(row, rowData.selector),
                            })
                          )
                        : []
                    }
                  />
                </div>
              );
            } else if (items.uniqueKey === 'brokerList') {
              let columns =
                items.formSubDetailsInternalDTOList &&
                items.formSubDetailsInternalDTOList.map((val, index) => {
                  return {
                    selector: val.selector,
                    width:
                      val.selector === 'edit-icon' ||
                      val.selector === 'delete-icon'
                        ? '4%'
                        : val.displayWidth,
                    name: val.displayName,
                    cell:
                      val.selector === 'edit-icon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-edit-button"
                                onClick={() =>
                                  addEditBrokerHandler(row, 'edit')
                                }
                                key={index}
                              ></div>
                            );
                          }
                        : val.selector === 'delete-icon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-2-trash"
                                onClick={() => handleDelete(row)}
                                key={index}
                              ></div>
                            );
                          }
                        : '',
                  };
                });
              return (
                <div className="col" key={i}>
                  <div className="page-sub-title">
                    Distributor List
                    {addBrokerEnable ? (
                      <Button
                        className="small mb8 outline add-button-custom flex-row vam"
                        onClick={() => addEditBrokerHandler('', 'add')}
                        style={{
                          float: 'right',
                          cursor: 'pointer',
                          marginTop: '-13px',
                          height: '27px',
                        }}
                      >
                        {' '}
                        <i className="icon-add-button"></i>Add Distributor
                      </Button>
                    ) : (
                      ''
                    )}
                  </div>
                  <Input
                    formType={items}
                    isEditable="notShowing"
                    onChangeHandler={(type, event) =>
                      handlers[type.onChangeFunction](type, event, 'search')
                    }
                    dataTableEventHandler={(type, event) =>
                      handlers[items.onChangeFunction](type, event, items)
                    }
                    // selectedRow={(selectedProps) =>
                    //   handlers[items.selector](selectedProps)
                    // }
                    selectedRow={(selectedProps) => {
                      if (!items || !items.selector) {
                        console.error('Items or items.selector is undefined');
                        return;
                      }
                      const selectedFunc = handlers[items?.selector];
                      if (typeof selectedFunc === 'function') {
                        selectedFunc(selectedProps);
                      } else {
                        console.error(
                          `Handler not found or invalid for selector: ${items?.selector}`
                        );
                      }
                    }}
                    dataTableColumn={columns}
                    conditionalRowStyles={conditionalRowStylesTwo}
                  />
                </div>
              );
            }
          })}
      </div>

      <Button
        className="small default mr20"
        style={{ marginTop: 30 + 'px' }}
        onClick={() => setSelectedTab((prev) => prev + 1)}
      >
        Next
      </Button>

      <FormActions
        form={addEditFormInputs}
        isClose={() => setAddForm(!addForm)}
        isOpen={addForm}
        action={action}
        productDetailsId={productDetailsId}
        brokerDistributorId={brokerDistributorId}
      />
    </>
  );
}

export default memo(DistributorBroker);
