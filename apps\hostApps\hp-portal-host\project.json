{"name": "hp-portal-host", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/hostApps/hp-portal-host/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "dist/apps/hostApps/hp-portal-host", "main": "apps/hostApps/hp-portal-host/src/main.jsx", "tsConfig": "apps/hostApps/hp-portal-host/tsconfig.app.json", "rspackConfig": "apps/hostApps/hp-portal-host/rspack.config.js", "assets": ["apps/hostApps/hp-portal-host/src/favicon.ico", "apps/hostApps/hp-portal-host/src/assets"]}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "optimization": true, "sourceMap": false}}, "dependsOn": []}, "serve": {"executor": "@nx/rspack:module-federation-dev-server", "options": {"buildTarget": "hp-portal-host:build:development", "port": 3000, "host": "0.0.0.0", "publicPath": "auto", "allowedHosts": "all"}, "configurations": {"development": {}, "production": {"buildTarget": "hp-portal-host:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/rspack:module-federation-static-server", "defaultConfiguration": "production", "options": {"serveTarget": "hp-portal-host:serve"}, "configurations": {"development": {"serveTarget": "hp-portal-host:serve:development"}, "production": {"serveTarget": "hp-portal-host:serve:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hostApps/hp-portal-host/jest.config.js"}}}, "implicitDependencies": []}