// export { ReportGraph };
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable array-callback-return */
/* eslint-disable @nx/enforce-module-boundaries */
import React, { useCallback, useEffect, useState } from 'react';
import './ReportStyle.scss';
import { ReportService } from './ReportService';
import DBInput from '../../../../../../../../../../../libs/hp-components/src/lib/common/module_dashboard/components/db-inputs/DBInput';

const ReportGraph = ({ data = [] }) => {
  const [dashboardInputs, setDashboardInputs] = useState([]);

  useEffect(() => {
    if (Array.isArray(data) && data.length > 0) {
      const processedCharts = ReportService.reportDynamicCharts(data);

      processedCharts?.forEach((dashboard) => {
        dashboard.charts?.forEach((dbControl) => {
          switch (dbControl.type) {
            case 'CountList': {
              const matching = data.find((item) => item.type === 'CountList');
              if (matching) {
                dbControl.data = matching.value;
              }
              dbControl.isLoading = false;
              break;
            }

            case 'BarChart':
              dbControl.data.datasets[0].data =
                dbControl?.chartData?.dataCounts;
              dbControl.data.labels = dbControl?.chartData?.labelNames;
              dbControl.isLoading = false;
              break;

            case 'PieChart':
              dbControl.data = {
                ...dbControl.data,
                datasets: [
                  {
                    ...dbControl.data.datasets[0],
                    data: dbControl?.chartData?.dataCounts || [],
                  },
                ],
                labels: dbControl?.chartData?.labelNames || [],
              };
              dbControl.isLoading = false;
              break;

            default:
              break;
          }
        });
      });

      setDashboardInputs(processedCharts);
    } else {
      setDashboardInputs([]);
    }
  }, [data]);

  const widgetHandler = () => {
    '';
  };

  const ReportDB = useCallback(() => {
    return dashboardInputs?.map((inputs, parentIndex) => (
      <div className={inputs.layoutClassName} key={parentIndex}>
        {inputs?.charts.map((formInputs) => (
          <div draggable={false} key={formInputs?.id} id={formInputs?.id}>
            <DBInput
              formType={formInputs}
              widgetClick={widgetHandler}
              cardLayoutClass="three-col-layout-small-tile-20 mb20"
            />
          </div>
        ))}
      </div>
    ));
  }, [dashboardInputs]);

  return <>{dashboardInputs?.length > 0 ? <ReportDB /> : null}</>;
};

export { ReportGraph };
