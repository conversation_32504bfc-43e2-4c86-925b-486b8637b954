import React, { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';
import { PersistGate } from 'redux-persist/integration/react';
import App from './app/app';
import '@hp/styles/index.scss';
import '@hp/assets';
import 'react-tooltip/dist/react-tooltip.css';
import { BrowserRouter } from 'react-router-dom';
import { store, persistor } from '@hp/store';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import './registerServiceWorker.js';
const loader = document.getElementById('html-loader');
if (loader) {
  loader.remove(); // OR loader.style.display = 'none';
}

const root = ReactDOM.createRoot(document.getElementById('main-module'));

root.render(
  <StrictMode>
    <BrowserRouter>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <ConfigProvider
            theme={{
              token: {
                fontFamily: "'Roboto', sans-serif",
              },
            }}
          >
            <App />
          </ConfigProvider>
        </PersistGate>
      </Provider>
    </BrowserRouter>
  </StrictMode>
);
