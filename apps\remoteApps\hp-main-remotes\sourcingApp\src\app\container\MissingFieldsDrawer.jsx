/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON>, Drawer, Alert, Card, Tag, Space, Typography, List } from 'antd';
import {
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import './MissingFieldsDrawer.scss';

const { Title, Text } = Typography;

const MissingFieldsDrawer = ({ 
  isOpen, 
  onClose, 
  tableData, 
  orderedPolicyBeans,
  rfqId 
}) => {
  const [missingFieldsData, setMissingFieldsData] = useState([]);

  // Define mandatory fields that should be checked
  const mandatoryFields = [
    'total',
    'freightCost', 
    'warranty',
    'deliveryTime',
    'lastFee',
    'cancellationCharge',
    'creditCardFee'
  ];

  // Function to check for missing fields in supplier data
  const checkMissingFields = () => {
    if (!tableData || !orderedPolicyBeans) return [];

    const missingData = tableData.map((supplier) => {
      const missingFields = [];
      
      // Check each mandatory field
      mandatoryFields.forEach((field) => {
        if (orderedPolicyBeans.includes(field)) {
          const value = supplier[field];
          if (
            value === null || 
            value === undefined || 
            value === '' || 
            value === ' ' ||
            String(value).trim() === ''
          ) {
            missingFields.push({
              field: field,
              displayName: field
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .charAt(0)
                .toUpperCase() +
                field
                  .replace(/([A-Z])/g, ' $1')
                  .trim()
                  .slice(1)
            });
          }
        }
      });

      return {
        supplierName: supplier.supplierName || 'Unknown Supplier',
        serialNo: supplier.serialNo,
        missingFields: missingFields,
        hasMissingFields: missingFields.length > 0
      };
    }).filter(supplier => supplier.hasMissingFields);

    return missingData;
  };

  useEffect(() => {
    if (isOpen) {
      const missing = checkMissingFields();
      setMissingFieldsData(missing);
    }
  }, [isOpen, tableData, orderedPolicyBeans]);

  const renderMissingFieldsContent = () => {
    if (missingFieldsData.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
          <Title level={4}>All Required Fields Complete</Title>
          <Text type="secondary">All suppliers have provided the necessary information.</Text>
        </div>
      );
    }

    return (
      <div>
        <Alert
          message={`${missingFieldsData.length} supplier${missingFieldsData.length > 1 ? 's' : ''} ${missingFieldsData.length > 1 ? 'have' : 'has'} missing required fields`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <List
          dataSource={missingFieldsData}
          renderItem={(supplier, index) => (
            <motion.div
              key={supplier.serialNo}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card 
                size="small" 
                style={{ marginBottom: 16 }}
                title={
                  <Space>
                    <Text strong>{supplier.supplierName}</Text>
                    <Tag color="orange">
                      {supplier.missingFields.length} missing field{supplier.missingFields.length > 1 ? 's' : ''}
                    </Tag>
                  </Space>
                }
              >
                <List
                  size="small"
                  dataSource={supplier.missingFields}
                  renderItem={(field) => (
                    <List.Item
                      actions={[
                        <Link
                          key="edit"
                          to={`/sourcing/rfq/supplier-details/${rfqId}/${supplier.serialNo}?field=${field.field}`}
                          onClick={onClose}
                        >
                          <Button 
                            type="link" 
                            size="small" 
                            icon={<EditOutlined />}
                          >
                            Edit
                          </Button>
                        </Link>
                      ]}
                    >
                      <Text>{field.displayName}</Text>
                    </List.Item>
                  )}
                />
              </Card>
            </motion.div>
          )}
        />
      </div>
    );
  };

  return (
    <Drawer
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#faad14' }} />
          <span>Missing Fields Report</span>
        </Space>
      }
      placement="right"
      onClose={onClose}
      open={isOpen}
      width={480}
      className="missing-fields-drawer"
    >
      {renderMissingFieldsContent()}
    </Drawer>
  );
};

export default MissingFieldsDrawer;
