/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from 'antd';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import './MissingFieldsDrawer.scss';

const MissingFieldsDrawer = ({ 
  isOpen, 
  onClose, 
  tableData, 
  orderedPolicyBeans,
  rfqId 
}) => {
  const [missingFieldsData, setMissingFieldsData] = useState([]);

  // Define mandatory fields that should be checked
  const mandatoryFields = [
    'total',
    'freightCost', 
    'warranty',
    'deliveryTime',
    'lastFee',
    'cancellationCharge',
    'creditCardFee'
  ];

  // Function to check for missing fields in supplier data
  const checkMissingFields = () => {
    if (!tableData || !orderedPolicyBeans) return [];

    const missingData = tableData.map((supplier) => {
      const missingFields = [];
      
      // Check each mandatory field
      mandatoryFields.forEach((field) => {
        if (orderedPolicyBeans.includes(field)) {
          const value = supplier[field];
          if (
            value === null || 
            value === undefined || 
            value === '' || 
            value === ' ' ||
            String(value).trim() === ''
          ) {
            missingFields.push({
              field: field,
              displayName: field
                .replace(/([A-Z])/g, ' $1')
                .trim()
                .charAt(0)
                .toUpperCase() +
                field
                  .replace(/([A-Z])/g, ' $1')
                  .trim()
                  .slice(1)
            });
          }
        }
      });

      return {
        supplierName: supplier.supplierName || 'Unknown Supplier',
        serialNo: supplier.serialNo,
        missingFields: missingFields,
        hasMissingFields: missingFields.length > 0
      };
    }).filter(supplier => supplier.hasMissingFields);

    return missingData;
  };

  useEffect(() => {
    if (isOpen) {
      const missing = checkMissingFields();
      setMissingFieldsData(missing);
    }
  }, [isOpen, tableData, orderedPolicyBeans]);

  // Animation variants for the drawer
  const drawerVariants = {
    hidden: {
      x: '100%',
      opacity: 0,
    },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration: 0.3
      }
    },
    exit: {
      x: '100%',
      opacity: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration: 0.2
      }
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { delay: 0.1, duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            className="missing-fields-overlay"
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={onClose}
          />
          
          {/* Drawer */}
          <motion.div
            className="missing-fields-drawer"
            variants={drawerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Header */}
            <div className="drawer-header">
              <div className="header-content">
                <ExclamationCircleOutlined className="header-icon" />
                <h3>Missing Fields Report</h3>
              </div>
              <Button
                type="text"
                icon={<CloseOutlined />}
                onClick={onClose}
                className="close-button"
              />
            </div>

            {/* Content */}
            <motion.div 
              className="drawer-content"
              variants={contentVariants}
              initial="hidden"
              animate="visible"
            >
              {missingFieldsData.length === 0 ? (
                <div className="no-missing-fields">
                  <div className="success-message">
                    <div className="success-icon">✓</div>
                    <h4>All Required Fields Complete</h4>
                    <p>All suppliers have provided the necessary information.</p>
                  </div>
                </div>
              ) : (
                <div className="missing-fields-list">
                  <div className="summary">
                    <p className="summary-text">
                      {missingFieldsData.length} supplier{missingFieldsData.length > 1 ? 's' : ''} 
                      {missingFieldsData.length > 1 ? ' have' : ' has'} missing required fields
                    </p>
                  </div>

                  {missingFieldsData.map((supplier, index) => (
                    <motion.div
                      key={supplier.serialNo}
                      className="supplier-card"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="supplier-header">
                        <h4 className="supplier-name">{supplier.supplierName}</h4>
                        <span className="missing-count">
                          {supplier.missingFields.length} missing field{supplier.missingFields.length > 1 ? 's' : ''}
                        </span>
                      </div>
                      
                      <div className="missing-fields-grid">
                        {supplier.missingFields.map((field, fieldIndex) => (
                          <div key={fieldIndex} className="missing-field-item">
                            <span className="field-name">{field.displayName}</span>
                            <Link
                              to={`/sourcing/rfq/supplier-details/${rfqId}/${supplier.serialNo}?field=${field.field}`}
                              className="edit-link"
                              onClick={onClose}
                            >
                              Edit
                            </Link>
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>

            {/* Footer */}
            <div className="drawer-footer">
              <Button onClick={onClose} className="close-footer-button">
                Close
              </Button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MissingFieldsDrawer;
