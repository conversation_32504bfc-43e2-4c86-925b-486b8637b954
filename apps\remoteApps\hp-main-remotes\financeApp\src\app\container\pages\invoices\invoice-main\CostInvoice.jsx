/* eslint-disable no-undef */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react/no-unknown-property */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getINVDetailsForAccPay, getNewFormForCostInv } from '@hp/mainstore';
import { showDialog } from '@hp/mainstore';
import { AP_invIdConstant, AP_USER } from '@hp/constants';
import { accPayConstants } from '@hp/mainstore';
import { EmailModal } from '../invoice-common/EmailModal';
import { invoiceService } from '../invoice-common/invoice.service';
import { useAppRouterDom } from '@hp/utils';
import {
  ButtonCommon,
  CommonSpinner,
  globalutils,
  TraceEvents,
  useConfirm,
  FileUploadWithViewer,
  MultiRowInput,
} from '@hp/components';
import { traceEvent, submitCostInvoice } from '@hp/mainstore';
import { Tabs } from 'antd';
import { InvMatchingComponent } from '../invoice-sub/InvMatchingComponent';
import { InvDatatable } from '../invoice-sub/inv-table/INV-DataTable';
const CostInvoice = (props) => {
  const dispatch = useDispatch();

  const { domParameters, navigate } = useAppRouterDom();
  const { confirm } = useConfirm();
  const { TabPane } = Tabs;
  const [getTraceEvents, setTraceEvents] = React.useState([]);
  const [addCommentsValue, setAddCommentsValue] = useState('');
  const [emailModalOpen, setEmailModalOpen] = React.useState(false);
  const [onSaveClicked, setOnSaveClicked] = useState(false);
  const [onSubmittedClick, setOnSubmittedClick] = useState(false);
  const [onSubmitvalidationSuccess, setOnSubmitvalidationSuccess] =
    useState(false);
  const [isLoading, setisLoading] = useState(false);
  const [alertMsgFlag, setAlertMsgFlag] = useState(false);
  const [showInvoiceToggle, setShowInvoiceToggle] = useState(true);
  const [enableThreeCol, setEnableThreeCol] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState('0');
  const [emailTemplateDetails, setEmailTemplate] = useState({
    toAddress: [],
    subject: '',
    content: '',
  });
  const [otherChargesFormData, setOtherChargesFormData] = useState();
  const [filePath, setFilePath] = useState('');
  const [poOrInvResponse, setPoOrInvResponse] = useState([]);

  const {
    accpayINVDetails,
    accpayEntityChange,
    accPaytraceEvents,
    accpayBreadCrumb,
    invEventTime,
    createNewInvoice,
    createNewInvoiceFailed,
    invDetailsEditResponse,
    invDetailsEditError,
    costInvSubmit,
    apCachedData,
    apListingFilteredData,
  } = useSelector((state) => state.accpay);

  const { innerMenuName } = useSelector((store) => store.menu);

  const [focusedInput, setFocusedInput] = useState('');
  const [invId, setInvId] = useState(
    domParameters && /^\d+$/.test(domParameters?.invId)
      ? Number(domParameters?.invId)
      : null
  );
  const focusInputRef = useRef();
  focusInputRef.current = focusedInput;
  const table_name = domParameters?.parameters || '';
  const mainMenu = domParameters?.menuData || '';
  const subMenu = domParameters?.submenu || '';
  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user?.userId);
  const clientId = user?.clientId;
  let tableName = [];
  if (table_name.indexOf(',') > -1) {
    tableName = table_name.split(',');
  }

  const approvalObj = {
    clientId: clientId,
    invId: invId,
    commonTableId: invId,
    status: tableName[1],
    invStatus: tableName[1],
    userId: userId,
    userComments: addCommentsValue,
    emailTempId: null,
  };

  const onEnterKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      // invNonPoSave();
    }
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    setShowInvoiceToggle(true);
    const timeout = setTimeout(() => {
      setShowInvoiceToggle(false);
    }, 3000);
    return () => {
      clearTimeout(timeout);
    };
  }, []);

  useEffect(() => {
    if (accpayINVDetails && accpayINVDetails.value) {
      accpayINVDetails.value.filePath
        ? setFilePath(accpayINVDetails.value.filePath)
        : null;

      setAlertMsgFlag(accpayINVDetails.value.autoSubmitAlertFlag);
      if (accpayINVDetails?.value?.formDetailsDtoList) {
        accpayINVDetails?.value?.formDetailsDtoList.map((items) => {
          if (items.uniqueKey === 'totalcharges') {
            setOtherChargesFormData(items?.formSubDetailsInternalDTOList[0]);
          }
        });
      }
      setisLoading(false);
      setPoOrInvResponse(accpayINVDetails.value);
    }
  }, [accpayINVDetails]);

  useEffect(() => {
    if (accpayEntityChange?.value) {
      let tempForm = { ...poOrInvResponse };
      tempForm?.formDetailsDtoList?.map((item, index) => {
        if (item.uniqueKey === 'itemList') {
          let itemList = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'itemList'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, itemList);
        } else if (item.uniqueKey === 'location') {
          let location = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'location'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, location);
        } else if (item.uniqueKey === 'totalcharges') {
          let totalcharges = accpayEntityChange.value.find(
            (entry) => entry.uniqueKey === 'totalcharges'
          );
          tempForm?.formDetailsDtoList.splice(index, 1, totalcharges);
          setOtherChargesFormData(
            totalcharges?.formSubDetailsInternalDTOList[0]
          );
        }
      });
      setPoOrInvResponse(tempForm);
    }
  }, [accpayEntityChange]);

  useEffect(() => {
    setisLoading(false);
    if (costInvSubmit?.value) {
      if (costInvSubmit?.value?.validationSuccess) {
        navigate(`/${mainMenu}/${subMenu}/APListing/${table_name}`);
      } else {
        amountAndInvDuplicateMessageFunc(costInvSubmit);
      }
    }
    return () => {
      dispatch({
        type: accPayConstants.SUBMIT_COST_INVOICE_RESET,
      });
    };
  }, [costInvSubmit]);

  const amountAndInvDuplicateMessageFunc = (validationResponse) => {
    var errorMessageBuilder = '';
    if (validationResponse) {
      if (validationResponse.value.amountExceededFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.amountOrQuantityExceeded
        );
      }
      if (validationResponse.value.quantityExceededFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.quantityExccededWarning
        );
      }
      if (validationResponse.value.invoiceNumberDuplicationFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.duplicateInvoice
        );
      }
      if (validationResponse.value.lineValidationWarningFlag) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.lineValidationWarning
        );
      }
      if (validationResponse.value.grnValidationFailed) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.grnValidationFailureMessage
        );
      }
      if (validationResponse.value.unitPriceValidationFailed) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.unitPriceValidationFailureMessage
        );
      }
      if (validationResponse.value.profileValidatorFailureMessage) {
        errorMessageBuilder = errorMessageBuilder.concat(
          validationResponse.value.profileValidatorFailureMessage
        );
      }
      funcToSetResponseMessage('error', errorMessageBuilder);
    }
  };

  const getInvAccPayFormResponse = (invIdParam) => {
    dispatch(
      getINVDetailsForAccPay({
        key: AP_invIdConstant,
        id: invIdParam,
        userId: globalutils.getDataFromStorage('userId'),
        reqType: 'subscribe',
      })
    );
  };

  const getCostInvCreateForm = () => {
    dispatch(
      getNewFormForCostInv({
        userId: globalutils.getDataFromStorage('userId'),
        clientId,
        reqType: 'subscribe',
      })
    );
  };

  useEffect(() => {
    getCostInvCreateForm();
    // }
  }, []);

  useEffect(() => {
    if (invId) {
      setisLoading(true);
      getInvAccPayFormResponse(invId);
      dispatch(traceEvent({ tableName: 'INV', tableId: invId }));
    }
  }, [invId]);

  useEffect(() => {
    if (accPaytraceEvents) {
      setTraceEvents(accPaytraceEvents.value);
      let data = null;
      dispatch({
        type: accPayConstants.ACCPAY_TRACE_EVENTS_SUCCESS,
        data,
      });
    }
  }, [accPaytraceEvents]);

  useEffect(() => {
    if (onSubmitvalidationSuccess) {
      notify();
      setOnSubmitvalidationSuccess(false);
    }
  }, [onSubmitvalidationSuccess]);

  const iconClickHandler = (event) => {
    event.preventDefault();
  };

  const onAddCommentsChangeHandler = (event) => {
    setAddCommentsValue(event.target.value);
  };

  useEffect(() => {
    setisLoading(false);
    setAddCommentsValue('');
    let error = null;
    let invEditedDetailsResponse = null;
    if (invDetailsEditResponse && invDetailsEditResponse.value) {
      funcToSetResponseMessage('success', invDetailsEditResponse.value);
      dispatch({
        type: accPayConstants.ACCPAY_PASS_INV_EDITED_DETAILS_SUCCESS,
        invEditedDetailsResponse,
      });
      dispatch(traceEvent({ tableName: 'INV', tableIdz: invId }));
      setAddCommentsValue('');
      setOnSaveClicked(false);
    } else if (invDetailsEditError) {
      if (invDetailsEditError.response && invDetailsEditError.response.data)
        funcToSetResponseMessage('error', invDetailsEditError.response.data);
      setOnSaveClicked(false);
      dispatch({
        type: accPayConstants.ACCPAY_PASS_INV_EDITED_DETAILS_FAILURE,
        error,
      });
    }
  }, [invDetailsEditResponse, invDetailsEditError]);

  useEffect(() => {
    setisLoading(true);
    setAddCommentsValue('');
    if (createNewInvoice && createNewInvoice.value) {
      funcToSetResponseMessage('success', 'Saved successfully!');
      setOnSaveClicked(false);
      var invId = createNewInvoice.value.inv_id;
      setInvId(invId);
      reload(invId);
      getInvAccPayFormResponse(invId);
    } else if (createNewInvoiceFailed) {
      if (
        createNewInvoiceFailed.response &&
        createNewInvoiceFailed.response.data
      )
        funcToSetResponseMessage(
          'error',
          createNewInvoiceFailed.response?.data?.message
        );
      setOnSaveClicked(false);
    }
    return () => {
      dispatch({
        type: accPayConstants.ACCPAY_CREATE_NEW_INVOICE_RESET,
      });
    };
  }, [createNewInvoice, createNewInvoiceFailed]);

  const submitOnClick = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');
    if (isConfirmed) {
      setOnSubmittedClick(true);
    }
  };

  const saveDraftOnClick = () => {
    setOnSaveClicked(true);
  };
  const closeOnClick = () => {
    navigate(-1);
  };
  const reload = (savedInvId) => {
    navigate(
      `/${mainMenu}/${subMenu}/APListing/${table_name}/cost-inv/${savedInvId}`
    );
  };

  const notify = () => {
    dispatch(submitCostInvoice({ userId, obj: approvalObj }));
  };

  const sendButton = () => {
    setOnSaveClicked(true);
  };
  const functionsName = {
    saveDraftOnClick,
    submitOnClick,
    closeOnClick,
  };

  const navigateRoute = (id, e) => {
    e.preventDefault();
    invoiceService.previousNext(
      id,
      dispatch,
      apCachedData,
      apListingFilteredData
    );
  };

  const setValueOfDataTable = (dataTableValue) => {
    let formDetails = cloneDeep(poOrInvResponse);
    if (formDetails) {
      formDetails.invItemsDtoList = dataTableValue;
      formDetails?.formDetailsDtoList?.map((item) => {
        item.type === 'DataTable' ? (item.value = dataTableValue) : null;
      });
    }
    setPoOrInvResponse(formDetails);
  };

  return (
    <div>
      <CommonSpinner visible={isLoading} />
      <div className="inv-prev-next-icons">
        <div
          className="prev-wrap"
          onMouseEnter={() => setShowInvoiceToggle(true)}
          onMouseLeave={() => setShowInvoiceToggle(false)}
        >
          <div
            className="prev-container"
            style={{ display: showInvoiceToggle ? 'block' : 'none' }}
          >
            <div
              title="Previous"
              data-tooltip-id="invoice-prev"
              onClick={(e) => navigateRoute('prev', e)}
              className="icon-arrow-left2 fl prev"
              style={{ display: showInvoiceToggle ? 'block' : 'none' }}
            ></div>
          </div>
        </div>

        <div
          className="next-wrap"
          onMouseEnter={() => setShowInvoiceToggle(true)}
          onMouseLeave={() => setShowInvoiceToggle(false)}
        >
          <div
            className="next-container"
            style={{ display: showInvoiceToggle ? 'block' : 'none' }}
          >
            <div
              title="Next"
              data-tooltip-id="invoice-next"
              onClick={(e) => navigateRoute('next', e)}
              className="icon-arrow-right2 fr next"
              style={{ display: showInvoiceToggle ? 'block' : 'none' }}
            ></div>
          </div>
        </div>
      </div>
      <div className="invoice-compare mb20">
        <div className="pull-right view-switcher">
          <div
            onClick={() => setEmailModalOpen(true)}
            style={{ cursor: 'pointer' }}
            className="service-request-launcher  fr  "
            hidden={alertMsgFlag}
          >
            <i
              className="icon-drawer mr8"
              style={{ verticalAlign: 'baseline' }}
            ></i>

            <label className="label">Preview Email</label>
          </div>
          <div className=" service-request-launcher  fr ">
            <input
              type="checkbox"
              id="inv-img"
              style={{ cursor: 'pointer' }}
              checked={!enableThreeCol}
              onChange={() => setEnableThreeCol(!enableThreeCol)}
            />
            <label className="label mb2" for="inv-img">
              Invoice Image
            </label>
          </div>
        </div>
        <div style={{ backgroundColor: '#fff', padding: '24px 0px 24px 24px' }}>
          <Tabs
            activeKey={activeTabKey}
            onChange={(key) => setActiveTabKey(key)}
            tabBarStyle={{ marginTop: '-24px', fontFamily: 'Roboto' }}
          >
            <TabPane className="th " tab={'Invoice'} key={0}>
              <div className={`viewHolder ${!enableThreeCol ? 'view-1' : ''}`}>
                <InvMatchingComponent
                  accpayBreadCrumb={accpayBreadCrumb}
                  className="boxed unbox"
                  invId={invId}
                  tableName={tableName}
                  disabled={false}
                  addCommentsValue={addCommentsValue}
                  onSaveClick={onSaveClicked}
                  onSaveDisable={(event) => setOnSaveClicked(event)}
                  onSubmittedClick={onSubmittedClick}
                  onSubmittedClickDisable={(event) =>
                    setOnSubmittedClick(event)
                  }
                  notificationBar={(type, message) =>
                    funcToSetResponseMessage(type, message)
                  }
                  onSubmitvalidationSuccess={(event) =>
                    setOnSubmitvalidationSuccess(event)
                  }
                  setisLoading={(event) => setisLoading(event)}
                  filePath={() => null}
                  threeColLayout={enableThreeCol}
                  isEditable={true}
                  setPOINVCallback={(form) => {
                    setPoOrInvResponse(form);
                  }}
                />
                {!enableThreeCol ? (
                  <div className="boxed">
                    <div
                      style={{
                        fontWeight: 700,
                        marginBottom: '16px',
                        fontFamily: 'Roboto',
                      }}
                    >
                      Invoice Image
                      <span
                        title="Click to open in new tab"
                        className="icon-attachment"
                        style={{ cursor: 'pointer', float: 'right' }}
                      ></span>
                    </div>
                    <FileUploadWithViewer />
                  </div>
                ) : (
                  ''
                )}

                <div className="mb20"></div>
              </div>
            </TabPane>
            <TabPane className="th " tab={'Invoice Items'} key={1}>
              <div className="inv-div">
                <div className="force-full-width">
                  {tableName &&
                  (tableName[0] === 'PP' || tableName[0] === 'INV') ? (
                    <InvDatatable
                      fullWidth
                      disabledFlag={false}
                      status={tableName[1]}
                      setValueOfDataTable={setValueOfDataTable}
                      invData={poOrInvResponse?.formDetailsDtoList}
                    />
                  ) : null}
                </div>
              </div>
            </TabPane>
            <TabPane className="th " tab={'Other Charges'} key={2}>
              <div className="force-full-width">
                <MultiRowInput formType={otherChargesFormData} />
              </div>
            </TabPane>
          </Tabs>
        </div>
        <EmailModal
          isModalOpen={emailModalOpen}
          isModalClose={() => setEmailModalOpen(false)}
          invId={invId}
          invEventTime={invEventTime}
          tableName={tableName}
          emailDetails={emailTemplateDetails}
        />
      </div>

      <ButtonCommon functionsName={functionsName} />

      <div className="boxed mb40" style={{ marginRight: '0px' }}>
        <TraceEvents
          key="traceEventsEditor"
          onAddCommentsChangeHandler={onAddCommentsChangeHandler}
          addCommentsValue={addCommentsValue}
          enableComments={true}
          data={getTraceEvents}
          sendButton={sendButton}
          onEnterKeyPress={onEnterKeyPress}
          invId={invId}
          userId={userId}
        />
      </div>
    </div>
  );
};

export { CostInvoice };
