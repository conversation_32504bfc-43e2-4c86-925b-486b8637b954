/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
import { Button, MultiEmailInput, RichTextEditor } from '@hp/components';
import { useState, useEffect } from 'react';

function EmailHandler({
  emailTemplate,
  carbonCopy,
  mailData,
  cancel,
  emailAddressData,
}) {
  const [warning, setWarning] = useState(false);
  const [emailAddress, setEmailAddress] = useState([]);

  const [template, setTemplate] = useState({
    subject: '',
    toAddressList: [],
    bccList: [],
    ccList: [],
    content: '',
  });
  useEffect(() => {
    if (emailTemplate) {
      setTemplate({ ...template, ...emailTemplate });
    }
    if (emailAddressData.length) {
      setEmailAddress(emailAddressData);
    }
  }, [emailTemplate, emailAddressData]);

  const ccHandler = (index, item) => {
    let temArray = emailAddress.with(index, {
      ...item,
      isActive: true,
    });
    setEmailAddress(temArray);
  };

  const onRichTextEditorChangeHandler = (event, key) => {
    setTemplate((prv) => {
      return { ...prv, [key]: event };
    });
  };

  const onChangeHandler = (data, key, removeDuplicates) => {
    if (key === 'subject') {
      setTemplate({ ...template, [key]: data });
      return;
    }
    const changedArray = removeDuplicates(data);
    setTemplate({ ...template, [key]: changedArray });
  };
  const mailSendHandler = () => {
    if (template?.toAddressList.length) {
      mailData(template);
      return;
    }
    setWarning(true);
    setTimeout(() => {
      setWarning(false);
    }, 2000);
  };
  return (
    <div
      className="new-compose-body"
      style={{
        height: '100%',
        position: 'relative',
        width: '100%',
      }}
    >
      <div
        className="to-compose-input-custom-for-multiEmail"
        style={{ alignItems: 'baseline' }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            flex: 11,
          }}
        >
          {emailAddress.map((item, index) => {
            if (item.isActive)
              return (
                <div
                  className="compose-input-custom-for-multiEmail"
                  key={index}
                >
                  <p className="prefix ">{item.label} :</p>

                  <MultiEmailInput
                    emailList={
                      template[item.listName] ? template[item.listName] : []
                    }
                    emailOnchange={(address, removeDuplicates) =>
                      onChangeHandler(address, item.listName, removeDuplicates)
                    }
                  />
                </div>
              );
          })}
          <div className="compose-input ">
            <p className="prefix " style={{ marginBottom: '0px' }}>
              Subject :
            </p>
            <input
              style={{ fontWeight: 'bold' }}
              type="text"
              autoComplete="false"
              onChange={(event) =>
                onChangeHandler(event.target.value, 'subject')
              }
              value={template?.subject ? template.subject : ''}
            />
          </div>
        </div>
        {carbonCopy && (
          <div className="button-cc-bcc-div" style={{ flex: 1 }}>
            {emailAddress.map((item, index) => {
              if (!item.isActive) {
                return (
                  <button key={index} onClick={() => ccHandler(index, item)}>
                    {item.label}
                  </button>
                );
              }
            })}
          </div>
        )}
      </div>
      <div className="mb20">
        <RichTextEditor
          name="content"
          onChange={(event) => onRichTextEditorChangeHandler(event, 'content')}
          value={template?.content ? template.content : ''}
        />
      </div>

      <div className="mb8 fr">
        <Button
          className="small default mr20"
          onClick={() => mailSendHandler()}
        >
          Send
        </Button>
        <Button className="small secondary " onClick={() => cancel()}>
          Cancel
        </Button>
      </div>

      {warning ? (
        <div
          className={'notification-bar matrix type-info'}
          style={{
            zIndex: 1,
            width: '100%',
            position: 'absolute',
            bottom: '0px',
          }}
        >
          <p>Address is Required</p>
        </div>
      ) : (
        ''
      )}
    </div>
  );
}

export default EmailHandler;
