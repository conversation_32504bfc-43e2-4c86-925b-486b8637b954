/* eslint-disable no-self-assign */
/* eslint-disable no-constant-condition */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */

import { Button, Input, formValidationUtil, useConfirm } from '@hp/components';
import { memo, useState, useLayoutEffect } from 'react';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import { bbMPNEdit } from '@hp/mainstore';
import { bbMPNADD } from '@hp/mainstore';
import cloneDeep from 'lodash.clonedeep';
function FormActions(props) {
  const {
    formInputs,
    isOpen,
    productFileMasterId,
    eventHandlers,
    isClose,
    id,
    action,
    isnotify,
  } = props;
  const [notify, setnotify] = useState(isnotify);
  const [addedFile, setsAddedFile] = useState(false);
  const [editedFile, setEditedFile] = useState(false);
  const [formInputsFormAction, setformInputsFormAction] = useState([]);
  // const [formDetails, setFormDetails] = useState();

  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const { mpnAddSuccess, mpnEditSuccess } = useSelector((store) => store.bb);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  useLayoutEffect(() => {
    if (isOpen) {
      setnotify(null);
    }
  }, [isOpen]);

  useLayoutEffect(() => {
    if (mpnAddSuccess && addedFile && action === 'add') {
      if (mpnAddSuccess.httpStatus === 'OK') {
        funcToSetResMessageInModal('success', mpnAddSuccess.value);
      } else {
        funcToSetResMessageInModal('error', 'Something went wrong');
      }
    }

    if (mpnEditSuccess && editedFile && action === 'edit') {
      if (mpnEditSuccess.httpStatus === 'OK') {
        funcToSetResMessageInModal('success', mpnEditSuccess.value);
      } else {
        funcToSetResMessageInModal('error', 'Something went wrong');
      }
    }
  }, [mpnAddSuccess, mpnEditSuccess]);

  useLayoutEffect(() => {
    const form = formInputs.filter((el) => el.uniqueKey === 'mpn-sub-table');
    if (form && form.length) {
      setformInputsFormAction(form[0].formSubDetailsInternalDTOList);
    }
  }, [formInputs]);

  const addProduct = async () => {
    const isConfirmed = await confirm('Are you sure you want to proceed?');

    if (isConfirmed) {
      const validatedElement = formValidationUtil.validateForm(
        cloneDeep(formInputsFormAction)
      );

      setformInputsFormAction(validatedElement.formList); // 👈 updates input field state with error flags

      if (!validatedElement.validSuccess) return; // 👈 stops if validation failed

      // convert values
      const tempArray = validatedElement.formList.map((item) => {
        if (item.uniqueKey === 'quantity') {
          item.value = parseFloat(item.value);
        } else if (item.uniqueKey === 'stdprice') {
          item.value = parseFloat(item.value);
        }
        return item;
      });

      // dispatch action
      if (action === 'add') {
        dispatch(
          bbMPNADD({
            productFileMasterId,
            obj: tempArray,
            reqType: 'subscribe',
          })
        );
        setsAddedFile(true);
      } else if (action === 'edit') {
        dispatch(bbMPNEdit({ obj: tempArray, productDetailsId: id }));
        setEditedFile(true);
      }

      let timerId = setTimeout(() => {
        isClose(false);
        clearTimeout(timerId);
      }, 3000);
    }
  };

  return (
    <Modal
      className="Modal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={isOpen}
    >
      <div
        onClick={() => isClose(false)}
        className="modal-close icon-close"
      ></div>
      <div className="page-title">Shortage Parts Details</div>
      <div className="two-col-layout">
        {formInputsFormAction && formInputsFormAction.length
          ? formInputsFormAction.map((element, index) => {
              return (
                <Input
                  onChangeHandler={(type, event) =>
                    eventHandlers.onChangeHandler(type, event, 'add')
                  }
                  isEditable="notShowing"
                  formType={element}
                  key={index}
                />
              );
            })
          : ''}
      </div>
      <Button className="mr8  default" onClick={addProduct}>
        Submit
      </Button>
      <Button onClick={() => isClose(false)} className="info mr20">
        Cancel
      </Button>
      {notify != null ? (
        <div
          className={['notification-bar', 'type-' + notify.type].join(' ')}
          style={{
            position: 'sticky !important',
            width: 90 + '%',
          }}
        >
          <i className="icon-close" onClick={() => setnotify(null)}></i>
          {notify.resMessage}
        </div>
      ) : (
        ''
      )}
    </Modal>
  );
}

export default memo(FormActions);
