@import '../../@hp/styles/variables.scss';

.styled-ant-table {
  // Add box shadow to the table
  // box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px,
  //   rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;
  background-color: $modal_bg;

  .ant-table {
    border-bottom: $grey_color solid 1px;
  }

  /* Header cell style for th directly within thead */
  .ant-table-thead > tr > th {
    border: none;
    font-size: 16px; // Adjust the font size as needed
    font-weight: bold;
    background: #d3d3d3;
    // border-bottom: 2px solid transparent;
    border: 0.5px solid $grey_color; // Adjust the border color
    border-radius: 0% !important;
    text-align: center; // Adjust the border-radius if needed
  }

  /* Header cell style for th within tr within thead */
  .ant-table-thead > tr > td {
    border: none;
    font-size: 16px; // Adjust the font size as needed
    font-weight: bold;
    background: #d3d3d3;
    border-bottom: 2px solid transparent;
    border: 1px solid $grey_color; // Adjust the border color
    border-radius: 0% !important;
    text-align: center; // Adjust the border-radius if needed
    cursor: pointer;
  }

  .ant-table-cell {
    padding: 8px;
    border: thin solid $grey_color;
  }

  .ant-table-row {
    padding: 8px;
  }

  /* Custom styles for the pagination */
  .ant-pagination {
    /* Your pagination styles here */
  }

  /* Optional: Add styles to the pagination buttons or other pagination elements */
  .ant-pagination-item {
    color: $primary_color_rgb;
    background-color: #fff;
    border-radius: 50%;
  }

  /* Optional: Add styles to the "Go to" input field */
  .ant-pagination-options {
    /* Your styles here */
  }

  /* Optional: Add styles to the "Total" text */
  .ant-pagination-total-text {
    /* Your styles here */
  }

  .drag-handle {
    cursor: move !important;
    color: #000;
    :hover {
      color: $primary_color_rgb;
    }
  }

  .delete-handle {
    cursor: pointer !important;
    :hover {
      color: $error_color;
    }
  }
}
