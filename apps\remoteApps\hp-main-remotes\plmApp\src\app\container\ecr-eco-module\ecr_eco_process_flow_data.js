export const ecrFlowCreateData = {
  nodes: [
    {
      workflowDetailsId: 1,
      nodeName: 'Create ECR',
      orderId: 1,
      workflowMasterId: 2,
      nodeData: {
        status: 'in-progress',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-file-text2',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 2,
      nodeName: 'Draft',
      orderId: 2,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-purchase',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 3,
      nodeName: 'Pending Review',
      orderId: 3,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-purchase',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 4,
      nodeName: 'Approval',
      orderId: 4,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-tree-testing',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 5,
      nodeName: 'Change Execution',
      orderId: 5,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-file-text2',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 6,
      nodeName: 'Queue for ECO',
      orderId: 6,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-menu-invoice',
        generatedNumber: null,
      },
    },
  ],
  edges: [
    {
      id: '1,2',
      source: 1,
      target: 2,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '2,3',
      source: 2,
      target: 3,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '3,4',
      source: 3,
      target: 4,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '4,5',
      source: 4,
      target: 5,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '5,6',
      source: 5,
      target: 6,
      type: '',
      label: null,
      status: 'processed',
    },
  ],
};

export const ecoFlowCreateData = {
  nodes: [
    {
      workflowDetailsId: 1,
      nodeName: 'Create ECO',
      orderId: 1,
      workflowMasterId: 2,
      nodeData: {
        status: 'in-progress',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-file-text2',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 2,
      nodeName: 'Draft',
      orderId: 2,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-purchase',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 3,
      nodeName: 'Pending Review',
      orderId: 3,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-purchase',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 4,
      nodeName: 'Approval',
      orderId: 4,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-tree-testing',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 5,
      nodeName: 'Released',
      orderId: 5,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-file-text2',
        generatedNumber: null,
      },
    },
    {
      workflowDetailsId: 6,
      nodeName: 'Implemented',
      orderId: 6,
      workflowMasterId: 2,
      nodeData: {
        status: 'yet-to-complete',
        userName: null,
        date: null,
        time: null,
        url: null,
        iconType: 'icon-menu-invoice',
        generatedNumber: null,
      },
    },
  ],
  edges: [
    {
      id: '1,2',
      source: 1,
      target: 2,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '2,3',
      source: 2,
      target: 3,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '3,4',
      source: 3,
      target: 4,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '4,5',
      source: 4,
      target: 5,
      type: '',
      label: null,
      status: 'processed',
    },
    {
      id: '5,6',
      source: 5,
      target: 6,
      type: '',
      label: null,
      status: 'processed',
    },
  ],
};
