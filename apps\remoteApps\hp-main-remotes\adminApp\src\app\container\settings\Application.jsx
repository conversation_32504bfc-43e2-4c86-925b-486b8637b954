/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 03-02-2022 15:40:13
 * @modify date 2024-08-01 15:16:37
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import {
  dataTableServiceProvider,
  formValidationUtil,
  Input,
  useConfirm,
} from '@hp/components';
import { Button } from '@hp/components';
import {
  showDialog,
  resetaddApplicationFormDetails,
  reseteditApplicationFormDetails,
  resetdeleteApplicationFormDetails,
} from '@hp/mainstore';
import {
  addApplicationFormDetails,
  deleteApplicationFormDetails,
  editApplicationFormDetails,
  getApplicationFormDetails,
} from '@hp/mainstore';

const Application = (props) => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { applicationFormDetails, applicationFormDetailsResponse } =
    useSelector((store) => store.settings);

  const appId = '';
  const [formDetails, setFormDetails] = useState([]);
  const [notify, setnotify] = useState(null);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [selected, setSelected] = useState({});
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    if (applicationFormDetails && applicationFormDetails.value) {
      setFormDetails(applicationFormDetails.value);
      setTableData(
        applicationFormDetails.value.find(
          (val) => val.uniqueKey === 'dataTable'
        ).value
      );
    }
  }, [applicationFormDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      dispatch(resetaddApplicationFormDetails());
      dispatch(reseteditApplicationFormDetails());
      dispatch(resetdeleteApplicationFormDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  // Function to Show Alerts
  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  useEffect(() => {
    if (
      applicationFormDetailsResponse &&
      applicationFormDetailsResponse.value
    ) {
      funcToSetResponseMessage(
        applicationFormDetailsResponse.value.type,
        applicationFormDetailsResponse.value.text
      );
    }
    dispatch(getApplicationFormDetails());
  }, [applicationFormDetailsResponse]);

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let appNotExist = true;
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (
            element.uniqueKey === 'appName' ||
            element.uniqueKey === 'appCode'
          ) {
            if (element.value === null || element.value.trim() === '') {
              appNotExist = false;
              element.uniqueKey === 'appName'
                ? funcToSetResMessageInModal(
                    'error',
                    'Please Enter Application Name'
                  )
                : funcToSetResMessageInModal(
                    'error',
                    'Please Enter Application Code'
                  );
            } else {
              tempArray.map((item) => {
                if (item.uniqueKey === 'dataTable') {
                  item.value.map((val) => {
                    if (val.appName === element.value) {
                      if (selected.appName === element.value) {
                        return;
                      } else {
                        appNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Application Name already exists'
                        );
                        return;
                      }
                    }
                    if (val.appCode === element.value) {
                      if (selected.appCode === element.value) {
                        return;
                      } else {
                        appNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Application Code already exists'
                        );
                        return;
                      }
                    }
                  });
                }
              });
            }
          }
        });
    });
    if (appNotExist) {
      edit
        ? dispatch(
            editApplicationFormDetails({
              formDetails: formDetails,
              deptId: selected.appId,
            })
          )
        : dispatch(addApplicationFormDetails(formDetails));
      setShowModal(false);
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.disableFlag = 'F';
          element.value = '';
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setEdit(true);
    setSelected(obj);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          entry.uniqueKey === 'appName' ? (entry.value = obj.appName) : '';
          entry.uniqueKey === 'appCode' ? (entry.value = obj.appCode) : '';
          entry.uniqueKey === 'description'
            ? (entry.value = obj.description)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.appId,
      'appId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.appId
      ? handleConfirm(obj.appId)
      : funcToSetResponseMessage(
          'error',
          'Please Select an Application to Delete'
        );
  };

  //Function to confirm delete
  const handleConfirm = async (appId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteApplicationFormDetails(appId));
      setDisable(false);
      setShowModal(false);
      setSelected({});
    }
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey, element) => {
    let tempArray = cloneDeep(formDetails);
    let data = formValidationUtil.validateInputMaxLengthDynamic(event, element);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element, elementIndex) => {
          if (element.uniqueKey === uniqueKey) {
            entry.formSubDetailsInternalDTOList.splice(elementIndex, 1, data);
          }
        });
    });
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelected(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'T';
          entry.uniqueKey === 'appName' ? (entry.value = obj.appName) : '';
          entry.uniqueKey === 'appCode' ? (entry.value = obj.appCode) : '';
          entry.uniqueKey === 'description'
            ? (entry.value = obj.description)
            : '';
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.appId,
      'appId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  // const handleFilterData = (filterData) => {
  //   let tempArray = cloneDeep(formDetails);
  //   tempArray.map((element) =>
  //     element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
  //   );
  //   setFormDetails(tempArray);
  // };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey) {
            if (element.uniqueKey === 'pageSubtitle') {
              while (showModal) {
                return (
                  <Modal
                    className="Modal"
                    overlayClassName="ModalOverlay"
                    ariaHideApp={false}
                    isOpen={showModal}
                    key={index}
                  >
                    <div
                      onClick={() => {
                        setEdit(false);
                        handleCancel();
                        setSelected({});
                        setConditionalRowStyles([]);
                      }}
                      className="modal-close icon-close"
                      style={{ fontSize: 20 + 'px' }}
                    ></div>
                    <div className="boxed mb20">
                      <Input indexKey={index} formType={element} />
                      <div>
                        {formControlsBinding(
                          element.formSubDetailsInternalDTOList
                        )}
                      </div>
                      {showButtons && (
                        <div>
                          <Button
                            onClick={() => handleSave()}
                            className="default mr20"
                          >
                            Save
                          </Button>
                          <Button
                            onClick={() => handleCancel()}
                            className="info mr20"
                          >
                            Cancel
                          </Button>
                        </div>
                      )}
                    </div>
                    {notify != null ? (
                      <div
                        className={[
                          'notification-bar',
                          'type-' + notify.type,
                        ].join(' ')}
                        style={{
                          position: 'sticky !important',
                          width: 90 + '%',
                        }}
                      >
                        <i
                          className="icon-close"
                          onClick={() => setnotify(null)}
                        ></i>
                        {notify.resMessage}
                      </div>
                    ) : (
                      ''
                    )}
                  </Modal>
                );
              }
            } else if (element.uniqueKey === 'dataTable') {
              let columns =
                element.formSubDetailsInternalDTOList &&
                element.formSubDetailsInternalDTOList.map((val, idx) => {
                  return {
                    selector: val.selector,
                    width:
                      val.selector === 'editIcon' ||
                      val.selector === 'deleteIcon'
                        ? '4%'
                        : '',
                    name: val.displayName,
                    cell:
                      val.selector === 'editIcon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-edit-button"
                                onClick={() => handleEdit(row)}
                                key={idx}
                              ></div>
                            );
                          }
                        : val.selector === 'deleteIcon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-2-trash"
                                onClick={() => handleDelete(row)}
                                key={idx}
                              ></div>
                            );
                          }
                        : '',
                  };
                });
              return (
                <Input
                  indexKey={index}
                  formType={element}
                  dataTableEventHandler={(obj) => {
                    handleRowClick(obj);
                  }}
                  dataTableColumn={columns}
                  conditionalRowStyles={conditionalRowStyles}
                />
              );
            } else if (element.uniqueKey === 'add_button') {
              return (
                <div
                  style={{ float: 'right', marginBottom: 10 + 'px' }}
                  key={index}
                >
                  <Button
                    onClick={() => {
                      handleCancel();
                      setSelected({});
                      setConditionalRowStyles([]);
                      setEdit(false);
                      setShowModal(true);
                      setShowButtons(true);
                    }}
                    className="small mb8 outline add-button-custom flex-row vam"
                  >
                    <i className="icon-add-button "> </i>Add
                  </Button>
                </div>
              );
            } else if (element.uniqueKey === 'search') {
              return;
              // return (
              //   <div style={{ display: "inline-block", width: "200px" }} key={index}>
              //     <Input
              //       key={index}
              //       formType={element}
              //       isEditable="notShowing"
              //       tableListData={tableData}
              //       getFilteredData={(filterData) =>
              //         handleFilterData(filterData)
              //       }
              //     />
              //   </div>
              // );
            } else {
              return (
                <Input
                  indexKey={index}
                  disabledFlag={disable}
                  formType={element}
                  isEditable="notShowing"
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey,
                      element
                    );
                  }}
                />
              );
            }
          }
        })
      : null;
  };

  return (
    <div className="">
      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}
    </div>
  );
};
export { Application };
