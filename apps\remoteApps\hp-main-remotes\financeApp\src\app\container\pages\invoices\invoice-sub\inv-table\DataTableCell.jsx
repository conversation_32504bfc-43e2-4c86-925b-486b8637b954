import { useState } from 'react';
import { AdvancedSelect, TextInput } from '@hp/components';
import { Tooltip } from 'antd';

import { useDispatch } from 'react-redux';
import { showDialog } from '@hp/mainstore';
const UNEDITABLE_COLUMNS = ['rcvdQty', 'amountConverted', 'serialNo'];
const ADVANCED_FIELDS = ['glMasterCode', 'dept', 'taxCode'];

const DataTableCell = ({ row, index, column, dataTableForm, onChange }) => {
  const [cellValue, setCellValue] = useState(row[column.selector]);

  const isNumericField = [
    'quantity',
    'unitPriceConverted',
    'amountConverted',
    'subTotalConverted',
    'taxAmountConverted',
    'serialNo',
  ].includes(column.selector);

  const dispatch = useDispatch();
  const validateValue = (value) => {
    const trimmedValue = value.trim();
    const cleanedValue = trimmedValue.replace(/,/g, '');
    return !isNaN(Number(cleanedValue));
  };

  const handleTextInputChange = (e) => {
    let value = e.target.value;

    if (isNumericField && !validateValue(value)) {
      dispatch(
        showDialog({
          showPopup: true,
          type: 'error',
          responseMessage: 'Character not allowed',
          canClose: true,
          autoHide: true,
        })
      );
      return;
    }

    setCellValue(value);
    onChange(e, index);
  };

  const handleAdvancedSelectChange = (selected, selector, index) => {
    const selectVal = selected
      ? { id: selected.value, value: selected.label }
      : { id: '', value: '' };

    setCellValue(selectVal);
    onChange({ target: { name: selector, value: selectVal } }, index);
  };

  const getAdvancedOptions = (selector) => {
    const fieldData = dataTableForm?.formSubDetailsInternalDTOList?.find(
      (item) => item.selector === selector
    );
    return (
      fieldData?.comboBoxOptions?.map((combo) => ({
        value: combo.commonId ?? combo.commonName,
        label: combo.commonCode,
      })) || []
    );
  };

  const tooltipPosition = column.right ? 'topRight' : 'topLeft';

  const renderCell = () => {
    const isEditable =
      column.editing && !UNEDITABLE_COLUMNS.includes(column.selector);
    const isAdvancedField = ADVANCED_FIELDS.includes(column.selector);

    if (column.editing && isAdvancedField) {
      return (
        <AdvancedSelect
          value={cellValue}
          className="cell-100"
          onChange={(event) =>
            handleAdvancedSelectChange(event, column.selector, index)
          }
          options={getAdvancedOptions(column.selector)}
          getLabel={true}
        />
      );
    }

    if (isEditable) {
      return (
        <TextInput
          type={column.type || 'text'}
          name={column.selector}
          style={{
            width: '100%',
            borderRight: '1px solid #999',
            textAlign: column.right ? 'right' : 'start',
          }}
          onChange={handleTextInputChange}
          value={cellValue || ''}
          label={'false'}
        />
      );
    }

    if (isAdvancedField) {
      return <p className="single-line-ellipsis">{cellValue?.value || ''}</p>;
    }
    return <p className="single-line-ellipsis">{cellValue}</p>;
  };
  return (
    <Tooltip
      classNames={{ root: 'ant-tooltip-container' }}
      title={cellValue}
      placement={tooltipPosition}
    >
      {renderCell()}
    </Tooltip>
  );
};

export { DataTableCell };
