/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { getViewRolesListing, getMenuRolesDetails } from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { Roles, TextInput, dataTableServiceProvider } from '@hp/components';
import Modal from 'react-modal';

const ViewRoles = (props) => {
  const dispatch = useDispatch();
  const { viewroleslist, menuroles } = useSelector((store) => store.admin);
  const [TableListing, setTableList] = useState();
  const [searchBox, setSearchBox] = useState();
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  const [state, setState] = useState([]);
  const [modalRoles, setModalRoles] = useState(false);

  useEffect(() => {
    getParameter();
  }, [props]);

  useEffect(() => {
    if (viewroleslist && viewroleslist !== undefined) {
      setTableList(viewroleslist);
      setSearchBox(viewroleslist);
    }
  }, [viewroleslist]);

  useEffect(() => {
    if (menuroles && menuroles.roleMenuList.length > 0) {
      setState(menuroles);
    }
  }, [menuroles]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const getParameter = () => {
    dispatch(getViewRolesListing());
  };

  const DataTableEventHandler = (e) => {
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      e.roleId,
      'roleId'
    );
    setConditionalStyles(styleAttribute);

    let roleId = e.roleId;
    dispatch(getMenuRolesDetails(roleId));
    setModalRoles(true);
  };

  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const columns = [
    {
      name: 'Role Name',
      selector: 'roleName',
      sortable: true,
    },
    {
      name: 'Description',
      selector: 'description',
      sortable: true,
    },
  ];

  return (
    <>
      <div className="page-title">View Roles</div>
      <div className="two-col-layout mb20">
        <div className="three-col-layout">
          <TextInput
            placeholder="Search"
            style={{ width: 105 + '%' }}
            value={filterString.filterStr}
            onChange={(e) => handleChange(e)}
          />
        </div>
      </div>

      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          theme={'solarized'}
          onRowClicked={(e) => DataTableEventHandler(e)}
          conditionalRowStyles={conditionalRowStyles}
        />
      </div>
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={modalRoles}
      >
        <div
          onClick={() => setModalRoles(false)}
          className="modal-close icon-close"
        ></div>
        <Roles data={state} />
      </Modal>
    </>
  );
};

export { ViewRoles };
