/* eslint-disable react/no-unescaped-entities */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

import { useState, useEffect } from 'react';
import { CheckBoxInput, useConfirm, Input } from '@hp/components';

import { useSelector, useDispatch } from 'react-redux';

import CreateAs from './CreateAs';
import {
  getItemNodeListOnSearch,
  getPdmItemSearchForm,
  pasteItemNodes,
  getPdmSearchPaginationListSize,
  pdmConstants,
} from '@hp/mainstore';
import '@hp/styles/Header.scss';
function NodeSearchTable(props) {
  const { checkedIds, setCheckedIds, checkedIndex, setCheckedIndex } = props;

  const [searchFormList, setSearchFormList] = useState([]);
  const [createAsForm, setcreateAsForm] = useState({});
  const [saveAs, setSaveAs] = useState(false);
  const [searchValue, setsearchValue] = useState('');
  const [searchList, setSearchList] = useState([]);
  const [loading, setLoading] = useState(false);

  const { confirm } = useConfirm();

  const dispatch = useDispatch();
  const { nodeSearchRes, nodeSearchForm, createAsData } = useSelector(
    (store) => store.pdm
  );
  const { dynamicListLimitAndOffset } = useSelector((store) => store.util);

  useEffect(() => {
    if (!nodeSearchRes) dispatch(getPdmItemSearchForm());
  }, []);

  useEffect(() => {
    if (dynamicListLimitAndOffset && searchValue) {
      dispatch(
        getItemNodeListOnSearch({
          itemName: searchValue,
          limit: dynamicListLimitAndOffset?.limit,
          offset: dynamicListLimitAndOffset?.offset,
        })
      );
    }
  }, [dynamicListLimitAndOffset]);

  useEffect(() => {
    if (nodeSearchForm?.value) {
      if (!nodeSearchRes) {
        const list = [];
        nodeSearchForm.value.map((formList) => {
          if (formList.type === 'Button') list.push({ ...formList, type: '' });
          else if (formList.uniqueKey === 'createAs') setcreateAsForm(formList);
          else list.push(formList);
        });

        setSearchFormList(list);
      } else {
        const list = [];
        nodeSearchForm.value.map((formList) => {
          if (formList.uniqueKey === 'createAs') setcreateAsForm(formList);
          else list.push(formList);
        });
        setSearchFormList(list);
      }
    }
  }, [nodeSearchForm]);

  useEffect(() => {
    setLoading(false);
    if (nodeSearchRes?.value.length) {
      const tableValue = nodeSearchForm.value.map((form) => {
        if (form.type === 'DataTableDynamicPagination')
          return { ...form, value: nodeSearchRes.value };
        if (form.type === '') return { ...form, type: 'Button' };
        else return form;
      });
      setSearchFormList(tableValue);
      setSearchList(nodeSearchRes.value);
    } else {
      const list = searchFormList.map((formList) => {
        if (formList.type === 'DataTableDynamicPagination')
          return { ...formList, value: [] };
        else if (formList.type === 'Button') return { ...formList, type: '' };
        else return formList;
      });
      setSearchFormList(list);
      setSearchList([]);
    }
  }, [nodeSearchRes]);

  const handleCheckbox = (position, id, item) => {
    let isExist = checkedIndex.includes(position);
    if (isExist) {
      setCheckedIndex(checkedIndex.filter((val) => val !== position));
      setCheckedIds(checkedIds.filter((val) => val !== id));
      let selectedData = createAsData.filter((val) => val.itemId !== id);
      dispatch({ type: pdmConstants.CREATE_AS_DATA, payload: selectedData });

      return;
    }

    setCheckedIndex([...checkedIndex, position]);
    setCheckedIds([...checkedIds, id]);
    if (!createAsData)
      return dispatch({
        type: pdmConstants.CREATE_AS_DATA,
        payload: [{ ...item, position }],
      });
    dispatch({
      type: pdmConstants.CREATE_AS_DATA,
      payload: [...createAsData, { ...item, position }],
    });
  };

  const moveHandler = async () => {
    if (!checkedIndex.length) return;

    let element = ' nodes';

    if (checkedIds.length === 1) {
      let elementName = searchList.filter(
        (list) => list.itemId === checkedIds[0]
      )[0].itemNameTem;
      if (elementName) element = `<b>${elementName}</b>`;
      else element = ``;
    }
    const isConfirmed = await confirm(
      `Are you sure you want to move ${element} to <b>${props.parentName}</b>`
    );
    if (isConfirmed) {
      dispatch(
        pasteItemNodes({ parentItemId: props.itemId, form: checkedIds })
      );
    }
  };

  const closeHandler = () => {
    // dispatch({type:pdmConstants.NODE_SEARCH_LIST_SUCCESS,payload:null})
    props.close();
  };

  const inputHandler = (type, event) => {
    let search = searchFormList
      .filter((list) => list.type === 'div')[0]
      ?.formSubDetailsInternalDTOList.map((val) => ({
        ...val,
        value: event.target.value,
      }));
    let newForm = searchFormList.map((list) => {
      if (list.type === 'div') {
        return { ...list, formSubDetailsInternalDTOList: search };
      } else return list;
    });

    setSearchFormList(newForm);
  };
  const searchHandler = (type) => {
    if (!type.value) return;
    setCheckedIds([]);
    setCheckedIndex([]);
    setLoading(true);
    setsearchValue(type.value);
    dispatch(
      getItemNodeListOnSearch({
        itemName: type.value,
        limit: dynamicListLimitAndOffset?.limit,
        offset: dynamicListLimitAndOffset?.offset,
      })
    );
    dispatch(getPdmSearchPaginationListSize(type.value));
    dispatch({
      type: pdmConstants.NODE_SEARCH_FORM_SUCCESS,
      payload: { ...nodeSearchForm, value: searchFormList },
    });
    dispatch({ type: pdmConstants.CREATE_AS_DATA, payload: [] });
  };

  const clearHandler = () => {
    setCheckedIds([]);
    setCheckedIndex([]);
    dispatch({ type: pdmConstants.NODE_SEARCH_LIST_SUCCESS, payload: null });
    dispatch(getPdmItemSearchForm());
    setsearchValue('');
    setSearchFormList([]);
  };
  const createAsHandler = () => {
    if (!createAsData || !createAsData.length) return;
    setSaveAs(true);
  };

  const onChangeHandlingFunctions = {
    inputHandler,
    searchHandler,
    moveHandler,
    clearHandler,
    createAsHandler,
  };

  return (
    <div
      style={
        loading
          ? { cursor: 'wait', position: 'relative' }
          : { cursor: 'default', position: 'relative' }
      }
    >
      <div
        onClick={closeHandler}
        className="  icon-close mb8 "
        style={{
          cursor: 'pointer',
          position: 'absolute',
          right: '-15px',
          top: '-15px',
        }}
        title={'close'}
      ></div>

      <div
        className="profile-header"
        style={{
          padding: '10px',
          marginBottom: '20px',
          lineHeight: '10px',
        }}
      >
        <h6 className="mb16">Item Catalogue</h6>
        <ul className="sub-data">
          <li>Search an existing component and add to the tree.</li>
          <li>Create a new component using "Create As".</li>
        </ul>
      </div>

      {searchFormList && searchFormList.length && !saveAs ? (
        <>
          {searchFormList.map((form, index) => {
            if (form.type === 'DataTableDynamicPagination') {
              return (
                <Input
                  key={index}
                  dataTableColumn={
                    form?.formSubDetailsInternalDTOList
                      ? form.formSubDetailsInternalDTOList.map(
                          (value, index) => {
                            return {
                              width:
                                value.selector === 'checkboxIcon'
                                  ? '8%'
                                  : value.selector === 'partNumber'
                                  ? '20%'
                                  : '',
                              name: value.displayName ? value.displayName : '',
                              selector: value.selector ? value.selector : '',
                              cell:
                                value.selector === 'checkboxIcon'
                                  ? (row) => {
                                      return (
                                        <CheckBoxInput
                                          checked={
                                            checkedIndex.includes(row.serialNo)
                                              ? true
                                              : false
                                          }
                                          onChange={() =>
                                            handleCheckbox(
                                              row.serialNo,
                                              row.itemId,
                                              row
                                            )
                                          }
                                        />
                                      );
                                    }
                                  : '',
                            };
                          }
                        )
                      : []
                  }
                  formType={form}
                  dataTableEventHandler={() => null}
                />
              );
            } else
              return (
                <Input
                  onChangeHandler={(type, event) =>
                    onChangeHandlingFunctions[type.onChangeFunction](
                      type,
                      event,
                      index
                    )
                  }
                  formType={form}
                  key={index}
                  isEditable="notShowing"
                />
              );
          })}
        </>
      ) : (
        ''
      )}
      {saveAs ? (
        <CreateAs
          from={createAsForm}
          data={createAsData}
          deleteHandler={handleCheckbox}
          close={() => setSaveAs(false)}
        />
      ) : (
        ''
      )}
    </div>
  );
}

export default NodeSearchTable;
