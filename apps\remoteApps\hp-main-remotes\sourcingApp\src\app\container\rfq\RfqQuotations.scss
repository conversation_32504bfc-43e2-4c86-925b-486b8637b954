@import '@hp/styles/variables.scss';
.quotation-main {
  display: flex;
  width: 100%;
  margin-top: '15px';
  background: white;

  .quotation-left-panel {
    width: 100%;
    flex-direction: column;
    max-width: 50%;
    padding-right: 24px;
    margin-top: 0px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    margin-right: 16px;
  }

  .quotation-right-panel {
    width: 100%;
    max-width: 50%;
    padding-left: 0px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    overflow: hidden;

    .ant-tabs {
      flex-grow: 1; /* Allow tabs to grow to fill the container */
      height: 100%; /* Ensure tabs take full height of the parent */
    }

    .ant-tabs-content {
      height: 100%; /* Make sure tab content is full height */
      background: #ffffff;
    }

    .ant-tabs-tabpane {
      height: 100%; /* Ensure each tab content is full height */
      padding: 16px;
    }
    .ant-tabs-nav {
      display: none !important;
    }
  }

  .rfq-table-body {
    width: 100%;
    display: flex;
  }

  .rfq-table-format {
    width: 100%;
    display: flex;
    padding: 4px;
    background-color: rgb(246 246 246 / 65%);
  }

  .primary-secondary-table-wrap {
    width: 100%;
  }

  .rfq-table-body-format {
    width: 100%;
    padding: 16px;
    background: #ffffff;

    .single {
      display: flex;
      align-items: center;
      height: 56px;
      background-color: #ffffff;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      margin-bottom: 8px;
      width: 100%;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        border-color: #d0d0d0;
      }

      .td {
        flex: 3;
        padding: 8px 12px;
        display: flex;
        align-items: center;

        > div {
          width: 100%;
        }

        &.this-is-empty {
          display: none;
        }

        .label {
          display: none;
        }

        .m20 {
          margin-bottom: 0;
        }

        .icon-calendar {
          &::before {
            display: none;
          }
        }

        p {
          font-size: 13px;
          line-height: 1.4;
          color: #262626;
          padding: 0;
          margin: 0;
          font-weight: 400;
        }

        input {
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          padding: 6px 8px;
          font-size: 13px;
          transition: border-color 0.2s ease;

          &:focus {
            border-color: #8c8c8c;
            box-shadow: 0 0 0 2px rgba(140, 140, 140, 0.15);
            outline: none;
          }
        }
      }
      .td.invoice {
        align-items: center;
        padding-top: 12px;

        input {
          height: 30px;
          font-size: 13px;
        }
      }

      .td.label {
        flex: 2;
        display: flex;
        padding: 8px 12px;
        align-items: center;
        background: #f8f9fa;
        border-right: 1px solid #e0e0e0;
        margin-right: 1px;

        i {
          display: block;
          font-size: 15px;
          margin-right: 10px;

          &.icon-not-checked {
            color: #ff4d4f;
          }

          &.icon-check-alert {
            color: #faad14;
            border-radius: 50%;
            background-color: #fff7e6;
            padding: 2px;
          }

          &.icon-check-success.grey .path1:before {
            content: '\eba0';
            color: #52c41a;
          }
        }

        p {
          padding: 0;
          margin: 0;
          font-size: 13px;
          font-weight: 500;
          color: #595959;
        }
      }

      &:nth-child(2n-0) {
        background-color: #fff;
      }
    }
  }

  .rfq-data-table-wrap {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 100%;
    align-items: stretch;

    .rfq-item {
      margin-top: 10px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      .searchbar {
        .label {
          display: none;
        }
      }

      .mb20 {
        align-self: stretch;
        flex-grow: 1;
        background: white;
      }
    }
  }

  .rfq-item {
    margin-bottom: 20px;
    .searchbar {
      width: 100%;
      max-width: 320px;
      position: relative;
      border: 1px solid #dcdcdc;
      border-radius: 20px;

      input[type='text'] {
        width: 100%;
        height: 40px;
        border: none;
        font-size: 12px;
        font-weight: 700;
        line-height: 20px;
        border-radius: 40px;
        background-color: #fff;
        padding: 0px 64px 0px 24px;
      }

      i {
        top: 0px;
        right: 0px;
        width: 40px;
        height: 40px;
        display: block;
        font-size: 16px;
        color: #4f4f4f;
        line-height: 40px;
        position: absolute;
        text-align: center;
        border-radius: 40px;
        pointer-events: none;
        background: #dcdcdc;
      }
    }
    .styledDatatable .rdt_Table .rdt_TableHeadRow {
      min-height: 40px;
      background: rgba($primary_color_rgb, 0.7);

      .rdt_TableCol {
        color: #fff;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }

  .mandatory::after {
    content: '*';
    color: #c74646;
    margin-left: 4px;
  }
  .td.invoice.readonly-mode input {
    border: none;
    background: transparent;
    box-shadow: none;
    pointer-events: none; /* Disable interaction */
  }
  .td.invoice.readonly-mode select {
    border: none;
    background: transparent;
    box-shadow: none;
    pointer-events: none; /* Disable interaction */
  }
  .td.invoice.readonly-mode .ant-picker {
    border: none !important;
    background: transparent !important;
    box-shadow: none;
    pointer-events: none; /* Disable interaction */
  }
  .td.invoice.readonly-mode .custom-select {
    background: transparent !important;
    box-shadow: none;
    pointer-events: none; /* Disable interaction */
  }
  // .td.invoice .ant-datepicker {
  //   margin-top: 20px !important;
  // }
  // .td.invoice.readonly-mode .ant-datepicker {
  //   margin-top: 20px !important;
  // }
}
.rfq-reference-title {
  font-size: 16px;
  margin-top: 32px;
  margin-bottom: 8px;
}
.rfq-table-body-format {
  .col {
    .single {
      &:first-child {
        border-top: 0.5px solid #e5e5e5;
      }

      &:last-child {
        border-bottom: 0.5px solid #e5e5e5;
      }
    }
  }
}
.title-text {
  font-family: inherit;
  font-size: 12px;
  color: #333;
  line-height: 1.5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 15px;
}
.align-input-right input {
  text-align: right;
}
// [data-label='Quote Subtotal'] input,
// [data-label='Quote Total'] input,
// [data-label='Quote Freight Cost'] input,
// [data-label='Late Fee'] input,
// [data-label='Cancellation Charge'] input,
// [data-label='Credit Card Fee'] input,
// [data-label='Service Charge'] input,
// [data-label='Quote Additional Fees'] input,
// [data-label='Other Fee'] input {
//   text-align: right;
// }

// Enhanced Modal Styling
.quotation-main {
  // Add a subtle header for the left panel
  .quotation-left-panel::before {
    content: 'Quotation Details';
    display: block;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e8e8e8;
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    margin: -1px -1px 16px -1px;
    border-radius: 6px 6px 0 0;
  }

  // Improve the table header visibility
  .rfq-table-header {
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  // Better spacing for the reference table
  .reference-table {
    .single:first-child {
      border-radius: 6px 6px 0 0;
    }

    .single:last-child {
      border-radius: 0 0 6px 6px;
      margin-bottom: 0;
    }
  }

  // Improve button styling in the modal
  .ant-btn {
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.outline {
      background: #ffffff;
      border: 1px solid #d9d9d9;
      color: #595959;

      &:hover {
        border-color: #8c8c8c;
        color: #262626;
      }
    }

    &.default {
      background: #262626;
      border-color: #262626;
      color: #ffffff;

      &:hover {
        background: #404040;
        border-color: #404040;
      }
    }
  }

  // Responsive design for smaller screens
  @media (max-width: 1200px) {
    flex-direction: column;

    .quotation-left-panel,
    .quotation-right-panel {
      max-width: 100%;
      width: 100%;
      margin-right: 0;
      margin-bottom: 16px;
    }

    .quotation-left-panel {
      padding-right: 0;
    }

    .quotation-right-panel {
      padding-left: 0;
    }
  }

  @media (max-width: 768px) {
    .rfq-table-body-format {
      padding: 12px;

      .single {
        height: auto;
        min-height: 48px;
        flex-direction: column;
        align-items: stretch;

        .td {
          padding: 6px 8px;

          &.label {
            background: #f0f0f0;
            border-right: none;
            border-bottom: 1px solid #e0e0e0;
            margin-right: 0;
            margin-bottom: 1px;
          }
        }
      }
    }
  }
}

// RfqQuotations.scss - Add these styles

.rfq-line-item {
  .styledDatatable {
    .input-text {
      input {
        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus,
        &:-webkit-autofill:active {
          -webkit-box-shadow: 0 0 0 30px white inset !important;
          box-shadow: 0 0 0 30px white inset !important;
          -webkit-text-fill-color: #333 !important;
          background-color: white !important;
          background-clip: content-box !important;
        }
      }
    }

    .custom-select-wrap {
      select {
        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus,
        &:-webkit-autofill:active {
          -webkit-box-shadow: 0 0 0 30px white inset !important;
          box-shadow: 0 0 0 30px white inset !important;
          -webkit-text-fill-color: #333 !important;
          background-color: white !important;
        }
      }
    }
  }
}
