.sr-cm-row-style {
  font-weight: 400;
  text-align: left;
  margin-right: 1px;
  font-size: 12px;
}
.sr-cm-row-style:hover {
  color: black;
  background-color: #83a5cf;
  cursor: pointer;
}

.sr-cm-error-row {
  color: #d37575;
  font-weight: 800;
  text-align: left;
  margin-right: 1px;
  font-size: 12px;
}
.sr-cm-warn-row:hover {
  color: black;
  background-color: #83a5cf;
  cursor: pointer;
}
.sr-cm-warn-row {
  background-color: #f7dede;
  color: #000000;
  font-weight: 400;
  text-align: left;
  margin-right: 1px;
  font-size: 12px;
}
.sr-cm-centered {
  position: sticky; /* or absolute */
  top: 50%;
  left: 50%;
}

.sr-cm-padd {
  padding: 12px;
}

.sr-cm-cellExpandClassname {
  /* needed on chrome */
  float: left;
  float: inline-end;
  display: table;
  block-size: 100%;
  > span {
    display: table-cell;
    vertical-align: middle;
    cursor: pointer;
  }
}

.sr-cm-box {
  display: flex;
  justify-content: center;
  align-items: center;
}

.sr-cm-item:first-child {
  margin-left: auto;
}

.sr-cm-item:last-child {
  margin-left: auto;
}

.report-border {
  border: 1px solid #cfd0d1;
}
