/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PoMatrix } from '../../invoices/invoice-common/PoMatrix';
import { GRNViewModel } from '../../invoices/invoice-common/GRNViewModel';
import { POMatchingComponent } from '../../invoices/invoice-sub/POMatchingComponent';
import Modal from 'react-modal';
import cloneDeep from 'lodash.clonedeep';
import { AP_USER } from '@hp/constants';
import { useAppRouterDom } from '@hp/utils';
import { Button, TraceEvents } from '@hp/components';
import {
  getInvDetailsForStatus,
  getWaitGrn,
  traceEvent,
  getGrnView,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';const PoSettledWaitinv = (props) => {
  const { domParameters, navigate } = useAppRouterDom();
  const mainMenu = domParameters?.menuData || '';
  const subMenu = domParameters?.submenu || '';
  const PO_ID = domParameters?.poId || '';
  const table_name = domParameters?.parameters || '';
  let user = globalutils.getDataFromStorage('all');
  const userId = user?.userId;
  const dispatch = useDispatch();
  const {
    invdetails,
    accpayBreadCrumb,
    accPaytraceEvents,

    viewGrnitems,
  } = useSelector((store) => store.accpay);
  const { innerMenuName } = useSelector((store) => store.menu);
  const onCloseClickFunc = () => {
    navigate(-1);
  };
  const [mainListArray, setMainListArray] = useState([]);
  const [subArrayMap, setSubArrayMap] = useState([]);
  const [getTraceEvents, setTraceEvents] = React.useState([]);
  const [invId, setInvId] = useState();
  const [clientDateFormat, setClientDateFormat] = useState('dd-MM-yyyy');
  const [popUpData, setPopUpData] = useState([]);
  const [getPoDetailsDto, setPoDetailsDto] = React.useState([]);
  const [showApplyAll, setShowApplyAll] = useState(false);
  const [itemListHead, setItemListHead] = useState([]);
  const [itemListRow, setItemListRow] = useState([]);
  const [popup, setPopup] = useState(false);
  const [flag, setFlag] = useState(false);

  let tableName = [];
  if (table_name.indexOf(',') > -1) {
    tableName = table_name.split(',');
  }

  useEffect(() => {
    if (
      invdetails !== undefined &&
      invdetails?.value?.length > 0 &&
      invdetails !== 'cleared'
    ) {
      setSubArrayMap([]);
      getMainInvoiceList(invdetails.value);
      setInvId(invdetails.value[0].invDetailsDto.inv_id);
    }
  }, [invdetails]);

  useEffect(() => {
    if (accPaytraceEvents) {
      setTraceEvents(accPaytraceEvents.value);
    }
  }, [accPaytraceEvents]);

  function getMainInvoiceList(invdetails) {
    let mainArray = [];
    invdetails.map((element, key) => {
      mainArray.push(element.invDetailsDto);
      setSubArrayMap((oldArray) => [
        ...oldArray,
        {
          invId: element.invDetailsDto.inv_id,
          itemData: element.invItemsBean,
        },
      ]);
      setMainListArray(mainArray);
      return mainArray;
    });
  }
  const customStyles = {
    content: {
      left: 230 + 'px',
      right: 230 + 'px',
    },
  };

  const viewPopupClose = () => {
    setFlag(false);
    setPopUpData([]);
  };
  useEffect(() => {
    if (viewGrnitems !== undefined) {
      let tempArray = cloneDeep(viewGrnitems?.value);

      tempArray?.map((entry) => {
        setPopUpData(tempArray);
        setPoDetailsDto(entry.filter((data) => data.type !== 'DataTable'));

        var itemList = entry.filter((data) => data.type === 'DataTable');
        var itemListHeader = [];
        var applyAll = false;
        itemList && itemList.length
          ? itemList[0].formSubDetailsInternalDTOList
            ? itemList[0].formSubDetailsInternalDTOList.map((e) => {
                itemListHeader.push({
                  name: e.displayName,
                  selector: e.selector,
                  sortable: true,
                  width: e.displayWidth,
                });
              })
            : null
          : '';

        applyAll = itemList[0].value.some(
          (el) =>
            el.poExpandableItemsSubDto &&
            el.poExpandableItemsSubDto.poItemsSubDtoList &&
            el.poExpandableItemsSubDto.poItemsSubDtoList.length
        );

        setShowApplyAll(!applyAll);
        setItemListHead(itemListHeader);
        itemList ? setItemListRow(itemList[0].value) : null;
      });
    }
    return () => {
      setPoDetailsDto();
      setItemListRow([]);
    };
  }, [viewGrnitems]);

  useEffect(() => {
    let poId = PO_ID;
    dispatch(traceEvent({ tableName: 'PO', tableId: PO_ID }));
    setPopUpData([]);
    if (
      innerMenuName === 'Waiting for Invoice' ||
      innerMenuName === 'Settled'
    ) {
      dispatch(getInvDetailsForStatus(PO_ID));
      setPopUpData([]);
    } else if (innerMenuName === 'Waiting for GRN') {
      dispatch(getWaitGrn(poId));
    }
  }, [PO_ID, dispatch, innerMenuName]);
  const [view] = React.useState('view-1');

  const waitingInvoiceClose = () => {
    onCloseClickFunc();
  };
  const waitingGRNView = () => {
    dispatch(getGrnView(PO_ID));
    setFlag(true);
    //dispatch(accPayAction.getGrnView(30494));
  };

  const functionsName = {
    waitingInvoiceClose,
    waitingGRNView,
  };
  return (
    <>
      <div className="invoice-compare mb20">
        <div className={'viewHolder ' + view}>
          <POMatchingComponent
            accpayBreadCrumb={accpayBreadCrumb}
            className="boxed unbox"
            poId={PO_ID}
            tableName={tableName}
            invId={invId}
            disableTableActions={true}
            hideTable={false}
          />

          {flag ? (
            <GRNViewModel
              isModalopen={flag}
              popUpData={popUpData}
              invID={null}
              isModalClose={() => viewPopupClose()}
            />
          ) : (
            <PoMatrix
              isModalopen={!flag}
              poID={PO_ID}
              invID={null}
              combinedInvSupplier={true}
              hideTable={true}
            />
          )}
        </div>

        <div className="mb20"></div>
      </div>
      <div className="boxed mb40">
        <TraceEvents
          disabled={true}
          enableComments={true}
          data={getTraceEvents}
          invId={invId}
          userId={userId}
        />
      </div>

      {!flag ? (
        <div id="btn-bottom-white-bar" className="group fixed-button-bar">
          <Button
            lassName="group fixed-button-bar"
            className="secondary-error mr20 fr button mb20"
            onClick={() => waitingInvoiceClose()}
          >
            Cancel
          </Button>
          <Button
            className="default mr20 fr button mb20"
            onClick={() => waitingGRNView()}
          >
            View GRN
          </Button>
        </div>
      ) : (
        <div id="btn-bottom-white-bar" className="group fixed-button-bar">
          <Button
            className="secondary-error mr20 fr button mb20"
            onClick={() => waitingInvoiceClose()}
          >
            Cancel
          </Button>
          <Button
            className="default mr20 fr button mb20"
            onClick={() => viewPopupClose()}
          >
            Hide GRN
          </Button>
        </div>
      )}

      <Modal
        className="Modal-for-status"
        overlayClassName="ModalOverlay"
        style={customStyles}
        ariaHideApp={false}
        isOpen={popup}
      >
        <h2 className="page-sub-title mb20 mt20">Preview Changes</h2>

        <div
          onClick={() => setPopup(!popup)}
          className="modal-close icon-close mb20"
        ></div>
        <div className={'user-card alt-card mb8 '} key={'x'}>
          <div className="user-wrap">
            <div className="alt-card-items">
              {popUpData.map((element, index) => {
                return (
                  <div
                    key={'alt-' + index}
                    className="alt-single-item"
                    style={{ flexShrink: 1 + 'em' }}
                  >
                    <div className="alt-label">
                      <div>{element.displayName}</div>
                    </div>
                    <div className="alt-text">
                      <div>{element.value}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div className="mb20" />
      </Modal>
    </>
  );
};

export { PoSettledWaitinv };
