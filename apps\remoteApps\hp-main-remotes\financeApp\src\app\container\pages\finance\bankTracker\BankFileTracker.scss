@import '@hp/styles/variables.scss';
.bank-file-tracker {
  .file-card {
    .bank-title {
      display: flex;
      gap: 8px;
    }
    .round {
      width: 25px;
      height: 25px;
      border-radius: 50%;
      background-color: #4c6bb0;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px;
      color: white;
    }
    .date-head {
      font-size: 14px;
    }
  }

  .bank-custom-collapse {
    margin-left: 16px;

    .ant-collapse-header {
      padding: 8px 12px;
    }
    .status-overview {
      display: flex;
      gap: 8px;

      * {
        font-size: 12px;
        border-radius: 20px;
        padding: 2px 8px;
        font-weight: 600;
        &::first-letter {
          text-transform: uppercase;
        }
      }
    }
    .status-count {
      position: absolute;
      background-color: #eb3838;
      right: 2px;
      top: -10px;
      padding: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 8px;
      height: 15px;
      width: 15px;
      border-radius: 50%;
    }
    .status-ok {
      background-color: #e6f4ea;
      color: #1b5e20;
    }
    .status-warning {
      background-color: #fff8e1;
      color: #f57f17;
    }
    .status-error {
      background-color: #fdecea;
      color: #b71c1c;
    }

    .session-element {
      display: flex;
      align-items: center;
      font-size: 12px;
    }
    .session-element .status-overview {
      flex: 1;
      justify-content: end;
      .status-ok {
        border: 1px solid #1b5e20;
      }
      .status-waring {
        border: 1px solid #f57f17;
      }
      .status-error {
        border: 1px solid #b71c1c;
      }
    }

    .element-details {
      flex: 3;
    }
    .element-details .field {
      display: flex;
    }
    .element-details .label {
      font-weight: 600;
      display: inline-block;
      width: 22%;
    }
    .element-details .msg {
      padding: 4px;
      background-color: rgb(238, 237, 237);
      width: 100%;
      border-radius: 4px;
      font-size: 11px;
    }
  }
}
