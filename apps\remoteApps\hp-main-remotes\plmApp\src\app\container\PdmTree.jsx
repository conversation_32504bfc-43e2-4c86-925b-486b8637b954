/* eslint-disable react-hooks/exhaustive-deps */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Treeview } from '@hp/components';
import { ItemFileUpload, ItemSetup, ItemSource, ItemView } from './item';
import { BuildList, LaunchProduct } from './buildBuy';
import { treeView, getPDMItemTree } from '@hp/mainstore';
import { useLocation } from 'react-router-dom';

function PdmTree() {
  const location = useLocation();
  const parameters = location.pathname.split('/').slice(-1)[0];

  const dispatch = useDispatch();
  const [action, setAction] = useState('');
  const [node, setNode] = useState('');

  const { pdmItemTreeList } = useSelector((store) => store.pdm);

  const { innerMenuName } = useSelector((store) => store.menu);

  useEffect(() => {
    dispatch(getPDMItemTree());
  }, []);

  useEffect(() => {
    if (
      pdmItemTreeList &&
      pdmItemTreeList.value &&
      pdmItemTreeList.value.length
    ) {
      dispatch(treeView(pdmItemTreeList.value));
    }
  }, [pdmItemTreeList]);

  const detailScreen = (screen) => {
    if (screen === 'itemsetup') {
      return <ItemSetup Action={action} Node={node} parameter={screen} />;
    } else if (screen === 'fileupload') {
      return <ItemFileUpload Node={node} parameter={screen} />;
    } else if (screen === 'itemview') {
      return <ItemView Node={node} parameter={screen} />;
    } else if (screen === 'buildlist') {
      return <BuildList Node={node} parameter={screen} />;
    } else if (screen === 'launchproduct') {
      return <LaunchProduct Node={node} parameter={screen} />;
    } else if (screen === 'itemsource') {
      return <ItemSource itemId={node.id} parameter={screen} />;
    }
  };
  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      <h1 className="page-title">{innerMenuName || ''}</h1>
      <div
        className="two-col-layout"
        style={{
          alignItems: 'stretch',
        }}
      >
        <div
          className="card"
          style={{
            flex: 1,
            minHeight: '70vh',
            overflow: 'auto',
          }}
        >
          <Treeview
            form={(action, node) => {
              setAction(action);
              setNode(node);
            }}
            parameter={parameters}
            handleScrollToTop={handleScrollToTop}
          />
        </div>
        <div
          className="card"
          style={{
            flex: 2,
            minHeight: '70vh',
            // height: 'max-content',
            // maxHeight: '140vh',
            overflow: 'auto',
          }}
        >
          {node ? detailScreen(parameters) : ''}
        </div>
      </div>
    </>
  );
}

export default PdmTree;
