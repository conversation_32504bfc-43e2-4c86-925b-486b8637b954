{"name": "financeApp", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/remoteApps/hp-main-remotes/financeApp/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "dist/apps/remoteApps/hp-main-remotes/financeApp", "index": "apps/remoteApps/hp-main-remotes/financeApp/src/main.jsx", "baseHref": "/", "main": "apps/remoteApps/hp-main-remotes/financeApp/src/main.jsx", "tsConfig": "apps/remoteApps/hp-main-remotes/financeApp/tsconfig.app.json", "rspackConfig": "apps/remoteApps/hp-main-remotes/financeApp/rspack.config.js", "assets": ["apps/remoteApps/hp-main-remotes/financeApp/src/favicon.ico", "apps/remoteApps/hp-main-remotes/financeApp/src/assets"], "styles": ["libs/hp-styles/src/index.scss"]}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "optimization": true, "sourceMap": false, "rspackConfig": "apps/remoteApps/hp-main-remotes/financeApp/rspack.config.prod.js"}}}, "serve": {"executor": "@nx/rspack:module-federation-dev-server", "options": {"buildTarget": "financeApp:build:development", "port": 4201}, "configurations": {"development": {}, "production": {"buildTarget": "financeApp:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/rspack:module-federation-static-server", "defaultConfiguration": "production", "options": {"serveTarget": "financeApp:serve"}, "configurations": {"development": {"serveTarget": "financeApp:serve:development"}, "production": {"serveTarget": "financeApp:serve:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/remoteApps/hp-main-remotes/financeApp/jest.config.js"}}}}