/* eslint-disable react-hooks/exhaustive-deps */

import { Button, globalutils } from '@hp/components';
import { getItemDeletedNodes, showDialog } from '@hp/mainstore';
import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useState } from 'react';
import { useConfirm } from '@hp/components';
import DataTable from 'react-data-table-component';
import { Ellipsis } from 'react-awesome-spinners';
import SubDataTable from './SubDataTable';
import { pdmConstants, confirmDeletion } from '@hp/mainstore';

function PdmDeletedNodes() {
  const [tableRow, setTableRow] = useState([]);
  const [subList, setsubList] = useState([]);

  const { confirm } = useConfirm();

  const dispatch = useDispatch();

  const { allPdmDeletedNodes, nodePermanentDeleteRes } = useSelector(
    (store) => store.pdm
  );
  const { innerMenuName } = useSelector((store) => store.menu);

  useEffect(() => {
    dispatch(getItemDeletedNodes());
  }, []);

  useEffect(() => {
    if (nodePermanentDeleteRes && nodePermanentDeleteRes.value) {
      funcToSetResponseMessage('success', nodePermanentDeleteRes.value);
      setTableRow([]);
      dispatch(getItemDeletedNodes());
    }
    return () => {
      dispatch({
        type: pdmConstants.DELETE_NODES_PERMANENTLY_SUCCESS,
        payload: null,
      });
    };
  }, [nodePermanentDeleteRes]);

  useEffect(() => {
    if (
      allPdmDeletedNodes &&
      allPdmDeletedNodes.value &&
      allPdmDeletedNodes.value.length
    ) {
      setTableRow(allPdmDeletedNodes.value);
    }
  }, [allPdmDeletedNodes]);

  const columns = [
    { name: '#', selector: 'serialNo', width: '10%' },
    { name: 'Description', selector: 'itemName' },
    { name: 'Part #', selector: 'partNumber' },
    { name: 'User', selector: 'userName' },
    { name: 'Time Stamp', selector: 'eventTime', width: '12%' },
  ];
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;

    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  const toggle = (row) => {
    if (
      row.pdmItemDeletedEventLogSubDtoList &&
      row.pdmItemDeletedEventLogSubDtoList.length
    ) {
      setsubList(row.pdmItemDeletedEventLogSubDtoList);
    } else {
      setsubList([]);
    }
  };

  const deleteAllHandler = async () => {
    const isConfirmed = await confirm(
      'All nodes will permanently get deleted, continue?'
    );
    if (isConfirmed) {
      dispatch(confirmDeletion());
    }
  };

  // const DataTableEventHandler = (e, index) => {
  //   const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
  //     e.serialNo,
  //     'serialNo'
  //   );
  //   setConditionalStyles(styleAttribute);
  // };

  return (
    <div>
      <h1 className="page-title">{innerMenuName || ''}</h1>
      {tableRow.length ? (
        <div>
          <div className="mb24 styledDatatable">
            <DataTable
              highlightOnHover={true}
              noHeader={true}
              striped={true}
              dense={false}
              columns={columns}
              customStyles={{
                rows: {
                  style: {
                    '&:hover': {
                      cursor: 'pointer',
                    },
                  },
                },
              }}
              progressComponent={
                <Ellipsis color={globalutils.spinnerColor()} />
              }
              pagination={true}
              paginationDefaultPage={1}
              defaultSortAsc={true}
              defaultSortField="orderId"
              paginationResetDefaultPage={true}
              paginationPerPage={10}
              data={tableRow}
              // expandableRowDisabled={(row) =>
              //   !row.pdmItemDeletedEventLogSubDtoList.length
              // }
              expandableRowExpanded={(row) => row.expanded}
              expandableRows={true}
              onRowExpandToggled={(toggledState, row) => toggle(row)}
              expandableRowsComponent={<SubDataTable subData={subList} />}
            />
          </div>

          <Button
            className="small error button mr20"
            onClick={deleteAllHandler}
          >
            Delete All
          </Button>
        </div>
      ) : (
        ''
      )}
    </div>
  );
}

export default PdmDeletedNodes;
