import { TextInput } from '@hp/components';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { showDialog, updateItemSource } from '@hp/mainstore';

const ItemSourceEditForm = ({ itemSupplier, onUpdate, onCancel }) => {
  const { itemSourceEditRes } = useSelector((store) => store.pdm);

  const dispatch = useDispatch();
  const itemSourceId = itemSupplier?.itemSourceId;
  // const itemId = itemSupplier?.itemId;
  const [showResponse, setShowResponse] = useState(false);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = false;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  useEffect(() => {
    if (showResponse && itemSourceEditRes?.value) {
      funcToSetResponseMessage('success', itemSourceEditRes.value);
      setShowResponse(false);
    }
  }, [itemSourceEditRes, showResponse]);

  const fieldsToShow = [
    'supplieName',
    'supplierPartNum',
    'email',
    'mobile',
    'leadTime',
    'discount',
    'cost',
    'count',
    'buildTime',
  ];

  const [completeData, setCompleteData] = useState({});
  const [displayedData, setDisplayedData] = useState({});

  useEffect(() => {
    if (itemSupplier) {
      setCompleteData({ ...itemSupplier });

      const filteredData = {};
      fieldsToShow.forEach((field) => {
        filteredData[field] = itemSupplier[field] ?? '';
      });
      setDisplayedData(filteredData);
    }
  }, [itemSupplier]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setDisplayedData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = () => {
    const hasChanges =
      displayedData.leadTime !== completeData.leadTime ||
      displayedData.discount !== completeData.discount ||
      displayedData.cost !== completeData.cost ||
      displayedData.buildTime !== completeData.buildTime;

    if (!hasChanges) {
      funcToSetResponseMessage('info', 'No changes detected');
      return;
    }

    const itemSupplierDto = {
      ...completeData,
      leadTime: displayedData.leadTime || 0,
      discount: displayedData.discount || 0,
      cost: displayedData.cost || 0,
      buildTime: displayedData.buildTime || 0,
    };

    setShowResponse(true);
    dispatch(updateItemSource([itemSourceId, itemSupplierDto]));
    onUpdate(false);
  };

  const fieldConfig = {
    supplieName: { label: 'Supplier Name', disabled: true },
    supplierPartNum: { label: 'Part Number', disabled: true },
    email: { label: 'Email', disabled: true },
    mobile: { label: 'Mobile', disabled: true },
    count: { label: 'Total PO Issued', disabled: true },
    leadTime: { label: 'Lead Time', disabled: false },
    discount: { label: 'Discount', disabled: false },
    cost: { label: 'Price', disabled: false },
    buildTime: { label: 'Build Time', disabled: false },
  };
  return (
    <div className="card">
      <div className="two-col-layout">
        {Object.entries(displayedData).map(([fieldName, fieldValue]) => (
          <div key={fieldName} className="mb16">
            <TextInput
              className="mb16"
              label={fieldConfig[fieldName]?.label || fieldName}
              name={fieldName}
              value={fieldValue?.toString() || ''}
              disabled={fieldConfig[fieldName]?.disabled}
              onChange={handleChange}
            />
          </div>
        ))}
      </div>
      <div className="">
        <Button
          className="small default mr20 mb20 fl button"
          onClick={handleSave}
        >
          Save
        </Button>
        <Button
          onClick={onCancel}
          className="small outline mr20 fl button sameRow"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default ItemSourceEditForm;
