/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-key */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-unused-vars */

/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 18-03-2022 15:40:13
 * @modify date 23-03-2022 15:40:20
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import { AP_USER } from '@hp/constants';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import Modal from 'react-modal';
import { settingsConstants } from '@hp/mainstore';
import { dataTableServiceProvider, Input, useConfirm } from '@hp/components';
import { Button } from '@hp/components';
import {
  addEntityFormDetails,
  deleteEntityFormDetails,
  editEntityFormDetails,
  getCityDetails,
  getEntityFormDetails,
  getStateDetails,
  showDialog,
  settingsResetStateField,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';

const Entities = () => {
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const { entityFormDetails, entityFormDetailsResponse, stateList, cityList } =
    useSelector((store) => store.settings);

  let user = globalutils.getDataFromStorage('all');
  const clientIdVal = user.clientId;
  const [clientId, setClientId] = useState(clientIdVal);
  const [formDetails, setFormDetails] = useState([]);
  const [disable, setDisable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showButtons, setShowButtons] = useState(false);
  const [selected, setSelected] = useState({});
  const [conditionalRowStyles, setConditionalRowStyles] = useState([]);
  const [notify, setnotify] = useState(null);
  const [countryId, setCountryId] = useState();
  const [tableData, setTableData] = useState([]);
  const [addresses, setAddresses] = useState([]);

  // Fetch Entity details form
  const getEntityFormDetailsFn = (clientId) => {
    dispatch(getEntityFormDetails(clientId));
  };

  useEffect(() => {
    if (entityFormDetails && entityFormDetails.value) {
      setFormDetails(entityFormDetails.value);
      setTableData(
        entityFormDetails.value.find((val) => val.uniqueKey === 'dataTable')
          .value
      );
    }
  }, [entityFormDetails]);

  // Clear Notification
  useEffect(() => {
    return () => {
      dispatch(
        settingsResetStateField({ fieldNames: 'entityFormDetailsResponse' })
      );
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  // Function to Show Alerts
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  useEffect(() => {
    if (stateList && stateList.value && stateList.value !== undefined) {
      let tempArray = formDetails && cloneDeep(formDetails);
      tempArray.map((entry) => {
        entry.uniqueKey === 'pageSubtitle' &&
          entry.formSubDetailsInternalDTOList.map((element) => {
            element.uniqueKey === 'stateSelect'
              ? (element.comboBoxOptions = stateList.value)
              : '';
          });
      });
      setFormDetails(tempArray);
    }
  }, [stateList]);

  useEffect(() => {
    if (cityList && cityList.value && cityList.value !== undefined) {
      let tempArray = formDetails && cloneDeep(formDetails);
      tempArray.map((entry) => {
        entry.uniqueKey === 'pageSubtitle' &&
          entry.formSubDetailsInternalDTOList.map((element) => {
            element.uniqueKey === 'citySelect'
              ? (element.comboBoxOptions = cityList.value)
              : '';
          });
      });
      setFormDetails(tempArray);
    }
  }, [cityList]);

  useEffect(() => {
    if (entityFormDetailsResponse?.value) {
      funcToSetResponseMessage(
        entityFormDetailsResponse.value.type,
        entityFormDetailsResponse.value.text
      );
    }
    getEntityFormDetailsFn(clientId);
  }, [entityFormDetailsResponse]);

  // Save Button Function
  const handleSave = () => {
    let tempArray = cloneDeep(formDetails);
    let entityNotExist = true;
    tempArray.map((entry) => {
      if (entry.uniqueKey === 'pageSubtitle') {
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === 'name' || element.uniqueKey === 'code') {
            if (element.value === null || element.value.trim() === '') {
              entityNotExist = false;
              element.uniqueKey === 'name'
                ? funcToSetResMessageInModal(
                    'error',
                    'Please Enter Entity Name'
                  )
                : funcToSetResMessageInModal(
                    'error',
                    'Please Enter Entity Code'
                  );
            } else {
              tempArray.map((item) => {
                if (item.uniqueKey === 'dataTable') {
                  item.value.map((val) => {
                    if (val.entityName === element.value) {
                      if (selected.entityName === element.value) {
                        return;
                      } else {
                        entityNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Entity Name already exists'
                        );
                      }
                    }
                    if (val.entityCode === element.value) {
                      if (selected.entityCode === element.value) {
                        return;
                      } else {
                        entityNotExist = false;
                        funcToSetResMessageInModal(
                          'error',
                          'Entity Code already exists'
                        );
                        return;
                      }
                    }
                  });
                }
              });
            }
          }

          if (element.uniqueKey === 'address1') {
            element.value = addresses.join(';');
          }
        });
      }
    });
    setFormDetails(tempArray);
    if (entityNotExist) {
      edit
        ? dispatch(
            editEntityFormDetails({
              formDetails: formDetails,
              entityId: selected.entityId,
            })
          )
        : dispatch(addEntityFormDetails(formDetails));
      setShowModal(false);
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setShowModal(false);
    setDisable(false);
    setAddresses([]);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          element.disableFlag = 'F';
          element.value = '';
        });
    });
    setFormDetails(tempArray);
  };

  // Edit Button Function
  const handleEdit = (obj) => {
    setShowModal(true);
    setShowButtons(true);
    setDisable(false);
    setEdit(true);
    setSelected(obj);
    const addressesFromEntity = obj.entityAddressBeanList
      ? obj.entityAddressBeanList.map((address) => address.entityAddress)
      : [];
    setAddresses(addressesFromEntity);

    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'F';
          if (entry.uniqueKey === 'name') entry.value = obj.entityName;
          else if (entry.uniqueKey === 'code') entry.value = obj.entityCode;
          else if (entry.uniqueKey === 'description')
            entry.value = obj.entityDesc;
          else if (entry.uniqueKey === 'address1') entry.value = obj.address1;
          else if (entry.uniqueKey === 'address2') entry.value = obj.address2;
          else if (entry.uniqueKey === 'countryId') {
            entry.value = obj.countryCode;
            setCountryId(obj.countryCode);
            obj.countryCode && dispatch(getStateDetails(obj.countryCode));
          } else if (entry.uniqueKey === 'stateSelect') {
            entry.value = obj.stateCode;
            countryId &&
              obj.stateCode &&
              dispatch(
                getCityDetails({ countryId: countryId, stateId: obj.stateCode })
              );
          } else if (entry.uniqueKey === 'citySelect')
            entry.value = obj.cityCode;
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.entityId,
      'entityId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  //Delete Button Function
  const handleDelete = (obj) => {
    obj.entityId
      ? handleConfirm(obj.entityId)
      : funcToSetResponseMessage('error', 'Please Select an Entity to Delete');
  };

  //Function to confirm delete
  const handleConfirm = async (entityId) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      dispatch(deleteEntityFormDetails(entityId));
      setDisable(false);
      setShowModal(false);
      setSelected({});
    }
  };

  // Lets to type in the form fields
  const handleOnChange = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = event.target.value;
          }
        });
    });
    setFormDetails(tempArray);
  };

  // Select a value in the select combo-box
  const handleSelect = (event, uniqueKey) => {
    getEntityFormDetailsFn(event.target.value);
    setClientId(event.target.value);
    setDisable(false);
    setConditionalRowStyles([]);
  };

  const handleCountrySelect = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = parseInt(event.target.value);
            dispatch(getStateDetails(event.target.value));
            setCountryId(event.target.value);
          } else if (element.uniqueKey === 'stateSelect') {
            element.value = '';
          } else if (element.uniqueKey === 'citySelect') {
            element.value = '';
          }
        });
    });
    setFormDetails(tempArray);
  };

  const handleStateSelect = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = parseInt(event.target.value);
            dispatch(
              getCityDetails({
                countryId: countryId,
                stateId: event.target.value,
              })
            );
          } else if (element.uniqueKey === 'citySelect') {
            element.value = '';
          }
        });
    });
    setFormDetails(tempArray);
  };

  const handleCitySelect = (event, uniqueKey) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((entry) => {
      entry.uniqueKey === 'pageSubtitle' &&
        entry.formSubDetailsInternalDTOList.map((element) => {
          if (element.uniqueKey === uniqueKey) {
            element.value = parseInt(event.target.value);
          }
        });
    });
    setFormDetails(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
    handleSelect,
    handleCountrySelect,
    handleStateSelect,
    handleCitySelect,
  };

  // Function to set values in the form fields
  const handleRowClick = (obj) => {
    setShowModal(true);
    setShowButtons(false);
    setSelected(obj);
    setDisable(true);
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      element.uniqueKey === 'pageSubtitle' &&
        element.formSubDetailsInternalDTOList.map((entry) => {
          entry.disableFlag = 'T';
          if (entry.uniqueKey === 'name') entry.value = obj.entityName;
          else if (entry.uniqueKey === 'code') entry.value = obj.entityCode;
          else if (entry.uniqueKey === 'description')
            entry.value = obj.entityDesc;
          else if (entry.uniqueKey === 'address1') entry.value = obj.address1;
          else if (entry.uniqueKey === 'address2') entry.value = obj.address2;
          else if (entry.uniqueKey === 'countryId') {
            entry.value = obj.countryCode;
            setCountryId(obj.countryCode);
            obj.countryCode && dispatch(getStateDetails(obj.countryCode));
          } else if (entry.uniqueKey === 'stateSelect') {
            entry.value = obj.stateCode;
            countryId &&
              obj.stateCode &&
              dispatch(
                getCityDetails({ countryId: countryId, stateId: obj.stateCode })
              );
          } else if (entry.uniqueKey === 'citySelect')
            entry.value = obj.cityCode;
        });
    });
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      obj.entityId,
      'entityId'
    );
    setConditionalRowStyles(styleAttribute);
    setFormDetails(tempArray);
  };

  const handleFilterData = (filterData) => {
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) =>
      element.uniqueKey === 'dataTable' ? (element.value = filterData) : ''
    );
    setFormDetails(tempArray);
  };

  const handleAddNewAddress = () => {
    // Find the value of address1
    const address1Value = formDetails
      .find((entry) => entry.uniqueKey === 'pageSubtitle')
      ?.formSubDetailsInternalDTOList.find(
        (element) => element.uniqueKey === 'address1'
      )?.value;

    if (address1Value) {
      // Append the new address to the addressList
      setAddresses((prevAddressList) => [...prevAddressList, address1Value]);

      // Clear the address1 field after adding it to the list
      let tempArray = cloneDeep(formDetails);
      tempArray.map((entry) => {
        if (entry.uniqueKey === 'pageSubtitle') {
          entry.formSubDetailsInternalDTOList.map((element) => {
            if (element.uniqueKey === 'address1') {
              element.value = ''; // Clear the address1 field
            }
          });
        }
      });
      setFormDetails(tempArray);
    } else {
      funcToSetResMessageInModal('error', 'Please Enter the Address');
    }
  };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey === 'pageSubtitle') {
            let len = element.formSubDetailsInternalDTOList.length;
            while (showModal) {
              return (
                <Modal
                  className="Modal"
                  overlayClassName="ModalOverlay"
                  ariaHideApp={false}
                  isOpen={showModal}
                >
                  <div
                    onClick={() => {
                      setEdit(false);
                      handleCancel();
                      setSelected({});
                      setConditionalRowStyles([]);
                    }}
                    className="modal-close icon-close"
                    style={{ fontSize: 20 + 'px' }}
                  ></div>
                  <div className="boxed" style={{ marginTop: 8 }}>
                    <Input key={index} formType={element} />
                    <div className="two-col-layout">
                      <div className="col">
                        {formControlsBinding(
                          element.formSubDetailsInternalDTOList.slice(0, 4)
                        )}
                      </div>
                      <div className="col">
                        {formControlsBinding(
                          element.formSubDetailsInternalDTOList.slice(4)
                        )}
                        <Button
                          onClick={() => handleAddNewAddress()}
                          className="small default mr20"
                        >
                          Add new address
                        </Button>
                      </div>
                    </div>

                    <div className="mb4">
                      {addresses.length > 0 && (
                        <div>
                          <h3 className="page-sub-title">Addresses</h3>
                          <div
                            className="address-list"
                            style={{
                              padding: '16px',
                              display: 'flex',
                              flexWrap: 'wrap', // Allow addresses to wrap to the next row
                              gap: '8px', // Space between address boxes
                            }}
                          >
                            {addresses.map((address, index) => (
                              <div
                                key={index}
                                style={{
                                  border: '1px solid #ddd',
                                  borderRadius: '4px',
                                  backgroundColor: '#f7f6d7',
                                  padding: '8px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '8px',
                                  flexShrink: 0, // Prevents the boxes from shrinking
                                  cursor: 'pointer', // Show pointer on hover
                                  position: 'relative', // For hover effect
                                  width: 'calc(50% - 8px)', // Set width for 3 addresses per row
                                  boxSizing: 'border-box', // Ensure padding is included in the width
                                }}
                                title={address} // Show full address on hover
                                onClick={() => {
                                  // Move the address to the address1 input field
                                  const tempArray = cloneDeep(formDetails);
                                  tempArray.map((entry) => {
                                    if (entry.uniqueKey === 'pageSubtitle') {
                                      entry.formSubDetailsInternalDTOList.map(
                                        (element) => {
                                          if (
                                            element.uniqueKey === 'address1'
                                          ) {
                                            element.value = address; // Set the address1 input field value
                                          }
                                        }
                                      );
                                    }
                                  });
                                  setFormDetails(tempArray);

                                  // Remove the address from the addresses state
                                  setAddresses((prevAddresses) =>
                                    prevAddresses.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <p
                                  style={{
                                    margin: 0,
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    flex: 1, // Allow the address text to take up remaining space
                                  }}
                                >
                                  {address}
                                </p>
                                <span
                                  className="icon icon-cancel-circle"
                                  style={{ cursor: 'pointer' }}
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent the parent div's onClick from firing
                                    // Remove the address from the addresses list
                                    setAddresses((prevAddresses) =>
                                      prevAddresses.filter(
                                        (_, i) => i !== index
                                      )
                                    );
                                  }}
                                ></span>
                                <div
                                  style={{
                                    position: 'absolute',
                                    top: '-20px',
                                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                    color: '#fff',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    opacity: 0, // Hidden by default
                                    transition: 'opacity 0.2s',
                                    pointerEvents: 'none', // Prevents hover from blocking clicks
                                  }}
                                >
                                  Edit
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {showButtons && (
                      <div>
                        <Button
                          onClick={() => handleSave()}
                          className="default mr20"
                        >
                          Save
                        </Button>
                        <Button
                          onClick={() => handleCancel()}
                          className="info mr20"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                  {notify != null ? (
                    <div
                      className={[
                        'notification-bar',
                        'type-' + notify.type,
                      ].join(' ')}
                      style={{
                        position: 'sticky !important',
                        width: 90 + '%',
                        bottom: 0 + 'px',
                      }}
                    >
                      <i
                        className="icon-close"
                        onClick={() => setnotify(null)}
                      ></i>
                      {notify.resMessage}
                    </div>
                  ) : (
                    ''
                  )}
                </Modal>
              );
            }
          } else if (element.uniqueKey === 'add_button') {
            return (
              <div style={{ float: 'right', marginBottom: 10 + 'px' }}>
                <Button
                  onClick={() => {
                    handleCancel();
                    setSelected({});
                    setConditionalRowStyles([]);
                    setEdit(false);
                    setShowModal(true);
                    setShowButtons(true);
                  }}
                  className="small mb8 outline add-button-custom flex-row vam"
                >
                  <i className="icon-add-button "> </i>Add
                </Button>
              </div>
            );
          } else if (element.uniqueKey === 'clientName' && clientIdVal === 1) {
            return (
              <div className="three-col-layout">
                {/* <div style={{ width: '200px', marginTop: '25px' }}>
                  <Input
                    formType={data.find((item) => item.uniqueKey === 'search')}
                    isEditable="notShowing"
                    tableListData={tableData}
                    getFilteredData={(filterData) =>
                      handleFilterData(filterData)
                    }
                  />
                </div> */}
                <Input
                  isEditable={'notShowing'}
                  key={index}
                  formType={element}
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey
                    );
                  }}
                />
              </div>
            );
          } else if (element.uniqueKey === 'search') {
            return;
          } else if (element.uniqueKey === 'dataTable') {
            let columns =
              element.formSubDetailsInternalDTOList &&
              element.formSubDetailsInternalDTOList.map((val) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() => handleEdit(row)}
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                            ></div>
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={(obj) => {
                  handleRowClick(obj);
                }}
                dataTableColumn={columns}
                conditionalRowStyles={conditionalRowStyles}
              />
            );
          } else {
            return (
              <Input
                key={index}
                disabledFlag={disable}
                formType={element}
                isEditable="notShowing"
                onChangeHandler={(element, event) => {
                  onChangeHandlingFunctions[element.onChangeFunction](
                    event,
                    element.uniqueKey
                  );
                }}
              />
            );
          }
        })
      : null;
  };

  return (
    <div className="">
      {formDetails && formDetails.length
        ? formControlsBinding(formDetails)
        : ''}
    </div>
  );
};
export { Entities };
