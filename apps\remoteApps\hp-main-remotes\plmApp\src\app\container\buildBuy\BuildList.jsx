/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import DataTable from 'react-data-table-component';
import {
  TextInput,
  ButtonCommon,
  useConfirm,
  ToggleInput,
  globalutils,
} from '@hp/components';
import {
  showDialog,
  getBuyOrBuildList,
  postBuildList,
  pdmConstants,
} from '@hp/mainstore';

function BuildList(props) {
  const [itemDetails, setitemDetails] = useState({});
  const [buildTable, setBuildTable] = useState([]);
  const [nodeId, setNodeId] = useState(null);
  const [getQnty, setQnty] = useState(null);
  const [prToggle, setPrToggle] = useState(false);
  const { confirm } = useConfirm();

  const Headings = [
    { name: 'Part #', selector: 'partNumber', width: '15%' },
    { name: 'Rev #', selector: 'curRevNum', width: '10%' },
    { name: 'Item Name', selector: 'itemName', width: '20%' },
    { name: 'Qty per', selector: 'qty', width: '10%' },
    { name: 'Originated By', selector: 'orginatedBy', width: '15%' },
    { name: 'Date', selector: 'creationDate', width: '10%' },
    { name: 'Qty Needed', selector: 'qtyNeeded', width: '15%' },
    { name: 'In Stock', selector: 'inStock', width: '10%' },
  ];

  const dispatch = useDispatch();

  const { pdmBuyOrBuildList, buildListSuccess } = useSelector(
    (store) => store.pdm
  );
  useEffect(() => {
    formHandeler(props.Action, props.Node);
  }, [props.Action, props.Node]);

  useEffect(() => {
    if (pdmBuyOrBuildList) {
      setBuildTable(
        pdmBuyOrBuildList &&
          pdmBuyOrBuildList.value &&
          pdmBuyOrBuildList.value.length
          ? pdmBuyOrBuildList.value
          : []
      );
    }
  }, [pdmBuyOrBuildList]);

  const conditionalRowStyles = [
    {
      when: (row) => row['itemsForPRFlag'] === true,
      style: {
        color: '#c74646 ',
      },
    },
  ];

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
      })
    );
  };

  useEffect(() => {
    if (buildListSuccess) {
      funcToSetResponseMessage('success', buildListSuccess.value);
    }
    return () => {
      dispatch({
        type: pdmConstants.POST_BUILD_LIST_SUCCESS,
        buildListSuccess: undefined,
      });
    };
  }, [buildListSuccess]);

  const formHandeler = (action, node) => {
    setPrToggle(false);
    setBuildTable([]);
    setQnty('');
    setNodeId(node.id);
    setitemDetails(node);
    dispatch({
      type: pdmConstants.GET_BUY_OR_BUILD_LIST_SUCCESS,
      payload: null,
    });
    if (node?.qty) {
      setQnty(node.qty);
      dispatch(
        getBuyOrBuildList({ itemId: node.id, quantity: node.qty, flag: false })
      );
    }
  };

  const handleChange = (event) => {
    setQnty(event.target.value);
  };

  const handleBuild = async () => {
    const isConfirmed = await confirm(' Add to buylist? ');
    if (isConfirmed) {
      const userId = globalutils.getDataFromStorage('userId');
      let body = {
        pdmItemTreeList: buildTable,
        itemId: nodeId,
        buildQnty: parseInt(getQnty),
        userId: userId,
      };
      dispatch(postBuildList(body));
    }
  };

  const functionsName = {
    handleBuild,
  };

  return (
    <>
      <div className="  three-col-layout mb20">
        <TextInput
          label={'Item # '}
          className={'mb20'}
          disabled={true}
          value={
            itemDetails && itemDetails.itemCode ? itemDetails.itemCode : ''
          }
        />
        <TextInput
          label={'Item Name'}
          className={'mb20'}
          disabled={true}
          value={
            itemDetails && itemDetails.itemName ? itemDetails.itemName : ''
          }
        />
        <TextInput
          label={'Quantity'}
          className={'mb20'}
          onChange={(e) => handleChange(e)}
          value={getQnty}
        />

        <button
          className="small default button"
          style={{ marginTop: '20px', width: '10px' }}
          onClick={() => {
            if (nodeId)
              dispatch(
                getBuyOrBuildList({
                  itemId: nodeId,
                  quantity: getQnty,
                  flag: prToggle,
                })
              );
          }}
        >
          Submit
        </button>
      </div>
      <ToggleInput
        className="flex-right mb20"
        label="Items For PR"
        onChange={() => {
          dispatch(
            getBuyOrBuildList({
              itemId: nodeId,
              quantity: getQnty,
              flag: !prToggle,
            })
          );
          setPrToggle(!prToggle);
        }}
        checked={prToggle}
      />
      <div className="mb20 styledDatatable ">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationTotalRows={buildTable.length}
          paginationPerPage={10}
          conditionalRowStyles={conditionalRowStyles}
          columns={Headings}
          data={buildTable}
        />
      </div>
      <ButtonCommon functionsName={functionsName} />
    </>
  );
}

export { BuildList };
