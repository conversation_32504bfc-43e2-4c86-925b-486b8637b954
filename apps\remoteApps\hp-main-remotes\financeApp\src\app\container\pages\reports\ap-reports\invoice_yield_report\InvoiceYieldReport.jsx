/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */

import React, { useState, useEffect } from 'react';
import { Button, CommonSpinner, globalutils } from '@hp/components';
import '@hp/styles/Dashboard.scss';
import { useDispatch, useSelector } from 'react-redux';
import { getAPReports, resetgetAPReports } from '@hp/mainstore';
import { ReportDataList } from '../components/ReportDataList';
import { ReportGraph } from '../components/ReportGraph';
import { useAppRouterDom } from '@hp/utils';
import DataTable from 'react-data-table-component';
import { Badge, DatePicker } from 'antd';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

const { RangePicker } = DatePicker;

const InvoiceYieldReport = () => {
  const { apReportDTO, loading } = useSelector((state) => state.accpay);
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();

  const urlParam = domParameters?.parameters || '';
  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user.userId);
  const clientId = user.clientId;

  const [reportData, setReportData] = useState();
  const [graphData, setGraphData] = useState();
  const [periodCovered, setPeriodCovered] = useState();
  const [columns, setColumns] = useState();
  const [rows, setRows] = useState();
  const [tableHeader, setTableHeader] = useState([]);

  let endDate = new Date();
  let startDate = new Date();
  startDate.setDate(endDate.getDate() - 30);

  // Function to format the date as "MM-DD-YYYY"
  const formatDateMMDDYYYY = (date) => {
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let year = date.getFullYear();
    return `${month}-${day}-${year}`;
  };

  let formattedEndDate = formatDateMMDDYYYY(endDate);
  let formattedStartDate = formatDateMMDDYYYY(startDate);

  const [dateChange, setDateChange] = useState({
    fromDate: formattedStartDate,
    toDate: formattedEndDate,
  });
  useEffect(() => {
    dispatch(
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'subscribe',
        fromDate: dateChange.fromDate,
        toDate: dateChange.toDate,
      })
    );
  }, []);
  useEffect(() => {
    return () => {
      dispatch(resetgetAPReports());
    };
  }, []);

  useEffect(() => {
    if (apReportDTO?.value) {
      const { listView, graphDetails, periodCovered } = apReportDTO.value;
      if (Array.isArray(graphDetails)) {
        const tableList = graphDetails.find(
          (item) => item.type === 'TableList'
        );
        if (tableList) {
          const { columns, values: rows } = tableList.value || {};
          let newColumns = columns?.map((col) => {
            if (col.selector === 'overDueStatus') {
              return {
                ...col,
                cell: (row) =>
                  row.dueFlag === 'RF' ? (
                    <div
                      style={{ color: 'red' }}
                      className="display-title custom-overflow"
                      title={row[col.selector]}
                    >
                      {row[col.selector]}
                    </div>
                  ) : (
                    <div
                      className="display-title custom-overflow"
                      title={row[col.selector]}
                    >
                      {row[col.selector]}
                    </div>
                  ),
              };
            }
            return col;
          });

          setColumns(newColumns || []);
          setRows(rows || []);
          setTableHeader(tableList.heading || '');
        }

        const chartData = graphDetails.filter(
          (item) => item.type !== 'TableList'
        );
        setGraphData(chartData);
      }

      setReportData(listView);
      setPeriodCovered(periodCovered);
    }
  }, [apReportDTO]);

  const onDateChange = (e) => {
    const [start, end] = e;
    const startDate = globalutils.formatDate(start.toDate(), 'mm-dd-yyyy');
    const endDate = globalutils.formatDate(end.toDate(), 'mm-dd-yyyy');
    setDateChange({
      fromDate: startDate.replaceAll('/', '-'),
      toDate: endDate.replaceAll('/', '-'),
    });
  };
  const searchHandler = () => {
    dispatch(
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'subscribe',
        fromDate: dateChange.fromDate,
        toDate: dateChange.toDate,
      })
    );
  };
  // useEffect(() => {
  //   if (apReportDTO) {
  //     setLoading();
  //   }
  // }, [apReportDTO]);
  return (
    <>
      <div className="page-sub-title">Invoice Yield Report</div>
      <CommonSpinner visible={loading} />{' '}
      {apReportDTO ? (
        <>
          {periodCovered ? (
            <Badge.Ribbon text={periodCovered} style={{ fontFamily: 'Roboto' }}>
              {' '}
            </Badge.Ribbon>
          ) : (
            ''
          )}
          <div className="header-nav card  mb20">
            <div className="three-col-layout mb20">
              <div className="mt20">
                <RangePicker
                  style={{ width: '100%' }}
                  format="MM-DD-YYYY"
                  allowClear={false}
                  value={
                    dateChange?.fromDate && dateChange?.toDate
                      ? [
                          dayjs(dateChange.fromDate, 'MM-DD-YYYY').isValid()
                            ? dayjs(dateChange.fromDate, 'MM-DD-YYYY')
                            : null,
                          dayjs(dateChange.toDate, 'MM-DD-YYYY').isValid()
                            ? dayjs(dateChange.toDate, 'MM-DD-YYYY')
                            : null,
                        ]
                      : null
                  }
                  onChange={onDateChange}
                />
              </div>
              <div>
                {' '}
                <Button
                  className=" mr20 mt20 small default "
                  style={{ width: '10%' }}
                  onClick={searchHandler}
                >
                  Search
                </Button>
              </div>
              <div></div>
            </div>
          </div>
          <div className="report-body-main">
            <div className="report-data-body card  mr20">
              <ReportDataList data={reportData} />
            </div>
            <div className="report-graph-body ">
              {/* <ReportGraph data={graphData} isLoading={() => setLoading()} /> */}
              <ReportGraph data={graphData || []} />
            </div>
          </div>
          <div className="card mt20">
            <div className="page-sub-title">{tableHeader}</div>
            <div className="styledDatatable mb20">
              <DataTable
                persistTableHead
                noHeader
                highlightOnHover
                striped
                columns={columns}
                data={rows}
                pagination
                paginationDefaultPage={1}
                paginationResetDefaultPage
              />
            </div>
          </div>
        </>
      ) : (
        ''
      )}
    </>
  );
};
export { InvoiceYieldReport };
