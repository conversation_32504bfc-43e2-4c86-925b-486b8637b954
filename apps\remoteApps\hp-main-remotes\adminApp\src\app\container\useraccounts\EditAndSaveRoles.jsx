/* eslint-disable no-useless-concat */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-eval */

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 01-06-2021 10:21:43
 * @modify date 2024-07-29 15:49:01
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import { Scrollbars } from 'react-custom-scrollbars';
import {
  showDialog,
  updateRolesSetting,
  getMenuRolesDetails,
  saveCreateNewRoles,
  deleteRolesById,
  getMenusListing,
  getClientComboBox,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import { AP_USER } from '@hp/constants';
import {
  resetgetMenusListing,
  resetgetMenuRolesDetails,
  resetupdateRolesSetting,
  resetdeleteRolesById,
  resetsaveCreateNewRoles,
  setButtonsForRole,
  resetsetButtonsForRole,
} from '@hp/mainstore';
import cloneDeep from 'lodash.clonedeep';
import { useAppRouterDom } from '@hp/utils';
import { useConfirm } from '@hp/components/common';
import {
  Button,
  CheckBoxInput,
  Roles,
  Select,
  TextAreaInput,
  TextInput,
} from '@hp/components/ui';
import { globalutils } from '@hp/components';

const EditAndSaveRoles = (props) => {
  const { domParameters, navigate, location } = useAppRouterDom();
  const module = domParameters?.module || '';
  const roleId = domParameters?.parameters || '';
  const roleOperation = location?.state ? location.state : '';
  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const {
    createmenudetails,
    menuroles,
    saveCreateNewRoleSuccess,
    deleteRoleResponse,
    updateSettingsResponse,
  } = useSelector((store) => store.admin);

  const { clientCombolist } = useSelector((store) => store.email);

  let user = globalutils.getDataFromStorage('all');
  const userId = user?.userId;
  //this state is to manage menuRoleDto to backend
  const [state, setState] = useState({
    clientId: '',
    description: '',
    roleId: '',
    roleMenuList: [],
    roleName: '',
  });
  //this state maintains buttons on the clicking particular menu
  const [buttonsToRole, setButtonsToRole] = useState([]);
  //this state maintains whthere its save or edit
  const [saveEditStatusState, setSaveEditStatusState] = useState('');
  const [clientComboState, setClientComboState] = useState([]);
  const [flag, setFlag] = useState(false);
  const [deleted, setDeleted] = useState(false);
  const [rowData, setRowData] = useState();

  useEffect(() => {
    getMenuRolesDetailsFn();
    return () => {
      dispatch(resetsetButtonsForRole());
      dispatch(resetgetMenusListing());
      dispatch(resetgetMenuRolesDetails());
      dispatch(resetupdateRolesSetting());
      dispatch(resetdeleteRolesById());
      dispatch(resetsaveCreateNewRoles());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
    };
  }, []);

  useEffect(() => {
    let localState = cloneDeep(state);
    if (
      menuroles &&
      menuroles.value &&
      menuroles.value.roleMenuList.length > 0
    ) {
      Object.entries(menuroles.value).map(([key, value]) => {
        roleOperation === 'createAs'
          ? key === 'roleMenuList'
            ? (localState[key] = value)
            : (localState[key] = '')
          : (localState[key] = value);
      });
      setState(localState);
    }
  }, [menuroles]);

  useEffect(() => {
    if (
      clientCombolist &&
      clientCombolist.value &&
      clientCombolist.value.length > 0
    ) {
      const clientOptions = clientCombolist.value.map((value) => {
        return {
          value: value.clientId,
          display: value.clientName,
        };
      });
      setClientComboState(clientOptions);
    }
  }, [clientCombolist]);

  useEffect(() => {
    if (
      deleteRoleResponse &&
      deleteRoleResponse.value &&
      deleteRoleResponse.value !== undefined
    ) {
      funcToSetResponseMessage(
        deleteRoleResponse.value.type,
        deleteRoleResponse.value.text
      );
      if (deleteRoleResponse.value.type === 'success') {
        setDeleted(true);
        dispatch(
          updateRolesSetting({ roleId: 0, module: module, userId: userId })
        );
      }
    }
  }, [deleteRoleResponse]);

  useEffect(() => {
    let localState = cloneDeep(state);
    if (
      createmenudetails &&
      createmenudetails.value &&
      createmenudetails.value.roleMenuList.length > 0
    ) {
      Object.entries(createmenudetails.value).map(([key, value]) => {
        localState[key] = value ? value : '';
      });
      setState(localState);
    }
  }, [createmenudetails]);

  useEffect(() => {
    if (
      saveCreateNewRoleSuccess &&
      saveCreateNewRoleSuccess.value &&
      saveCreateNewRoleSuccess.value !== undefined
    ) {
      if (saveCreateNewRoleSuccess.value.roleId) {
        setState({ ...state, roleId: saveCreateNewRoleSuccess.value.roleId });
        dispatch(
          getMenuRolesDetails({
            roleId: saveCreateNewRoleSuccess.value.roleId,
            module: module,
          })
        );
      }
      funcToSetResponseMessage(
        saveCreateNewRoleSuccess.value.type,
        saveCreateNewRoleSuccess.value.text
      );
    }
  }, [saveCreateNewRoleSuccess]);

  useEffect(() => {
    if (
      updateSettingsResponse &&
      updateSettingsResponse.value &&
      updateSettingsResponse.value !== undefined
    ) {
      funcToSetResponseMessage(
        updateSettingsResponse.value.type,
        updateSettingsResponse.value.text
      );
      if (deleted) {
        navigate(-1);
        return;
      }
      if (updateSettingsResponse.value.roleDto) {
        let localState = cloneDeep(state);
        Object.entries(updateSettingsResponse.value.roleDto).map(
          ([key, value]) => {
            localState[key] = value;
          }
        );
        setState(localState);
      } else navigate(-1);
    }
  }, [updateSettingsResponse]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type: type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const createRoles = (state) => {
    if (saveEditStatusState === 'edit') {
      dispatch(
        saveCreateNewRoles({
          state: state,
          type: saveEditStatusState,
          module: module,
          userId: userId,
        })
      );
    } else {
      if (state.clientId?.length > 0 && state.roleName?.length > 0) {
        dispatch(
          saveCreateNewRoles({
            state: state,
            type: saveEditStatusState,
            module: module,
            userId: userId,
          })
        );
      } else {
        funcToSetResponseMessage(
          'error',
          'Please fill all the required fields'
        );
      }
    }
  };

  const handleOnChange = (event) => {
    let menuRole = cloneDeep(state);
    menuRole[event.target.name] = event.target.value;
    setState(menuRole);
  };

  const handleButtonCheck = (btn) => {
    let fullAccessFlag =
      rowData && rowData.data && rowData.data.fullAccess === 'Y'
        ? true
        : rowData.data.readOnly === 'Y' || rowData.data.disabled === 'Y'
        ? false
        : false;

    return btn && btn.length > 0
      ? btn.map((data, idx) => {
          return (
            <div className="flex-row button-row-tr card-3 mb20" key={idx}>
              <div className="left-controls">
                <CheckBoxInput
                  className="td"
                  key={idx}
                  disabled={!fullAccessFlag}
                  checked={data.accessFlag === 'Y' ? true : false}
                  onChange={() => {
                    data.accessFlag === 'Y'
                      ? (data.accessFlag = 'N')
                      : (data.accessFlag = 'Y');
                    setFlag(!flag);
                  }}
                />
                <div className="td">
                  <Button className={data.type}>{data.label}</Button>
                </div>
              </div>
            </div>
          );
        })
      : null;
  };

  const handleOnRowClick = (rowClickedData) => {
    if (rowClickedData) {
      setRowData(rowClickedData);
      let buttonMenuData = cloneDeep(state);
      let indices = '';
      if (
        rowClickedData.parent !== undefined &&
        rowClickedData.index !== undefined
      ) {
        indices =
          'buttonMenuData.roleMenuList' +
          rowClickedData.parent +
          '[' +
          rowClickedData.index +
          ']';
      } else {
        indices = 'buttonMenuData.roleMenuList' + '[' + 0 + ']';
      }
      eval(indices).buttonDtoList
        ? setButtonsToRole(eval(indices).buttonDtoList)
        : setButtonsToRole([]);
      setState(buttonMenuData);
      rowClickedData.data &&
        rowClickedData.data.buttonDtoList &&
        handleButtonCheck(rowClickedData.data.buttonDtoList);
    }
  };

  const deleteRoles = async () => {
    let roleId = state.roleId;
    const isConfirmed = await confirm(
      'Are you sure you want to delete this role?'
    );
    if (isConfirmed) {
      dispatch(
        deleteRolesById({ roleId: roleId, module: module, userId: userId })
      );
    }
  };

  const updateSettings = () => {
    let roleId = deleted ? 0 : state?.roleId;
    dispatch(
      updateRolesSetting({ roleId: roleId, module: module, userId: userId })
    );
  };

  const getMenuRolesDetailsFn = () => {
    let menu = null;
    dispatch(setButtonsForRole(menu));
    if (roleId > 0) {
      dispatch(getMenuRolesDetails({ roleId: roleId, module: module }));

      setSaveEditStatusState(roleOperation);
    } else {
      dispatch(getMenusListing(module));
      setSaveEditStatusState(roleOperation);
    }
    dispatch(getClientComboBox());
    return () => {
      setButtonsToRole([]);
    };
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <>
      <div className="page-title">Roles Management</div>
      <div className="two-col-layout mb20">
        <div className="col">
          <Select
            label="Client"
            className="mb20"
            requiredField={true}
            value={state && state.clientId ? state.clientId : ''}
            name="clientId"
            options={clientComboState}
            onChange={handleOnChange}
          />
          <div className="mb20">
            <div className="access-control-table">
              <div className="thead">
                <div className="tr flex-row">
                  <div className="th menu" style={{ width: 250 + 'px' }}>
                    Menu
                  </div>
                  <div className="th">Disabled</div>
                  <div className="th">Read Only</div>
                  <div className="th">Full Access</div>
                </div>
              </div>
              <div className="tbody">
                <Scrollbars
                  // This will activate auto-height
                  autoHeight
                  autoHeightMin={100}
                  autoHeightMax={830}
                >
                  <Roles
                    data={state}
                    handleOnRowClick={(onRowClickData) =>
                      handleOnRowClick(onRowClickData)
                    }
                    accessPropsChangeHandler={(newState) =>
                      setState({ ...state, roleMenuList: [newState] })
                    }
                  />
                </Scrollbars>
              </div>
            </div>
          </div>
          <div id="btn-bottom-white-bar" className="group fixed-button-bar">
            <Button
              onClick={() => createRoles(state)}
              className="default mr20 fl button mb20"
            >
              Save
            </Button>
            {roleId > 1 && (
              <Button
                className="secondary-error mr20 fl button mb20"
                onClick={deleteRoles}
              >
                Delete Role
              </Button>
            )}
            <Button
              className="secondary-error mr20 fl button mb20"
              onClick={handleCancel}
            >
              Cancel
            </Button>

            <Button
              onClick={updateSettings}
              className="small outline mr20 fr button mb20"
            >
              Update Settings
            </Button>
          </div>
        </div>

        <div className="col">
          <div className="card">
            <TextInput
              label="Role Name"
              className="mb20"
              name="roleName"
              required={true}
              value={state && state.roleName ? state.roleName : ' '}
              onChange={handleOnChange}
            />

            <TextAreaInput
              label="Description"
              className="mb40"
              value={state && state.description ? state.description : ' '}
              name="description"
              onChange={handleOnChange}
            />
            <fieldset style={{ height: 800 + 'px' }}>
              <legend>Buttons:</legend>
              <div className="card-3 mb20">
                <div className="access-control-table">
                  <div className="thead ">
                    <div className="tr flex-row"></div>
                  </div>
                  <div className="tbody">
                    {buttonsToRole && buttonsToRole.length
                      ? handleButtonCheck(buttonsToRole)
                      : ''}
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
        </div>
      </div>
    </>
  );
};

export { EditAndSaveRoles };
