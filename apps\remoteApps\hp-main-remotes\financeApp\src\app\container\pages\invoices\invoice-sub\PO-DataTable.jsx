/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @nx/enforce-module-boundaries */
import React, { useState, useEffect } from 'react';
import DataTable from 'react-data-table-component';
import { SearchComponent, DataTableRowSub } from '@hp/components';
import { showDialog } from '@hp/mainstore';

import { dataTableServiceProvider } from '../../../../../../../../../../libs/hp-util/src/lib/dataTableProviderUtil';
import { useDispatch } from 'react-redux';

function PoDataTable(props) {
  const dispatch = useDispatch();
  const {
    fullWidth,

    disabledFlag,
    setValueOfDataTable,
    poData,
    invData,
    disableTableActions,
  } = props;
  const [poDataTableState, setPODataTable] = useState([]);
  const [poDataTableData, setpoDataTableData] = useState([]);
  const [TableListinglength, setTableListlength] = useState(0);

  useEffect(() => {
    if (poData && poData !== undefined && poData.length) {
      const dataTable = poData.filter((item) => item.type === 'DataTable');
      setPODataTable(dataTable);

      let dataTableRows =
        dataTable && dataTable.length
          ? dataTableServiceProvider.getDataTableRows(dataTable[0])
          : [];

      setpoDataTableData(dataTableRows);
    }

    return () => {
      // setPODataTable([]);
    };
  }, [poData]);

  const setNotification = (type, message) => {
    dispatch(
      showDialog({
        showPopup: true,
        type: type,
        responseMessage: message,
        canClose: true,
        autoHide: true,
      })
    );
  };

  function findByPropertyName(invData, propertyName, propertyValue) {
    let tempvalue = null;
    invData?.map((element) => {
      if (element.uniqueKey === 'taxCode') {
        tempvalue = element.comboBoxOptions.find(
          (obj) => obj[propertyName] === propertyValue
        );
      }
    });
    return tempvalue;
  }
  function rowDblClickHandler(selectedPoObject) {
    let invoiceDataValue = selectedPoObject;

    if (selectedPoObject.taxCode != null) {
      let dataResult = findByPropertyName(
        invData,
        'commonCode',
        selectedPoObject.taxCode
      );

      invoiceDataValue = {
        ...invoiceDataValue,
        taxCode: {
          value: dataResult.commonCode,
          Id: dataResult.commonId,
        },
      };
    }

    if (selectedPoObject?.isPOClosed === 'Y') {
      setNotification('info', 'PO Line has been Closed/Cancelled.');
      return;
    }
    if (selectedPoObject?.itemUsedFlag) {
      setNotification('info', 'Line item fully used');
      return;
    }
    if (disabledFlag) {
      setNotification('error', 'Click edit to update.');
      return;
    }

    if (invData) {
      const dataTableIndex = invData.findIndex(
        (item) => item.type === 'DataTable'
      );
      const dataTableArray = invData[dataTableIndex]?.value || [];

      if (
        !dataTableArray.some((e) => e.poItemId === selectedPoObject.poItemId)
      ) {
        const selectedObjCopy = structuredClone(invoiceDataValue);

        invData[dataTableIndex] = {
          ...invData[dataTableIndex],
          value: [...dataTableArray, selectedObjCopy], // Create a new array instead of pushing
        };

        setNotification(
          'success',
          `Line No : ${selectedPoObject.serialNo} has been added successfully.`
        );
      } else {
        setNotification(
          'error',
          `Line No : ${selectedPoObject.serialNo} already exists!`
        );
      }
      setValueOfDataTable(invData[dataTableIndex].value);
    }
  }

  const fileterdDataFunc = (data) => {
    if (JSON.stringify(poDataTableData) !== JSON.stringify(data)) {
      setpoDataTableData(data);
    }
  };

  return (
    <>
      <div className="inv-item">
        {fullWidth ? '' : <div className=" page-sub-title">PO Items</div>}
        <div className="searchbar">
          <SearchComponent
            tableListData={
              poDataTableState &&
              poDataTableState.length &&
              poDataTableState.value !== null
                ? poDataTableState[0].value
                : []
            }
            getFilteredData={(data) => fileterdDataFunc(data)}
          />
          <i className="icon-search"></i>
        </div>
        <div className="mb16"></div>
        {Array.isArray(poDataTableData) ? (
          <div
            className="mb24 styledDatatable"
            title={
              poDataTableData?.length === 0 || disableTableActions
                ? ''
                : 'Double click a PO line to add to the Invoice items.'
            }
          >
            <a className="hoverTitle">
              <DataTable
                persistTableHead
                highlightOnHover={true}
                noHeader={true}
                striped={true}
                dense={true}
                onRowDoubleClicked={(event) =>
                  !disableTableActions ? rowDblClickHandler(event) : ''
                }
                pagination={true}
                paginationDefaultPage={1}
                paginationResetDefaultPage={true}
                paginationTotalRows={TableListinglength}
                paginationPerPage={10}
                conditionalRowStyles={[
                  {
                    when: (row) => row.isPOClosed === 'Y' || row.itemUsedFlag,
                    style: {
                      borderBottom: '1px solid white !important',
                      '> *': {
                        color: 'grey !important',
                      },
                      '&:hover': {
                        '.rdt_TableCell': {
                          color: 'grey !important',
                          backgroundColor: 'transparent !important',
                        },
                        '&:nth-of-type(2n + 1)': {
                          '.rdt_TableCell': {
                            color: 'grey !important',
                            backgroundColor: ' #f9f9f9 !important',
                          },
                        },
                      },
                    },
                  },
                ]}
                // conditionalRowStyles={conditionalRowStyles}
                columns={
                  poDataTableState && poDataTableState.length
                    ? dataTableServiceProvider.getDataTableColumns(
                        poDataTableState[0]
                      )
                    : []
                }
                data={poDataTableData}
                expandableRows={true}
                expandableRowsComponent={
                  <DataTableRowSub
                    subInfo={poDataTableState[0]?.formSubDetailsInternalDTOList}
                  />
                }
              />
            </a>
          </div>
        ) : null}
      </div>
    </>
  );
}

export { PoDataTable };
