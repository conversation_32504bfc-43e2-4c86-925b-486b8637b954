import { APListing } from '../app/container/pages/aplisting/index';
import { Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import { InvoiceReceived } from './container/pages/invoices/invoice-main/InvoiceReceived';
import { CostInvoice, PaymentReqList } from './container/pages/invoices';
import { ApEmailsInbox } from './container/pages/apEmails/ApEmailsInbox';
import {
  AccrualReport,
  DPOReport,
  InvoiceYieldReport,
  PaymentAnalysis,
  ReturnedInvoice,
  StatusReport,
} from './container/pages/reports';
import { PaymentReqComponent } from './container/pages/invoices/payment-request/PaymentReqComponent';
import { InvoiceCompareComponent } from './container/pages/invoices';
import { AnimatePresence, motion } from 'framer-motion';
import { InvoiceNonPO } from './container/pages/invoices/invoice-main/InvoiceNonPo';
import { InvoiceUnmatched } from './container/pages/invoices/invoice-main/InvoiceUnmatched';
import { FinListing } from './container/pages/finance/finance-main/FinListing';
import { BankFileTracker } from './container/pages/finance';
import { FinanceReconciliation } from './container/pages/finance/finance-main/FinanceReconciliation';
import { APFileStatus } from './container/pages/reports/APFileStatus';
import { PoSettledWaitinv } from './container/pages/purchase-orders/po-main/PoSettledWaitinv';
import { CombinedInvSupplier } from './container/pages/paymentposting';
import {
  AccPayDashboard,
  PurchaseDashboard,
  APReportDashboard,
  DynamicRoute,
} from '@hp/components';
import { InvInProcess } from './container/pages/invoices/invoice-main/InvInProcess';
import { InvInProcessView } from './container/pages/invoices/invoice-main/InvInprocessView';
import { ErrorBoundary } from '@hp/components';
import { PfPaymentStatusReport } from './container/pages/finance/financeReports/PfPaymentStatusReport';
import { PfStatementReconciliationReport } from './container/pages/finance/financeReports/PfStatementReconciliationReport';
import { PfSummaryReport } from './container/pages/finance/financeReports/PfSummaryReport';
import { useEffect } from 'react';
import { utils } from '@hp/utils';

const PageTransition = ({ children }) => {
  return (
    <motion.div
      initial={{ opacity: 0, z: -50 }}
      animate={{ opacity: 1, z: 0 }}
      exit={{ opacity: 0, z: 50 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  );
};

const RouteWithTransition = ({ element }) => {
  return <PageTransition>{element}</PageTransition>;
};
const DynamicComponent = () => {
  const { pathname } = useLocation();
  const lastSegment = pathname.split('/').pop();

  switch (lastSegment) {
    case 'accounts-payable':
      return <AccPayDashboard />;
    case 'ap-payments':
      return <AccPayDashboard />;
    case 'purchase-order':
      return <PurchaseDashboard />;
    case 'ap-reports':
      return <APReportDashboard />;

    default:
      return <AccPayDashboard />;
  }
};

export function App() {
  useEffect(() => {
    async function decryptAndSetUser() {
      await utils.userdetailsDecrypt();
    }

    decryptAndSetUser();
  }, []);
  let location = useLocation();
  let navigate = useNavigate();
  useEffect(() => {
    const pathMap = {
      '/finance-ap/ap-reconciliation':
        '/finance-ap/ap-reconciliation/FinListingRecon/recon,pymt-bank',
    };

    if (pathMap[location.pathname]) {
      navigate(pathMap[location.pathname]);
    }
  }, [location.pathname, navigate]);
  return (
    <ErrorBoundary>
      {' '}
      <AnimatePresence mode="popLayout">
        <Routes>
          {/* <Route exact path="/fin-statements/" element={<FinListing />} /> */}
          <Route
            exact
            path="/ap-reconciliation/"
            element={<FinanceReconciliation />}
          />

          <Route
            exact
            path="/:submenu/APListing/:parameters"
            element={<RouteWithTransition element={<APListing />} />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/invoice/:Keyvalue/:poId/:invId"
            element={<InvoiceCompareComponent />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/invoice/:Keyvalue/:poId/:invId/extraction"
            element={<InvoiceCompareComponent />}
          />
          <Route
            exact
            path="/:submenu/InvoiceReceived/received"
            element={<InvoiceReceived />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/:invoice/:Keyvalue/null/:invId"
            element={<InvoiceNonPO />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/non-po/:invId"
            element={<InvoiceNonPO />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/cost-inv/:invId"
            element={<CostInvoice />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/unmatched/:Keyvalue/:invId"
            element={<InvoiceUnmatched />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/payment_req/:invId"
            element={<InvoiceNonPO />}
          />
          <Route
            exact
            path="/:submenu/PaymentReqList/:parameter"
            element={<PaymentReqList />}
          />
          <Route
            exact
            path="/:submenu/ApEmailsInbox/:parameters"
            element={<ApEmailsInbox />}
          />
          <Route
            exact
            path="/:submenu/InvoiceYieldReport/:parameters"
            element={<InvoiceYieldReport />}
          />
          <Route
            exact
            path="/:submenu/PaymentReqList/:parameter"
            element={<PaymentReqList />}
          />

          <Route
            exact
            path="/:submenu/NewPaymentReq/:parameters"
            element={<PaymentReqComponent />}
          />
          <Route
            exact
            path="/:submenu/PaymentReqList/:parameters/:pymtReqId"
            element={<PaymentReqComponent />}
          />
          <Route
            exact
            path="/:submenu/FinListing/:parameters"
            element={<FinListing />}
          />
          <Route
            exact
            path="/:submenu/FinListingRecon/:parameters"
            element={<FinanceReconciliation />}
          />
          <Route
            exact
            path="/:submenu/FinListingSap/:parameters"
            element={<FinListing />}
          />

          <Route
            exact
            path="/:submenu/report/ap-file-status"
            element={<APFileStatus />}
          />
          <Route
            exact
            path="/:submenu/InvoiceYieldReport/:parameters"
            element={<InvoiceYieldReport />}
          />
          <Route
            exact
            path="/:submenu/DPOReport/:parameters"
            element={<DPOReport />}
          />
          <Route
            exact
            path="/:submenu/StatusReport/:parameters"
            element={<StatusReport />}
          />
          <Route
            exact
            path="/:submenu/AccrualReport/:parameters"
            element={<AccrualReport />}
          />
          <Route
            exact
            path="/:submenu/PaymentAnalysis/:parameters"
            element={<PaymentAnalysis />}
          />
          <Route
            exact
            path="/:submenu/ReturnedInvoice/:parameters"
            element={<ReturnedInvoice />}
          />
          <Route
            exact
            path="/:submenu/ReturnedInvoice/:parameters"
            element={<ReturnedInvoice />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/PP/:Keyvalue/:ppId"
            element={<CombinedInvSupplier />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/PP/:Keyvalue/:ppId/mail-access/:obj"
            element={<CombinedInvSupplier />}
          />
          <Route
            exact
            path="/:submenu/APListing/:parameters/PO/:Keyvalue/:poId"
            element={<PoSettledWaitinv />}
          />
          <Route path="/:submenu/" element={<DynamicRoute />} />
          <Route
            exact
            path="/:submenu/Invoice/in-process"
            element={<InvInProcess />}
          />
          <Route
            exact
            path="/:submenu/InProcessView/:serialNo"
            element={<InvInProcessView />}
          />
          <Route
            exact
            path="/:submenu/paymentStatusReport/:parameters"
            element={<PfPaymentStatusReport />}
          />
          <Route
            exact
            path="/:submenu/paymentSumReport/:parameters"
            element={<PfSummaryReport />}
          />
          <Route
            exact
            path="/:submenu/statementReconciliation/:parameters"
            element={<PfStatementReconciliationReport />}
          />
          <Route
            exact
            path="/:submenu/FileTracker/file-tracker"
            element={<BankFileTracker />}
          />
        </Routes>
      </AnimatePresence>
    </ErrorBoundary>
    //financeApp
  );
}

export default App;
