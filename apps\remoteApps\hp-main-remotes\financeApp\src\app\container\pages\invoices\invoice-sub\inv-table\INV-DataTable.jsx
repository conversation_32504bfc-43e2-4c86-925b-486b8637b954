/* eslint-disable eqeqeq */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, {
  useEffect,
  useCallback,
  useState,
  useMemo,
  useRef,
} from 'react';
import DataTable from 'react-data-table-component';
import { useDispatch, useSelector } from 'react-redux';
import '@hp/styles/UploadDoc.scss';
import '@hp/styles/dataTable.scss';
import cloneDeep from 'lodash.clonedeep';
import '@hp/styles/InvoiceCompare.scss';
import {
  Button,
  dataTableServiceProvider,
  globalutils,
  SearchComponent,
  TextInput,
  useConfirm,
  DataTableRowSub,
} from '@hp/components';
import { showDialog } from '@hp/mainstore';

import { DataTableCell } from './DataTableCell';
import './InvDataTable.scss';
const InvDatatable = (props) => {
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const buttonData = useSelector((state) => state.buttons.buttons || null);
  const {
    fullWidth,
    disabledFlag,
    onSaveClick,
    setValueOfDataTable,
    status,
    invData,
    newRowIndex,
    deleteRow,
  } = props;
  const [invTableColums, setinvTableColums] = useState([]);
  const [filterData, setFilterData] = useState([]);
  const [editingId, setEditingId] = useState('');
  const [searchText, setSearchText] = useState('');

  const [defaultPage, setDefaultPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowPerPage, setRowPerPage] = useState(10);

  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [dataTableForm, setDataTableForm] = useState([]);
  let tableName = globalutils.getTableName();

  const innerDataRef = useRef([]);

  useEffect(() => {
    if (invData && invData !== undefined) {
      const dataTable = invData?.filter((item) => item.type === 'DataTable');
      if (!dataTable && !dataTable[0]) return;
      setDataTableForm(dataTable[0]);
      const dataTableColumns = dataTableServiceProvider.getDataTableColumns(
        dataTable[0]
      );
      const dataTableRows = dataTableServiceProvider.getDataTableRows(
        dataTable[0]
      );
      var columnArray = cloneDeep(dataTableColumns);
      columnArray?.map((items) => {
        if (
          items?.selector === 'unitPriceConverted' ||
          items?.selector === 'amountConverted' ||
          items?.selector === 'taxAmountConverted'
        ) {
          items.right = true;
        }
      });
      setinvTableColums(columnArray);
      setFilterData(dataTableRows);
      innerDataRef.current = dataTableRows;
    }
  }, [invData]);

  const tableColumn = useMemo(() => {
    return invTableColums && invTableColums.length
      ? invTableColums.map((col) => {
          const selector = col?.selector;

          if (!col.editable) {
            return col;
          }

          return {
            ...col,
            style: {
              padding: '0px 16px 2px 14px',
            },
            name:
              col.required === 'Y' ? (
                <span className="mandatory">{col.name}</span>
              ) : (
                col.name
              ),
            cell: (row, index, column) => {
              return editTable(row, index, column, selector);
            },
          };
        })
      : [];
  }, [invTableColums, disabledFlag, editingId]);

  const createColumns = useCallback(() => {
    if (tableName && tableName[0] === 'PP') {
      return [...tableColumn];
    }

    return [
      {
        name: 'Actions',
        allowOverflow: true,
        width: '120px',
        cell: (row, index) => inlineEditable(row, index, currentPage),
      },
      ...tableColumn,
    ];
  }, [tableColumn]);

  useEffect(() => {
    if (onSaveClick) {
      save();
    }
  }, [onSaveClick]);

  useEffect(() => {
    if (newRowIndex || newRowIndex === 0) {
      let totalPageCount = Math.ceil(filterData.length / 10);

      setDefaultPage(totalPageCount > 1 ? totalPageCount : 1);

      let defaultPageCopy = newDefaultPage(totalPageCount);

      let indexCopy = newRowIndex % 10;

      edit(
        {
          amount: null,
          amountConverted: null,
          description: null,
          invId: null,
          inv_item_id: null,
          poItemId: null,
          quantity: null,
          rcvdQty: null,
          serialNo: null,
          tax: null,
          taxAmount: null,
          taxAmountConverted: null,
          unit: null,
          unitPrice: null,
          unitPriceConverted: null,
          unitType: null,
          taxCode: null,
        },
        indexCopy,

        defaultPageCopy
      );
    }
  }, [newRowIndex]);

  const newDefaultPage = (totalPageCount) => {
    return totalPageCount > 1 ? totalPageCount : 1;
  };

  const isEditing = (record, index, currentPage) => {
    let globalIndex = index + (currentPage - 1) * rowPerPage;
    return globalIndex === editingId;
  };

  const getGlobalIndex = (index, currentPage) => {
    let globalIndex = index + (currentPage - 1) * rowPerPage;
    return globalIndex;
  };
  const setNotification = (type, message) => {
    dispatch(
      showDialog({
        showPopup: true,
        type: type,
        responseMessage: message,
        canClose: true,
        autoHide: true,
      })
    );
  };

  const decimalPointHandler = (data) => {
    return Math.trunc(parseFloat(data) * 100) / 100;
  };

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  const edit = (record, index, currentPage) => {
    if (!disabledFlag) {
      let globalIndex = index + (currentPage - 1) * rowPerPage;

      const styleAttribute = dataTableServiceProvider?.lineItemDataTableRow(
        globalIndex,
        filterData
      );
      setConditionalStyles(styleAttribute);
      setEditingId(globalIndex);
    } else {
      funcToSetResponseMessage('info', 'Click edit button to proceed!');
    }
  };

  const cancel = () => {
    setEditingId('');
  };

  const deleteHandler = async (row, index, currentPage) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      if (status === 'payment-request' && row?.pymtReqId) {
        deleteRow(row);
        return;
      }
      let globalIndex = getGlobalIndex(index, currentPage);

      filterData.splice(globalIndex, 1);
      setEditingId('');
      setFilterData(filterData);
      setValueOfDataTable(filterData);
    }
  };

  const save = () => {
    const tempData = [...innerDataRef.current];

    var validateSuccessFlag = invLineItemValidationDynamic();
    if (validateSuccessFlag) {
      setEditingId('');
      setFilterData(tempData);
      setValueOfDataTable(tempData);
    } else setNotification('error', 'Mandatory fields missing in line item.');
  };

  const getElementsForCurrentPage = (tempData, currentPage, pageSize) => {
    const startId = (currentPage - 1) * pageSize;
    const endId = startId + pageSize;
    return tempData.slice(startId, endId);
  };

  const invLineItemValidationDynamic = (index, currentPage) => {
    let validateSuccessFlag = true;

    const tempData = [...filterData];

    let elements = getElementsForCurrentPage(tempData, currentPage, 10);

    const currentPageData = elements.filter((i) => i === index);

    const dataTableCopy = invData?.find((item) => item.type === 'DataTable');

    const requiredSelectors = dataTableCopy?.formSubDetailsInternalDTOList
      ?.filter((subDetail) => subDetail.required === 'Y')
      .map((subDetail) => subDetail.selector);
    if (requiredSelectors != null) {
      for (const selector of requiredSelectors) {
        const valueAbsentFlag = currentPageData?.some(
          (data) => !data[selector]
        );
        if (valueAbsentFlag) {
          validateSuccessFlag = false;
          break;
        }
      }
    }
    return validateSuccessFlag;
  };
  const lineSave = (row, index, currentPage) => {
    const tempData = [...innerDataRef.current];
    var validateSuccessFlag = invLineItemValidationDynamic(index, currentPage);

    if (validateSuccessFlag) {
      setEditingId('');
      setFilterData(tempData);
      setValueOfDataTable(tempData);
    } else {
      setNotification('error', 'Mandatory fields missing in line item.');
    }
  };

  const formOnChange = (event, index) => {
    const { name, value } = event.target;

    const globalIndex = index + (currentPage - 1) * rowPerPage;
    const updatedLineItem = { ...innerDataRef.current[globalIndex] };

    // Handle value transformation based on field
    let updatedValue = value;
    if (name === 'serialNo') {
      updatedValue = parseInt(value, 10);
    } else if (name === 'quantity') {
      updatedValue = value.replace(',', '');
    }

    updatedLineItem[name] = updatedValue;

    // Handle computed fields
    if (name === 'quantity' || name === 'unitPriceConverted') {
      const quantity = parseFloat(
        updatedLineItem.quantity?.toString().replace(',', '') || 0
      );
      const unitPrice = parseFloat(
        updatedLineItem.unitPriceConverted?.toString().replace(',', '') || 0
      );

      updatedLineItem.amountConverted =
        quantity && unitPrice
          ? decimalPointHandler(quantity * unitPrice)
          : '0.00';
    }

    if (name === 'subTotalConverted') {
      updatedLineItem.subTotalConverted = decimalPointHandler(
        updatedLineItem.subTotalConverted
      );
    }

    const updatedData = [...innerDataRef.current];
    updatedData[globalIndex] = updatedLineItem;
    innerDataRef.current = updatedData;
  };
  const editTable = (row, index, column, selector) => {
    const editing = isEditing(row, index, currentPage);

    return (
      <DataTableCell
        row={row}
        index={index}
        column={{ ...column, editing }}
        onChange={formOnChange}
        dataTableForm={dataTableForm}
      />
    );
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  const handleRowsPerPageChange = (newPerPage) => {
    setRowPerPage(newPerPage);
  };
  const inlineEditable = (row, index, currentPage) => {
    const editable = isEditing(row, index, currentPage);
    if (editable) {
      return (
        <div style={{ display: 'flex' }}>
          <span
            title="Click to Save"
            style={{ cursor: 'pointer', display: 'inline' }}
            onClick={() => lineSave(row, index, currentPage)}
            className="icon icon-saveicon mr20"
          ></span>

          <span
            title="Click to Cancel"
            style={{ cursor: 'pointer', display: 'inline' }}
            onClick={() => cancel(row, index, currentPage)}
            className="icon-reply mr20"
          ></span>

          <span
            title="Click to Delete"
            style={{ cursor: 'pointer', display: 'inline' }}
            onClick={() => deleteHandler(row, index, currentPage)}
            className="icon icon-bin"
          ></span>
        </div>
      );
    }
    return (
      <div
        style={{ width: '30px' }}
        onClick={() => edit(row, index, currentPage)}
      >
        <div
          id={index}
          disabled={disabledFlag}
          className="icon-edit-button"
        ></div>
      </div>
    );
  };
  const fileterdDataFunc = (data) => {
    setFilterData(data);
  };

  return (
    <div className="inv-item inv-item-table mb20">
      {!fullWidth ? <div className=" page-sub-title">Invoice Items</div> : ''}
      <div className="flex-row">
        <div className="searchbar">
          <SearchComponent
            tableListData={innerDataRef.current}
            getFilteredData={(data) => fileterdDataFunc(data)}
            searchText={setSearchText}
          />
          <i className="icon-search"></i>
        </div>

        {!fullWidth ? (
          <div className="flex-row" style={{ float: 'right' }}>
            {(tableName &&
              tableName[0] === 'INV' &&
              (status === 'non-po' || status === 'cost-inv')) ||
            status === 'payment-request' ||
            status === 'credit_note' ? (
              <Button
                className="small mb8 outline add-button-custom flex-row vam"
                disabled={disabledFlag}
                style={{ cursor: 'pointer', float: 'right' }}
                title={'Click to add rows.'}
                onClick={() => props.setItemClickModal(true)}
              >
                <i className="icon-add-button "></i> Add
              </Button>
            ) : null}
          </div>
        ) : (
          ''
        )}
      </div>
      <div className="mb16"></div>
      {Array.isArray(filterData) ? (
        <div className="styledDatatable">
          <DataTable
            persistTableHead={true}
            highlightOnHover={true}
            noHeader={true}
            striped={true}
            dense={true}
            style={{
              fontFamily: 'Roboto',
            }}
            // onRowClicked={dataTableClick}
            // disabled={disabledFlag}
            conditionalRowStyles={conditionalRowStyles}
            pagination={true}
            // paginationDefaultPage={1}
            paginationDefaultPage={defaultPage}
            paginationResetDefaultPage={true}
            // paginationTotalRows={TableListinglength}
            paginationPerPage={10}
            columns={createColumns()}
            data={filterData}
            onChangePage={handlePageChange}
            onChangeRowsPerPage={handleRowsPerPageChange}
            expandableRows={true}
            expandableRowsComponent={
              <DataTableRowSub
                subInfo={dataTableForm?.formSubDetailsInternalDTOList}
                rowData={dataTableForm?.value}
              />
            }
          />
        </div>
      ) : null}
    </div>
  );
};

export { InvDatatable };
