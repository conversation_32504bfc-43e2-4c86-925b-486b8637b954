/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-redeclare */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */

import { Input, formValidationUtil } from '@hp/components';
import { memo, useState, useCallback, useLayoutEffect } from 'react';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import {
  brokerLists,
  getDistributorDetails,
  addEditBroker,
} from '@hp/mainstore';
import cloneDeep from 'lodash.clonedeep';
import { processMPNCache } from './processMPNCache';

function FormActions(props) {
  const {
    form,
    isOpen,
    isClose,
    action,
    productDetailsId,
    brokerDistributorId,
  } = props;

  const { processAddEditResponse } = useSelector((store) => store.bb);

  const dispatch = useDispatch();

  const AddEditForm = useCallback(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: call back to avoid unnecessary rendering.
     */
    const [formInputs, setFormInputs] = useState([]);
    const [DistributorId, setDistributorId] = useState();

    // const [notify, setnotify] = useState();

    const { distributorDetails } = useSelector((store) => store.bb);

    useLayoutEffect(() => {
      if (form) {
        setFormInputs(form);
        setDistributorId();
      }
    }, [form]);

    useLayoutEffect(() => {
      /**
       * <AUTHOR> Augustine
       * @description: broker value rendering.
       */
      if (distributorDetails && formInputs && formInputs.length) {
        let tempArray = cloneDeep(formInputs);
        tempArray.map((element) => {
          if (element.uniqueKey === 'page-title') {
            element &&
              element.formSubDetailsInternalDTOList.map((items) => {
                if (items.uniqueKey === 'email') {
                  items.disableFlag = 'T';
                  items.value =
                    distributorDetails && distributorDetails.value
                      ? distributorDetails.value.email
                      : '';
                } else if (items.uniqueKey === 'contact') {
                  items.disableFlag = 'T';
                  items.value =
                    distributorDetails && distributorDetails.value
                      ? distributorDetails.value.contactName
                      : '';
                }
              });
          }
        });
        setFormInputs(tempArray);
      }
    }, [distributorDetails]);

    // const funcToSetResMessageInModal = (type, resMessage) => {
    //   setnotify({ type, resMessage });
    // };

    // useLayoutEffect(() => {
    //   setnotify(null);
    // }, [isOpen]);

    useLayoutEffect(() => {
      if (processAddEditResponse?.value) {
        let productId = processMPNCache.addDistributor.mpnRowId;
        dispatch(brokerLists({ masterID: productId, reqType: 'subscribe' }));
      }
    }, [processAddEditResponse]);

    const onChangeHandler = (event, selectedItem, uniqueKey) => {
      // let validatedElement = formValidationUtil.isFormcontrolValidDynamic(
      //   event,
      //   selectedItem
      // );

      let tempArray = cloneDeep(formInputs);
      tempArray.map((entry) => {
        entry.uniqueKey === 'page-title' &&
          entry.formSubDetailsInternalDTOList.map((items) => {
            if (items.uniqueKey === uniqueKey && items.type !== 'SearchInput') {
              items.value = event.target.value;
            } else if (
              items.uniqueKey === uniqueKey &&
              items.type === 'SearchInput'
            ) {
              items.value = event.value;
            }
            if (items.uniqueKey === 'broker') {
              items.comboBoxOptions.map((values) => {
                if (
                  values.commonName === items.value &&
                  selectedItem.type === 'SearchInput'
                ) {
                  dispatch(getDistributorDetails(values.commonId));
                  setDistributorId(values.commonId);
                }
              });
            } else if (
              items.uniqueKey === 'email' &&
              selectedItem.uniqueKey === 'broker'
            ) {
              if (!selectedItem.value) {
                items.value = '';
              }
              items.disableFlag = 'F';
            } else if (
              items.uniqueKey === 'contact' &&
              selectedItem.uniqueKey === 'broker'
            ) {
              if (!selectedItem.value) {
                items.value = '';
              }
              items.disableFlag = 'F';
            }
          });
      });
      // tempArray.map((element) => {
      //   if (element.uniqueKey === 'page-title') {
      //     element &&
      //       element.formSubDetailsInternalDTOList.map((items, elementIndex) => {
      //         if (items.uniqueKey === selectedItem.uniqueKey) {
      //           element.formSubDetailsInternalDTOList.splice(
      //             elementIndex,
      //             1,
      //             validatedElement
      //           );

      //           // items.value = event?.target?.value
      //           //   ? event.target.value
      //           //   : event.value;
      //         }
      //       });
      //   }
      // });
      setFormInputs(tempArray);
      // seterrorFlag(validatedElement.errorFlag);

      // if (validatedElement && validatedElement.uniqueKey === 'stock') {
      //   setStockError(validatedElement.errorFlag);
      // } else if (validatedElement && validatedElement.uniqueKey === 'price') {
      //   setPriceError(validatedElement.errorFlag);
      // }
    };

    const onSubmitHandler = () => {
      // let validate = formValidationUtil.checkMandatoryField(formInputs);
      // let validatedFc = cloneDeep(validate.formList);
      let validatedElement = formValidationUtil.validateForm(formInputs);
      let validatedFc = cloneDeep(validatedElement.formList);
      setFormInputs(validatedFc);

      if (validatedElement.validSuccess) {
        dispatch(
          addEditBroker({
            formDetailsDto: formInputs,
            brokerDistributorId: brokerDistributorId,
            action: action,
            productDetailsId: productDetailsId,
            DistributorId: DistributorId,
          })
        );
      }
    };

    const onHandleCancel = () => {
      isClose();
    };
    const onChangeHandlingFunctions = {
      onChangeHandler,
      onSubmitHandler,
      onHandleCancel,
    };
    return (
      <Modal
        className="Modal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isOpen}
      >
        <div onClick={isClose} className="modal-close icon-close"></div>
        {formInputs && formInputs.length
          ? formInputs.map((element, index) => {
              if (element.uniqueKey === 'page-title') {
                return <Input key={index} formType={element} />;
              }
            })
          : ''}
        <div className="two-col-layout mb40">
          {formInputs && formInputs.length
            ? formInputs.map((element) => {
                if (element.uniqueKey === 'page-title') {
                  return (
                    element &&
                    element.formSubDetailsInternalDTOList.map(
                      (items, index) => {
                        return (
                          <Input
                            key={index}
                            formType={items}
                            isEditable="notShowing"
                            onChangeHandler={(items, event) =>
                              onChangeHandler(event, items, items.uniqueKey)
                            }
                          />
                        );
                      }
                    )
                  );
                }
              })
            : ''}
        </div>
        <div>
          {formInputs && formInputs.length
            ? formInputs.map((element, index) => {
                if (
                  element.uniqueKey === 'submitButton' ||
                  element.uniqueKey === 'cancelButton'
                ) {
                  return (
                    <Input
                      key={index}
                      formType={element}
                      onChangeHandler={(element, event) =>
                        onChangeHandlingFunctions[element.onChangeFunction](
                          event,
                          element.uniqueKey,
                          element
                        )
                      }
                    />
                  );
                }
              })
            : ''}
        </div>
        {/* {notify != null ? (
          <div
            className={["notification-bar", "type-" + notify.type].join(" ")}
            style={{
              position: "sticky !important",
              width: 90 + "%",
            }}
          >
            <i className="icon-close" onClick={() => setnotify(null)}></i>
            {notify.resMessage}
          </div>
        ) : (
          ""
        )} */}
      </Modal>
    );

    // if (formInputs && formInputs.length) {
    //   setformInputsFormAction(formInputs);
    // }
  }, [isOpen]);

  return <AddEditForm />;
}
export default memo(FormActions);
