/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */
import {
  Button<PERSON><PERSON><PERSON>,
  CommonSpinner,
  formValidationUtil,
  Input,
  ProcessFlow,
  TraceEvents,
  useConfirm,
} from '@hp/components';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  getECOProcessFlowDetails,
  getECOSavedDocumentsDetails,
  viewOrEditECODetailsFormDetails,
  getECRECOTraceEventsDetails,
  showDialog,
  viewOrEditECRDetailsFormDetails,
  getECRSavedDocumentsDetails,
  updateECOStatusListDetails,
  getECRECOTraceEventsHistory,
  getECOPurchaseNotificationFormDetails,
  approveECOStatus,
  resetviewOrEditECODetailsFormDetails,
  resetgetECOSavedDocumentsDetails,
  resetgetECOProcessFlowDetails,
  resetgetECRECOTraceEventsDetails,
  resetsaveECOPurchaseNotificationFormDetails,
} from '@hp/mainstore';
import './ecr_eco_common.scss';
import { Tabs } from 'antd';
import { ECRECOTree } from './ECRECOTree';
import { useLocation, useNavigate } from 'react-router-dom';
import ECRECOHeader from './ECRECOHeader';
import ECRViewInECO from './ECRViewInECO';
import { useAppRouterDom } from '@hp/utils';
import { ECOPurchaseNotifForm } from './ECOPurchaseNotifForm';
import ECRECOPartNoSection from './ECRECOPartNoSection';
import AffectedItems from './AffectedItems';
import { RejectModal } from '../RejectModal';
import ECRECOApprovers from './ECRECOApprovers';
import { globalutils } from '@hp/components';

const ECOViewPage = (props) => {
  const [currentTab, setCurrentTab] = useState('1');
  const [ecoForm, setEcoForm] = useState([]);
  const [treeDataList, setTreeDataList] = useState([]);
  const [traceEvents, setTraceEvents] = useState([]);
  const [processFlowData, setProcessFlowData] = useState(null);
  const [selectedPartNumbers, setSelectedPartNumbers] = useState(null);
  const [filteredPartNumber, setFilteredPartNumber] = useState(null);
  const [isECRViewDisabled, setIsECRViewDisabled] = useState(true);
  const [buttonCommonData, setButtonCommonData] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const [errorFlag, setErrorFlag] = useState(false);
  const [showApproverModal, setShowApproverModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [rejectReason, setRejectReason] = useState('');
  const [listValues, setListValues] = useState([
    { label: 'ECO #', value: null },
    { label: 'ECR #', value: null },
    { label: 'Title', value: null },
    { label: 'Requestor', value: null },
    { label: 'Originator', value: null },
  ]);
  const location = useLocation();
  const { confirm } = useConfirm();
  const state = location?.state || null;
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    ecoDetailsForm,
    ecoSavedDocuments,
    ecoProcessFlowRes,
    ecrEcoTraceEvents,
    ecoChangeStatusRes,
    saveEcoPurchaseNotifFormRes,
    loading,
  } = useSelector((store) => store.pdm);
  const buttonData = useSelector((state) => state.buttons.buttons || null);
  const { domParameters } = useAppRouterDom();
  // const menuData = domParameters?.menuData || '';
  // const submenu = domParameters?.submenu || '';

  const segments = location.pathname.split('/');
  const menuData = segments[1] || '';
  const submenu = segments[segments.length - 4] || '';
  const parameter = segments[segments.length - 3] || '';
  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;
  const ecoId = domParameters?.ecoId ?? null;
  const ecrId = domParameters?.ecrId ?? null;
  const selectedEcoData = state?.selectedEcoData ?? null;
  const isApproval = state?.isApproval ?? null;
  const authorised = state?.authorised ?? null;
  const isPurchase = state?.isPurchase ?? null;
  const showPurchaseNotificationSaveButton =
    state?.showPurchaseNotificationSaveButton ?? null;
  let handleSaveButton;

  useEffect(() => {
    dispatch(
      viewOrEditECODetailsFormDetails({
        ecoId: ecoId,
        userId: userId,
        clientId: clientId,
        method: 'View',
      })
    );
    dispatch(getECOSavedDocumentsDetails(ecoId));
    dispatch(getECOProcessFlowDetails({ ecoId: ecoId, clientId: clientId }));
    dispatch(getECRECOTraceEventsDetails({ tableName: 'ECO', tableId: ecoId }));
    if (ecrId) {
      dispatch(
        viewOrEditECRDetailsFormDetails({
          ecrId: ecrId,
          userId: userId,
          clientId: clientId,
          method: 'View',
        })
      );
      dispatch(getECRSavedDocumentsDetails(ecrId));
    }
    if (isPurchase)
      dispatch(
        getECOPurchaseNotificationFormDetails({
          ecoId: ecoId,
          userId: userId,
          clientId: clientId,
        })
      );
    if (isApproval && !authorised) {
      funcToSetResponseMessage(
        'error',
        'You are not authorised to approve/reject this ECO.'
      );
    }
    return () => {
      dispatch(resetviewOrEditECODetailsFormDetails());
      dispatch(resetgetECOSavedDocumentsDetails());
      dispatch(resetgetECOProcessFlowDetails());
      dispatch(resetgetECRECOTraceEventsDetails());
      dispatch(resetsaveECOPurchaseNotificationFormDetails());
      dispatch(
        showDialog({
          showPopup: false,
          type: '',
          responseMessage: '',
          canClose: true,
          autoHide: true,
        })
      );
      setEcoForm([]);
    };
  }, []);

  useEffect(() => {
    if (buttonData && buttonData !== undefined) {
      let tempButtonData = [];
      if (isApproval)
        tempButtonData = buttonData.map((item) => ({
          ...item,
          disable: isApproval && !authorised,
        }));
      // else if (parameter === 'draft') return;
      // else
      //   tempButtonData = [
      //     {
      //       ...buttonData?.find((btn) => btn.funcPath === 'handleReject'),
      //       disable: isApproval && !authorised,
      //     },
      //   ];
      if (showPurchaseNotificationSaveButton) {
        tempButtonData.splice(0, 0, {
          buttonId: null,
          menuId: null,
          label: 'Save',
          type: 'default mr20 fl button mb20',
          funcPath: 'handleSave',
          accessFlag: 'Y',
          parameter: '',
          labelEn: 'Save',
          disable: false,
        });
      }
      tempButtonData.push({
        buttonId: null,
        menuId: null,
        label: 'Close',
        type: 'outline mr20 fr button mb20',
        funcPath: 'handleClose',
        orderId: 2,
        accessFlag: 'Y',
        parameter: '',
        labelEn: 'Close',
        disable: false,
      });
      setButtonCommonData(tempButtonData);
    }
  }, [buttonData]);

  useEffect(() => {
    if (ecoDetailsForm?.value) {
      let tempForm = [...ecoDetailsForm.value];
      let tempSelectedPartNumbers = [];
      let tempPartNoOptions = [];
      ecoDetailsForm.value?.forEach((item) => {
        if (item.uniqueKey === 'ecoView') {
          handleHeaderValues(item?.formSubDetailsInternalDTOList ?? []);
        } else if (item.uniqueKey === 'partNoTable') {
          if (item?.value?.length) {
            item?.value?.forEach((entry) => {
              tempSelectedPartNumbers.push(entry.value);
              tempPartNoOptions.push({
                commonId: entry.value,
                commonName: entry.partNo,
              });
            });
            setSelectedPartNumbers(tempSelectedPartNumbers);
          }
        }
      });
      tempForm = tempForm.map((item) => {
        if (item.uniqueKey === 'affectedItems') {
          // Clone the item and its nested properties
          const newItem = {
            ...item,
            formSubDetailsInternalDTOList: [
              ...item.formSubDetailsInternalDTOList,
            ],
          };

          newItem.formSubDetailsInternalDTOList =
            newItem.formSubDetailsInternalDTOList.map((entry) => {
              if (entry.uniqueKey === 'partNumberSelect') {
                // Create a new object with the updated 'comboBoxOptions'
                return { ...entry, comboBoxOptions: tempPartNoOptions };
              }
              return entry;
            });

          return newItem;
        }
        return item;
      });
      setEcoForm(tempForm);
    }
  }, [ecoDetailsForm]);

  useEffect(() => {
    if (ecoSavedDocuments?.value) {
      setTreeDataList(ecoSavedDocuments.value);
    }
  }, [ecoSavedDocuments]);

  useEffect(() => {
    if (ecoProcessFlowRes?.value) {
      setProcessFlowData({
        nodes: ecoProcessFlowRes.value?.pdmProfileWorkflowRenderingDtos,
        edges: ecoProcessFlowRes.value?.pdmProfileWorkflowEdgeDtos,
      });
    }
  }, [ecoProcessFlowRes]);

  useEffect(() => {
    if (ecrEcoTraceEvents?.value) {
      setTraceEvents(ecrEcoTraceEvents.value);
    }
  }, [ecrEcoTraceEvents]);

  useEffect(() => {
    if (ecoChangeStatusRes?.value) {
      funcToSetResponseMessage(
        ecoChangeStatusRes.value?.type,
        ecoChangeStatusRes.value?.text
      );
      navigate({
        pathname: `/${menuData}/${submenu}/eco/${parameter}`,
      });
    }
  }, [ecoChangeStatusRes]);

  useEffect(() => {
    if (saveEcoPurchaseNotifFormRes?.value) {
      funcToSetResponseMessage(
        saveEcoPurchaseNotifFormRes.value?.type,
        saveEcoPurchaseNotifFormRes.value?.text
      );
    }
  }, [saveEcoPurchaseNotifFormRes]);

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  const handleToggleIcon = (event) => {
    event.stopPropagation();
    setIsECRViewDisabled(!isECRViewDisabled);
  };

  function handleHeaderValues(formData) {
    let tempListValues = [...listValues];
    formData.forEach((item) => {
      switch (item.uniqueKey) {
        case 'ecoNo':
          tempListValues[0].value = item.value;
          break;
        case 'ecrNo':
          tempListValues[1].value = item.value;
          break;
        case 'title':
          tempListValues[2].value = item.value;
          break;
        case 'requestedBy':
          tempListValues[3].value = item.value;
          break;
        case 'originator':
          tempListValues[4].value = item.value;
          break;
        // case 'partNum':
        //   setSelectedPartNo(item.value);
        //   break;
      }
    });
    setListValues(tempListValues);
  }

  const handleAdvancedSelect = (event, uniqueKey, element) => {
    let tempForm = [...ecoForm];
    tempForm.map((item) => {
      if (item.uniqueKey === 'affectedItems') {
        item.formSubDetailsInternalDTOList.map((entry) => {
          if (entry.uniqueKey === uniqueKey) {
            entry.value = event;
            setFilteredPartNumber(event);
          }
        });
      }
    });
    setEcoForm(tempForm);
  };

  const onChangeHandlingFunctions = {
    handleAdvancedSelect,
  };

  const handleTabs = (item) => {
    if (item.uniqueKey === 'processFlow') {
      return (
        <div
          className="boxed mb20"
          style={{ maxWidth: '600px', maxHeight: '600px' }}
        >
          <h3 className="page-sub-title">Process Flow</h3>
          <div className="ecr-process-flow">
            {processFlowData ? (
              <ProcessFlow dataFlowResponse={processFlowData} />
            ) : (
              ''
            )}
          </div>
        </div>
      );
    } else if (item.uniqueKey === 'ecoView') {
      return (
        <>
          <div className="card mb20">
            <Input formType={item} />
          </div>
          {selectedPartNumbers?.length ? (
            <div className="card mb20">
              <ECRECOPartNoSection
                parameter={'view'}
                formData={ecoDetailsForm?.value ?? []}
              />
            </div>
          ) : (
            ''
          )}
        </>
      );
    } else if (item.uniqueKey === 'affectedItems') {
      return selectedPartNumbers?.length ? (
        <div className="card mb20">
          <Input
            formType={item.formSubDetailsInternalDTOList[0]}
            isEditable="notShowing"
            onChangeHandler={(element, event) => {
              onChangeHandlingFunctions[element.onChangeFunction](
                event,
                element.uniqueKey,
                element
              );
            }}
          />
          <AffectedItems
            selectedPartNumbers={selectedPartNumbers}
            filteredPartNumber={filteredPartNumber}
          />
        </div>
      ) : (
        ''
      );
    } else if (item.uniqueKey === 'attachments') {
      return (
        <div className="mb20">
          <div className="ecr-eco-docs-container">
            {ecrId ? (
              <div className="ecr-eco-view-icon">
                <span
                  className={
                    isECRViewDisabled
                      ? 'icon icon-circle-up'
                      : 'icon icon-circle-down'
                  }
                  onClick={handleToggleIcon}
                ></span>
              </div>
            ) : (
              ''
            )}
            <div
              className={
                isECRViewDisabled
                  ? 'ecr-eco-section-show'
                  : 'ecr-eco-section-hide'
              }
            >
              {!isECRViewDisabled ? (
                <div className="ecr-eco-minimized-title">
                  <div className="page-sub-title">New Documents</div>
                </div>
              ) : (
                ''
              )}
              <ECRECOTree treeDataList={treeDataList} disabled />
            </div>
          </div>
          {ecrId ? (
            <div className="ecr-eco-docs-container">
              <div className="ecr-eco-view-icon">
                <span
                  className={
                    isECRViewDisabled
                      ? 'icon icon-circle-down '
                      : 'icon icon-circle-up'
                  }
                  onClick={handleToggleIcon}
                ></span>
              </div>
              <div
                className={
                  isECRViewDisabled
                    ? 'ecr-eco-section-hide'
                    : 'ecr-eco-section-show'
                }
              >
                {isECRViewDisabled ? (
                  <div className="ecr-eco-minimized-title">
                    <div className="page-sub-title">Related Documents</div>
                  </div>
                ) : (
                  ''
                )}
                <ECRViewInECO />
              </div>
            </div>
          ) : (
            ''
          )}
        </div>
      );
    }
  };

  async function handleApproval() {
    const isConfirmed = await confirm('Are you sure you want to continue?');
    if (isConfirmed) {
      if (selectedEcoData)
        dispatch(
          updateECOStatusListDetails({
            status: 'for-approval',
            userId: userId,
            clientId: clientId,
            flag: 'N',
            payload: [selectedEcoData],
          })
        );
      else funcToSetResponseMessage('error', 'Please Select at least one ECO');
    }
  }

  function handleReject() {
    setShowPopup(true);
  }

  async function handleClose() {
    navigate(-1);
  }

  function handleSave() {
    if (handleSaveButton) handleSaveButton();
  }

  async function handleApprove() {
    const isConfirmed = await confirm('Are you sure you want to continue?');
    if (isConfirmed) {
      if (selectedEcoData?.ecoId)
        dispatch(
          approveECOStatus({
            ecoId: selectedEcoData.ecoId,
            status: 'approved',
            userId: userId,
            clientId: clientId,
          })
        );
      else funcToSetResponseMessage('error', 'Please Select at least one ECO');
    }
  }

  const functionsName = {
    handleApproval,
    handleReject,
    handleClose,
    handleSave,
    handleApprove,
  };

  const setSaveButtonFunction = (func) => {
    handleSaveButton = func; // Capture the child's function
  };

  return (
    <>
      {ecoForm?.length ? (
        <>
          <div className="pull-right view-switcher">
            <div
              className="service-request-launcher fr"
              onClick={() => {
                setShowApproverModal(true);
              }}
            >
              <i className="icon-user-check mr8"></i>
              <label className="label">Approvers</label>
            </div>
          </div>
          <ECRECOApprovers
            isViewOnly
            userId={userId}
            ecoId={ecoId}
            modalOpen={showApproverModal}
            handleModalClose={(flag) => setShowApproverModal(flag)}
            approvalFormData={ecoForm.find(
              (entry) => entry.uniqueKey === 'approvalForm'
            )}
          />
        </>
      ) : (
        ''
      )}
      <ECRECOHeader listValues={listValues} noOfFields={5} />
      <div className="ecr-eco-main-container">
        {showPopup ? (
          <RejectModal
            rejectClick={() => {
              if (rejectReason?.trim().length > 0) {
                dispatch(
                  updateECOStatusListDetails({
                    status: 'rejected',
                    userId: userId,
                    clientId: clientId,
                    flag: 'N',
                    payload: [
                      { ...selectedEcoData, rejectionReason: rejectReason },
                    ],
                  })
                );
                setErrorFlag(false);
                setShowPopup(false);
                setRejectReason('');
              } else setErrorFlag(true);
              setErrorMessage(null);
            }}
            isModalOpen={showPopup}
            isModalClose={() => {
              setShowPopup(false);
              setErrorFlag(false);
              setRejectReason('');
              setErrorMessage(null);
            }}
            onRejectCommentsChangeHandler={(e) => {
              let isValid = formValidationUtil.validateInputMaxLengthStatic(
                e,
                500
              );
              setErrorFlag(isValid.errorFlag);
              setErrorMessage(isValid.errorMessage);
              if (!isValid.errorFlag) {
                setRejectReason(e.target.value);
              }
            }}
            rejectComments={rejectReason}
            errorFlag={errorFlag}
            errorMessage={errorMessage}
          />
        ) : (
          ''
        )}
        {ecoForm?.length ? (
          <Tabs
            activeKey={currentTab}
            tabBarStyle={{
              fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
            }}
            items={
              isPurchase
                ? [
                    {
                      key: String(1),
                      label: 'Purchase Notification Form',
                      children: (
                        <ECOPurchaseNotifForm
                          ecoId={ecoId}
                          setSaveButtonFunction={setSaveButtonFunction}
                        />
                      ),
                    },
                    ...ecoForm.map((item, index) => {
                      if (
                        !(
                          item.uniqueKey === 'partNoTable' ||
                          item.uniqueKey === 'approvalForm'
                        )
                      )
                        return {
                          key: String(index + 2),
                          label: item?.displayName,
                          children: handleTabs(item),
                        };
                    }),
                  ]
                : ecoForm.map((item, index) => {
                    if (
                      !(
                        item.uniqueKey === 'partNoTable' ||
                        item.uniqueKey === 'approvalForm'
                      )
                    )
                      return {
                        key: String(index + 1),
                        label: item?.displayName,
                        children: handleTabs(item),
                      };
                  })
            }
            onChange={(tabIndex) => setCurrentTab(tabIndex)}
          />
        ) : (
          ''
        )}
        {traceEvents?.length ? (
          <div>
            <div className=" page-sub-title">Events</div>
            <div className="boxed mb20">
              <Tabs
                activeKey={'1'}
                tabBarStyle={{
                  fontFamily: 'Roboto, Verdana, Arial, Tahoma, sans-serif',
                }}
                items={[
                  {
                    key: '1',
                    label: 'WorkFlow Events',
                    children: (
                      <TraceEvents
                        showRejectReason
                        externalHistoryFunction
                        externalHistoryValue={'pdm'}
                        key="traceEventsEditor"
                        enableComments={false}
                        data={traceEvents}
                        userId={userId}
                        onEnterKeyPress={() => null}
                        sendButton={() => null}
                        handleHistoryClick={(eventId, editFunction) =>
                          dispatch(
                            getECRECOTraceEventsHistory({
                              editFunction: editFunction,
                              eventId: eventId,
                              userId: userId,
                            })
                          )
                        }
                      />
                    ),
                  },
                ]}
              />
            </div>
          </div>
        ) : (
          ''
        )}
        <CommonSpinner visible={loading} />
        {selectedEcoData ? (
          <ButtonCommon
            tempButtonData={buttonCommonData}
            functionsName={functionsName}
          />
        ) : (
          ''
        )}
      </div>
    </>
  );
};

export { ECOViewPage };
