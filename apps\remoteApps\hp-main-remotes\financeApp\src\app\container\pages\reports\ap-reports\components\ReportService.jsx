/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable no-unused-vars */
/* eslint-disable no-console */
/* eslint-disable array-callback-return */
import cloneDeep from 'lodash.clonedeep';
import { globalutils } from '@hp/components';
import '@hp/styles/layout.scss';
import { Dbutils } from '../../../../../../../../../../../libs/hp-components/src/lib/common/module_dashboard/utils/DButils';

let themesColor = Dbutils.dashboardColors();

function chartDataCreation(mapData) {
  if (mapData) {
    let labelNames = [];
    let labelNamesWithId = [];
    let Colors = [];
    let dataCounts = [];
    let navigationLinks = [];
    let hoverDataSet = [];
    Object.entries(mapData).map((e) => {
      labelNamesWithId.push({ val: e[1].displayStatus, id: e[1].orderId });
      if (e[1].count !== 0) {
        dataCounts.push(e[1].count);
        Colors.push(e[1].colour);

        labelNames.push(e[1].displayStatus);
      }

      if (e[1].onHoverDataSet) {
        hoverDataSet.push({
          displayStatus: e[1].displayStatus,
          dataSet: e[1].displayText,
        });
      }

      navigationLinks.push(e[1].functionPath);
    });
    return {
      labelNames,
      Colors,
      dataCounts,
      navigationLinks,
      labelNamesWithId,
      hoverDataSet,
    };
  }
}

function searchFilter(options) {
  if (options) {
    return options && options.length
      ? options.map((autoFill) => {
          return {
            // required: id and value
            //id: option.name,
            //value: option.name,
            // optional: label, node
            // label: option.name, // use a custom label instead of the value
            // node: option.name, // use a custom ReactNode to display the option

            id: autoFill ? autoFill.commonId : '',
            value: autoFill ? autoFill.commonName : '',
          };
        })
      : [];
  }
}

function navigate(url, date, statusKey, payload) {
  var redirectURL = '';
  switch (statusKey) {
    case 'Invoices':
      var from = globalutils.formatDate(date.fromDate, 'mm-dd-yyyy');
      var to = globalutils.formatDate(date.toDate, 'mm-dd-yyyy');
      redirectURL = `${url}/${from}/${to}`;
      break;
    case 'Outstanding Invoice':
      redirectURL = url;
      break;
    case 'On Hold':
      redirectURL = url + '/' + 1;
      break;
    case 'Invoice Over Due':
      redirectURL = url + '/' + 1;
      break;
    default:
      break;
  }
  Dbutils.getRedirect(redirectURL, payload);
}

const customToolTip = function (context) {
  // Tooltip Element
  let tooltipEl = document.getElementById('chartjs-tooltip');
  let isTooltipHovered = false;
  // Create element on first render
  if (!tooltipEl) {
    tooltipEl = document.createElement('div');
    tooltipEl.id = 'chartjs-tooltip';
    tooltipEl.style.transition = 'opacity 0.3s ease-in-out'; // Add transition for opacity
    document.body.appendChild(tooltipEl);

    // Add a click event handler to the tooltip content
    //tooltipEl.addEventListener('click', handleTooltipClick);
    tooltipEl.addEventListener('mouseout', function (e) {
      // Check if the mouseout event is not leaving the tooltip itself or its content
      if (!isTooltipHovered && e.target !== tooltipEl) {
        tooltipEl.style.opacity = 0;
        isTooltipHovered = false;
      }
    });

    // Add a mouseover event to keep the tooltip visible on hover
    tooltipEl.addEventListener('mouseover', function () {
      tooltipEl.style.opacity = 1;
      isTooltipHovered = false;
    });
  }
  tooltipEl.style.opacity = 0;
  // Hide if no tooltip
  const tooltipModel = context.tooltip;
  if (tooltipModel.opacity === 0) {
    tooltipEl.style.opacity = 0;
    return;
  }

  // Set caret Position
  tooltipEl.classList.remove('above', 'below', 'no-transform');
  if (tooltipModel.yAlign) {
    tooltipEl.classList.add(tooltipModel.yAlign);
  } else {
    tooltipEl.classList.add('no-transform');
  }

  function getBody(bodyItem) {
    return bodyItem.lines;
  }
  function hoverDataSetView(titleLines, bodyLines) {
    const hoverData = context.chart.data.datasets[0].hoverDataSet;

    if (hoverData && hoverData.length) {
      const selectedIndex = context.tooltip.dataPoints[0].dataIndex;

      let innerHtml =
        '<div style="background-color: #fff; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); padding: 16px; width: 200px;">';
      innerHtml +=
        '<table style="width:100%; border-collapse: collapse; margin-bottom: 8px;">';

      hoverData[selectedIndex].dataSet.forEach((details, index) => {
        innerHtml += `<tr style="border-top:  ${
          index !== 0 ? '1px solid #ddd' : 'none'
        }; width:100%; font-size:12px">`;
        innerHtml += `<td style="padding: 8px; width:100%;  font-size:12px"" >${details.label}</td>`;
        innerHtml += `<td style="padding: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-weight: bold; width:100%; font-size:12px; " ">${details.value}</td>`;
        innerHtml += '</tr>';
      });

      innerHtml += '</table></div>';

      return innerHtml;
    } else {
      // Default tooltip
      let innerHtml =
        '<div style="display: flex; align-items: center; padding: 16px; width: 200px; background-color: #fff; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);">';
      innerHtml +=
        '<div style="width: 10px; border-radius: 50%; height: 10px; margin-right: 10px;  ' +
        context.tooltip.labelColors[0].backgroundColor +
        '"></div>';
      // titleLines.forEach(function (title) {
      //   innerHtml += '<div style="font-weight: bold;">' + title + '</div>';
      // });
      bodyLines.forEach(function (body) {
        innerHtml += '<div>' + body + '</div>';
      });

      return innerHtml;
    }
  }

  // Set Text
  if (tooltipModel.body) {
    const titleLines = tooltipModel.title || [];
    const bodyLines = tooltipModel.body.map(getBody);

    tooltipEl.innerHTML = hoverDataSetView(titleLines, bodyLines);

    // Add a timeout to delay the appearance for the animation effect
    setTimeout(function () {
      tooltipEl.style.opacity = 1;
    }, 100); // Adjust the delay as needed
  }

  const position = context.chart.canvas.getBoundingClientRect();

  // Display, position, and set styles for font
  tooltipEl.style.position = 'absolute';
  tooltipEl.style.left =
    position.left + window.pageXOffset + tooltipModel.caretX + 'px';
  tooltipEl.style.top =
    position.top + window.pageYOffset + tooltipModel.caretY + 'px';
  tooltipEl.style.pointerEvents = 'auto'; // Make the tooltip clickable
};
function barChartRedirect(url, payload) {
  Dbutils.getRedirect(url + '/' + 1, payload);
}

var barChartOptions = {
  indexAxis: 'y',
  dbName: 'apDashboard',
  responsive: true,
  maintainAspectRatio: false,
  layout: {
    padding: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
  },
  animation: {
    duration: 5000,
  },

  elements: {
    bar: {
      borderWidth: 1,
    },
  },

  scales: {
    x: {
      beginAtZero: true,
      min: 0,
      max: 100,
      grace: '5%',
      ticks: {
        stepSize: 20,
        callback: function (value) {
          return ((value / this.max) * 100).toFixed(0) + ''; // convert it to percentage
        },
      },
      grid: {
        display: false,
      },
    },

    y: {
      stacked: false,
      beginAtZero: true,
      max: 1000, // Set an appropriate maximum value for the y-axis
      callback: function (value) {
        // Display values on top of bars only if they are within a reasonable range
        return value <= 200 ? value : '';
      },

      grid: {
        display: false,
      },
    },
  },

  // responsive: true,
  // maintainAspectRatio: true,
  plugins: {
    legend: {
      display: false,
      position: 'bottom',
      labels: {
        usePointStyle: true,
        boxWidth: 6,
        boxHeight: 6,
      },
    },
    datalabels: {
      textAlign: 'center',
      padding: 17,
      stretch: 100,
      lineWidth: 10,
      color: '#fff',
      display: true,
      segment: true,
      font: {
        size: 13,
      },
      formatter: function (value, context) {
        // You can customize the label format if needed
        return value;
      },
      filter: function (value, context) {
        // Add any conditions here to filter labels if necessary
        return true;
      },
    },
    title: {
      display: false,
      text: 'Total Invoices',
    },
  },
  tooltips: {
    enabled: true,
  },
};
// invoice data
var barChartData = {
  labels: [],
  datasets: [
    {
      indexAxis: 'y',
      label: '',
      data: [],
      navigationLinks: [],
      fill: false,
      base: 0,
      barThickness: 50,
      maxBarThickness: 100,
      minBarLength: 0,
      backgroundColor:
        themesColor[0]?.AccPayDBColor?.barChartColor?.backgroundColor,
      borderColor: themesColor[0]?.AccPayDBColor?.barChartColor?.borderColor,
      borderWidth: 1,
      hoverDataSet: null,
    },
  ],
};

//Emails options
const pieChartOptions = {
  elements: {
    bar: {
      borderWidth: 0,
    },
    arc: {
      borderWidth: 0,
    },
  },
  onHover: (event, elements) => {
    if (elements.length > 0) {
      return;
    } else {
      let tooltipEl = document.getElementById('chartjs-tooltip');
      if (tooltipEl) {
        tooltipEl.style.opacity = 0;
      }
    }
  },
  hover: {
    animationDuration: 0,
  },
  scales: {
    x: {
      display: false,
      beginAtZero: true,
      min: 0,
      max: 100,
      grace: '5%',
      ticks: {
        stepSize: 20,
        callback: function (value) {
          return ((value / this.max) * 100).toFixed(0) + ''; // convert it to percentage
        },
      },
      grid: {
        display: false,
      },
    },

    y: {
      display: false,
      grid: {
        display: false,
      },
    },
  },
  responsive: true,
  maintainAspectRatio: false,
  cutout: 80,

  plugins: {
    datalabels: {
      textAlign: 'center',
      padding: 17,
      stretch: 100,
      lineWidth: 10,
      color: '#fff',
      display: true,
      segment: true,
      font: {
        size: 15,
      },
    },

    tooltip: {
      enabled: false,
      external: customToolTip,
    },

    legend: {
      display: true,
      position: 'bottom',
      labels: {
        usePointStyle: true,
        boxWidth: 6,
        boxHeight: 6,
        borderColor: themesColor[0]?.AccPayDBColor?.pieChartColor?.borderColor,
        pointStyle: 'circle',
        borderWidth: 5,
      },
    },
    title: {
      display: false,
      text: 'Total Emails',
    },
  },
};
//Emails data
var pieChartData = {
  labels: [],

  datasets: [
    {
      label: 'Count',
      data: [],
      navigationLinks: [],
      backgroundColor:
        themesColor[0]?.reportDBColor?.pieChartColor?.backgroundColor,
      // borderColor: themesColor[0]?.AccPayDBColor?.pieChartColor?.borderColor,
      hoverOffset: 4,
      fill: false,
      borderColor: 'white', // Set the borderColor for the pie segments
      borderWidth: 3, // Set the borderWidth for the pie segments
      hoverDataSet: null,
    },
  ],
};

function validationCheckForCharts(dashboardData) {
  try {
    dashboardData.map((dashBoard) => {
      dashBoard.charts.map((dbControl) => {
        switch (dbControl.type) {
          case 'CountList':
            dbControl.isLoading = false;
            break;
          case 'BarChart':
            dbControl.data.datasets[0].data = dbControl.chartData?.dataCounts;
            dbControl.data.datasets[0].hoverDataSet =
              dbControl.chartData?.hoverDataSet;
            dbControl.data.labels = dbControl.chartData?.labelNames;
            dbControl.data.navigationLinks =
              dbControl.chartData?.navigationLinks;
            dbControl.isLoading = false;
            break;
          case 'PieChart':
            var labelNamesWithId = dbControl?.chartData?.labelNamesWithId;
            labelNamesWithId?.sort((a, b) => {
              return a.id - b.id;
            });
            var labelNames = labelNamesWithId.map((item) => item.val);
            dbControl.data.datasets[0].data = dbControl?.chartData?.dataCounts;
            dbControl.data.labels = dbControl.chartData?.labelNames;
            dbControl.data.datasets[0].hoverDataSet =
              dbControl?.chartData?.hoverDataSet;
            dbControl.data.labels = labelNames;
            dbControl.data.navigationLinks =
              dbControl.chartData?.navigationLinks;
            dbControl.isLoading = false;
            break;
          // case 'dbCount':
          //   dbControl.data.datasets[0].data = dbControl.chartData?.dataCounts;
          //   dbControl.data.datasets[0].hoverDataSet =
          //     dbControl.chartData?.hoverDataSet;
          //   dbControl.data.labels = dbControl.chartData?.labelNames;
          //   dbControl.data.navigationLinks =
          //     dbControl.chartData?.navigationLinks;
          //   dbControl.isLoading = false;
          //   break;

          default:
            break;
        }
      });
    });
  } catch (error) {
    console.log(error);
  }

  return dashboardData;
}

function reportDynamicCharts(charts, userDBDetails) {
  const chartOptions = {
    PieChart: pieChartOptions,
    BarChart: barChartOptions,
  };
  const chartDataSet = {
    PieChart: pieChartData,
    BarChart: barChartData,
  };
  const colType = {
    'three-col': 'single-col-layout mb40',
    'two-col': 'two-col-layout mb40',
  };
  var dashboardCharts = [];
  try {
    if (charts && charts?.length) {
      charts = charts.sort((a, b) => a.id - b.id);

      const groupedCharts = [];

      for (let i = 0; i < charts.length; i++) {
        const dbChart = charts[i];
        const key = colType[dbChart.columnType];
        let found = false;

        // Check if there is an existing group with the same key
        for (let j = 0; j < groupedCharts.length; j++) {
          if (groupedCharts[j].layoutClassName === key) {
            groupedCharts[j].charts.push({
              type: dbChart.type,
              heading: dbChart.heading,
              id: dbChart.id,
              size: { width: 200, height: 340 },
              options: cloneDeep(chartOptions[dbChart.type]),
              data: cloneDeep(chartDataSet[dbChart.type]),
              chartData: cloneDeep(chartDataCreation(dbChart.value)),
              isLoading: true,
              isSearchEnable: false,
            });
            found = true;
            break;
          }
        }

        // If no existing group, create a new one
        if (!found) {
          groupedCharts.push({
            layoutClassName: key,
            charts: [
              {
                type: dbChart.type,
                heading: dbChart.heading,
                id: dbChart.id,
                size: { width: 200, height: 500 },
                options: cloneDeep(chartOptions[dbChart.type]),
                data: cloneDeep(chartDataSet[dbChart.type]),
                chartData: cloneDeep(chartDataCreation(dbChart.value)),
                isLoading: true,
                isSearchEnable: false,
              },
            ],
          });
        }
      }
      // for (let i = 0; i < charts.length; i += 3) {
      //   let chunk = charts.slice(i, i + 3);
      //   let layoutClassName = "three-col-layout-60";
      //   chunk = await chunk.map((item) => {
      //     return {
      //       type: item?.type,
      //       heading: item?.heading,
      //       id: item?.id,
      //       size: { width: 200, height: 340 },
      //       options: cloneDeep(chartOptions[item.type]),
      //       data: cloneDeep(chartDataSet[item.type]),
      //       chartData: cloneDeep(chartDataCreation(item.value)),
      //       isLoading: true,
      //       isSearchEnable: false,
      //     }
      //   });

      //   dashboardCharts.push({
      //     layoutClassName: layoutClassName,
      //     charts: chunk,
      //   });

      // }

      var validResponse = validationCheckForCharts(groupedCharts);

      console.log(validResponse);
    }
  } catch (error) {
    console.log(error);
  }

  return validResponse;
}

function barchartDataViewer(formType) {
  const barLength = formType.data.datasets[0].data;
  const barLabels = formType.data.labels;
  const barPagination = [];

  if (barLength?.length > 7) {
    for (let i = 0; i < barLength.length; i += 7) {
      let barChunk = barLength.slice(i, i + 7);
      let lableChunk = barLabels.slice(i, i + 7);

      barChunk = barChunk.map((data) => {
        return data;
      });
      lableChunk = lableChunk.map((label) => {
        return label;
      });

      barPagination.push({
        bars: barChunk,
        labels: lableChunk,
      });
    }
    return barPagination;
  } else {
    return formType;
  }
}

export const ReportService = {
  barChartOptions,
  pieChartOptions,
  pieChartData,
  barChartData,
  reportDynamicCharts,
};
