import { Route, Routes } from 'react-router-dom';
import {
  CreateUser,
  CreateUserMain,
} from './container/useraccounts/createuser-main';
import { EditUser } from './container/useraccounts/editusers';
import { EditUserTabs } from './container/useraccounts/editusers/EditUserTabs';
import {
  CreateRoles,
  MenuManagement,
  ResetPassword,
  UserRoleConfig,
  EditAndSaveRoles,
} from './container/useraccounts';
import { ButtonCreation } from './container/useraccounts/buttons/ButtonCreation';
import {
  AccountPolicySettings,
  Application,
  AssignApp,
  Client,
  DateAndTime,
  Departments,
  Divisions,
  Entities,
  JobCode,
  Modules,
  UserSession,
} from './container/settings';
import { FailedFiles } from './container/alerts/FailedFiles';
import { FTP } from './container/alerts/diagnostic/FTP';
import Email from './container/alerts/diagnostic/Email';
import { ChangePassword, Preferences } from './container/profile';
import { AdminEmailInbox } from './container/emails/AdminEmailInbox';
import { Outbox, Sent } from './container/emails';
import {
  ErrorBoundary,
  // DynamicRoute
} from '@hp/components';

export function App() {
  return (
    <ErrorBoundary>
      {' '}
      <Routes>
        {/* <Route path="/:submenu/" element={<DynamicRoute />} /> */}
        <Route
          exact
          path="/:submenu/CreateUser/:parameters"
          element={<CreateUserMain />}
        />
        <Route
          exact
          path="/:submenu/EditUser/:parameters"
          element={<EditUser />}
        />
        <Route
          exact
          path="/:submenu/EditUser/edit-users/:userId"
          element={<EditUserTabs />}
        />
        <Route
          exact
          path="/:submenu/ResetPassword/:parameters"
          element={<ResetPassword />}
        />
        <Route
          exact
          path="/:submenu/UserRoleConfig/:parameters"
          element={<UserRoleConfig />}
        />
        <Route
          exact
          path="/:submenu/application/:parameter"
          element={<Application />}
        />
        <Route
          exact
          path="/:submenu/modules/:parameter"
          element={<Modules />}
        />
        <Route
          exact
          path="/:submenu/UserSession/:parameter"
          element={<UserSession />}
        />
        <Route
          exact
          path="/:submenu/org,clientSetup/:parameter"
          element={<Client />}
        />
        <Route
          exact
          path="/:submenu/Date&Time/:parameters"
          element={<DateAndTime />}
        />
        <Route
          exact
          path="/:submenu/org,assignApp/:parameter"
          element={<AssignApp />}
        />
        <Route
          path="/:submenu/org,entities/:parameter"
          element={<Entities />}
        />
        <Route
          exact
          path="/:submenu/org,departments/:parameter"
          element={<Departments />}
        />
        <Route
          exact
          path="/:submenu/org,divisions/:parameter"
          element={<Divisions />}
        />
        <Route
          exact
          path="/:submenu/org,jobCode/:parameter"
          element={<JobCode />}
        />
        <Route
          exact
          path="/:submenu/AuthenticationPolicy/:parameters"
          element={<AccountPolicySettings />}
        />
        <Route
          exact
          path="/:submenu/Alerts/failed-files"
          element={<FailedFiles />}
        />
        <Route
          exact
          path="/:submenu/diagnostic/diagnostic-ftp"
          element={<FTP />}
        />
        <Route
          exact
          path="/:submenu/diagnostic/diagnostic-email"
          element={<Email />}
        />
        <Route
          exact
          path="/:submenu/profile/chg-pwd"
          element={<ChangePassword />}
        />
        <Route
          exact
          path="/:submenu/profile/preferences"
          element={<Preferences />}
        />
        <Route
          exact
          path="/:submenu/Emails/inbox"
          element={<AdminEmailInbox />}
        />
        <Route exact path="/:submenu/Emails/sent" element={<Sent />} />
        <Route exact path="/:submenu/Emails/outbox" element={<Outbox />} />
        <Route
          exact
          path="/:submenu/CreateUser/create-users/:clientId/:userId"
          element={<CreateUser />}
        />
        <Route
          exact
          path="/:subMenu/Buttons/:parameters"
          element={<ButtonCreation />}
        />
        <Route
          exact
          path="/:subMenu/CreateRolesTable/:parameters"
          element={<CreateRoles />}
        />
        <Route
          exact
          path="/:subMenu/MenuManagement/:parameters"
          element={<MenuManagement />}
        />
        <Route
          exact
          path="/:subMenu/EditAndSaveRoles/:parameters/:module"
          element={<EditAndSaveRoles />}
        />
      </Routes>
    </ErrorBoundary>
  );
}
export default App;
