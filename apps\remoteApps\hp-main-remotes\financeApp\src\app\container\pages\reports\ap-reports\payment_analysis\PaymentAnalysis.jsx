/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';

import '../ApReportStyle.scss';
import { useDispatch, useSelector } from 'react-redux';
import { getAPReports } from '@hp/mainstore';
import { useAppRouterDom } from '@hp/utils';
import { globalutils } from '@hp/components';

const PaymentAnalysis = (props) => {
  const { apReportDTO } = useSelector((state) => state.accpay);
  const dispatch = useDispatch();
  const { domParameters } = useAppRouterDom();
  const urlParam = domParameters?.parameters || '';
  let user = globalutils.getDataFromStorage('all');
  const userId = parseInt(user?.userId);
  const clientId = user?.clientId;
  const [reportData, setReportData] = useState();
  const [graphData, setGraphData] = useState();

  useEffect(() => {
    dispatch(
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'subscribe',
        fromDate: '',
        toDate: '',
      })
    );
    return () => {
      getAPReports({
        para: urlParam,
        clientId: clientId,
        userId: userId,
        type: 'unsubscribe',
        fromDate: '',
        toDate: '',
      });
    };
  }, [urlParam]);

  useEffect(() => {
    if (apReportDTO && apReportDTO.value) {
      setReportData(apReportDTO.value.listView);
      setGraphData(apReportDTO.value.graphDetails);
    }
  }, [apReportDTO]);

  return (
    <>
      <div className="page-sub-title">Payment Analysis</div>
      <div className="report-body-main">
        <div className="report-body-main card mr20 ">
          {/* {reportData ? <ReportDataList data={reportData} /> : ''} */}
        </div>
        <div className="report-graph-body ">
          {/* {graphData ? <ReportGraph data={graphData} /> : ''} */}
        </div>
      </div>
    </>
  );
};
export { PaymentAnalysis };
