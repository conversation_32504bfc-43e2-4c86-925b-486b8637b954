/* eslint-disable react/jsx-key */
/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * <AUTHOR> sherlin
 * @email <EMAIL>
 */
import React, { useState, useLayoutEffect } from 'react';
import { Input, Button, useConfirm } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';
import { CommonSpinner } from '@hp/components';
import { dataTableServiceProvider } from '@hp/components';
import DistributorFormActions from './Distributor-form-actions';
import {
  bbForms,
  ClientList,
  bbMPNviewSearchSubList,
  bbMPNviewSearch,
  bbMPNDelete,
} from '@hp/mainstore';
const DistributorView = () => {
  const dispatch = useDispatch();
  const { confirm } = useConfirm();
  const {
    bbFormDetails,
    mpnAddSuccess,
    loading,
    mpnFiles,
    mpnSubFiles,
    mpnEDITSuccess,
    MPNDelete,
  } = useSelector((store) => store.bb);
  //=========================================Local States Declaration Section Begins ===========================================//
  const [formInputs, setFormInputs] = useState([]);
  const [action, setAction] = useState('');

  const [isSearch, setIsSearch] = useState({
    isSearchDisabled: true,
    isClientValue: '',
    isDateValue: '',
    addOnchange: false,
  });
  const [addForm, setAddForm] = useState(false);
  const [productFileMasterId, setProductFileMasterId] = useState(null);
  const [conditionalRowStyles, setConditionalStyles] = useState([]);
  const [productDetailId, setProductDetailId] = useState('');

  //=========================================Local States Declaration Section Ends===========================================//
  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Component did mount First call for this component.
     */

    dispatch(bbForms('subscribe'));
    dispatch(ClientList('subscribe'));
    return () => {
      dispatch(bbForms('unSubscribe'));
      dispatch(ClientList('unSubscribe'));
    };
  }, []);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: form response.
     */
    if (bbFormDetails) {
      setFormInputs(bbFormDetails.value);
    }
  }, [bbFormDetails]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: getting Main list based on client Search.
     */
    if (mpnFiles?.value) {
      const stateValues = [...formInputs];
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'mpn_file_table'
      );
      if (foundIndex !== -1) {
        if (mpnFiles?.value && mpnFiles?.value.length) {
          stateValues[foundIndex].value = mpnFiles.value;
        } else {
          let subIndex = stateValues.findIndex(
            (e) => e.uniqueKey === 'mpndetails'
          );
          stateValues[foundIndex].value = [];
          stateValues[subIndex].value = [];
        }

        setFormInputs(stateValues);
        setProductFileMasterId(null);
      }
    }
  }, [mpnFiles]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: getting Sublist based on client Search.
     */
    if (mpnSubFiles?.value) {
      const stateValues = [...formInputs];
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'mpndetails'
      );
      if (foundIndex !== -1) {
        stateValues[foundIndex].value = mpnSubFiles.value;
        setFormInputs(stateValues);
      }
    }
  }, [mpnSubFiles]);
  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Clearing the values in add Dialogbox.
     */

    if (!addForm) {
      const stateValues = [...formInputs];
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'mpn-sub-table'
      );
      if (foundIndex !== -1) {
        stateValues[foundIndex].formSubDetailsInternalDTOList.map(
          (el) => (el.value = '')
        );
      }

      setFormInputs(stateValues);
    }
  }, [addForm]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description:subTable Refresh.
     */
    if (mpnAddSuccess) {
      setAddForm(false);
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
  }, [mpnAddSuccess]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description:subTable Refresh.
     */
    if (mpnEDITSuccess) {
      setAddForm(false);
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
  }, [mpnEDITSuccess]);

  useLayoutEffect(() => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description:subTable Refresh.
     */
    if (MPNDelete) {
      setAddForm(false);
      dispatch(
        bbMPNviewSearchSubList({
          masterID: productFileMasterId,
          reqType: 'subscribe',
        })
      );
    }
  }, [MPNDelete]);
  const editHandler = (data) => {
    /**
     * <AUTHOR> Augustine
     * @email <EMAIL>
     * @description: Function to edit row data.
     */
    const formData = formInputs
      .filter((el) => el.uniqueKey === 'mpn-sub-table')[0]
      .formSubDetailsInternalDTOList?.map((list) => {
        if (list.uniqueKey === 'mpn_no') {
          return { ...list, value: data.mpn };
        } else if (list.uniqueKey === 'ipn_no') {
          return { ...list, value: data.productName };
        } else if (list.uniqueKey === 'mfg') {
          return { ...list, value: data.mfgName };
        } else if (list.uniqueKey === 'stdprice') {
          return { ...list, value: data.stdPrice };
        } else if (list.uniqueKey === 'desc') {
          return { ...list, value: data.description };
        }
      });

    let newForm = formInputs.map((form) => {
      if (form.uniqueKey === 'mpn-sub-table') {
        return { ...form, formSubDetailsInternalDTOList: formData };
      } else return form;
    });

    setFormInputs(newForm);
    setProductDetailId(data.productFileDetailsId);
    setAddForm(true);
  };
  const deleteHandler = async (data) => {
    /**
     * <AUTHOR> Augustine
     * @email <EMAIL>
     * @description: Function to delete row data.
     */

    const isConfirmed = await confirm('Are you sure you want to delete');
    if (isConfirmed) dispatch(bbMPNDelete(data.productFileDetailsId));
  };

  const forms = (formControl) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function to Render Form Element.
     */
    return formControl
      ? formControl.map((element, index) => {
          if (element.uniqueKey === 'page-title') {
            return (
              <>
                <Input key={index} formType={element} />
                <div className="flex-row">
                  <div className="three-col-layout flex-row">
                    {forms(element.formSubDetailsInternalDTOList)}
                  </div>
                </div>
              </>
            );
          } else {
            if (element.uniqueKey === 'mpndetails') {
              return (
                <>
                  {productFileMasterId != null && mpnFiles ? (
                    <div style={{ float: 'right', marginBottom: 10 + 'px' }}>
                      <Button
                        className="small mb8 outline add-button-custom flex-row vam"
                        onClick={() => {
                          setProductDetailId('');
                          setAddForm(true);
                          setAction('add');
                        }}
                      >
                        <i className="icon-add-button "> </i>Add
                      </Button>
                    </div>
                  ) : (
                    ''
                  )}
                  <Input
                    formType={element}
                    dataTableEventHandler={(e) => handleRowClick(e)}
                    conditionalRowStyles={conditionalRowStyles}
                    // isCopy={true}
                    dataTableColumn={
                      element.formSubDetailsInternalDTOList &&
                      Array.isArray(element.formSubDetailsInternalDTOList) &&
                      element.formSubDetailsInternalDTOList.length
                        ? element.formSubDetailsInternalDTOList.map((value) => {
                            return {
                              width: value.displayWidth
                                ? value.displayWidth
                                : '',
                              name: value.displayName ? value.displayName : '',
                              selector: value.selector ? value.selector : '',
                              cell:
                                value.selector === 'editIcon'
                                  ? function displayCell(row) {
                                      return (
                                        <div
                                          className="icon-edit-button"
                                          onClick={() => {
                                            editHandler(row);
                                            setAction('edit');
                                          }}
                                        ></div>
                                      );
                                    }
                                  : value.selector === 'deleteIcon'
                                  ? function displayCell(row) {
                                      return (
                                        <div
                                          className="icon-2-trash"
                                          onClick={() => {
                                            deleteHandler(row);
                                          }}
                                        ></div>
                                      );
                                    }
                                  : '',
                            };
                          })
                        : []
                    }
                  />
                </>
              );
            }

            return (
              <Input
                onChangeHandler={(type, event) =>
                  handlers[element.onChangeFunction](type, event, 'search')
                }
                // isCopy={true}
                dataTableEventHandler={handlers.handleRowClick}
                conditionalRowStyles={conditionalRowStyles}
                isEditable="notShowing"
                formType={element}
              />
            );
          }
        })
      : null;
  };

  const onChangeHandler = (type, event, action) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for user input content change.
     */

    const stateValues = [...formInputs];
    if (action === 'add') {
      let foundIndex = stateValues.findIndex(
        (e) => e.uniqueKey === 'mpn-sub-table'
      );
      if (!foundIndex !== -1) {
        const subIndex = stateValues[
          foundIndex
        ].formSubDetailsInternalDTOList.findIndex(
          (e) => e.uniqueKey === type.uniqueKey
        );
        stateValues[foundIndex].formSubDetailsInternalDTOList[subIndex].value =
          event.target.value;
      }
    } else {
      if (type.uniqueKey === 'client_control') {
        stateValues[0].formSubDetailsInternalDTOList[0].value =
          event.target.value;
      } else if (type.uniqueKey === 'date_control') {
        stateValues[0].formSubDetailsInternalDTOList[1].value = event;
      }
      var isClient = stateValues[0].formSubDetailsInternalDTOList[0].value;
      var isDate = stateValues[0].formSubDetailsInternalDTOList[1].value;
    }

    if (isClient && isDate) {
      const searchButtonIndex =
        stateValues[0].formSubDetailsInternalDTOList.findIndex(
          (el) => el.uniqueKey === 'button-search'
        );
      const clearButtonIndex =
        stateValues[0].formSubDetailsInternalDTOList.findIndex(
          (el) => el.uniqueKey === 'clear-button'
        );
      if (searchButtonIndex !== -1) {
        stateValues[0].formSubDetailsInternalDTOList[
          searchButtonIndex
        ].disableFlag = 'F';
      }
      if (clearButtonIndex !== -1) {
        stateValues[0].formSubDetailsInternalDTOList[
          clearButtonIndex
        ].disableFlag = 'F';
      }

      setIsSearch({
        ...isSearch,
        isSearchDisabled: false,
        isClientValue: isClient,
        isDateValue: isDate,
      });
    }
    setFormInputs(stateValues);
  };

  const handleRowClick = (type, event) => {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for dataTable Row click.
     */
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      type.serialNo,
      'serialNo'
    );
    setConditionalStyles(styleAttribute);
    setProductFileMasterId(type.productFileMasterId);

    dispatch(
      bbMPNviewSearchSubList({
        masterID: type.productFileMasterId,
        reqType: 'subscribe',
      })
    );
  };

  function searchMPN() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: Function for Search.
     */
    if (!isSearch.isSearchDisabled) {
      dispatch(
        bbMPNviewSearch({
          clientID: isSearch.isClientValue,
          uploadedOn: isSearch.isDateValue,
          reqType: 'subscribe',
        })
      );
    }
  }
  function onClearFunction() {
    /**
     * <AUTHOR> sherlin
     * @email <EMAIL>
     * @description: to clear all selected controls.
     */
    if (!isSearch.isSearchDisabled) {
      var updatedState = [...formInputs];
      updatedState.map((input) => {
        input.value = null;
        input.formSubDetailsInternalDTOList.map((subInput) => {
          subInput.value = null;
          if (subInput.uniqueKey === 'button-search') {
            subInput.disableFlag = 'T';
          }
          if (subInput.uniqueKey === 'clear-button') {
            subInput.disableFlag = 'T';
          }
        });
      });
    }
  }

  const handlers = {
    onChangeHandler,
    handleRowClick,
    searchMPN,
    onClearFunction,
  };

  return (
    <>
      {/* Loader for entire Component */}
      <CommonSpinner visible={loading} />

      {/* rendering Form Controls */}
      {formInputs && formInputs.length ? forms(formInputs) : ''}

      {/* child component for ADD UPDATE DELETE in Uploaded MPN LIST*/}
      <DistributorFormActions
        isClose={() => setAddForm(!addForm)}
        isOpen={addForm}
        formInputs={formInputs}
        id={productDetailId}
        productFileMasterId={productFileMasterId}
        eventHandlers={handlers}
        action={action}
      />
    </>
  );
};
export { DistributorView };
