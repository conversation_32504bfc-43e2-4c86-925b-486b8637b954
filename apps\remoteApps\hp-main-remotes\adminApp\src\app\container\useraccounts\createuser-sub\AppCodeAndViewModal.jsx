/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { Select, Button } from '@hp/components';
import { useDispatch, useSelector } from 'react-redux';

import { getAppNamesCombo } from '@hp/mainstore';
const AppCodeAndViewModal = (props) => {
  const dispatch = useDispatch();
  const { appNamesCombo } = useSelector((store) => store.admin);
  const [appNames, setAppNames] = useState();
  const [appNameForTable, setAppNameForTable] = useState();
  const [viewTypeForTable, setViewTypeForTable] = useState();

  useEffect(() => {
    getAppComboBox();
  }, []);

  useEffect(() => {
    if (appNamesCombo && appNamesCombo !== undefined) {
      const appOptions = appNamesCombo.map((value) => {
        return {
          value: value.appCode,
          display: value.appName,
        };
      });
      setAppNames(appOptions);
    }
  }, [appNamesCombo]);

  const getAppComboBox = () => {
    dispatch(getAppNamesCombo());
  };

  const getAppName = (e) => {
    let appCode = e.target.value;
    setAppNameForTable(appCode);
  };

  const getView = (e) => {
    let viewType = e.target.value;
    setViewTypeForTable(viewType);
  };

  const onSaveClick = () => {
    if (appNameForTable && viewTypeForTable !== undefined) {
      props.onSavePassAppNameAndViewType(appNameForTable, viewTypeForTable);
    }
  };

  return (
    <div>
      <div className="two-col-layout">
        <div className="col">
          <Select
            label="Application Name"
            className="mb20"
            onChange={(e) => getAppName(e)}
            options={appNames}
          />
        </div>

        <div className="col">
          <Select
            label="Select View"
            className="mb20"
            onChange={(e) => getView(e)}
            options={[
              {
                value: 'User Based View',
                display: 'User Based View',
              },
              {
                value: 'Full View',
                display: 'Full View',
              },
            ]}
          />
        </div>
      </div>
      <Button className="mr16 default" onClick={onSaveClick}>
        Save
      </Button>
    </div>
  );
};
export { AppCodeAndViewModal };
