/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react';
import <PERSON>ailHand<PERSON> from './EmailHandler';
import { useDispatch, useSelector } from 'react-redux';
import { accPayConstants } from '@hp/mainstore';
import { CommonSpinner } from '@hp/components';
import { sendEmailReq } from '@hp/mainstore';

function EmailToRequestor({ modalClose, templateId, invId }) {
  const { emailTemplateForm, emailError } = useSelector(
    (state) => state.accpay
  );

  const [isLoading, setIsLoading] = useState(true);
  const dispatch = useDispatch();

  useEffect(() => {
    if (emailTemplateForm) {
      setIsLoading(false);
    }

    return () => {
      dispatch({
        type: accPayConstants.GET_EMAIL_TEMPLATE_SUCCESS,
        payload: null,
      });
    };
  }, [emailTemplateForm]);

  useEffect(() => {
    if (emailError) {
      modalClose();
      dispatch({
        type: accPayConstants.GET_EMAIL_TEMPLATE_FAILURE,
        payload: null,
      });
    }
  }, [emailError]);

  const sendMailHandler = (data) => {
    let obj = {
      subject: data?.subject || '',
      content: data?.content || '',
      toAddressList: data?.toAddressList || [],
      ccList: data?.ccList || [],
      templateId,
      txnId: invId,
    };
    setIsLoading(true);
    dispatch(sendEmailReq(obj));
  };

  return (
    <>
      <CommonSpinner visible={isLoading} />
      <EmailHandler
        emailTemplate={
          emailTemplateForm?.value ? emailTemplateForm.value : null
        }
        carbonCopy={true}
        mailData={(data) => sendMailHandler(data)}
        cancel={() => modalClose()}
        emailAddressData={[
          {
            label: 'To',
            listName: 'toAddressList',
            isActive: true,
            address: [],
          },
          { label: 'Cc', listName: 'ccList', isActive: false, address: [] },
        ]}
      />
    </>
  );
}

export default EmailToRequestor;
