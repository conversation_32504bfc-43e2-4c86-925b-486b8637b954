/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { showDialog } from '@hp/mainstore';
import { InboxOutlined } from '@ant-design/icons';
import { Upload } from 'antd';
import { DocumentViewer, useConfirm } from '@hp/components';
import { AP_file_url } from '@hp/constants';
import './ecr_upload.scss';

const { Dragger } = Upload;

const ECRUploadComponent = ({
  fileList,
  updatedFileList,
  handleDeleteAttachment,
}) => {
  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  const [filePath, setFilePath] = useState([]);
  const [showDoc, setShowDoc] = useState(false);
  const [hidePreview, setHidePreview] = useState(false);

  const props = {
    name: (file) => (file.attachmentId ? file.fileName : file.name),
    multiple: true,
    beforeUpload: (file) => {
      let fileType = file.name?.split('.').pop();
      let acceptedTypes = ['pdf', 'txt', 'png', 'pptx', 'xlsx', 'docx'];
      let isAccepted = acceptedTypes.some(
        (acceptedType) => acceptedType === fileType
      );
      if (!isAccepted)
        funcToSetResponseMessage('error', 'File type not supported');
      return isAccepted;
    },
    onPreview(file) {
      setFilePath(file.attachmentId ? `${AP_file_url + file.path}` : file?.url);
      setShowDoc(true);
    },
    customRequest({ file, onSuccess }) {
      setTimeout(() => {
        onSuccess('ok');
      }, 0);
    },
    async onRemove(file) {
      let res = await confirm('Are you sure you want to delete?');
      if (file.attachmentId && file.ecrIdOrecoId)
        handleDeleteAttachment(file.attachmentId, file.ecrIdOrecoId);
      return res;
    },
    onChange({ file, fileList }) {
      const { status } = file;
      if (status === 'done') {
        let objectURL = URL.createObjectURL(file?.originFileObj);
        updatedFileList(
          fileList.map((file) => {
            file.url = file?.url ?? objectURL;
            return file;
          })
        );
      } else if (status === 'uploading') {
        updatedFileList(fileList);
      } else if (status === 'removed') {
        updatedFileList(fileList);
        funcToSetResponseMessage('success', 'File Deleted Successfully');
      } else if (status === 'error') {
        funcToSetResponseMessage('success', 'File Upload Failed');
      }
    },
  };

  function funcToSetResponseMessage(type, resMessage) {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  }

  return (
    <div className="upload-main-containe">
      {showDoc ? (
        <div
          style={{ height: '670px' }}
          className={hidePreview ? 'hide-preview' : 'show-preview'}
          onAnimationEnd={() => {
            if (hidePreview) setShowDoc(false);
            setHidePreview(false);
          }}
        >
          <div
            style={{
              position: 'absolute',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              top: '120px',
              right: '45px',
              height: '40px',
              width: '40px',
              backgroundColor: '#323639',
            }}
          >
            <span
              className="icon-close"
              style={{ color: 'white', cursor: 'pointer' }}
              onClick={() => {
                setHidePreview(true);
              }}
            ></span>
          </div>

          <DocumentViewer
            fileURL={filePath}
            fileType={'iframe-blob'}
            iframeStyle={{ width: 100 + '%', height: 100 + '%' }}
          />
        </div>
      ) : (
        <div>
          <Dragger {...props} fileList={fileList} style={{ height: '600px' }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              Click or drag files to this area to upload
            </p>
          </Dragger>
        </div>
      )}
    </div>
  );
};
export { ECRUploadComponent };
