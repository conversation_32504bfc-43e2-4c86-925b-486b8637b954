/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';

const POSettledExpandableItems = (props) => {
  const { data, columns, innerTableData } = props;
  const [tableColumns, setTableColumns] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [invId, setInvId] = useState();

  useEffect(() => {
    if (props) {
      setTableColumns(columns);
      data.inv_id ? setInvId(data.inv_id) : null;
      if (innerTableData) {
        innerTableData.map(function (listData) {
          listData && listData.invId
            ? listData.invId === data.inv_id
              ? setTableData(listData.itemData)
              : null
            : null;
        });
      }
    }
  }, [innerTableData]);

  return (
    <div className="two-col-layout-20 expandable-items-custom-styling force-max-width styledDatatable">
      <DataTable
        key={invId}
        highlightOnHover={true}
        noHeader={true}
        striped={true}
        dense={true}
        columns={tableColumns}
        data={tableData}
      />
    </div>
  );
};
export default POSettledExpandableItems;
