/* eslint-disable no-undef */

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import {
  AdvancedSelect,
  Button,
  dataTableServiceProvider,
  TextInput,
} from '@hp/components';
import React, { useState, useEffect } from 'react';

import Modal from 'react-modal';
import { useSelector } from 'react-redux';

const ItemListModal = (props) => {
  const numberRegex = new RegExp(/^[+-]?\d+(\.\d+)?$/);
  const amountRegex = new RegExp(/^\d*(\.\d+)?$/);
  const {
    isModalOpen,
    isModalClose,
    isAddItem,
    itemData,
    onAddFlag,
    invId,
    poOrInvDto,
  } = props;
  const { accpayPoDetails, accpayTableSelected } = useSelector(
    (store) => store.accpay
  );
  const [serialNoList, setSerialNoList] = useState([]);
  const [serialNoErrorFlag, setSerialNoErrorFlag] = useState(false);
  const [serialNoErrorMsg, setSerialNoErrorMsg] = useState();

  const [quantityErrorFlag, setQuantityErrorFlag] = useState(false);
  const [taxAmountErrorFlag, setTaxAmountErrorFlag] = useState(false);
  const [unitErrorFlag, setUnitErrorFlag] = useState(false);
  const [taxErrorFlag, setTaxErrorFlag] = useState(false);
  const [descriptionFlag, setDescriptionFlag] = useState(false);
  const [hsnCodeErrorFlag, setHsnCodeErrorFlag] = useState(false);
  const [unitTypeErrorFlag, setUnitTypeErrorFlag] = useState(false);

  const [quantityErrorMsg, setQuantityErrorMsg] = useState();
  const [taxAmountErrorMsg, setTaxAmountErrorMsg] = useState();

  const [unitPriceErrorMsg, setUnitPriceErrorMsg] = useState();
  const [hsnCodeErrorMsg, setHsnCodeErrorMsg] = useState();
  const [unitTypeErrorMsg, setUnitTypeErrorMsg] = useState();

  const [descriptionMsg, setDescriptionMsg] = useState();
  const [amountErrorMsg, setAmountErrorMsg] = useState();

  const [amountErrorFlag, setAmountErrorFlag] = useState(false);
  const [unitPriceErrorFlag, setUnitPriceErrorFlag] = useState(false);
  const [validate, setValidate] = useState(true);

  const [advanceSelectForms, setAdvanceSelectForms] = useState([]);
  const lineArray = [];

  const [invItemsState, setInvItemsState] = useState({
    invId: invId,
    inv_item_id: '',
    description: '',
    unit: '',
    unitType: '',
    hsnCode: '',
    taxCode: '',
    amount: '',
    amountConverted: '',
    quantity: '',
    unitPriceConverted: '',
    taxAmountConverted: '',
    poItemId: '',
    serialNo: '',
    itemNumber: '',
    glMasterCode: { id: '', value: '' },
    dept: { id: '', value: '' },
  });

  useEffect(() => {
    if (accpayTableSelected && accpayTableSelected !== undefined) {
      if (
        accpayTableSelected.invObj &&
        accpayTableSelected.invObj !== undefined &&
        accpayTableSelected.invObj.length
      ) {
        const dataTable = accpayTableSelected.invObj.filter(
          (item) => item.type === 'DataTable'
        );

        const dataTableRows = dataTableServiceProvider.getDataTableRows(
          dataTable[0]
        );
        dataTableRows?.map((list) => {
          lineArray.push(list.serialNo);
        });
      }
    }
  }, [accpayTableSelected]);

  useEffect(() => {
    if (poOrInvDto?.length) {
      let lineItem = poOrInvDto.filter((dto) => dto.uniqueKey === 'itemList')[0]
        ?.formSubDetailsInternalDTOList;
      if (!lineItem?.length) return;

      let advSelectKeys = ['glCode', 'dept'];
      let advSelectForms = [];
      lineItem.map((item) => {
        if (advSelectKeys.includes(item.uniqueKey)) {
          advSelectForms.push({
            ...item,
            type: 'AdvancedSelect',
            className: 'mb20',
          });
        }
      });
      setAdvanceSelectForms(advSelectForms);
    }
  }, [poOrInvDto]);

  useEffect(() => {
    if (isAddItem === true) {
      setInvItemsState({
        invId: invId,
        inv_item_id: '',
        description: '',
        unit: '',
        taxCode: '',
        amount: '',
        amountConverted: '',
        quantity: '',
        unitPriceConverted: '',
        taxAmountConverted: '',
        serialNo: '',
        poItemId: '',
        unitType: '',
        hsnCode: '',
        glMasterCode: { id: '', value: '' },
        dept: { id: '', value: '' },
      });
    }
  }, [onAddFlag]);

  useEffect(() => {
    if (itemData && isAddItem === false) {
      setInvItemsState({
        invId: itemData?.invId,
        inv_item_id: itemData?.inv_item_id,
        description: itemData?.description,
        unit: itemData?.unit,
        taxCode: itemData?.taxCode,
        amount: itemData?.amount,
        amountConverted: itemData?.amountConverted,
        quantity: itemData?.quantity,
        unitPriceConverted: itemData?.unitPriceConverted,
        taxAmountConverted: itemData?.taxAmountConverted,
        serialNo: itemData?.serialNo,
        poItemId: itemData?.poItemId,
        unitType: itemData?.unitType,
        hsnCode: itemData?.hsnCode,
        glMasterCode: itemData?.glMasterCode,
        dept: itemData?.dept,
      });
      //setInvItemsState({ invItemsState: data });
    }
  }, [itemData]);

  useEffect(() => {
    var result = [];
    if (accpayPoDetails !== undefined) {
      accpayPoDetails?.value && accpayPoDetails?.value.poItemsDtoList
        ? accpayPoDetails.value.poItemsDtoList.map(function (data) {
            result.push({ serialNo: data.serialNo, quantity: data.quantity });
          })
        : '';
      setSerialNoList(result);
    }
  }, [accpayPoDetails]);

  const handleAllonItemList = (e, key) => {
    if (key === 'serialNo') {
      setSerialNoErrorFlag(!numberRegex.test(e.target.value));

      let targetValue = parseInt(e.target.value);

      if (
        lineArray.includes(e.target.value) ||
        lineArray.includes(targetValue)
      ) {
        setSerialNoErrorFlag(true);
        setSerialNoErrorMsg('Value already exists');
      } else if (!numberRegex.test(e.target.value)) {
        setSerialNoErrorFlag(true);
        setSerialNoErrorMsg('Enter Valid Number');
      } else {
        setSerialNoErrorFlag(false);
      }

      setInvItemsState({
        ...invItemsState,
        [e.target.name]: targetValue,
      });
      setValidate(serialNoErrorFlag);
    } else if (key === 'quantity') {
      setQuantityErrorFlag(!numberRegex.test(e.target.value));

      if (!numberRegex.test(e.target.value)) {
        setQuantityErrorMsg('Enter Valid Number');
      }

      let taxAmount;
      if (invItemsState.taxAmountConverted !== '') {
        taxAmount = Number(invItemsState.taxAmountConverted);
      } else {
        taxAmount = 0;
      }
      let totalAmount =
        e.target.value * invItemsState.unitPriceConverted + taxAmount;

      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
        amountConverted: Number(totalAmount).toFixed(2),
      });
      setValidate(quantityErrorFlag);
    } else if (key === 'unitPrice') {
      setUnitPriceErrorFlag(!amountRegex.test(e.target.value));
      if (!amountRegex.test(e.target.value)) {
        setUnitPriceErrorMsg('Enter Valid Number');
      }
      let taxAmount;
      if (invItemsState.taxAmountConverted !== '') {
        taxAmount = Number(invItemsState.taxAmountConverted);
      } else {
        taxAmount = 0;
      }

      let totalAmount = e.target.value * invItemsState.quantity + taxAmount;

      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
        amountConverted: Number(totalAmount).toFixed(2),
      });
      setValidate(unitPriceErrorFlag);
    } else if (key === 'taxAmount') {
      setTaxAmountErrorFlag(!amountRegex.test(e.target.value));
      if (!amountRegex.test(e.target.value)) {
        setTaxAmountErrorMsg('Enter Valid Number');
      }

      let beforeTaxAmount =
        invItemsState.quantity * invItemsState.unitPriceConverted;
      let afterTaxAmount = beforeTaxAmount + Number(e.target.value);
      let totalAmount;
      if (e.target.value !== '') {
        totalAmount = afterTaxAmount;
      } else {
        totalAmount = beforeTaxAmount;
      }
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
        amountConverted: Number(totalAmount).toFixed(2),
      });
      setValidate(taxAmountErrorFlag);
    } else if (key === 'description') {
      if (e.target.value !== '') {
        setDescriptionFlag(false);
      }
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
      });
    } else if (key === 'hsnCode') {
      if (e.target.value !== '') {
        setDescriptionFlag(false);
      }
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
      });
    } else if (key === 'unitType') {
      if (e.target.value !== '') {
        setDescriptionFlag(false);
      }
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
      });
    } else if (key === 'unit') {
      // setUnitErrorFlag(!numberRegex.test(e.target.value));
      if (e.target.value !== '') {
        setUnitErrorFlag(false);
      }
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
      });
      // setValidate(unitErrorFlag);
    } else if (key === 'taxCode') {
      if (e.target.value !== '') {
        setTaxErrorFlag(false);
      }

      // setTaxErrorFlag(!amountRegex.test(e.target.value));
      // if (!amountRegex.test(e.target.value)) {
      //   setTaxErrorMsg('Enter Valid Number');
      // }
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
      });
      // setValidate(taxErrorFlag);
    } else if (key === 'itemNumber') {
      setInvItemsState({
        ...invItemsState,
        [e.target.name]: e.target.value,
      });
    }
    // else if (key === 'amount') {
    //   setAmountErrorFlag(!amountRegex.test(e.target.value));
    //   if (!amountRegex.test(e.target.value)) {
    //     setTaxErrorMsg('Enter Valid Number');
    //   }
    //   setInvItemsState({
    //     ...invItemsState,
    //     [e.target.name]: e.target.value,
    //   });
    //   setValidate(amountErrorFlag);
    // }
  };

  const OnClickSave = () => {
    let serialNoEmpty = true;
    let descriptionEmpty = true;
    let hsnCodeEmpty = true;
    // let unitEmpty = true;
    let unitTypeEmpty = true;

    let quantityEmpty = true;
    let taxAmountConvertedEmpty = true;
    let unitPriceConvertedEmpty = true;

    if (invItemsState.serialNo === '') {
      setSerialNoErrorFlag(true);
      setSerialNoErrorMsg('Please enter a serial number');
      serialNoEmpty = true;
    } else if (!numberRegex.test(invItemsState.serialNo)) {
      setSerialNoErrorFlag(true);
      setSerialNoErrorMsg('Enter Valid Number');
      serialNoEmpty = true;
    } else {
      serialNoEmpty = false;
      setSerialNoErrorFlag(false);
    }
    if (invItemsState.description === '') {
      setDescriptionFlag(true);
      setDescriptionMsg('Please enter the description');
      descriptionEmpty = true;
    } else {
      descriptionEmpty = false;
      setDescriptionFlag(false);
    }
    if (invItemsState.hsnCode === '') {
      hsnCodeEmpty = true;
    } else {
      hsnCodeEmpty = false;
      setHsnCodeErrorFlag(false);
    }

    if (invItemsState.unitType === '') {
      unitTypeEmpty = true;
    } else {
      unitTypeEmpty = false;
      setUnitTypeErrorFlag(false);
    }

    if (invItemsState.quantity === '') {
      setQuantityErrorFlag(true);
      setQuantityErrorMsg('Please enter the quantity');
      quantityEmpty = true;
    } else if (!numberRegex.test(invItemsState.quantity)) {
      setQuantityErrorFlag(true);
      setQuantityErrorMsg('Enter Valid Number');
      quantityEmpty = true;
    } else {
      quantityEmpty = false;
      setQuantityErrorFlag(false);
    }
    if (invItemsState.taxAmountConverted === '') {
      setTaxAmountErrorFlag(true);
      setTaxAmountErrorMsg('Please enter a tax amount');
      taxAmountConvertedEmpty = true;
    } else if (!amountRegex.test(invItemsState.taxAmountConverted)) {
      setTaxAmountErrorFlag(true);
      setTaxAmountErrorMsg('Enter Valid Number');
      taxAmountConvertedEmpty = true;
    } else {
      taxAmountConvertedEmpty = false;
      setTaxAmountErrorFlag(false);
    }
    if (invItemsState.unitPriceConverted === '') {
      setUnitPriceErrorFlag(true);
      setUnitPriceErrorMsg('Please enter a unit price');
      unitPriceConvertedEmpty = true;
    } else if (!amountRegex.test(invItemsState.unitPriceConverted)) {
      setUnitPriceErrorFlag(true);
      setUnitPriceErrorMsg('Enter Valid Number');
      unitPriceConvertedEmpty = true;
    } else {
      unitPriceConvertedEmpty = false;
      setUnitPriceErrorFlag(false);
    }

    if (
      !unitPriceConvertedEmpty &&
      !taxAmountConvertedEmpty &&
      !quantityEmpty &&
      !descriptionEmpty &&
      !serialNoEmpty
    ) {
      props.onSaveItemListClick(invItemsState);

      setInvItemsState({
        invId: invId,
        inv_item_id: '',
        description: '',
        unit: '',
        taxCode: '',
        hsnCode: '',
        unitType: '',
        amount: '',
        amountConverted: '',
        quantity: '',
        unitPriceConverted: '',
        taxAmountConverted: '',
        serialNo: '',
        poItemId: '',
        glMasterCode: { id: '', value: '' },
        dept: { id: '', value: '' },
      });
    }
  };

  const onIsModalClose = () => {
    isModalClose();
    setInvItemsState({
      invId: invId,
      inv_item_id: '',
      description: '',
      unit: '',
      taxCode: '',
      amount: '',
      amountConverted: '',
      quantity: '',
      unitPriceConverted: '',
      taxAmountConverted: '',
      serialNo: '',
      poItemId: '',
      unitType: '',
      hsnCode: '',
      glMasterCode: { id: '', value: '' },
      dept: { id: '', value: '' },
    });
    setSerialNoErrorFlag(false);
    setQuantityErrorFlag(false);
    setTaxAmountErrorFlag(false);
    setUnitErrorFlag(false);
    setTaxErrorFlag(false);
    setDescriptionFlag(false);
    setUnitPriceErrorFlag(false);
  };

  const handleAdvancedSelectOnchange = (selectData, form, index) => {
    let temArr = [...advanceSelectForms];
    let selectVal = selectData
      ? { id: selectData.value, value: selectData.label }
      : { id: '', value: '' };

    temArr.splice(index, 1, { ...form, value: selectVal });

    setInvItemsState({ ...invItemsState, [form.selector]: selectVal });

    setAdvanceSelectForms(temArr);
  };
  return (
    <Modal
      className="ItemListModal"
      overlayClassName="ModalOverlay"
      ariaHideApp={false}
      isOpen={isModalOpen}
      style={{
        content: {
          padding: '0px',
        },
      }}
    >
      <div className="modal-close-sticky">
        <span
          onClick={onIsModalClose}
          className="modal-close icon-close"
        ></span>
      </div>
      <div style={{ padding: '0px 40px' }}>
        <h2 className="page-sub-title">Item List</h2>

        <div className="two-col-layout  mb20">
          <TextInput
            label="Line"
            name="serialNo"
            className="mb20"
            error={{
              flag: serialNoErrorFlag,
              message: serialNoErrorMsg,
            }}
            required={true}
            value={invItemsState.serialNo || ''}
            onChange={(e) => handleAllonItemList(e, 'serialNo')}
          />
          <TextInput
            label="Part #"
            name="itemNumber"
            className="mb20"
            value={invItemsState.itemNumber || ''}
            onChange={(e) => handleAllonItemList(e, 'itemNumber')}
          />
          <TextInput
            label="Description"
            name="description"
            className="mb20"
            value={invItemsState.description || ''}
            required={true}
            error={{
              flag: descriptionFlag,
              message: descriptionMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'description')}
          />
          <TextInput
            label="HSN Code"
            name="hsnCode"
            className="mb20"
            value={invItemsState.hsnCode || ''}
            // required={true}
            error={{
              flag: hsnCodeErrorFlag,
              message: hsnCodeErrorMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'hsnCode')}
          />

          <TextInput
            label="Quantity"
            name="quantity"
            className="mb20"
            value={invItemsState.quantity || ''}
            required={true}
            error={{
              flag: quantityErrorFlag,
              message: quantityErrorMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'quantity')}
          />
          <TextInput
            label="Unit Price"
            name="unitPriceConverted"
            className="mb20"
            value={invItemsState.unitPriceConverted || ''}
            required={true}
            error={{
              flag: unitPriceErrorFlag,
              message: unitPriceErrorMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'unitPrice')}
          />
          <TextInput
            label="Unit Type"
            name="unitType"
            className="mb20"
            value={invItemsState.unitType || ''}
            error={{
              flag: unitTypeErrorFlag,
              message: unitTypeErrorMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'unitType')}
          />
          <TextInput
            label="Tax Amount"
            name="taxAmountConverted"
            className="mb20"
            value={invItemsState.taxAmountConverted || ''}
            required={true}
            error={{
              flag: taxAmountErrorFlag,
              message: taxAmountErrorMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'taxAmount')}
          />
          <TextInput
            label="Tax Code"
            name="taxCode"
            className="mb20"
            value={invItemsState.taxCode || ''}
            onChange={(e) => handleAllonItemList(e, 'taxCode')}
          />
          <TextInput
            label="Amount"
            name="amountConverted"
            className="mb20"
            value={invItemsState.amountConverted || ''}
            disabled={true}
            error={{
              flag: amountErrorFlag,
              message: amountErrorMsg,
            }}
            onChange={(e) => handleAllonItemList(e, 'amount')}
          />

          {advanceSelectForms?.length
            ? advanceSelectForms?.map((form, index) => {
                return (
                  <AdvancedSelect
                    key={index}
                    label={form.displayName}
                    options={
                      form?.comboBoxOptions
                        ? form.comboBoxOptions.map((data) => {
                            return {
                              value: data.commonId || data.commonName || '',
                              label: data.commonCode,
                            };
                          })
                        : []
                    }
                    value={form.value}
                    onChange={(value) =>
                      handleAdvancedSelectOnchange(value, form, index)
                    }
                    onModal={true}
                    getLabel={true}
                  />
                );
              })
            : ''}
        </div>

        <div className="model-fixed-button-bar flex-right ">
          {isAddItem === true ? (
            <Button
              className="small default primary mr20 "
              onClick={OnClickSave}
            >
              Save
            </Button>
          ) : (
            <Button className="small default" onClick={OnClickSave}>
              Save
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export { ItemListModal };
