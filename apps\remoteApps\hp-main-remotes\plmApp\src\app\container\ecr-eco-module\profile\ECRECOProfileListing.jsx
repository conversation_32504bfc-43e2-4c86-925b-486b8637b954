/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */

/**
 * <AUTHOR> <PERSON><PERSON>
 * @email <EMAIL>
 * @create date 27-09-2024 09:40:13
 * @modify date 01-10-2024 12:20:40
 * @desc [description]
 */

import React, { useState, useEffect } from 'react';
import {
  Button,
  useConfirm,
  Input,
  CheckboxBtn,
  CommonSpinner,
  Select,
  TextInput,
} from '@hp/components';
import Modal from 'react-modal';
import { useDispatch, useSelector } from 'react-redux';
import cloneDeep from 'lodash.clonedeep';
import { pdmConstants } from '@hp/mainstore';
import { CategoryList } from '@hp/components';
import { useAppRouterDom } from '@hp/utils';
import {
  getECRECOProfileForm,
  getECRECOProfileFormByClient,
  showDialog,
  setECRECOProfileSelectedClient,
  deleteECRECOProfile,
  getECRECOProfileNames,
  createAsECRECOProfile,
  activateECRECOProfileInList,
  activateOrDeactivateECRECOProfile,
  setPageName,
  resetECRECOProfileNames,
  pdmResetStateField,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';
const ECRECOProfileListing = () => {
  const { domParameters, navigate } = useAppRouterDom();
  const menuData = domParameters?.menuData || '';
  const submenu = domParameters?.submenu || '';
  // const parameter = domParameters?.parameters || '';

  const { confirm } = useConfirm();
  const dispatch = useDispatch();
  const {
    ecrEcoProfileList,
    ecrEcoProfileSelectedClient,
    ecrEcoProfileNamesList,
    ecrEcoDeleteAction,
    ecrEcoProfileCreateAs,
    ecrEcoProfileModificationRes,
  } = useSelector((store) => store.pdm);
  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);

  let user = globalutils.getDataFromStorage('all');
  let userId = user.userId;
  let clientIdVal = user.clientId ?? null;
  const redirectPath = `/plm/pdm_settings/ecr_eco/profile/0`;
  const [clientId, setClientId] = useState(
    ecrEcoProfileSelectedClient ?? clientIdVal
  );
  const [formDetails, setFormDetails] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isModal, setIsModal] = useState(false);
  const [createAsOptions, setCreateAsOptions] = useState([]);
  const [profileId, setProfileId] = useState(null);
  const [newProfileName, setNewProfileName] = useState('');
  const [notify, setnotify] = useState(null);
  useEffect(() => {
    dispatch(setPageName(innerMenuName));
    let tempClientId = ecrEcoProfileSelectedClient ?? clientId;
    tempClientId
      ? dispatch(
          getECRECOProfileFormByClient({
            userId: userId,
            clientId: tempClientId,
          })
        )
      : dispatch(getECRECOProfileForm(userId));
  }, []);

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
  };

  const handleProceed = async (msg) => {
    const isConfirm = await confirm(msg);
    if (isConfirm) {
      dispatch(
        activateECRECOProfileInList({ profileId: profileId, userId: userId })
      );
    }
  };

  useEffect(() => {
    setLoading(false);
    if (ecrEcoProfileCreateAs?.value) {
      let tempType = ecrEcoProfileCreateAs.value.type;
      funcToSetResponseMessage(tempType, ecrEcoProfileCreateAs.value?.text);
      if (ecrEcoProfileCreateAs.value?.context === 'empty') {
        handleProceed(ecrEcoProfileCreateAs.value?.text);
      } else if (tempType === 'success')
        dispatch(
          getECRECOProfileFormByClient({ userId: userId, clientId: clientId })
        );
    }
    return () => {
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoProfileCreateAs' }));
    };
  }, [ecrEcoProfileCreateAs]);

  useEffect(() => {
    if (ecrEcoDeleteAction?.value) {
      let tempType = ecrEcoDeleteAction.value.type;
      funcToSetResponseMessage(tempType, ecrEcoDeleteAction.value?.text);
      if (ecrEcoDeleteAction.value?.context === 'empty') {
        handleProceed(ecrEcoDeleteAction.value?.text);
      } else if (tempType === 'success')
        dispatch(
          getECRECOProfileFormByClient({ userId: userId, clientId: clientId })
        );
      setProfileId(null);
    }

    return () => {
      dispatch(pdmResetStateField({ fieldName: 'ecrEcoDeleteAction' }));
    };
  }, [ecrEcoDeleteAction]);

  useEffect(() => {
    if (ecrEcoProfileModificationRes?.value) {
      let tempType = ecrEcoProfileModificationRes.value.type;
      funcToSetResponseMessage(
        tempType,
        ecrEcoProfileModificationRes.value?.text
      );
      if (ecrEcoProfileModificationRes.value?.context === 'empty') {
        handleProceed(ecrEcoProfileModificationRes.value?.text);
      } else if (tempType === 'success')
        dispatch(
          getECRECOProfileFormByClient({ userId: userId, clientId: clientId })
        );
      setProfileId(null);
    }

    return () => {
      dispatch(
        pdmResetStateField({ fieldName: 'ecrEcoProfileModificationRes' })
      );
    };
  }, [ecrEcoProfileModificationRes]);

  useEffect(() => {
    if (ecrEcoProfileList?.value) {
      setLoading(false);
      setFormDetails(ecrEcoProfileList.value);
    }
  }, [ecrEcoProfileList]);

  useEffect(() => {
    setLoading(false);
    if (ecrEcoProfileNamesList) {
      if (ecrEcoProfileNamesList?.value?.length) {
        let list = ecrEcoProfileNamesList.value.map((option) => {
          return {
            value: option.commonId,
            display: option.commonName ?? option.displayName,
          };
        });
        setCreateAsOptions(list);
        setIsModal(true);
      } else {
        funcToSetResponseMessage('info', 'No active profiles');
      }
    }
    return () => {
      dispatch(resetECRECOProfileNames());
    };
  }, [ecrEcoProfileNamesList]);

  const ecrEcoProfilePageHandler = (data, readOnly) => {
    navigate(`${data?.profileId}`, {
      state: { clientId, category: data?.categoryCode ?? null, readOnly },
    });
  };

  //function for setting notification
  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup,
        type,
        responseMessage: resMessage,
        canClose,
        autoHide,
      })
    );
  };

  //Function to confirm delete
  const handleDelete = async (obj) => {
    const isConfirmed = await confirm('Are you sure you want to delete?');
    if (isConfirmed) {
      setProfileId(obj.profileId);
      dispatch(
        deleteECRECOProfile({ profileId: obj.profileId, userId: userId })
      );
    }
  };
  const handleOnChange = (event, uniqueKey) => {
    let value = event.target.value;
    let tempArray = cloneDeep(formDetails);
    tempArray.map((element) => {
      if (element.uniqueKey === uniqueKey) {
        element.value = value;
      }
    });
    setClientId(value);
    setFormDetails(tempArray);
    setLoading(true);
    dispatch(getECRECOProfileFormByClient({ userId: userId, clientId: value }));
    dispatch(setECRECOProfileSelectedClient(value));
  };

  const onChangeHandlingFunctions = {
    handleOnChange,
  };

  const handleCreateAsSave = () => {
    if (!profileId || !newProfileName) {
      funcToSetResMessageInModal('info', 'Mandatory fields missing');
      return;
    } else {
      setnotify(null);
    }
    dispatch(
      createAsECRECOProfile({
        profileId: profileId,
        userId: userId,
        profileName: newProfileName,
      })
    );
    setIsModal(false);
  };

  // Function to Render Form Elements
  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.type === 'DataTable') {
            let columns =
              element.formTableListDetailsDtoList &&
              element.formTableListDetailsDtoList.map((val) => {
                return {
                  selector: val.selector,
                  width:
                    val.selector === 'editIcon' || val.selector === 'deleteIcon'
                      ? '4%'
                      : val.displayWidth ?? '',
                  name: val.displayName,
                  cell:
                    val.selector === 'editIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-edit-button"
                              onClick={() =>
                                ecrEcoProfilePageHandler(row, false)
                              }
                            ></div>
                          );
                        }
                      : val.selector === 'deleteIcon'
                      ? function displayCell(row) {
                          return (
                            <div
                              className="icon-2-trash"
                              onClick={() => handleDelete(row)}
                            ></div>
                          );
                        }
                      : val.selector === 'deactivateIcon'
                      ? function displayCell(row) {
                          return (
                            <CheckboxBtn
                              value={row.activeFlag ? row.activeFlag : 'N'}
                              checked={
                                row.activeFlag && row.activeFlag === 'Y'
                                  ? true
                                  : false
                              }
                              onChange={() => {
                                setProfileId(row.profileId);
                                dispatch(
                                  activateOrDeactivateECRECOProfile({
                                    profileId: row.profileId,
                                    userId: userId,
                                    flag: row.activeFlag === 'Y' ? 'N' : 'Y',
                                    category: row.categoryCode
                                      ? row.categoryCode
                                      : null,
                                  })
                                );
                              }}
                              key={index}
                            />
                          );
                        }
                      : '',
                };
              });
            return (
              <Input
                key={index}
                formType={element}
                dataTableEventHandler={(row) => {
                  ecrEcoProfilePageHandler(row, true);
                }}
                dataTableColumn={columns}
              />
            );
          } else if (element.uniqueKey === 'clientSelect') {
            return (
              <div
                key={index}
                style={{
                  width: '300px',
                  position: 'relative',
                  marginBottom: '-60px',
                  left: '240px',
                }}
              >
                <Input
                  key={index}
                  formType={element}
                  isEditable="notShowing"
                  onChangeHandler={(element, event) => {
                    onChangeHandlingFunctions[element.onChangeFunction](
                      event,
                      element.uniqueKey,
                      element
                    );
                  }}
                />
              </div>
            );
          }
        })
      : null;
  };
  useEffect(() => {
    const timer = setTimeout(() => {
      setnotify(null);
    }, 4000);

    return () => clearTimeout(timer); // 👈 cleanup function
  }, [notify]);
  return (
    <>
      <CommonSpinner visible={loading} />
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      <div
        className="header-right flex-row"
        style={{
          display: 'inline-block',
          position: 'relative',
          float: 'right',
          margin: '20px 0px',
        }}
      >
        <div
          className="username-wrap"
          disabled={clientId ? false : true}
          onClick={() => setShowDropdown(!showDropdown)}
        >
          <Button
            className="small mb8 outline add-button-custom flex-row vam button fr"
            disabled={clientId ? false : true}
          >
            <i className="icon-add-button "> </i>
            Add
          </Button>
          <CategoryList
            showDropdown={showDropdown}
            redirectPath={redirectPath}
            clientIdVal={clientId}
            categoryListData={
              formDetails.find((entry) => entry.uniqueKey === 'addButton')
                ?.comboBoxOptions
            }
          />
        </div>
      </div>
      <div className="">
        {formDetails && formDetails.length
          ? formControlsBinding(formDetails)
          : ''}
      </div>
      {clientId && formDetails?.length ? (
        <div className="flex-right">
          <Button
            className={' small default mb20 mr20 '}
            onClick={() => {
              dispatch(getECRECOProfileNames(clientId));
              setLoading(true);
              setProfileId(null);
              setNewProfileName('');
            }}
          >
            Create As
          </Button>
        </div>
      ) : (
        ''
      )}
      <Modal
        className="ItemListModal"
        overlayClassName="ModalOverlay"
        ariaHideApp={false}
        isOpen={isModal}
      >
        <div
          onClick={() => setIsModal(false)}
          className="modal-close icon-close"
        ></div>
        <div>
          <div className="two-col-layout mb20">
            <Select
              label="Profile List"
              options={createAsOptions}
              value={profileId == null ? '' : profileId}
              onChange={(e) => setProfileId(e.target.value)}
              required
            />
            <TextInput
              label="Profile Name"
              value={newProfileName}
              onChange={(e) => {
                setNewProfileName(e.target.value);
              }}
              required
            />
          </div>
          <Button
            className={'small default button '}
            onClick={handleCreateAsSave}
            style={{
              float: 'right',
            }}
          >
            Save
          </Button>
          {notify != null ? (
            <div
              className={['notification-bar', 'type-' + notify.type].join(' ')}
              style={{
                position: 'sticky !important',
                width: 90 + '%',
              }}
            >
              <i className="icon-close" onClick={() => setnotify(null)}></i>
              {notify.resMessage}
            </div>
          ) : (
            ''
          )}
        </div>
      </Modal>
    </>
  );
};
export { ECRECOProfileListing };
