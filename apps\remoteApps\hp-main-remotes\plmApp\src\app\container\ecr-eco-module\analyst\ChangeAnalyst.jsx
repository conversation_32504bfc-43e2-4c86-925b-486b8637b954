import { Button, Input, useConfirm } from '@hp/components';
import { cloneDeep } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  getChangeAnalystFormDetails,
  deleteChangeAnalystDetails,
  addUserAsChangeAnalystDetails,
  showDialog,
} from '@hp/mainstore';
import { globalutils } from '@hp/components';

const ChangeAnalyst = () => {
  const [formState, setFormState] = useState([]);
  const [firstMount, setFirstMount] = useState(true);
  const dispatch = useDispatch();
  const { confirm } = useConfirm();

  const { changeAnalystForm, deleteAnalyst, changeAnalyst } = useSelector(
    (store) => store.pdm
  );

  const { subMenuName, innerMenuName } = useSelector((store) => store.menu);

  let user = globalutils.getDataFromStorage('all');
  let userId = user?.userId;
  let clientId = user?.clientId;

  useEffect(() => {
    dispatch(getChangeAnalystFormDetails({ userId, clientId }));
  }, []);

  useEffect(() => {
    if (changeAnalystForm?.value) {
      let tempForm = cloneDeep(changeAnalystForm.value);
      setFormState(tempForm);
    }
  }, [changeAnalystForm]);

  useEffect(() => {
    if (firstMount) {
      setFirstMount(false);
    } else if (deleteAnalyst?.value && firstMount === false) {
      funcToSetResponseMessage(
        deleteAnalyst.value?.type,
        deleteAnalyst.value?.text
      );
    }
  }, [deleteAnalyst]);

  useEffect(() => {
    if (firstMount) {
      setFirstMount(false);
    }
    if (changeAnalyst?.value && firstMount === false) {
      funcToSetResponseMessage(
        changeAnalyst.value?.type,
        changeAnalyst.value?.text
      );
    }
  }, [changeAnalyst]);

  const handleChange = (event, uniqueKey, subElement) => {
    let tempArray = cloneDeep(formState);
    tempArray.forEach((entry) => {
      if (entry.uniqueKey === 'userDiv') {
        entry.formSubDetailsInternalDTOList.forEach((subElement) => {
          if (
            subElement.uniqueKey === 'userSelect' &&
            subElement.uniqueKey === uniqueKey
          ) {
            subElement.value = event;
          }
        });
      }
    });

    setFormState(tempArray);
  };

  const onChangeHandlingFunctions = {
    handleChange,
  };

  const funcToSetResponseMessage = (type, resMessage) => {
    let showPopup = true;
    let canClose = true;
    let autoHide = true;
    dispatch(
      showDialog({
        showPopup: showPopup,
        type: type,
        responseMessage: resMessage,
        canClose: canClose,
        autoHide: autoHide,
      })
    );
  };

  const handleDelete = async (obj) => {
    const isConfirmed = await confirm(
      'Are you sure you want to remove this Change Analyst?'
    );
    if (isConfirmed) {
      if (obj.userId) {
        dispatch(deleteChangeAnalystDetails(obj.userId));
        dispatch(getChangeAnalystFormDetails({ userId, clientId }));
      } else {
        funcToSetResponseMessage('error', 'Delete change analyst failed');
      }
    }
  };

  const handleAddUser = async (userId) => {
    const isConfirmed = await confirm(
      'Are you sure you want to add this user as Change Analyst?'
    );
    if (isConfirmed) {
      if (userId) {
        dispatch(addUserAsChangeAnalystDetails(userId));
        dispatch(getChangeAnalystFormDetails({ userId, clientId }));
      } else {
        funcToSetResponseMessage('error', 'Add user as change analyst failed');
      }
    }
  };

  const formControlsBinding = (data) => {
    return data
      ? data.map((element, index) => {
          if (element.uniqueKey) {
            if (element.uniqueKey === 'userDiv') {
              return (
                <div className="ecr-eco-settings" key={element.uniqueKey}>
                  {element.formSubDetailsInternalDTOList &&
                    element.formSubDetailsInternalDTOList.map(
                      (subElement, ind) => {
                        if (subElement.uniqueKey === 'userSelect') {
                          return (
                            <div
                              key={subElement.uniqueKey}
                              style={{
                                position: 'absolute',
                                top: '-15px',
                                left: '250px',
                                width: '300px',
                              }}
                            >
                              <Input
                                indexKey={ind}
                                formType={subElement}
                                style={{ width: subElement.displayWidth }}
                                onChangeHandler={(subElement, event) => {
                                  onChangeHandlingFunctions[
                                    subElement.onChangeFunction
                                  ](event, subElement.uniqueKey, subElement);
                                }}
                              />
                            </div>
                          );
                        } else if (subElement.uniqueKey === 'addUserButton') {
                          return (
                            <div
                              key={subElement.uniqueKey}
                              style={{
                                position: 'absolute',
                                right: '0',
                                marginTop: '10px',
                              }}
                            >
                              <Button
                                onClick={() => {
                                  const userId =
                                    subElement.userId ||
                                    formState
                                      .find((f) => f.uniqueKey === 'userDiv')
                                      ?.formSubDetailsInternalDTOList?.find(
                                        (sub) => sub.uniqueKey === 'userSelect'
                                      )?.value;
                                  if (userId) {
                                    handleAddUser(userId);
                                  } else {
                                    console.error('User ID not found');
                                  }
                                }}
                                className="small mb8 outline add-button-custom flex-row vam"
                              >
                                <i className="icon-add-button"></i>Add user as
                                Change Analyst
                              </Button>
                            </div>
                          );
                        }
                      }
                    )}
                </div>
              );
            } else if (element.uniqueKey === 'changeAnalyst') {
              let columns =
                element.formSubDetailsInternalDTOList &&
                element.formSubDetailsInternalDTOList.map((val, idx) => {
                  return {
                    selector: val.selector,
                    width:
                      val.selector === 'deleteIcon'
                        ? '4%'
                        : val.selector === 'serialNo'
                        ? '5%'
                        : '',
                    name: val.displayName,
                    cell:
                      val.selector === 'deleteIcon'
                        ? function displayCell(row) {
                            return (
                              <div
                                className="icon-2-trash"
                                onClick={() => handleDelete(row)}
                                key={idx}
                              ></div>
                            );
                          }
                        : '',
                  };
                });
              return (
                <Input
                  dataTablePersistHead={true}
                  key={index}
                  indexKey={index}
                  formType={element}
                  dataTableColumn={columns}
                />
              );
            }
          }
        })
      : null;
  };

  return (
    <>
      <h3 className="page-title">
        {subMenuName} / {innerMenuName}
      </h3>
      <div
        className=""
        style={{
          position: 'relative',
          marginTop: '30px',
        }}
      >
        {formState && formState.length ? formControlsBinding(formState) : ''}
      </div>
    </>
  );
};

export default ChangeAnalyst;
