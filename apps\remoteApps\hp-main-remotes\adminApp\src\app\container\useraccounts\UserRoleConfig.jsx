/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * <AUTHOR> <PERSON>
 * @email <EMAIL>
 * @create date 27-05-2021 15:40:56
 * @modify date 20-10-2021 14:51:46
 * @desc [description]
 */
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import {
  getRolesCombo,
  getUserListing,
  addUserRole,
  getClientComboBox,
  getUserRolesDetails,
  showDialog,
  resetaddUserRole,
  resetgetUserRolesDetails,
  resetdeleteRecordfromUsersRoles,
} from '@hp/mainstore';
import { useDispatch, useSelector } from 'react-redux';
import '@hp/styles/ProfilePage.scss';
import { AP_USER } from '@hp/constants';
import Modal from 'react-modal';
import { Button, ManageSelect, Select, TextInput } from '@hp/components';
import { dataTableServiceProvider } from '@hp/components';
import { globalutils } from '@hp/components';

const UserRoleConfig = (props) => {
  const dispatch = useDispatch();
  const {
    viewuserlist,
    userroleslist,
    rolesComboList,
    addRoleToUserRoleTable,
  } = useSelector((store) => store.admin);
  const { clientCombolist } = useSelector((store) => store.email);
  let user = globalutils.getDataFromStorage('all');
  const client_Id = user?.clientId;
  const [TableListing, setTableList] = useState();
  const [searchBox, setSearchBox] = useState();
  const [roleNames, setRoleNames] = useState();
  const [roleValue, setRoleValue] = useState([]);
  const [userIdRole, setUserIdRole] = useState();
  const [multiSelect, setMultiSelect] = useState();
  const [clientComboState, setClientComboState] = useState([]);
  const [clientIdSelect, setClientIdSelect] = useState(client_Id);
  const [filterString, setFilterString] = useState({
    filterStr: '',
  });
  //state to handle modal
  const [showModal, setShowModal] = useState(false);
  const [formDetails, setFormDetails] = useState({});
  const [showButtons, setShowButtons] = useState(true);
  const [notify, setnotify] = useState(null);
  useEffect(() => {
    if (clientIdSelect && clientIdSelect !== undefined) {
      getParameter();
    }
  }, [clientIdSelect]);
  useEffect(() => {
    if (
      clientCombolist &&
      clientCombolist.value &&
      clientCombolist.value.length > 0
    ) {
      const clientOptions = clientCombolist.value.map((value) => {
        return {
          value: value.clientId,
          display: value.clientName,
        };
      });
      setClientComboState(clientOptions);
    }
  }, [clientCombolist]);
  useEffect(() => {
    let rolesId =
      multiSelect && multiSelect !== undefined
        ? multiSelect.map((entry) => {
            return entry.commonId === undefined &&
              entry.profileInLangId !== null
              ? entry.profileInLangId
              : entry.commonId;
          })
        : [];
    setRoleValue(rolesId);
  }, [multiSelect]);

  useEffect(() => {
    getParameter();
    getUserRoleCombo();
    dispatch(getClientComboBox());
  }, [props]);

  useEffect(() => {
    return () => {
      dispatch(resetaddUserRole());
      dispatch(resetgetUserRolesDetails());
      dispatch(resetdeleteRecordfromUsersRoles());
    };
  }, []);

  const funcToSetResponseMessage = (type, resMessage) => {
    dispatch(
      showDialog({
        showPopup: true,
        type: type,
        responseMessage: resMessage,
        canClose: true,
        autoHide: true,
      })
    );
  };

  const funcToSetResMessageInModal = (type, resMessage) => {
    setnotify({ type, resMessage });
    const clearTimer = setTimeout(() => {
      setnotify(null);
      clearTimeout(clearTimer);
    }, 3200);
  };

  useEffect(() => {
    if (showModal) {
      setnotify(null);
    }
  }, [showModal]);

  useEffect(() => {
    if (addRoleToUserRoleTable && addRoleToUserRoleTable.value) {
      addRoleToUserRoleTable.value.userNotificationDto &&
        funcToSetResponseMessage(
          addRoleToUserRoleTable.value.userNotificationDto.type,
          addRoleToUserRoleTable.value.userNotificationDto.text
        );
      addRoleToUserRoleTable.value.userList &&
        setSearchBox(addRoleToUserRoleTable.value.userList);
    }
  }, [addRoleToUserRoleTable]);

  useEffect(() => {
    if (viewuserlist && viewuserlist.value) {
      setTableList(viewuserlist.value);
      setSearchBox(viewuserlist.value);
    }
  }, [viewuserlist]);

  useEffect(() => {
    if (
      rolesComboList &&
      rolesComboList.value &&
      rolesComboList.value !== undefined
    ) {
      const rolesOptions = rolesComboList.value.map((value) => {
        return {
          commonId: value.roleId,
          displayName: value.roleName, // changed label>displayName
        };
      });
      setRoleNames(rolesOptions);
    }
  }, [rolesComboList]);

  useEffect(() => {
    if (
      userroleslist &&
      userroleslist.value &&
      userroleslist.value !== undefined
    ) {
      var tempRoles = userroleslist.value.map((entry) => {
        return { displayName: entry.roleName, commonId: entry.roleId }; // changed label>displayName
      });
      setMultiSelect(tempRoles);
    }
  }, [userroleslist]);

  useEffect(() => {
    let filterableStr = filterString.filterStr;
    filterableStr == null || filterableStr === '' || filterableStr === undefined
      ? setSearchBox(TableListing)
      : null;
    globalSearch(TableListing);
  }, [filterString.filterStr]);

  const handleChange = (event) => {
    setFilterString({ filterStr: event.target.value });
  };

  const globalSearch = (FilterData) => {
    if (filterString.filterStr) {
      let FilterString = filterString.filterStr;

      const containsKeyword = (val) =>
        (typeof val === 'number' && String(val).indexOf(FilterString) !== -1) ||
        (typeof val === 'string' &&
          val.toLowerCase().indexOf(FilterString.toLowerCase()) !== -1);

      const filteredData = FilterData?.filter((entry) =>
        Object.values(entry).some(containsKeyword)
      );
      setSearchBox(filteredData);
    }
  };

  const getParameter = () => {
    let clientId =
      clientIdSelect && clientIdSelect !== undefined
        ? clientIdSelect
        : client_Id;
    dispatch(getUserListing(clientId));
  };

  const getUserRoleCombo = () => {
    let clientId = client_Id;
    dispatch(getRolesCombo(clientId));
  };

  const addRecord = () => {
    if (roleValue?.length > 0) {
      if (userIdRole) {
        dispatch(addUserRole({ roleValue, userIdRole, clientId: client_Id }));
        setShowModal(false);
      }
    } else {
      funcToSetResMessageInModal('error', 'Please select at least one role');
    }
  };

  // Cancel Button Function
  const handleCancel = () => {
    setMultiSelect([]);
    setShowModal(false);
  };

  const onRowClickHandler = (params) => {
    let userId = params.userId;
    const styleAttribute = dataTableServiceProvider.conditionalDataTableRow(
      params.userId,
      'userId'
    );
    let tempArray = [];
    if (params.roleName !== null && params.roleName.length > 0)
      params.roleName.split(',').map((element) => {
        tempArray.push({ roleName: element });
      });
    setFormDetails({
      firstName: params.firstName,
      middleName: params.middleName,
      lastName: params.lastName,
      userRoles: tempArray,
    });
    setShowModal(true);
    setConditionalStyles(styleAttribute);
    setUserIdRole(userId);
    dispatch(getUserRolesDetails(userId));
  };

  const [conditionalRowStyles, setConditionalStyles] = useState([]);

  const roles = [
    {
      name: 'Existing Roles',
      selector: 'roleName',
    },
  ];

  const columns = [
    {
      name: 'First Name',
      selector: 'firstName',
      sortable: true,
      width: '27%',
    },
    {
      name: 'Middle Name',
      selector: 'middleName',
      sortable: true,
      width: '19%',
    },
    {
      name: 'Last Name',
      selector: 'lastName',
      sortable: true,
      width: '27%',
    },
    {
      name: 'User Name',
      selector: 'username',
      sortable: true,
      width: '27%',
    },
    /*{
      name: 'Roles',
      selector: 'roleName',
      sortable: true,
      maxWidth: '47%',
    },*/
  ];
  const multiselectFilter = (option, filter, uniqueKey) => {
    const re = new RegExp(filter, 'i');
    return option.filter(({ label }) => label && label.match(re));
  };
  const handleOnChange = (event) => {
    setClientIdSelect(event.target.value);
  };
  return (
    <>
      <div className="page-title">User Role</div>

      <div className="two-col-layout mb20">
        {client_Id === 1 && (
          <Select
            label="Client"
            style={{ width: 200 + 'px' }}
            value={clientIdSelect}
            name="clientId"
            options={clientComboState}
            onChange={handleOnChange}
          />
        )}
        <TextInput
          label="Search"
          // style={{ width: 105 + "%" }}
          value={filterString.filterStr}
          style={{ width: 200 + 'px' }}
          onChange={(e) => handleChange(e)}
        />
      </div>

      <div className="mb24 styledDatatable">
        <DataTable
          highlightOnHover={true}
          noHeader={true}
          striped={true}
          dense={false}
          columns={columns}
          pagination={true}
          paginationDefaultPage={1}
          paginationResetDefaultPage={true}
          paginationPerPage={10}
          data={searchBox}
          onRowClicked={(event) => onRowClickHandler(event)}
          conditionalRowStyles={conditionalRowStyles}
        />
      </div>
      {showModal && (
        <Modal
          className="Modal"
          overlayClassName="ModalOverlay"
          ariaHideApp={false}
          isOpen={showModal}
        >
          <div
            onClick={() => {
              handleCancel();
            }}
            className="modal-close icon-close"
            style={{ fontSize: 20 + 'px' }}
          ></div>
          <div className="boxed mb20">
            <div>
              <div className="page-title">
                {' '}
                {formDetails.firstName +
                  ' ' +
                  (formDetails.middleName !== null &&
                  formDetails.middleName.length > 0
                    ? formDetails.middleName
                    : '') +
                  ' ' +
                  formDetails.lastName}{' '}
              </div>{' '}
            </div>
            {/* <div className="mb24 styledDatatable">
              <DataTable
                highlightOnHover={true}
                noHeader={true}
                striped={true}
                dense={false}
                columns={roles}
                data={formDetails.userRoles}
                conditionalRowStyles={conditionalRowStyles}
              />
            </div> */}
            <div>
              <div
                className="manage-select"
                style={{ marginBottom: 12 + 'px' }}
              >
                <div className="left">
                  <div className="manage-select-inner">
                    <div className="left">
                      <div
                        className="p-container manage-languages"
                        style={{ maxWidth: '754px' }}
                      >
                        <ManageSelect
                          isFullWidth
                          title={'Select User Roles'}
                          languageValue={multiSelect}
                          selectedLanguages={setMultiSelect}
                          langOptions={roleNames}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {showButtons && (
              <div>
                <Button onClick={() => addRecord()} className="default mr20">
                  Save
                </Button>
                <Button onClick={() => handleCancel()} className="info mr20">
                  Cancel
                </Button>
              </div>
            )}
          </div>
          {notify != null ? (
            <div
              className={['notification-bar', 'type-' + notify.type].join(' ')}
              style={{
                position: 'sticky !important',
                width: 90 + '%',
              }}
            >
              <i className="icon-close" onClick={() => setnotify(null)}></i>
              {notify.resMessage}
            </div>
          ) : (
            ''
          )}
        </Modal>
      )}
    </>
  );
};

export { UserRoleConfig };
