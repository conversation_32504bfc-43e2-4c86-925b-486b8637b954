{"name": "expenseApp", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/remoteApps/hp-main-remotes/expenseApp/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "dist/apps/remoteApps/hp-main-remotes/expenseApp", "main": "apps/remoteApps/hp-main-remotes/expenseApp/src/main.jsx", "tsConfig": "apps/remoteApps/hp-main-remotes/expenseApp/tsconfig.app.json", "rspackConfig": "apps/remoteApps/hp-main-remotes/expenseApp/rspack.config.js", "assets": ["apps/remoteApps/hp-main-remotes/expenseApp/src/favicon.ico", "apps/remoteApps/hp-main-remotes/expenseApp/src/assets"]}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "optimization": true, "sourceMap": false, "rspackConfig": "apps/remoteApps/hp-main-remotes/expenseApp/rspack.config.prod.js"}}}, "serve": {"executor": "@nx/rspack:module-federation-dev-server", "options": {"buildTarget": "expenseApp:build:development", "port": 4204}, "configurations": {"development": {}, "production": {"buildTarget": "expenseApp:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/rspack:module-federation-static-server", "defaultConfiguration": "production", "options": {"serveTarget": "expenseApp:serve"}, "configurations": {"development": {"serveTarget": "expenseApp:serve:development"}, "production": {"serveTarget": "expenseApp:serve:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/remoteApps/hp-main-remotes/expenseApp/jest.config.js"}}}}