/* eslint-disable @nx/enforce-module-boundaries */
/* eslint-disable array-callback-return */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useEffect } from 'react';
import { Input } from '@hp/components';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import { useDispatch, useSelector } from 'react-redux';

import { pdmItemView, pdmConstants } from '@hp/mainstore';

function ItemView(props) {
  const [itemViewTab, setItemViewTab] = useState([]);
  const [nodeId, setNodeId] = useState('');
  const [tabIndex, setTabIndex] = useState(0);
  const [tabName, setTabName] = useState('getPdmSourceList');
  const [tabValue, setTabValue] = useState(false);

  const dispatch = useDispatch();

  const { pdmTabView } = useSelector((store) => store.pdm);

  useEffect(() => {
    formHandeler(props.Action, props.Node);
  }, [props.Action, props.Node]);

  useEffect(() => {
    setTabValue(false);
    if (nodeId) {
      dispatch(pdmItemView({ itemId: nodeId, getterFunc: tabName }));
    }
  }, [nodeId, tabName]);

  useEffect(() => {
    if (
      pdmTabView &&
      pdmTabView.value &&
      pdmTabView.value.formDetailsDtoList &&
      pdmTabView.value.formDetailsDtoList.length
    ) {
      setTabValue(true);
      setItemViewTab(pdmTabView.value.formDetailsDtoList);
    }
    return () => {
      dispatch({ type: pdmConstants.GET_ITEM_TABVIEW_SUCCESS, payload: null });
    };
  }, [pdmTabView]);

  const formHandeler = (action, node) => {
    if (!node.id) return;
    setNodeId(node.id);
    setTabValue(false);
  };

  const TabDetails = (data) => {
    if (
      data?.formSubDetailsInternalDTOList &&
      data.formSubDetailsInternalDTOList.length
    ) {
      return data.formSubDetailsInternalDTOList.map((form, index) => {
        return <Input formType={form} key={index} />;
      });
    }
  };

  return (
    <Tabs
      className="profile-completion"
      style={{ maxWidth: '100%' }}
      onSelect={(tabindex) => {
        setTabIndex(tabindex);
      }}
      selectedIndex={tabIndex}
    >
      <TabList>
        {itemViewTab.map((tabData, index) => {
          if (tabData.type === 'TabPanel') {
            return (
              <Tab
                onClick={() => {
                  setTabName(tabData.getterFunc);
                }}
                key={index}
              >
                {tabData.displayName}
              </Tab>
            );
          }
        })}
      </TabList>
      {itemViewTab
        ? itemViewTab.map((tabData, index) => {
            if (tabData.type === 'TabPanel') {
              return (
                <div key={index}>
                  {tabValue ? (
                    <TabPanel key={index}>{TabDetails(tabData)}</TabPanel>
                  ) : (
                    ''
                  )}
                </div>
              );
            }
          })
        : ''}
    </Tabs>
  );
}

export { ItemView };
