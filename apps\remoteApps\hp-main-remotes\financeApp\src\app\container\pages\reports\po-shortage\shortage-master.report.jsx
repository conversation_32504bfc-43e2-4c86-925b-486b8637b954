/* eslint-disable react/no-unknown-property */
import {
  useEffect,
  useMemo,
  useRef,
  useState,
  useContext,
  createContext,
} from 'react';
import DataGrid from 'react-data-grid';
import { CellExpanderFormatter } from './formatters/CellExpanderFormatter';
import { useFocusRef } from './formatters/useFocusRef';
import ReactDOM from 'react-dom';
import { Button, TextInput } from '@hp/components';
import { Pagination } from '@hp/components';
let a = true;
let trmpRows = null;
export function MasterDetail({ direction, columnDef, records }) {
  const [filters, setFilters] = useState({
    key: '',
    value: '',
  });
  const [paginationRows, setPaginationRows] = useState([]);
  const [attributes, setAttributes] = useState({
    currentPage: 1,
    dataPerPage: 10,
  });

  const [activeCell, setActiveCell] = useState([]);
  const activeCell1 = [];
  const columns = useMemo(() => {
    return columnDef.map((column, index) => {
      column.headerCellClass = 'filter-cell  ';
      column.cellClass = (row) => {
        return 'sr-cm-padd ctb-react-grid-Container';
      };
      column.headerRenderer = (p) => {
        return (
          <FilterRenderer key={index} {...p}>
            {({ filters, ...rest }) => (
              <svg
                onClick={(e) => {
                  onClick(e, column.key);
                }}
                style={{ cursor: 'pointer' }}
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-three-dots-vertical"
                viewBox="0 0 16 16"
              >
                <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" />
              </svg>
            )}
          </FilterRenderer>
        );
      };
      // column.formatter=({ row, isCellSelected, onRowChange })=> {return <CellToolTipFormatter cellvalue={row[column.key]}/>}
      if (column.isSubtableActive) {
        column.colSpan = (args) => {
          return args.type === 'ROW' &&
            args.row.type === 'DETAIL' &&
            args.row[`${column.key}isActive`]
            ? 1
            : undefined;
        };

        column.cellClass = (row) => {
          return row.type === 'DETAIL' ? 'sr-cm-padd ' : undefined;
        };
        column.selectCell = null;
        column.formatter = ({ row, isCellSelected, onRowChange }) => {
          if (row.type === 'DETAIL' && row[`${column.key}isActive`]) {
            return (
              <ProductGrid
                isCellSelected={isCellSelected}
                column={row['sub' + column.key].column}
                row={row['sub' + column.key].row}
                parentId={row.parentId}
                direction={direction}
              />
            );
          }
          if (row.type === 'DETAIL') {
            return <></>;
          }

          if (row[`reqs${column.key}`]) {
            onRowChange({ ...row, selectCell: column.key });
          }

          if (row[`cellSub${column.key}`]) {
            return <div>{row[column.key]}</div>;
          }

          return (
            <CellExpanderFormatter
              expanded={row.expanded}
              isCellError={row[`is${column.key}CellError`]}
              isCellSelected={isCellSelected}
              selectCell={row.selectCell}
              cellvalue={row[column.key + 'Summary']}
              columnId={column.key}
              isActivecell={row[`${column.key}isActive`]}
              onCellExpand={() => {
                onRowChange({ ...row, selectCell: column.key });
              }}
            />
          );
        };
      }

      return column;
    });
  }, [direction, columnDef]);

  const [rows, setRows] = useState(records);
  const [click, setClick] = useState({
    x: 0,
    y: 0,
    isActive: false,
  });
  if (a) {
    a = false;
    trmpRows = records;
    setRows(records);
  }

  function onRowsChange(rows, { indexes }, expanded) {
    const row = rows[indexes[0]];
    if (row[`reqs${row.selectCell}`] && row.type === 'MASTER') {
      row[`reqs${row.selectCell}`] = false;
      if (rows[indexes[0] + 1].type === 'DETAIL') {
        rows.splice(indexes[0] + 1, 1);
      }
      rows.splice(indexes[0] + 1, 0, row.prevstate);
    } else {
      if (row.type === 'MASTER') {
        if (!row.expanded) {
          let detailsData = {
            type: 'DETAIL',
            selectCell: row.selectCell,
            id: row.id + 100,
            parentId: row.id,
            //  subtable:row["sub"+row.selectCell],
            activeCellCount: 1,
          };
          detailsData[`${row.selectCell}isActive`] = true;
          row[`${row.selectCell}isActive`] = true;
          detailsData[`sub${row.selectCell}`] = row['sub' + row.selectCell];

          rows.splice(indexes[0] + 1, 0, detailsData);
          setActiveCell([...activeCell, row.selectCell]);
          activeCell1.push(row.selectCell);
          row.expanded = true;
        } else {
          const detailsRow = rows[indexes[0] + 1];

          if (detailsRow[`${row.selectCell}isActive`]) {
            detailsRow.activeCellCount = detailsRow.activeCellCount - 1;
            detailsRow[`${row.selectCell}isActive`] = false;
            row[`${row.selectCell}isActive`] = false;
            row.prevstate = detailsRow;
            row[`reqs${row.selectCell}`] = true;
          } else {
            detailsRow.activeCellCount = detailsRow.activeCellCount + 1;
            detailsRow[`${row.selectCell}isActive`] = true;
            row[`${row.selectCell}isActive`] = true;
            detailsRow[`sub${row.selectCell}`] = row['sub' + row.selectCell];
            rows.splice(indexes[0] + 1, 1);

            row.prevstate = detailsRow;
            row[`reqs${row.selectCell}`] = true;
          }

          if (!detailsRow[`${row.selectCell}isActive`]) {
            for (var key in row) if (key.endsWith('isActive')) delete row[key];
            rows.splice(indexes[0] + 1, 1);
            var index = activeCell.indexOf(row.selectCell);
            row[`reqs${row.selectCell}`] = false;
            if (index !== -1) {
              activeCell.splice(index, 1);
              activeCell1.splice(index, 1);
              //  setActiveCell([activeCell])
            }
            row.expanded = false;
          }
        }
      }
    }
    setRows(rows);
  }
  function onClick(e, key) {
    setClick({
      x: e.clientX,
      y: e.clientY,
      isActive: true,
      key: key,
    });
  }

  const ref = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        setClick((click.isActive = false));
      }
    };
    document.addEventListener('mousedown', handleClickOutside, true);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, [click, setClick]);

  const filteredRows = useMemo(() => {
    return rows.filter((r) => {
      try {
        return filters.key
          ? r[filters.key].toLowerCase().includes(filters.value.toLowerCase())
          : true;
      } catch (error) {
        // return  r.type &&r.type=== 'DETAIL'&&filters.value!==''?  false: true
        return true;
      }
    });
  }, [rows, filters]);
  const paginate = (pageNumber) => {
    setAttributes({ ...attributes, currentPage: pageNumber });
  };
  useEffect(() => {
    if (attributes && filteredRows) {
      if (filteredRows.length <= 10) {
        // setAttributes({ currentPage: 1, dataPerPage: 10 });
        setPaginationRows(filteredRows);
      } else {
        var indexOfLastData = attributes.currentPage * attributes.dataPerPage;
        var indexOfFirstData = indexOfLastData - attributes.dataPerPage;
        var currentData = filteredRows.slice(indexOfFirstData, indexOfLastData);
        setPaginationRows(currentData);
      }

      // return () => {
      //   setPaginationRows([]);
      // };
    }
  }, [attributes, filteredRows]);
  return (
    <>
      <DataGrid
        rowKeyGetter={rowKeyGetter}
        columns={columns}
        rows={paginationRows}
        rowClass={(row) =>
          row['isWarnRow'] ? 'sr-cm-warn-row  ' : 'sr-cm-row-style '
        }
        onRowsChange={onRowsChange}
        headerRowHeight={45} //45
        rowHeight={(args) =>
          args.type === 'ROW' && args.row.type === 'DETAIL' ? 200 : 35
        }
        className="filterColumnClassName rdg-light  rdg-app-style"
        enableVirtualization={false}
        style={{ fontSize: '12px', height: '500px' }}
        direction={direction}
      />
      {records?.length > 10 && (
        <div className="table-div" style={{ marginTop: '15px' }}>
          <Pagination
            dataPerPage={attributes.dataPerPage}
            totalPosts={records?.length}
            paginate={paginate}
          />
        </div>
      )}
      {click?.isActive &&
        ReactDOM?.createPortal(
          <div
            className="boxed mb8"
            ref={ref}
            style={{
              // Note: computeTopWith and computeLeftWith are placeholders. You
              // need to provide their implementation.
              top: click.y,
              left: click.x,
              width: '200px',
              height: '180px',
              position: 'absolute',
            }}
          >
            {/* <div className="modal-close icon-close mb20"></div> */}
            <TextInput
              className="mb8"
              value={filters[click.key]}
              onChange={(e) => {
                filters.key = click.key;
                filters.value = e.target.value;
                setFilters({ ...filters });
              }}
            />

            <div style={{ float: 'right' }}>
              <Button
                onClick={() => {
                  setFilters({ ...filters, value: '' });
                  setRows([...trmpRows]);
                }}
                className="small mb8 outline add-button-custom flex-row vam mb20"
              >
                <i className="icon-clear"> </i>Clear
              </Button>
            </div>
          </div>,
          document.getElementById('main-module')
        )}
    </>
  );
}

function ProductGrid({ parentId, column, row, isCellSelected, direction }) {
  const gridRef = useRef(null);
  const [paginationRows, setPaginationRows] = useState([]);
  const [attributes, setAttributes] = useState({
    currentPage: 1,
    dataPerPage: 10,
  });
  useEffect(() => {
    if (!isCellSelected) return;
    //  gridRef
    // .current.element.querySelector<HTMLDivElement>('[tabindex="0"]')
    // .focus({ preventScroll: true });
  }, [isCellSelected]);

  function onKeyDown(event) {
    if (event.isDefaultPrevented()) {
      event.stopPropagation();
    }
  }
  const paginate = (pageNumber) => {
    setAttributes({ ...attributes, currentPage: pageNumber });
  };
  useEffect(() => {
    if (attributes && row) {
      if (row.length <= 10) {
        // setAttributes({ currentPage: 1, dataPerPage: 10 });
        setPaginationRows(row);
      } else {
        var indexOfLastData = attributes.currentPage * attributes.dataPerPage;
        var indexOfFirstData = indexOfLastData - attributes.dataPerPage;
        var currentData = row.slice(indexOfFirstData, indexOfLastData);
        setPaginationRows(currentData);
      }

      // return () => {
      //   setPaginationRows([]);
      // };
    }
  }, [attributes, row]);
  return (
    <div onKeyDown={onKeyDown}>
      <DataGrid
        className="rdg-light rdg-app-style"
        ref={gridRef}
        rows={paginationRows}
        columns={column}
        rowClass={(row) =>
          row['isRowError'] ? 'sr-cm-error-row  text-red' : 'sr-cm-row-style '
        }
        rowKeyGetter={rowKeyGetter}
        style={{ blockSize: 250 }}
        direction={direction}
        enableVirtualization={true}
      />
      <div className="table-div" style={{ marginTop: '15px' }}>
        <Pagination
          dataPerPage={attributes.dataPerPage}
          totalPosts={row?.length}
          paginate={paginate}
        />
      </div>
    </div>
  );
}

function rowKeyGetter(row) {
  return row.id;
}

const FilterContext = createContext(undefined);

//################################ util functions
function FilterRenderer({ isCellSelected, column, children }) {
  const filters = useContext(FilterContext);
  const { ref, tabIndex } = useFocusRef(isCellSelected);

  return (
    <div className="sr-cm-box" style={{ marginTop: '10px' }}>
      <div className="sr-cm-item" style={{ fontSize: '13px' }}>
        <b> {column.name}</b>
      </div>
      {<div className="sr-cm-item">{children({ ref, tabIndex, filters })}</div>}
    </div>
  );
}

// function selectStopPropagation(event) {
//   if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(event.key)) {
//     event.stopPropagation();
//   }
// }
