import { useFocusRef } from './useFocusRef';

export function CellExpanderFormatter({
  isCellSelected,
  expanded,
  cellvalue,
  columnId,
  selectCell,
  onCellExpand,
  isActivecell,
  isCellError,
}) {
  const { ref, tabIndex } = useFocusRef(isCellSelected);

  function handleKeyDown(e) {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      onCellExpand();
    }
  }

  return (
    <div
      className={
        isCellError
          ? 'cellExpandClassname sr-cm-error-row  sr-cm-text-red  '
          : 'cellExpandClassname  report-border  '
      }
      style={{ height: '100%', padding: '10px 12px 12px 12px' }}
    >
      <span onClick={onCellExpand} onKeyDown={handleKeyDown}>
        <span ref={ref} tabIndex={tabIndex}>
          {expanded && isActivecell ? `\u25BC ${cellvalue}` : `➤ ${cellvalue}`}
        </span>
      </span>
    </div>
  );
}
