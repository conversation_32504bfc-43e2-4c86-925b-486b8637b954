/* eslint-disable react/no-unknown-property */
import React from 'react';
function PoMasterCard(props) {
  const { formResponse, onDelete, linkbtn, poId, linkedPoId } = props;

  const handleDelete = async () => {
    onDelete();
  };
  return (
    <div className="po-card alt-card mb20">
      <div className="user-wrap">
        <div className="alt-card-items-v1">
          {formResponse &&
            formResponse
              ?.filter((element) => element.type !== 'DataTable')
              .map((element, index) => (
                <div
                  key={'alt-' + index}
                  className="alt-single-item"
                  style={{ flexShrink: '1em' }}
                >
                  <div className="alt-label">
                    <div>{element.displayName}</div>
                  </div>
                  <div className="alt-text">
                    <div>{element.value}</div>
                  </div>
                </div>
              ))}

          {linkbtn && poId && linkedPoId && (
            <button
              class="small mb8 outline add-button-custom vam button"
              style={{
                cursor: 'pointer',
                float: 'right',
                width: 'fit-content',
              }}
              title={'Click to unlink PO with payment request'}
              onClick={handleDelete}
            >
              {' '}
              Unlink
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
export default PoMasterCard;
