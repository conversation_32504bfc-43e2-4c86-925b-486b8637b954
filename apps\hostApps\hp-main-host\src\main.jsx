/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable no-console */
import { setRemoteDefinitions } from '@nx/react/mf';
if (process.env.NODE_ENV === 'production') {
  setTimeout(() => {
    const titleStyle = 'color: red; font-size: 40px; font-weight: bold;';
    const messageStyle = 'color: black; font-size: 16px;';
    const linkStyle = 'color: blue; font-size: 14px;';

    console.log('%cSTOP!', titleStyle);
    console.log(
      '%cThis is a browser feature intended for developers.\nDo not paste code here that you do not understand. It may allow attackers to steal your information.',
      messageStyle
    );
    console.log(
      '%cSee https://en.wikipedia.org/wiki/Self-XSS for more information.',
      linkStyle
    );
  }, 1000);

  setTimeout(() => {
    console.log = () => {};
    console.info = () => {};
    console.warn = () => {};
    console.error = () => {};
  }, 3000);
}

fetch('/assets/module-federation.manifest.json')
  .then((res) => res.json())
  .then((definitions) => setRemoteDefinitions(definitions))
  .then(() => import('./bootstrap').catch((err) => console.error(err)));
