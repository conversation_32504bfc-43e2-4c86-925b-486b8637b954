import React, { useState } from 'react';
import { Select } from 'antd';
import { pdmService } from '@hp/services';

let timeout;
let currentValue;
const fetch = (value, callback) => {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  currentValue = value;
  const getData = () => {
    pdmService.getECRECOPartNo(currentValue).then(
      (payload) => {
        let response = payload?.value;
        if (response)
          callback(
            response.map((item) => ({
              value: item.commonId,
              label: item.commonName,
            }))
          );
      },
      (err) => null
    );
  };
  if (value) {
    timeout = setTimeout(getData, 500);
  }
};

export const ECRECOPartNoSelect = (props) => {
  const [data, setData] = useState([]);
  // const [value, setValue] = useState();
  const handleSearch = (newValue) => {
    if (newValue.trim()?.length) fetch(newValue, setData);
  };
  const handleChange = (newValue) => {
    props.selectedValue(newValue);
    // setValue(newValue);
  };

  return (
    <Select
      showSearch
      labelInValue
      allowClear
      value={props.value}
      placeholder={props.placeholder}
      style={props.style}
      defaultActiveFirstOption={false}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      notFoundContent={null}
      options={data || []}
    />
  );
};
