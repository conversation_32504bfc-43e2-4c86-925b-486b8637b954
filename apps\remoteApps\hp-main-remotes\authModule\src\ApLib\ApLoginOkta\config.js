/* eslint-disable @nx/enforce-module-boundaries */
// // eslint-disable-next-line @nrwl/nx/enforce-module-boundaries
// import { environment } from 'apps/hp_main/mainshell/src/environments/environment.prod';

// export default {
//   oidc: {
//     clientId: environment.OktaClientID,
//     issuer: environment.OktaIssuer,
//     redirectUri: environment.oktaRedirectUri,
//     scopes: ['openid', 'profile', 'email'],
//     // pkce: true,
//     useInteractionCode: environment.oktaINteractionCode,
//     oktaBaseUrl: environment.oktaBaseURL
//   },

//   resourceServer: {
//     messagesUrl: '',
//   },
//   // app: {
//   //   basename: BASENAME,
//   // },
// };
// import { environment } from '@hp/mainstoreshell/environments/environment.prod'

export default {
    oidc: {
        // clientId: environment.OktaClientID,
        // issuer: environment.OktaIssuer,
        // redirectUri: environment.oktaRedirectUri,
        // scopes: ['openid', 'profile', 'email'],
        // pkce: true,
        // useInteractionCode: false, // Set to false to disable interaction code flow
        // oktaBaseUrl: environment.oktaBaseURL,
    },
    resourceServer: {
        messagesUrl: '',
    },
}
