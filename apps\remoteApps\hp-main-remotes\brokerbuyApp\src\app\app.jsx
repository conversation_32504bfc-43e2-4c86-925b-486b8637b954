import { Route, Routes } from 'react-router-dom';
import { MpnUpload } from './pages/buy-process/mpn-upload/MPN_Upload';
import { ClientBb } from './pages/settings/client/Client_bb';
import { ProcessMpn } from './pages/buy-process/process-mpn/Process_MPN';
import { Distributor } from './pages/settings/distributor/Distributor';
import { DistributorView } from './pages/settings/distributor_view/DistributorView';
import { Search } from './pages/buy-process/search/Search';
import { EmailSubFailed } from './pages/buy-process/exception/email-sub-failed/EmailSubFailed';
import { MpnView } from './pages/buy-process/mpn-view/MPN_View';
import { ErrorBoundary } from '@hp/components';
import { DynamicRoute } from '@hp/components';

export function App() {
  return (
    <ErrorBoundary>
      {' '}
      <Routes>
        <Route path="/:submenu/" element={<DynamicRoute />} />

        <Route exact path="/:submenu/mpn/mpn-upload" element={<MpnUpload />} />
        <Route
          exact
          path="/:submenu/client-details/client-bb"
          element={<ClientBb />}
        />
        <Route exact path="/:submenu/mpn/mpn-view" element={<MpnView />} />
        <Route
          exact
          path="/:submenu/mpn/process-mpn"
          element={<ProcessMpn />}
        />
        <Route
          exact
          path="/:submenu/distributor-details/distributor"
          element={<Distributor />}
        />
        <Route
          exact
          path="/:submenu/distributor-view/distributorView"
          element={<DistributorView />}
        />
        <Route exact path="/:submenu/bb-search/search" element={<Search />} />
        <Route
          exact
          path="/:submenu/exception/emailsubfail"
          element={<EmailSubFailed />}
        />
      </Routes>
    </ErrorBoundary>
  );
}
export default App;
