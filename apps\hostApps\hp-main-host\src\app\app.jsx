/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
/* eslint-disable react/display-name */
/* eslint-disable import/first */
/* eslint-disable @nx/enforce-module-boundaries */
import * as React from 'react';
import HPshell from '../hpshell/hpshell';
import { AnimatePresence, motion } from 'framer-motion';
import { Route, Routes } from 'react-router-dom';
import AppConfigProvider from './appConfig';
import relevantModules from '../../../../../scripts/hpmain.modules.json';
const MainAuthApp = React.lazy(() => import('mainAuthApp/Module'));
import '../../../../../libs/hp-assets/ico-moon/style.css';
import { MainForgotPassword } from '../../../../../libs/hp-auth/src/ap-lib/ap-forgot-password/ap-forgotpassword';
import { CommonSpinner, HpmainPagewrap } from '@hp/components';
import ApOktaLogin from '../oktaLogin/okta-login';
import 'react-datepicker/dist/react-datepicker.css';
import '../styles.scss';
import { LeftOutlined, ReloadOutlined, RightOutlined } from '@ant-design/icons';

import { utils } from '@hp/utils';
import { useDispatch } from 'react-redux';
import { refreshToken } from '@hp/mainstore';

const PageTransition = ({ children }) => {
  return (
    <motion.div
      initial={{ opacity: 0, z: -50 }}
      animate={{ opacity: 1, z: 0 }}
      exit={{ opacity: 0, z: 50 }}
      transition={{ duration: 0.5 }}
    >
      {children}
    </motion.div>
  );
};

const RouteWithTransition = ({ element }) => {
  return <PageTransition>{element}</PageTransition>;
};

export function App() {
  let dispatch = useDispatch();
  const [ColorPrimary, setColor] = React.useState('black');
  async function signMessage(message) {
    const { privateKey: privateKeyBase64, publicKey: publicKeyBase64 } =
      await utils.getOrCreateKeyPair();

    // Decode base64 to binary
    const privateKeyBinary = Uint8Array.from(atob(privateKeyBase64), (c) =>
      c.charCodeAt(0)
    ).buffer;

    // Import private key for signing
    const privateKey = await window.crypto.subtle?.importKey(
      'pkcs8',
      privateKeyBinary,
      {
        name: 'RSASSA-PKCS1-v1_5',
        hash: { name: 'SHA-256' },
      },
      true,
      ['sign']
    );

    const encoder = new TextEncoder();
    const data = encoder.encode(message);

    const signature = await window.crypto.subtle?.sign(
      { name: 'RSASSA-PKCS1-v1_5' },
      privateKey,
      data
    );

    const signatureBase64 = btoa(
      String.fromCharCode(...new Uint8Array(signature))
    );

    return {
      signature: signatureBase64,
      publicKey: publicKeyBase64, // already in base64 format
      message,
    };
  }
  const TAB_ID = crypto.randomUUID
    ? crypto.randomUUID()
    : Math.random().toString(36).substr(2, 9);

  React.useEffect(() => {
    const refreshAuthToken = async () => {
      try {
        const storedUser = JSON.parse(localStorage.getItem('user'));
        if (!storedUser?.refreshToken) return;

        const signedData = await signMessage(storedUser.refreshToken);
        const response = await dispatch(refreshToken(signedData));

        if (response?.payload?.token) {
          const updatedUser = {
            ...storedUser,
            token: response.payload.token,
            refreshToken: response.payload.refreshToken,
          };

          localStorage.setItem('user', JSON.stringify(updatedUser));
          localStorage.setItem('tokenUpdatedAt', Date.now().toString());
          // console.log("🔄 Token refreshed at:", new Date().toLocaleTimeString());
        } else {
          console.log(
            '⚠ No token in refresh response, skipping localStorage update'
          );
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
      }
    };

    const tryBecomeLeader = () => {
      const currentLock = JSON.parse(
        localStorage.getItem('tokenRefreshLeader') || '{}'
      );
      const now = Date.now();

      if (!currentLock.expiresAt || now > currentLock.expiresAt) {
        localStorage.setItem(
          'tokenRefreshLeader',
          JSON.stringify({
            ownerId: TAB_ID,
            expiresAt: now + 2 * 60 * 1000, // lock for 2 min
          })
        );
        return true;
      }

      return currentLock.ownerId === TAB_ID;
    };

    const intervalTime = 10 * 60 * 1000; // 10 minutes
    const lastUpdated =
      parseInt(localStorage.getItem('tokenUpdatedAt'), 10) || Date.now();

    const nextRefreshAt = lastUpdated + intervalTime;
    const now = Date.now();
    let remainingTime = nextRefreshAt - now;

    if (remainingTime < 0) remainingTime = 0;

    let intervalId;
    let timeoutId;

    timeoutId = setTimeout(() => {
      if (tryBecomeLeader()) {
        refreshAuthToken();
      }
      intervalId = setInterval(() => {
        if (tryBecomeLeader()) {
          refreshAuthToken();
        }
      }, intervalTime);
    }, remainingTime);

    return () => {
      clearTimeout(timeoutId);
      clearInterval(intervalId);
    };
  }, [dispatch]);

  const navButtonStyle = {
    width: '40px',
    height: '40px',
    background: '#cbcbcb',
    color: 'white',
    border: 'none',
    cursor: 'pointer',
    borderRadius: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
    transition: 'all 0.3s ease-in-out',
  };

  const reloadButtonStyle = {
    ...navButtonStyle,
    width: '40px',
    height: '40px',
  };

  const iconStyle = {
    fontSize: '20px',
    color: 'black',
  };
  function isPWA() {
    return (
      window.matchMedia('(display-mode: standalone)').matches ||
      window.navigator.standalone
    );
  }

  const [theme, setTheme] = React.useState(
    localStorage.getItem('hp-theme') || 'light'
  );
  React.useEffect(() => {
    const primaryColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--primary-color')
      .trim(); // Trim to remove extra spaces
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.setAttribute('name', 'theme-color');
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.setAttribute('content', primaryColor);

    // Set the meta theme color
    setColor(primaryColor);
  }, [theme]);
  function getModuleComponent(moduleName) {
    let ModuleComponent;

    switch (moduleName) {
      case 'mainAuthApp':
        ModuleComponent = React.lazy(() => import('mainAuthApp/Module'));
        break;
      case 'sourcingApp':
        ModuleComponent = React.lazy(() => import('sourcingApp/Module'));
        break;


      default:
        ModuleComponent = () => <div>Module not found</div>;
    }

    //App

    return ModuleComponent;
  }
  const excludedModule = 'mainAuthApp';

  return (
    <AppConfigProvider>
      {isPWA() && (
        <div
          style={{
            position: 'fixed',
            bottom: '9%',
            left: '2%',
            display: 'flex',
            gap: '10px',
            zIndex: '9999',
          }}
        >
          <button onClick={() => window.history.back()} style={navButtonStyle}>
            <LeftOutlined style={iconStyle} />
          </button>

          <button
            id="pwa-refresh-btn"
            onClick={() => window.location.reload()}
            style={reloadButtonStyle}
          >
            <ReloadOutlined style={iconStyle} />
          </button>

          <button
            onClick={() => window.history.forward()}
            style={navButtonStyle}
          >
            <RightOutlined style={iconStyle} />
          </button>
        </div>
      )}

      <React.Suspense
        fallback={
          <HpmainPagewrap>
            <CommonSpinner visible="true" />
          </HpmainPagewrap>
        }
      >
        <AnimatePresence mode="popLayout">
          <Routes>
            <Route
              path="/"
              element={
                <React.Suspense fallback={<CommonSpinner visible="true" />}>
                  <RouteWithTransition element={<MainAuthApp />} />
                </React.Suspense>
              }
            />

            <Route path="/okta-auth" element={<ApOktaLogin />} />
            <Route
              path="/api/okta/oidc/login_redirect"
              element={<ApOktaLogin />}
            />
            <Route path="/forgot-password" element={<MainForgotPassword />} />
            <Route path="/*" element={<HPshell />}>
              {relevantModules
                .filter((moduleName) => moduleName !== excludedModule) // Exclude mainAuthApp
                .map((moduleName) => {
                  const ModuleComponent = getModuleComponent(moduleName);

                  return (
                    <Route
                      key={moduleName}
                      path={`${
                        moduleName === 'financeApp'
                          ? `finance-ap/*`
                          : moduleName === 'settingsApp'
                          ? `settings/*`
                          : moduleName === 'adminApp'
                          ? 'administration/*'
                          : moduleName === 'referencesApp'
                          ? 'references/*'
                          : moduleName === 'procureApp'
                          ? 'requisition/*'
                          : moduleName === 'sourcingApp'
                          ? 'sourcing/*'
                          : moduleName === 'prLaunchApp'
                          ? 'prLaunch/*'
                          : moduleName === 'supplierApp'
                          ? 'manage-portal/*'
                          : moduleName === 'plmApp'
                          ? 'plm/*'
                          : moduleName === 'brokerbuyApp'
                          ? 'bb/*'
                          : `${moduleName}/*`
                      }`}
                      element={<ModuleComponent />}
                    />
                  );
                })}
              <Route path="*" element={<div>Module does not exist</div>} />
            </Route>
          </Routes>
        </AnimatePresence>
      </React.Suspense>
    </AppConfigProvider>
  );
}

export default App;
